USE membercentral
GO

ALTER TABLE dbo.ams_memberDataColumns 
ADD linkedDateColumnID int NULL, 
	linkedDateCompareDate date NULL, 
	linkedDateCompareDateAFID int NULL,
	linkedDateAdvanceDate date NULL, 
	linkedDateAdvanceAFID int NULL;
GO

ALTER TABLE dbo.ams_memberDataColumns ADD CONSTRAINT
	FK_ams_memberDataColumns_ams_memberDataColumns_linkedDateColumnID FOREIGN KEY
	(
	linkedDateColumnID
	) REFERENCES dbo.ams_memberDataColumns
	(
	columnID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER TABLE dbo.ams_memberDataColumns ADD CONSTRAINT
	FK_ams_memberDataColumns_af_advanceFormulas_linkedDateCompareDateAFID FOREIGN KEY
	(
	linkedDateCompareDateAFID
	) REFERENCES dbo.af_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER TABLE dbo.ams_memberDataColumns ADD CONSTRAINT
	FK_ams_memberDataColumns_af_advanceFormulas_linkedDateAdvanceAFID FOREIGN KEY
	(
	linkedDateAdvanceAFID
	) REFERENCES dbo.af_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO


ALTER FUNCTION dbo.fn_af_isInUse (@afid int)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @siteID int, @trashCount int;

	SELECT @siteID = siteID
	FROM dbo.af_advanceFormulas
	WHERE afID = @afID;

	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE rateStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE rateEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE termStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE termEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE graceAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE recogStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE recogEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ams_memberDataColumns WHERE linkedDateCompareDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ams_memberDataColumns WHERE linkedDateAdvanceAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.email_EmailBlasts WHERE afID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ams_virtualGroupConditionValues WHERE AFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE creditFromAdvanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE creditToAdvanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE advanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE advanceAFDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE nextAdvancementDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE nextPaymentDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE payThruDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE startDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE fromRefDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE toRefDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE cutoffDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE lastUpdatedDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE fromFollowUpDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE toFollowUpDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ev_recurringSeries WHERE afid = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.scheduledTasks WHERE periodDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	-- report extra
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/extra/*[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- report sub widget under d
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/subrule//condition/d/*[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- report sub widget d
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/subrule//condition/d[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- task automation run date
	SELECT @trashCount = COUNT(*) FROM dbo.tasks_automations WHERE runAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	-- task automation filters
	SELECT @trashCount = COUNT(*)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.filterXML.exist('/filter/field[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- task automation actions
	SELECT @trashCount = COUNT(*)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.actionXML.exist('/action/field[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	GOTO on_done;

	in_use:
	SET @inUse = 1;

	on_done:
	RETURN @inUse;

END
GO

ALTER PROC dbo.af_getAdvanceFormulaUsage
@afID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @siteID int;
	DECLARE @resourceTypes TABLE (usageName varchar(200), usageCount int);

	SELECT @siteID = siteID
	FROM dbo.af_advanceFormulas
	WHERE afID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Subscription Rates', COUNT(rateID)
	FROM dbo.sub_rates
	WHERE rateStartDateAFID = @afID
	OR rateEndDateAFID = @afID
	OR termStartDateAFID = @afID
	OR termEndDateAFID = @afID
	OR graceAFID = @afID
	OR recogStartDateAFID = @afID
	OR recogEndDateAFID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Custom Fields', COUNT(columnID)
	FROM dbo.ams_memberDataColumns
	WHERE linkedDateCompareDateAFID = @afID
	OR linkedDateAdvanceAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Email Blasts', COUNT(blastID)
	FROM dbo.email_EmailBlasts
	WHERE afID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Group Assignment Conditions', COUNT(DISTINCT conditionID)
	FROM dbo.ams_virtualGroupConditionValues
	WHERE afID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'MyCE Pages', COUNT(clePageID)
	FROM dbo.cms_myCEPages
	WHERE creditFromAdvanceDateAFID = @afID
	OR creditToAdvanceDateAFID = @afID
	OR advanceDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Contribution Programs', COUNT(programID)
	FROM dbo.cp_programs
	WHERE advanceAFDateAFID = @afID
	OR nextAdvancementDateAFID = @afID
	OR nextPaymentDateAFID = @afID
	OR payThruDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Referral Scheduled Reports', COUNT(scheduleReportID)
	FROM dbo.ref_scheduledReports
	WHERE startDateAFID = @afID
	OR fromRefDateAFID = @afID
	OR toRefDateAFID = @afID
	OR cutoffDateAFID = @afID
	OR lastUpdatedDateAFID = @afID
	OR fromFollowUpDateAFID = @afID
	OR toFollowUpDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Events', COUNT(seriesID)
	FROM dbo.ev_recurringSeries
	WHERE afid = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Scheduled Tasks', COUNT(taskID)
	FROM dbo.scheduledTasks
	WHERE periodDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Saved Reports', COUNT(reportID)
	FROM dbo.rpt_SavedReports
	WHERE siteID = @siteID
	AND (
		-- report extra
		otherXML.exist('/report/extra/*[@afid = sql:variable("@afID")]') = 1
		-- report sub widget under d
		OR otherXML.exist('/report/subrule//condition/d/*[@afid = sql:variable("@afID")]') = 1
		-- report sub widget d
		OR otherXML.exist('/report/subrule//condition/d[@afid = sql:variable("@afID")]') = 1
	);
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Tasks Automations', COUNT(ta.automationID)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.runAFID = @afID
	OR ta.filterXML.exist('/filter/field[@afid = sql:variable("@afID")]') = 1
	OR ta.actionXML.exist('/action/field[@afid = sql:variable("@afID")]') = 1;

	DELETE FROM @resourceTypes WHERE usageCount = 0;

	SELECT usageName, usageCount
	FROM @resourceTypes
	ORDER BY usageCount DESC, usageName;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_advanceMemberDataCompareDates

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpAdvanceDates') is not null
		DROP TABLE #tmpAdvanceDates;
	CREATE TABLE #tmpAdvanceDates (columnID int, newCompareDate date, newAdvanceDate date);

	INSERT INTO #tmpAdvanceDates (columnID, newCompareDate, newAdvanceDate)
	SELECT mdc.columnID, 
		dbo.fn_af_getAFDate(mdc.linkedDateCompareDate, afADCD.[datePart], afADCD.dateNum, afADCD.adjustTerm, afADCD.nextWeekday, afADCD.weekNumber),
		dbo.fn_af_getAFDate(mdc.linkedDateAdvanceDate, afAD.[datePart], afAD.dateNum, afAD.adjustTerm, afAD.nextWeekday, afAD.weekNumber)
	FROM dbo.ams_memberDataColumns AS mdc
	INNER JOIN dbo.af_advanceFormulas AS afAD ON afAD.AFID = mdc.linkedDateAdvanceAFID
	INNER JOIN dbo.af_advanceFormulas AS afADCD ON afADCD.AFID = mdc.linkedDateCompareDateAFID
	WHERE mdc.linkedDateAdvanceDate IS NOT NULL
	AND mdc.linkedDateAdvanceDate <= GETDATE();

	IF @@ROWCOUNT = 0
		GOTO on_done;

	UPDATE mdc
	SET mdc.linkedDateCompareDate = tmp.newCompareDate,
		mdc.linkedDateAdvanceDate = tmp.newAdvanceDate
	FROM dbo.ams_memberDataColumns as mdc
	INNER JOIN #tmpAdvanceDates as tmp on tmp.columnID = mdc.columnID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpAdvanceDates') is not null
		DROP TABLE #tmpAdvanceDates;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_setMemberData
@memberID int,
@orgID int,
@columnName varchar(128),
@columnValue varchar(max),
@recordedByMemberID int,
@byPassQueue bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY	

	DECLARE @columnID int, @allowMultiple bit, @minSelected int, @maxSelected int, @thisValueID int, @dataTypeCode varchar(20), @updateColumnID int;

	SELECT @columnID = mdc.columnID, @allowMultiple = mdc.allowMultiple, @minSelected = mdc.minSelected, @maxSelected = mdc.maxSelected, @dataTypeCode = dt.dataTypeCode
	FROM dbo.ams_memberDataColumns AS mdc
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = mdc.dataTypeID
	WHERE orgID = @orgID
	AND columnName = @columnName;

	IF @memberID is null or @columnID is null
		RAISERROR('Unable to save data. Information missing.',16,1);

	IF @dataTypeCode = 'DATE'
		SELECT @updateColumnID = columnID
		FROM dbo.ams_memberDataColumns 
		WHERE orgID = @orgID
		AND linkedDateColumnID = @columnID;

	BEGIN TRAN;
		-- delete records from memberdata for this column and user
		EXEC dbo.ams_deleteMemberData @memberID=@memberID, @columnID=@columnID, @byPassQueue=@byPassQueue;

		-- if allowMultiple = 1, then @columnValue will always be a list of valueIDs.
		IF @allowMultiple = 1 BEGIN
			select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',');
			while @thisValueID is not null BEGIN
				EXEC dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=@thisValueID, @columnValue=null, @byPassQueue=@byPassQueue;
				select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',') where listitem > @thisValueID;
			END

			-- validation for min/max selected
			IF @minSelected is not null and @maxSelected is not null BEGIN
				IF EXISTS(		
					select top 1 md.memberid
					from dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					where mdcv.columnID = @columnID
					and md.memberid = @memberID
					group by md.memberid
					having count(*) not between @minSelected and @maxSelected
				) RAISERROR('Unable to save data. Failed validation for min/max selected.',16,1);
			END
		END ELSE BEGIN
			EXEC dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=NULL, @columnValue=@columnValue, @byPassQueue=@byPassQueue;
		END
	COMMIT TRAN;

	IF @updateColumnID IS NOT NULL
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=@memberID, @columnID=@updateColumnID, 
			@recordedByMemberID=@recordedByMemberID, @byPassQueue=@byPassQueue;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

CREATE PROC dbo.ams_runLinkedDateCustomFieldRule
@orgID int,
@memberID int,
@columnID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @activeMemberID int, @linkedDateColumnID int, @linkedDateCompareDate date, @existingColValDate date, 
		@existingNumberofYears int, @numberofYears int, @columnName varchar(128);
	SET @activeMemberID = dbo.fn_getActiveMemberID(@memberID);

	SELECT @columnName = columnName, @linkedDateColumnID = linkedDateColumnID, @linkedDateCompareDate = linkedDateCompareDate
	FROM dbo.ams_memberDataColumns
	WHERE orgID = @orgID
	AND columnID = @columnID;

	IF @linkedDateColumnID IS NULL
		RAISERROR('Date column not specified.',16,1);

	-- update member
	-- when run for a single MemberID, this MUST be called from the context of PMI (via the ams_importMemberFromQueue)
	IF @memberID IS NOT NULL BEGIN
		SELECT @existingColValDate = mdcv.columnValueDate
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			AND mdc.columnID = @linkedDateColumnID
		WHERE md.memberID = @activeMemberID;

		SELECT @existingNumberofYears = mdcv.columnValueInteger
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			AND mdc.columnID = @columnID
		WHERE md.memberID = @activeMemberID;

		/* DateDiff calculation - Taken from: https://dba.stackexchange.com/a/108079 */
		SELECT @numberofYears = (CAST(CONVERT(char(8), @linkedDateCompareDate, 112) AS int) - CAST(CONVERT(char(8), @existingColValDate, 112) AS int)) / 10000 

		IF ISNULL(@numberofYears,0) <> ISNULL(@existingNumberofYears,0)
			EXEC dbo.ams_setMemberData @memberID=@activeMemberID, @orgID=@orgID, @columnName=@columnName, 
				@columnValue=@numberofYears, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		
		-- reprocess any applicable conditions without rolling up error
		IF @byPassQueue = 0 BEGIN			
			BEGIN TRY
				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT @orgID, @memberID, c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
			END TRY
			BEGIN CATCH
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		END ELSE BEGIN
			IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
				INSERT INTO #tmpMCGConditions (conditionID)
				SELECT distinct c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));
			END
		END
	END
	-- use PMI to mass update members
	ELSE BEGIN
		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
		CREATE TABLE #mc_PartialMemImport (membernumber varchar(50));

		DECLARE @sql varchar(max), @importResult xml, @errCount int, @environmentName varchar(50);
		SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

		EXEC ('ALTER TABLE #mc_PartialMemImport ADD [' + @columnName + '] int, rowID int;');

		set @sql = 'select memberNumber, numberofYears, ROW_NUMBER() over (order by memberNumber)
			from (
				select m.memberNumber, ((CAST(CONVERT(char(8), c.linkedDateCompareDate, 112) AS int) - CAST(CONVERT(char(8), mdcv_ldt.columnValueDate, 112) AS int)) / 10000) as numberofYears, mdcv.columnValueInteger as existingNumberofyears
				from dbo.ams_members as m
				left outer join dbo.ams_memberData as md 
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = ' + cast(@orgID as varchar(4)) + ' 
						and mdc.columnID = mdcv.columnID 
						and mdc.columnID = ' + cast(@columnID as varchar(10)) + '
					on md.memberid = m.memberID
				left outer join dbo.ams_memberData as md_ldt
					inner join dbo.ams_memberDataColumnValues as mdcv_ldt on mdcv_ldt.valueID = md_ldt.valueID
					inner join dbo.ams_memberDataColumns as mdc_ldt on mdc_ldt.orgID = ' + cast(@orgID as varchar(4)) + ' 
						and mdc_ldt.columnID = mdcv_ldt.columnID 
						and mdc_ldt.columnID = ' + cast(@linkedDateColumnID as varchar(10)) + '
					on md_ldt.memberid = m.memberID
				inner join dbo.ams_memberDataColumns as c on c.orgID = ' + cast(@orgID as varchar(4)) + ' 
					and c.columnID = ' + cast(@columnID as varchar(10)) + '
				where m.orgID = ' + cast(@orgID as varchar(4)) + ' 
				and m.memberid = m.activememberID 
				and m.status in (''A'',''I'')
			) as tmp
			where isnull(numberofYears,0) <> isnull(existingNumberofyears,0)';

		INSERT INTO #mc_PartialMemImport
		EXEC(@sql);

		IF EXISTS (select 1 from #mc_PartialMemImport) BEGIN
			EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Manual Partial Update', 
				@runByMemberID=@recordedByMemberID, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, 
				@bypassRO=1, @finalMSGHeader='MemberCentral automatically', @emailSubject='Linked Date Column Rule Run Report',
				@environmentName=@environmentName, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;

			IF @errCount > 0
				RAISERROR('Error running partial member update.',16,1);
		END

		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

CREATE PROC dbo.ams_autoRunLinkedDateCustomFieldRule
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCustomFields') IS NOT NULL 
		DROP TABLE #tmpCustomFields;
	CREATE TABLE #tmpCustomFields (columnID int, orgID int);

	DECLARE @orgID int, @columnID int, @sysMemberID int;
	SELECT @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

	INSERT INTO #tmpCustomFields (columnID, orgID)
	SELECT columnID, orgID
	FROM dbo.ams_memberDataColumns
	WHERE linkedDateColumnID IS NOT NULL;

	SELECT @columnID = MIN(columnID) FROM #tmpCustomFields;
	WHILE @columnID IS NOT NULL BEGIN
		SET @orgID = NULL;

		SELECT @orgID = orgID
		FROM #tmpCustomFields
		WHERE columnID = @columnID;

		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID,
			@recordedByMemberID=@sysMemberID, @byPassQueue=0;

		SELECT @columnID = MIN(columnID) FROM #tmpCustomFields WHERE columnID > @columnID;
	END
	
	IF OBJECT_ID('tempdb..#tmpCustomFields') IS NOT NULL 
		DROP TABLE #tmpCustomFields;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_createMemberDataColumn
@siteID int,
@columnName varchar(128),
@columnDesc varchar(255),
@allowMultiple bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(14,2),
@maxValueDecimal2 decimal(14,2),
@minValueDate date,
@maxValueDate date,
@linkedDateColumnID int, 
@linkedDateCompareDate date, 
@linkedDateCompareDateAFID int,
@linkedDateAdvanceDate date, 
@linkedDateAdvanceAFID int,
@recordedByMemberID int,
@columnID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @valueID int, @dataTypeID int, @displayTypeID int, @crlf varchar(10), @msgjson varchar(max);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	-- cannot be a reserved name
	IF (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1
		RAISERROR('That column name is invalid or already in use.', 16, 1);

	-- if not there, add it
	SET @columnID = null;
	SET @crlf = char(13) + char(10);
	SELECT @dataTypeID = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = @dataTypeCode;
		IF @dataTypeID is null RAISERROR('invalid datatypecode', 16, 1);
	SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode;
		IF @displayTypeID is null RAISERROR('invalid displaytypecode', 16, 1);

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO' BEGIN
		SET @displayTypeCode = 'CHECKBOX';
		SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode;
	END

	-- validations
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('CONTENTOBJ','DOCUMENTOBJ')
		SET @allowNull = 1;
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SET @allowNull = 1;
	IF @allowNull = 1
		SET @defaultValue = '';
	
	BEGIN TRAN;
		-- add column
		INSERT INTO dbo.ams_memberDataColumns (orgID, columnName, columnDesc, dataTypeID, displayTypeID, allowNull, 
			allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, 
			minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, linkedDateColumnID, 
			linkedDateCompareDate, linkedDateCompareDateAFID, linkedDateAdvanceDate, linkedDateAdvanceAFID)
		VALUES (@orgID, @columnName, @columnDesc, @dataTypeID, @displayTypeID, @allowNull, @allowNewValuesOnImport, 
			null, @isReadOnly, @allowMultiple, @minChars, @maxChars, @minSelected, @maxSelected, @minValueInt, @maxValueInt, 
			@minValueDecimal2, @maxValueDecimal2, @minValueDate, @maxValueDate, @linkedDateColumnID, @linkedDateCompareDate, 
			@linkedDateCompareDateAFID, @linkedDateAdvanceDate, @linkedDateAdvanceAFID);
		SELECT @columnID = SCOPE_IDENTITY();
		
		-- if adding a bit column, add the two values automatically
		IF @dataTypeCode = 'BIT' BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=0, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create bit column value 0', 16, 1);
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=1, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create bit column value 1', 16, 1);
		END

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create default column value', 16, 1);
			UPDATE dbo.ams_memberDataColumns 
			SET defaultValueID = @valueID
			WHERE columnID = @columnID;
		
			-- Anyone who doesnt have a value for this column needs this value.
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
			CREATE TABLE #tblMDDEF (memberid int PRIMARY KEY);

			insert into #tblMDDEF (memberid)
			select distinct m.memberid
			from dbo.ams_members as m
			left outer join dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
				on md.memberid = m.memberID
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
			and md.dataid is null;

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF;

			UPDATE dbo.ams_members
			SET dateLastUpdated = getdate()
			WHERE memberID in (select memberID from #tblMDDEF);

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
		END
	COMMIT TRAN;

	-- audit log
	SET @msgjson = 'New Custom Field [' + @columnName + '] has been created.' + @crlf
		+ 'Data stored as: ' + @dataTypeCode + @crlf
		+ 'Display field as: ' + @displayTypeCode;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES('{ "c":"auditLog", "d": {
		"AUDITCODE":"MEMCF",
		"ORGID":' + CAST(@orgID AS varchar(10)) + ',
		"SITEID":' + CAST(@siteID AS varchar(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
		"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

	-- recreate view for org
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- execute linked date custom field rules after creating org views
	IF @linkedDateColumnID IS NOT NULL
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_updateMemberDataColumn
@siteID int,
@columnID int,
@columnName varchar(128),
@columnDesc varchar(255),
@allowMultiple bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(14,2),
@maxValueDecimal2 decimal(14,2),
@minValueDate date,
@maxValueDate date,
@linkedDateColumnID int, 
@linkedDateCompareDate date, 
@linkedDateCompareDateAFID int,
@linkedDateAdvanceDate date, 
@linkedDateAdvanceAFID int,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	CREATE TABLE #tmpAuditLogData (
		[rowCode] varchar(20) PRIMARY KEY, [Name] varchar(128), [Data Stored As] varchar(max), [Display Field As] varchar(max), [Description] varchar(max),
		[New values can be added during Member Import] varchar(max), [Allow null values or no selection for this field] varchar(max),
		[Default Value] varchar(max), [Prevent editing field values] varchar(max), [Members can have multiple values for this field] varchar(max),
		[Min Characters] varchar(max), [Max Characters] varchar(max), [Min Selectable Options] varchar(max), [Max Selectable Options] varchar(max),
		[Min Value Integer] varchar(max), [Max Value Integer] varchar(max), [Min Value Decimal] varchar(max), [Max Value Decimal] varchar(max),
		[Min Value Date] varchar(max), [Max Value Date] varchar(max), [Linked Date Column] varchar(max), [Linked Date Compare Date] varchar(max),
		[Linked Date Compare Date Adv Formula] varchar(max), [Linked Date Adv Date] varchar(max), [Linked Date Adv Formula] varchar(max)
	);

	DECLARE @valueID int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int, 
		@oldColumnName varchar(128), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20), 
		@memberIDList varchar(max), @conditionIDList varchar(max), @msg varchar(max), @crlf varchar(2),
		@runLinkedDateCustomFieldRule bit = 0;

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	SET @crlf = char(13) + char(10);
	SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID;

	IF @columnID is null OR @columnName is null
		GOTO on_done;

	IF NOT EXISTS (SELECT 1 FROM dbo.ams_memberdataColumns where columnID = @columnID AND orgID = @orgID)
		RAISERROR('Invalid column.', 16, 1);

	-- new column name (if applicable) cannot be a reserved name
	IF (@columnName <> @oldColumnName AND (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1)
		RAISERROR('That column name is invalid or already in use.', 16, 1);

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
		SET @displayTypeCode = 'CHECKBOX';
	IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
		SET @displayTypeCode = 'RADIO';

	-- validations
	SELECT @olddataTypeCode = dt.dataTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
		and c.columnID = @columnID;
	SELECT @olddisplayTypeCode = dt.displayTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
		and c.columnID = @columnID;
	SELECT @displayTypeID = displayTypeID
		from dbo.ams_memberDataColumnDisplayTypes
		where displayTypeCode = @displayTypeCode;
	SELECT @dataTypeID = dataTypeID
		from dbo.ams_memberDataColumnDataTypes
		where dataTypeCode = @dataTypeCode;
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('CONTENTOBJ','DOCUMENTOBJ')
		SET @allowNull = 1;
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SET @allowNull = 1;
	IF @allowNull = 1
		SET @defaultValue = '';

	IF @linkedDateColumnID IS NOT NULL 
		AND EXISTS (SELECT 1 
					FROM dbo.ams_memberDataColumns 
					WHERE orgID = @orgID 
					AND columnID = @columnID 
					AND (ISNULL(linkedDateColumnID,0) <> @linkedDateColumnID 
						OR ISNULL(linkedDateCompareDate,'1/1/1900') <> @linkedDateCompareDate)
					)
		SET @runLinkedDateCustomFieldRule = 1;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	VALUES (
		'DATATYPECODE', 'STRING', 'STRING', 'STRING', 'CONTENTOBJ', 'BIT', 'BIT', 'STRING', 'BIT',
		'BIT', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'DECIMAL2',
		'DECIMAL2', 'DATE', 'DATE', 'STRING', 'DATE', 'STRING', 'DATE', 'STRING'
	);

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	SELECT 'OLDVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate,
		ldc.columnName,  c.linkedDateCompareDate, ldc_af.afName, c.linkedDateAdvanceDate, ldc_adv_af.afName
	FROM dbo.ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	LEFT OUTER JOIN dbo.ams_memberDataColumns AS ldc ON ldc.orgID = @orgID
		AND ldc.columnID = c.linkedDateColumnID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_af ON ldc_af.siteID = @siteID
		AND ldc_af.afID = c.linkedDateCompareDateAFID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_adv_af ON ldc_adv_af.siteID = @siteID
		AND ldc_adv_af.afID = c.linkedDateAdvanceAFID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	BEGIN TRAN;
		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT;
				IF @valueID = 0 SET @allowNull = 1;
		END 

		-- update column info
		UPDATE dbo.ams_memberDataColumns 
		SET columnName = @columnName,
			columnDesc = @columnDesc,
			allowNull = @allowNull,
			defaultValueID = nullif(@valueID,0),
			allowNewValuesOnImport = @allowNewValuesOnImport,
			isReadOnly = @isReadOnly,
			allowMultiple = @allowMultiple,
			minChars = @minChars,
			maxChars = @maxChars,
			minSelected = @minSelected,
			maxSelected = @maxSelected,
			minValueInt = @minValueInt,
			maxValueInt = @maxValueInt,
			minValueDecimal2 = @minValueDecimal2,
			maxValueDecimal2 = @maxValueDecimal2,
			minValueDate = @minValueDate,
			maxValueDate = @maxValueDate,
			linkedDateColumnID = @linkedDateColumnID, 
			linkedDateCompareDate = @linkedDateCompareDate, 
			linkedDateCompareDateAFID = @linkedDateCompareDateAFID,
			linkedDateAdvanceDate = @linkedDateAdvanceDate, 
			linkedDateAdvanceAFID = @linkedDateAdvanceAFID
		WHERE columnID = @columnID;

		IF @displayTypeCode <> @olddisplayTypeCode OR @dataTypeCode <> @olddataTypeCode 
			UPDATE vgc
			SET processValuesSection = 
						case
						when vge.expression in ('eq','neq') and @dataTypeCode in ('STRING','DECIMAL2','DATE') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') then 1
						when vge.expression in ('eq','neq','lt','lte','gt','gte') and @dataTypeCode = 'INTEGER' then 1
						when vge.expression in ('datepart','datediff') then 1
						when vge.expression in ('eq','neq') and @dataTypeCode = 'STRING' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 2
						when vge.expression in ('lt','lte','gt','gte','contains','contains_regex') and @dataTypeCode = 'STRING' then 2
						when vge.expression in ('eq','neq') and @dataTypeCode = 'BIT' then 3
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DECIMAL2' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 4
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DECIMAL2' then 4
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DATE' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 5
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DATE' then 5
						else null end
			FROM dbo.ams_virtualGroupConditions as vgc
			INNER JOIN dbo.ams_virtualGroupExpressions as vge on vge.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10));

		-- if changing the display type
		IF @displayTypeCode <> @olddisplayTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET displayTypeID = @displayTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
				
				-- err if any expressions are not 1,2,7,8 now that it will be a select
				IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
					RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1);

				-- create column values for those condition values that dont yet exist as column values
				declare @tblVals TABLE (newVal varchar(max));
				insert into @tblVals (newVal)
				select vgcv.conditionValue
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 1
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 2
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(14,2)))
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 3
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
					union
				select convert(varchar(10),vgcv.conditionValue,101)
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 4
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as date));

				DECLARE @newvalueID int, @minValue varchar(max);
				select @minValue = min(newVal) from @tblVals;
				while @minValue is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT;
					select @minValue = min(newVal) from @tblVals where newVal > @minValue;
				END

				-- get the valueID
				UPDATE vgcv
				SET vgcv.conditionValue = tmp.valueID
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN (
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 1 
					and vgcv.conditionValue = mdcv.columnvalueString
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 2 
					and cast(vgcv.conditionValue as decimal(14,2)) = mdcv.columnvalueDecimal2
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 3 
					and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 4 
					and cast(vgcv.conditionValue as date) = mdcv.columnvalueDate
				) as tmp on tmp.conditionID = vgc.conditionID
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			UPDATE dbo.ams_virtualGroupConditions
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END

		-- if changing the data type
		IF @dataTypeCode <> @olddataTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET dataTypeID = @dataTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- check ams_virtualGroupConditions for expression conflicts
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueString as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueString as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = cast(columnValueString as date)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueString as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueDecimal2 as varchar(255))
					where columnID = @columnID
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDecimal2 = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueInteger as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueInteger as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueInteger as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = convert(varchar(10),columnValueDate,101)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are date values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDate = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (11,12)
				) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1);
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueBit as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueString = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueBit as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueDecimal2 = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueBit as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END

			UPDATE dbo.ams_virtualGroupConditions
			set dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
			CREATE TABLE #tmpAffectedConditions (orgID int, conditionID int);

			UPDATE vgc
			SET vgc.subProc = CASE WHEN e.expression='datediff' then 'MD_DATEDIFF'
								WHEN e.expression='datepart' then 'MD_DATEPART'
								WHEN e.expression='contains' and @dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
								WHEN e.expression='contains_regex' and @dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
								WHEN e.expression='eq' and @dataTypeCode='STRING' then 'MD_EQ_STRING'
								WHEN e.expression='eq' and @dataTypeCode='BIT' then 'MD_EQ_BIT'
								WHEN e.expression='eq' and @dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
								WHEN e.expression='eq' and @dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
								WHEN e.expression='eq' and @dataTypeCode='DATE' then 'MD_EQ_DATE'
								WHEN e.expression='exists' and @dataTypeCode='STRING' then 'MD_EXISTS_STRING'
								WHEN e.expression='exists' and @dataTypeCode='BIT' then 'MD_EXISTS_BIT'
								WHEN e.expression='exists' and @dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
								WHEN e.expression='exists' and @dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
								WHEN e.expression='exists' and @dataTypeCode='DATE' then 'MD_EXISTS_DATE'
								WHEN e.expression='exists' and @dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
								WHEN e.expression='exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
								WHEN e.expression='gt' and @dataTypeCode='STRING' then 'MD_GT_STRING'
								WHEN e.expression='gt' and @dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
								WHEN e.expression='gt' and @dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
								WHEN e.expression='gt' and @dataTypeCode='DATE' then 'MD_GT_DATE'
								WHEN e.expression='gte' and @dataTypeCode='STRING' then 'MD_GTE_STRING'
								WHEN e.expression='gte' and @dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
								WHEN e.expression='gte' and @dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
								WHEN e.expression='gte' and @dataTypeCode='DATE' then 'MD_GTE_DATE'
								WHEN e.expression='lt' and @dataTypeCode='STRING' then 'MD_LT_STRING'
								WHEN e.expression='lt' and @dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
								WHEN e.expression='lt' and @dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
								WHEN e.expression='lt' and @dataTypeCode='DATE' then 'MD_LT_DATE'
								WHEN e.expression='lte' and @dataTypeCode='STRING' then 'MD_LTE_STRING'
								WHEN e.expression='lte' and @dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
								WHEN e.expression='lte' and @dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
								WHEN e.expression='lte' and @dataTypeCode='DATE' then 'MD_LTE_DATE'
								WHEN e.expression='neq' and @dataTypeCode='STRING' then 'MD_NEQ_STRING'
								WHEN e.expression='neq' and @dataTypeCode='BIT' then 'MD_NEQ_BIT'
								WHEN e.expression='neq' and @dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
								WHEN e.expression='neq' and @dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
								WHEN e.expression='neq' and @dataTypeCode='DATE' then 'MD_NEQ_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
								WHEN e.expression='not_exists' and @dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
								WHEN e.expression='not_exists' and @dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
								WHEN e.expression='not_exists' and @dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
								WHEN e.expression='not_exists' and @dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
								WHEN e.expression='not_exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
							END
				OUTPUT INSERTED.orgID, INSERTED.conditionID INTO #tmpAffectedConditions (orgID, conditionID)
			FROM dbo.ams_virtualGroupConditions AS vgc
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON e.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
			AND vgc.orgID = @orgID;

			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- reprocess conditions based on field
			IF EXISTS (SELECT 1 FROM #tmpAffectedConditions) BEGIN
				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT orgID, NULL, conditionID
				FROM #tmpAffectedConditions;

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				TRUNCATE TABLE #tblMCQRun;
			END

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
		END

		-- if valueID is not null, there is a def value. 
		-- Anyone who doesnt have a value for this column needs this value.
		IF nullif(@valueID,0) is not null BEGIN
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
			CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY);

			insert into #tblMDDEF (memberID)
			select distinct m.memberid
			from dbo.ams_members as m
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
				except
			select distinct md.memberID
			from dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID;

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF;

			UPDATE m
			SET m.dateLastUpdated = getdate()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID;


			-- reprocess conditions based on field
			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT distinct c.orgID, m.memberID, c.conditionID
			from dbo.ams_virtualGroupConditions as C
			cross join #tblMDDEF as m
			where c.orgID = @orgID
			and C.fieldcode = 'md_' + Cast(@columnID as varchar(10));

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			TRUNCATE TABLE #tblMCQRun;

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END

		
		-- check custom field validation ranges against existing data
		IF @minChars is not null and @maxChars is not null BEGIN
			IF @dataTypeCode = 'STRING' BEGIN
				IF EXISTS(
					select top 1 valueID
					from dbo.ams_memberdatacolumnValues
					where columnID = @columnID
					and len(columnValueString) > 0
					and len(columnValueString) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
			IF @dataTypeCode = 'CONTENTOBJ' BEGIN
				IF EXISTS(
					select top 1 mdcv.valueID
					from dbo.ams_memberdatacolumnValues as mdcv
					inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
					inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
					inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
					where mdcv.columnID = @columnID
					and len(cv.rawContent) > 0
					and len(cv.rawContent) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
		END
		IF @minSelected is not null and @maxSelected is not null BEGIN
			IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
			AND EXISTS(
				select top 1 md.memberid
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID
				group by md.memberid
				having count(*) not between @minSelected and @maxSelected
			)
			RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1);
		END
		IF @minValueInt is not null and @maxValueInt is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueInteger is not null
				and columnValueInteger not between @minValueInt and @maxValueInt
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDecimal2 is not null
				and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDate is not null and @maxValueDate is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDate is not null
				and columnValueDate not between @minValueDate and @maxValueDate
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END


		-- if there was a change in columnname
		IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
			-- update member fields
			UPDATE dbo.ams_memberFields
			SET dbField = @columnName
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- update saved query fields
			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedQueriesFields AS qf
			INNER JOIN dbo.ams_savedQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedLinkedRecordsQueriesFields AS qf
			INNER JOIN dbo.ams_savedLinkedRecordsQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));
		
			-- update virtual group conditions
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END
		
	COMMIT TRAN;

	-- new data
	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	SELECT 'NEWVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate,
		ldc.columnName,  c.linkedDateCompareDate, ldc_af.afName, c.linkedDateAdvanceDate, ldc_adv_af.afName
	FROM ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	LEFT OUTER JOIN dbo.ams_memberDataColumns AS ldc ON ldc.orgID = @orgID
		AND ldc.columnID = c.linkedDateColumnID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_af ON ldc_af.siteID = @siteID
		AND ldc_af.afID = c.linkedDateCompareDateAFID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_adv_af ON ldc_adv_af.siteID = @siteID
		AND ldc_adv_af.afID = c.linkedDateAdvanceAFID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

	-- audit log
	IF ISNULL(@msg,'') <> '' BEGIN
		SET @msg = 'Custom Field ' + STRING_ESCAPE(QUOTENAME(@oldColumnName),'json') + ' has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"MEMCF",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + @msg + '" } }');
	END

	-- recreate the view and cache tables	
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- execute linked date custom field rules after creating org views
	IF @runLinkedDateCustomFieldRule = 1
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	-- if this is a date field that is changing its name, check for subscription member date rules we may need to update
	IF @oldColumnName <> @columnName AND @dataTypeCode = 'DATE' BEGIN
	
		declare @tblJD TABLE (udid int PRIMARY KEY);
		insert into @tblJD (udid)
		select distinct udid 
		from customapps.dbo.schedTask_memberJoinDates as jd
		inner join membercentral.dbo.sites as s on s.siteCode = jd.siteCode
		where s.orgID = @orgID
		and ( joinDateFieldName = @oldColumnName 
			or rejoinDateFieldName = @oldColumnName 
			or droppedDateFieldName = @oldColumnName 
			or paidThruDateFieldName = @oldColumnName
			or renewalDateFieldName = @oldColumnName )

		IF @@ROWCOUNT = 0 GOTO on_done;

		UPDATE jd
		SET jd.joinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.joinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.rejoinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.rejoinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.droppedDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.droppedDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.paidThruDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.paidThruDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.renewalDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.renewalDateFieldName = @oldColumnName;
	END

	on_done:
	
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_importMemberFromQueue
@jobMemberID int,
@runImmediately bit,
@actualMemberID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusToProcess int, @statusProcessing int, @itemStatus int, @bypassListEmailUpdate bit,
		@orgID int, @recordTypeIDChanged bit, @recordTypeID int, @memberTypeID int, @isNewMember bit, @jobID int, 
		@prefixChanged bit, @prefix varchar(50), @firstNameChanged bit, @firstName varchar(75), @middleNameChanged bit, 
		@middleName varchar(25), @lastNameChanged bit, @lastName varchar(75), @suffixChanged bit, @suffix varchar(50),
		@membernumber varchar(50), @professionalSuffixChanged bit, @professionalSuffix varchar(100), @companyChanged bit, 
		@company varchar(200), @defaultSiteID int, @emailTypeID int, @email varchar(255), @websiteTypeID int, @website varchar(400), 
		@PLTypeID int, @LicenseNumber varchar(200), @ActiveDate datetime, @PLStatusID int, @addressTypeID int, 
		@attn varchar(100), @address1 varchar(100), @address2 varchar(100), @address3 varchar(100), @city varchar(75), 
		@stateID int, @postalCode varchar(25), @county varchar(50), @countryID int, @attnChanged bit, @address1Changed bit, 
		@address2Changed bit, @address3Changed bit, @cityChanged bit, @stateIDChanged bit, @postalCodeChanged bit, 
		@countyChanged bit, @countryIDChanged bit, @addressID int, @addressTagTypeID int, @addressTagType varchar(60), 
		@jobPhoneID int, @phoneTypeID int, @phone varchar(40), @phoneID int, @memberTypeIDChanged bit, @condCount int,
		@columnID int, @dataTypeCode varchar(20), @allowMultiple bit, @columnName varchar(255), @columnValue varchar(max),
		@columnValueString varchar(255), @columnValueDecimal2 decimal(14,2), @columnValueInteger int, 
		@columnvalueDate date, @columnValueBit bit, @columnValueContent varchar(max), @columnValueSiteResourceID int, 
		@displayTypeCode varchar(20), @isHTML bit, @contentID int, @columnvalueID int, @thisValueString varchar(255), 
		@oldStatus char(1), @statusChanged bit, @newMemberNumber varchar(50), @memberNumberChanged bit, @xmlMessage xml, 
		@orgcode varchar(10), @photoPath varchar(250), @photoPathTh varchar(250), @oldPrimaryEmailAddr varchar(255), 
		@primaryEmailTypeID int, @status char(1), @cmd varchar(8000), @lyrExtMIDChgRdyToProcessStID int, @dataXML xml, 
		@memberAdminSRID int, @minSiteID int, @emailTagTypeID int, @emailTagType varchar(60), @enteredByMemberID int, 
		@emailChanged bit, @professionalLicenseInfoChanged bit;
	declare @tblEmail TABLE (emailTypeID int, email varchar(255));
	declare @tblWebsite TABLE (websiteTypeID int, website varchar(400));
	declare @tblMPL TABLE (PLTypeID int, LicenseNumber varchar(200), ActiveDate datetime, PLStatusID int);
	declare @tblAddress TABLE (addressTypeID int, attn varchar(100), address1 varchar(100), address2 varchar(100),
		address3 varchar(100), city varchar(75), stateID int, postalCode varchar(25), county varchar(50), 
		countryID int, attnChanged bit, address1Changed bit, address2Changed bit, address3Changed bit, 
		cityChanged bit, stateIDChanged bit, postalCodeChanged bit, countyChanged bit, countryIDChanged bit);
	declare @tblAddressTags TABLE (addressTagTypeID int, addressTypeID int, addressTagType varchar(60));
	declare @tblEmailTags TABLE (emailTagTypeID int, emailTypeID int, emailTagType varchar(60));
	declare @tblPhone TABLE (phoneID int, addressTypeID int, phoneTypeID int, phone varchar(40));
	declare @tblValues TABLE (columnID int, columnValueString varchar(255), columnValueDecimal2 decimal(14,2), 
		columnValueInteger int, columnvalueDate date, columnValueBit bit, columnValueContent varchar(max), 
		columnName varchar(255), dataTypeCode varchar(20), displayTypeCode varchar(20), allowMultiple bit, actualValueID int);
	declare @tblMemberRun TABLE (memberID int PRIMARY KEY, orgID int);

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='MemberImport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusToProcess OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='Processing', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='lyrisExtIDChange', @queueStatus='readyToProcess', @queueStatusID=@lyrExtMIDChgRdyToProcessStID OUTPUT;

	-- get member info
	select @itemStatus=imp.queueStatusID, @orgID=imp.orgID, @jobID=imp.jobID, @actualMemberID=imp.actualMemberID, 
		@recordTypeIDChanged=imp.recordTypeIDChanged, @recordTypeID=imp.recordTypeID, @prefixChanged=imp.prefixChanged, 
		@prefix=imp.prefix, @firstNameChanged=imp.firstNameChanged, @firstName=imp.firstName, @middleName=imp.middleName,
		@middleNameChanged=imp.middleNameChanged, @lastNameChanged=imp.lastNameChanged, @lastName=imp.lastname, 
		@suffixChanged=imp.suffixChanged, @suffix=imp.suffix, @membernumber=imp.membernumber, 
		@professionalSuffixChanged=imp.professionalSuffixChanged, @professionalSuffix=imp.professionalSuffix,
		@companyChanged=imp.companyChanged, @company=imp.company, @status=imp.status, @statusChanged=imp.statusChanged,
		@newMemberNumber=imp.newMemberNumber, @memberNumberChanged=imp.memberNumberChanged, @memberTypeID=imp.memberTypeID, 
		@memberTypeIDChanged=imp.memberTypeIDChanged, @enteredByMemberID=mih.runByMemberID, @bypassListEmailUpdate=j.bypassListEmailUpdate
	from platformQueue.dbo.memimport_members as imp
	inner join platformQueue.dbo.memimport_jobs as j on j.jobID = imp.jobID
	inner join platformStatsMC.dbo.ams_memberImportHistory as mih on mih.jobID = j.jobID
	where imp.memberID = @jobMemberID;

	-- if memberID is not readyToProcess, kick out now
	IF @itemStatus <> @statusToProcess OR @orgID is null
		GOTO on_done;

	update platformQueue.dbo.memimport_members
	set queueStatusID = @statusProcessing
	where memberID = @jobMemberID;

	-- if protected member, mark member as done
	IF @actualmemberID IS NOT NULL AND EXISTS (SELECT 1 FROM dbo.ams_members WHERE memberID = @actualmemberID AND isProtected = 1) BEGIN
		EXEC platformQueue.dbo.queue_MemberUpdate_markMemberDone @jobID=@jobID, @jobMemberID=@jobMemberID;
		GOTO on_done;
	END

	-- temp table to store the conditions that should be reprocessed
	IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL 
		DROP TABLE #tmpMCGConditions;
	CREATE TABLE #tmpMCGConditions (conditionID int);

	insert into @tblEmail (emailTypeID, email)
	select emailTypeID, email
	from platformQueue.dbo.memimport_memberEmails
	where memberID = @jobMemberID;

	insert into @tblEmailTags (emailTagTypeID, emailTypeID, emailTagType)
	select emailTagTypeID, emailTypeID, emailTagType
	from platformQueue.dbo.memimport_memberEmailTags
	where memberID = @jobMemberID;

	insert into @tblWebsite (websiteTypeID, website)
	select websiteTypeID, website
	from platformQueue.dbo.memimport_memberWebsites
	where memberID = @jobMemberID;

	insert into @tblMPL (PLTypeID, LicenseNumber, ActiveDate, PLStatusID)
	select PLTypeID, LicenseNumber, ActiveDate, PLStatusID
	from platformQueue.dbo.memimport_memberProfessionalLicenses
	where memberID = @jobMemberID;

	insert into @tblAddress (addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, 
		county, countryID, attnChanged, address1Changed, address2Changed, address3Changed, cityChanged, 
		stateIDChanged, postalCodeChanged, countyChanged, countryIDChanged)
	select addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, 
		county, countryID, attnChanged, address1Changed, address2Changed, address3Changed, cityChanged, 
		stateIDChanged, postalCodeChanged, countyChanged, countryIDChanged
	from platformQueue.dbo.memimport_memberAddresses
	where memberID = @jobMemberID;

	insert into @tblAddressTags (addressTagTypeID, addressTypeID, addressTagType)
	select addressTagTypeID, addressTypeID, addressTagType
	from platformQueue.dbo.memimport_memberAddressTags
	where memberID = @jobMemberID;

	insert into @tblPhone (phoneID, addressTypeID, phoneTypeID, phone)
	select phoneID, addressTypeID, phoneTypeID, phone
	from platformQueue.dbo.memimport_memberPhones
	where memberID = @jobMemberID;

	insert into @tblValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, 
		columnValueBit, columnValueContent, columnName, dataTypeCode, displayTypeCode, allowMultiple, actualValueID)
	select columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit, 
		columnValueContent, columnName, dataTypeCode, displayTypeCode, allowMultiple, actualValueID
	from platformQueue.dbo.memimport_memberDataColumnValues
	where memberID = @jobMemberID;

	select @defaultSiteID = dbo.fn_getDefaultSiteIDFromOrgID(@orgID);
	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;

	SET @emailChanged = CASE WHEN EXISTS(SELECT 1 FROM @tblEmail) THEN 1 ELSE 0 END;
	SET @professionalLicenseInfoChanged = CASE WHEN EXISTS(SELECT 1 FROM @tblMPL) THEN 1 ELSE 0 END;
	
	IF @memberNumberChanged = 1 BEGIN
		select @photoPath = userAssetsPath + LOWER(@orgcode) + '\memberphotos\',
			@photoPathTh = userAssetsPath + LOWER(@orgcode) + '\memberphotosth\'
		from dbo.fn_getServerSettings();
	END

	BEGIN TRAN;
		-- If actualmemberID is 0, then create the account with basic info and let the remaining code update it
		IF @actualMemberID is null BEGIN
			EXEC dbo.ams_createMember @orgID=@orgID, @memberTypeID=@memberTypeID, @recordTypeID=@recordTypeID, @prefix=@prefix, @firstname=@firstName, 
				@middlename=@middlename, @lastname=@lastName, @suffix=@suffix, @professionalsuffix=@professionalSuffix, 
				@company=@company, @memberNumber=@membernumber, @status=@status, @bypassQueue=1, @bypassHook=0,
				@memberID=@actualMemberID OUTPUT, @newMemberNumber=@membernumber OUTPUT;

			-- create defaults on each site in the org
			select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID;
			while @minSiteID is not null begin
				EXEC dbo.ams_createMemberSiteDefault @memberID=@actualMemberID, @siteID=@minSiteID, @defaultUsername=null, @defaultPassword=null;
				select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @minSiteID;
			end

			UPDATE platformQueue.dbo.memimport_members SET newMemberID = @actualMemberID WHERE memberID = @jobMemberID;

			SET @isNewMember = 1;
		END ELSE 
			SET @isNewMember = 0;

		IF @memberNumberChanged = 1 BEGIN
			update dbo.ams_members set membernumber = @newMemberNumber, datelastupdated = getdate() where memberID = @actualMemberID;

			-- update lyris. cant run proc here because of distributed transaction issue.
			INSERT INTO platformQueue.dbo.queue_lyrisExtIDChange (orgcode, oldExternalID, newExternalID, statusID)
			VALUES (@orgcode, @membernumber, @newMemberNumber, @lyrExtMIDChgRdyToProcessStID);
		
			-- update any memberphotos
			IF dbo.fn_FileExists(@photoPath + LOWER(@membernumber) + '.jpg') = 1 BEGIN
				set @cmd = 'move ' + @photoPath + LOWER(@membernumber) + '.jpg ' + @photoPath + LOWER(@newMemberNumber) + '.jpg';
				EXEC master..xp_cmdshell @cmd, NO_OUTPUT;
			END
			IF dbo.fn_FileExists(@photoPathTh + LOWER(@membernumber) + '.jpg') = 1 BEGIN
				set @cmd = 'move ' + @photoPathTh + LOWER(@membernumber) + '.jpg ' + @photoPathTh + LOWER(@newMemberNumber) + '.jpg';
				EXEC master..xp_cmdshell @cmd, NO_OUTPUT;
			END

			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'm_membernumber'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @recordTypeIDChanged = 1 BEGIN
			update dbo.ams_members set recordTypeID = @recordTypeID, datelastupdated = getdate() where memberID = @actualMemberID;

			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'rt_role'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @memberTypeIDChanged = 1 BEGIN
			update dbo.ams_members set memberTypeID = @memberTypeID, datelastupdated = getdate() where memberID = @actualMemberID;

			-- if changing to a guest, ensure we have a default login set on each site in the org
			IF @isNewMember = 0 and @memberTypeID = 1 BEGIN
				select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID;
				while @minSiteID is not null begin
					EXEC dbo.ams_createMemberSiteDefault @memberID=@actualMemberID, @siteID=@minSiteID, @defaultUsername=null, @defaultPassword=null;
					select @minSiteID = min(siteID) from dbo.sites where orgID = @orgID and siteID > @minSiteID;
				end
			END
			
			insert into #tmpMCGConditions (conditionID)
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'm_membertypeid'
				except 
			select conditionID from #tmpMCGConditions;
		END

		IF @actualMemberID is not null BEGIN
			IF @prefixChanged = 1 BEGIN
				update dbo.ams_members set prefix = @prefix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_prefix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @firstNameChanged = 1 BEGIN
				update dbo.ams_members set firstname = @firstName, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_firstname'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @middleNameChanged = 1 BEGIN
				update dbo.ams_members set middlename = @middleName, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_middlename'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @lastNameChanged = 1 BEGIN
				update dbo.ams_members set lastname = @lastname, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_lastname'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @suffixChanged = 1 BEGIN
				update dbo.ams_members set suffix = @suffix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_suffix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @professionalSuffixChanged = 1 BEGIN
				update dbo.ams_members set professionalSuffix = @professionalSuffix, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_professionalsuffix'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @companyChanged = 1 BEGIN
				update dbo.ams_members set company = @company, datelastupdated = getdate() where memberID = @actualMemberID;

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_company'
					except 
				select conditionID from #tmpMCGConditions;
			END
			IF @statusChanged = 1 BEGIN
				select @oldStatus = [status] from dbo.ams_members where memberID = @actualMemberID;

				update dbo.ams_members set [status] = @status, datelastupdated = getdate() where memberID = @actualMemberID;

				if @oldStatus = 'I' BEGIN
					declare @msdefaultID int;

					select top 1 @msdefaultID = defaultID 
					from dbo.ams_memberSiteDefaults as msd
					inner join dbo.sites as s on s.orgID = @orgID and s.siteID = msd.siteID and msd.memberID = @actualMemberID 
					and msd.status='I';
					
					update dbo.ams_memberSiteDefaults
					set status = 'A'
					where defaultID = @msdefaultID;
				END

				insert into #tmpMCGConditions (conditionID)
				select c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'm_status'
					except 
				select conditionID from #tmpMCGConditions;
			END
		END

		IF @emailChanged = 1 BEGIN
			if @bypassListEmailUpdate = 0 and @isNewMember = 0 
				select top 1 @oldPrimaryEmailAddr = me.email, @primaryEmailTypeID = metag.emailTypeID
				from dbo.ams_memberEmails as me 
				inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
					and metag.memberID = me.memberID
					and metag.emailTypeID = me.emailTypeID
				inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
					and metagt.emailTagTypeID = metag.emailTagTypeID
					and metagt.emailTagType = 'Primary'
				where me.orgID = @orgID
				and me.memberID = @actualmemberID;

			select @emailTypeID = min(emailTypeID) from @tblEmail;
			while @emailTypeID is not null begin
				set @email = null;
				
				select @email=email from @tblEmail where emailTypeID = @emailTypeID;
				EXEC dbo.ams_saveMemberEmail @memberid=@actualMemberID, @emailTypeID=@emailTypeID, @email=@email, @enteredByMemberID=@enteredByMemberID, @byPassQueue=1;

				-- update lyris list email, cant run proc here because of distributed transaction issue.
				if @bypassListEmailUpdate = 0 and @isNewMember = 0 and isnull(@email,'') <> '' and @primaryEmailTypeID = @emailTypeID and isnull(@oldPrimaryEmailAddr,'') <> ''
					INSERT INTO platformQueue.dbo.queue_lyrisListEmailChange (orgcode, memberNumber, oldEmailAddress, newEmailAddress, statusID)
					select @orgcode, case when @memberNumberChanged = 1 then @newMemberNumber else @memberNumber end, @oldPrimaryEmailAddr, @email, qs.queueStatusID
					from platformQueue.dbo.tblQueueStatuses as qs
					inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
					where qt.queueType = 'lyrisListEmailChange'
					and qs.queueStatus = 'readyToProcess';

				select @emailTypeID = min(emailTypeID) from @tblEmail where emailTypeID > @emailTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblEmailTags) BEGIN
			select @emailTagTypeID = min(emailTagTypeID) from @tblEmailTags;
			while @emailTagTypeID is not null begin
				select @emailTypeID = null, @emailTagType = null;
				select @emailTypeID=emailTypeID, @emailTagType=emailTagType from @tblEmailTags where emailTagTypeID = @emailTagTypeID;
				EXEC dbo.ams_setMemberEmailTag @memberid=@actualMemberID, @orgID=@orgID, @emailTagType=@emailTagType, @emailTypeID=@emailTypeID, @byPassQueue=1;
				select @emailTagTypeID = min(emailTagTypeID) from @tblEmailTags where emailTagTypeID > @emailTagTypeID;
			end
		END

		IF EXISTS (select 1 from @tblWebsite) BEGIN
			select @websiteTypeID = min(websiteTypeID) from @tblWebsite;
			while @websiteTypeID is not null begin
				set @website = null;
				select @website=website from @tblWebsite where websiteTypeID = @websiteTypeID;
				EXEC dbo.ams_saveMemberWebsite @memberid=@actualMemberID, @websiteTypeID=@websiteTypeID, @website=@website, @byPassQueue=1;
				select @websiteTypeID = min(websiteTypeID) from @tblWebsite where websiteTypeID > @websiteTypeID;
			end
		END

		IF @professionalLicenseInfoChanged = 1 BEGIN
			select @PLTypeID = min(PLTypeID) from @tblMPL;
			while @PLTypeID is not null begin
				select @licenseNumber=null, @activeDate=null, @PLStatusID=null;
				select @licenseNumber=licenseNumber, @activeDate=activeDate, @PLStatusID=PLStatusID from @tblMPL where PLTypeID = @PLTypeID;
				EXEC dbo.ams_saveMemberProfessionalLicense @memberid=@actualMemberID, @PLTypeID=@PLTypeID, @licenseNumber=@licenseNumber, 
					@activeDate=@activeDate, @PLStatusID=@PLStatusID, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
				select @PLTypeID = min(PLTypeID) from @tblMPL where PLTypeID > @PLTypeID;
			end
		END

		IF EXISTS (select 1 from @tblAddress) BEGIN
			select @addressTypeID = min(addressTypeID) from @tblAddress;
			while @addressTypeID is not null begin
				select @attn=null, @address1=null, @address2=null, @address3=null, @city=null, @stateID=null,
					@postalCode=null, @county=null, @countryID=null, @addressID=null;
				select @attn=attn, @address1=address1, @address2=address2, @address3=address3, @city=city, @stateID=stateID, 
					@postalCode=postalCode, @county=county, @countryID=countryID, @attnChanged=attnChanged, 
					@address1Changed=address1Changed, @address2Changed=address2Changed, @address3Changed=address3Changed, 
					@cityChanged=cityChanged, @stateIDChanged=stateIDChanged, @postalCodeChanged=postalCodeChanged, 
					@countyChanged=countyChanged, @countryIDChanged=countryIDChanged
				from @tblAddress
				where addressTypeID=@addressTypeID;

				IF @attnChanged=0 set @attn = null;
				IF @address1Changed=0 set @address1 = null;
				IF @address2Changed=0 set @address2 = null;
				IF @address3Changed=0 set @address3 = null;
				IF @cityChanged=0 set @city = null;
				IF @postalCodeChanged=0 set @postalCode = null;
				IF @countyChanged=0 set @county = null;

				EXEC dbo.ams_saveMemberAddress @memberid=@actualMemberID, @addressTypeID=@addressTypeID, @attn=@attn, @address1=@address1,
					@address2=@address2, @address3=@address3, @city=@city, @stateID=@stateID, @postalCode=@postalCode, @county=@county,
					@countryID=@countryID, @byPassQueue=1, @addressID=@addressID OUTPUT;
				
				select @addressTypeID = min(addressTypeID) from @tblAddress where addressTypeID > @addressTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblAddressTags) BEGIN
			select @addressTagTypeID = min(addressTagTypeID) from @tblAddressTags;
			while @addressTagTypeID is not null begin
				select @addressTypeID = null, @addressTagType = null;
				select @addressTypeID=addressTypeID, @addressTagType=addressTagType from @tblAddressTags where addressTagTypeID = @addressTagTypeID;
				EXEC dbo.ams_setMemberAddressTag @memberid=@actualMemberID, @orgID=@orgID, @addressTagType=@addressTagType, @addressTypeID=@addressTypeID, @byPassQueue=1;
				select @addressTagTypeID = min(addressTagTypeID) from @tblAddressTags where addressTagTypeID > @addressTagTypeID;
			end
		END

		IF EXISTS (SELECT 1 from @tblPhone) BEGIN
			select @jobPhoneID = min(phoneID) from @tblPhone;
			while @jobPhoneID is not null begin
				select @addressTypeID = null, @addressID = null, @phoneTypeID = null, @phone = null, @phoneID = null;
				select @addressTypeID = addressTypeID, @phoneTypeID = phoneTypeID, @phone = phone from @tblPhone where phoneID = @jobPhoneID;

				select @addressID = addressID from dbo.ams_memberAddresses where orgID = @orgID and memberID=@actualMemberID and addressTypeID=@addressTypeID;
				IF @addressID is null 
					EXEC dbo.ams_saveMemberAddress @memberid=@actualMemberID, @addressTypeID=@addressTypeID, @attn=null, @address1=null,
						@address2=null, @address3=null, @city=null, @stateID=null, @postalCode=null, @county=null,
						@countryID=null, @byPassQueue=1, @addressID=@addressID OUTPUT;

				EXEC dbo.ams_saveMemberPhone @memberid=@actualMemberID, @addressID=@addressID, @phoneTypeID=@phoneTypeID, @phone=@phone, 
					@byPassQueue=1, @phoneID=@phoneID OUTPUT;

				select @jobPhoneID = min(phoneID) from @tblPhone where phoneID > @jobPhoneID;
			end
		END

		IF EXISTS (SELECT 1 from @tblValues) BEGIN
			select @columnID = min(columnID) from @tblValues;
			while @columnID is not null begin
				select @columnName = null, @dataTypeCode = null, @allowMultiple = null, @columnValueString = null, @columnValueDecimal2 = null, 
					@columnValueInteger = null, @columnvalueDate = null, @columnValueBit = null, @columnValueContent = null, @columnValueSiteResourceID = null;
				select @columnName = columnName, @dataTypeCode = dataTypeCode, @displayTypeCode = displayTypeCode, @allowMultiple = allowMultiple
				from @tblValues 
				where columnID = @columnID;

				IF @dataTypeCode <> 'CONTENTOBJ' BEGIN
					IF @allowMultiple = 0 BEGIN
						select @columnValueString = columnValueString, @columnValueDecimal2 = columnValueDecimal2, 
							@columnValueInteger = columnValueInteger, @columnvalueDate = columnvalueDate, @columnValueBit = columnValueBit
						from @tblValues 
						where columnID = @columnID;

						IF @columnValueString is not null OR @columnValueDecimal2 is not null OR @columnValueInteger is not null 
								OR @columnvalueDate is not null OR @columnValueBit is not null BEGIN
							IF @dataTypeCode = 'STRING'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueString, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
							IF @dataTypeCode = 'DECIMAL2'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueDecimal2, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
							IF @dataTypeCode = 'INTEGER'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueInteger, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
							IF @dataTypeCode = 'DATE'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnvalueDate, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
							IF @dataTypeCode = 'BIT'
								EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueBit, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
						END ELSE
							EXEC dbo.ams_deleteMemberData @memberID=@actualMemberID, @columnID=@columnID, @byPassQueue=1;
					END ELSE BEGIN
						select @columnValue = COALESCE(@columnValue + ',','') + cast(actualValueID as varchar(10)) from @tblValues where columnID = @columnID;
						IF @columnValue is not null
							EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValue, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
						ELSE
							EXEC dbo.ams_deleteMemberData @memberID=@actualMemberID, @columnID=@columnID, @byPassQueue=1;
					END
				END ELSE BEGIN
					SELECT @columnValueContent = isnull(columnValueContent,'')
						from @tblValues
						where columnID = @columnID;

					SELECT @columnValueSiteResourceID = mdcv.columnValueSiteResourceID
						from dbo.ams_memberData as md
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
						inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
						where md.memberid = @actualMemberID
						and mdc.columnID = @columnID;

					SET @isHTML = 0;
					IF @displayTypeCode = 'HTMLCONTENT'
						SET @isHTML = 1;

					IF @columnValueSiteResourceID is not null BEGIN
						SELECT @contentID = contentID FROM dbo.cms_content WHERE siteResourceID = @columnValueSiteResourceID;
						EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=@isHTML, @contentTitle='', @contentDesc='', 
							@rawcontent=@columnValueContent, @memberID=@actualMemberID;
					END ELSE BEGIN
						EXEC dbo.cms_createContentField @siteID=@defaultSiteID, @isHTML=@isHTML, @isActive=1, @languageID=1, @contentTitle='', 
							@contentDesc='', @rawContent=@columnValueContent, @contentID=@contentID OUTPUT, @contentSiteResourceID=@columnValueSiteResourceID OUTPUT;
						EXEC dbo.ams_setMemberData @memberID=@actualMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@columnValueSiteResourceID, @recordedByMemberID=@enteredByMemberID, @byPassQueue=1;
					END
				END

				select @columnID = min(columnID) from @tblValues where columnID > @columnID;
			end
		END

		-- populate conditions to run
		-- if we are changing memberType, we need to force run groups so the user can get in the correct system group.
		-- If we need to run groups, and there are no conditions, then we need to run GroupsOnly to ensure they run.
		IF @isNewMember = 1 OR @memberTypeIDChanged = 1 BEGIN
			SELECT @condCount = COUNT(conditionID) FROM #tmpMCGConditions;
			IF @condCount = 0
				INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
				VALUES (@jobID, @orgID, @actualMemberID, NULL, 'GroupsOnly', @runImmediately);
			ELSE
				INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
				SELECT DISTINCT @jobID, @orgID, @actualMemberID, conditionID, 'ConditionsAndGroups', @runImmediately
				FROM #tmpMCGConditions;
		END ELSE 
			INSERT INTO platformQueue.dbo.memimport_conditionsToRun (jobID, orgID, memberID, conditionID, type, runImmediately)
			SELECT DISTINCT @jobID, @orgID, @actualMemberID, conditionID, 'ConditionsAndGroupsChanged', @runImmediately
			FROM #tmpMCGConditions;

		IF @isNewMember = 0 BEGIN
			WITH memberChanges as (
				SELECT memberID, replace(replace(dbo.fn_cleanInvalidXMLChars(change),'\','\\'),'"','\"') as change
				FROM platformQueue.dbo.memimport_mongo
				WHERE memberID = @actualMemberID
				AND jobID = @jobID
			)
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"historyEntries_SYS_ADMIN_MEMUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_MEMUPDATE", "ORGID":' + cast(@orgID as varchar(10)) + 
				', "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + 
				', "RECEIVERMEMBERID":' + cast(@actualMemberID as varchar(10)) + 
				', "MAINMESSAGE":"Member Info Updated", "CHANGES":[], "MESSAGES":[ ' +
				stuff((SELECT ', "' + change + '"'
					FROM memberChanges
					ORDER BY change
					FOR XML PATH(''), TYPE).value('.','varchar(max)')
				,1,1,'') + ' ]'+
				', "SELFUPDATE":' + CASE WHEN @enteredByMemberID = @actualMemberID THEN 'true' ELSE 'false' END + 
				', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
				' } }'
			FROM (
				SELECT DISTINCT memberID
				FROM memberChanges
			) tmp;
		END
	COMMIT TRAN;

	-- run the updateMemberData hook for each site in the org
	IF @isNewMember = 0 BEGIN
		DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));
		DECLARE @tblUpdatedFields TABLE (columnName sysname);
		DECLARE @tblSites TABLE (memberAdminSiteResourceID int);

		INSERT INTO @tblUpdatedFields (columnName)
		SELECT columnName
		FROM platformQueue.dbo.memimport_mongo
		WHERE memberID = @actualMemberID
		AND jobID = @jobID;

		-- pro lic fields
		IF @professionalLicenseInfoChanged = 1
			INSERT INTO @tblUpdatedFields (columnName)
			SELECT DISTINCT c.columnName
			FROM @tblMPL AS tmp
			INNER JOIN dbo.ams_memberProfessionalLicenseTypes AS mlt ON mlt.orgID = @orgID
				AND mlt.PLTypeID = tmp.PLTypeID
			INNER JOIN dbo.cache_org_memberColumns AS c ON c.orgID = @orgID
				AND c.[area] = 'Professional Licenses'
				AND c.columnName IN (mlt.PLName + '_licenseNumber',mlt.PLName + '_activeDate',mlt.PLName + '_status')
				EXCEPT
			SELECT columnName
			FROM @tblUpdatedFields;

		SELECT @dataXML = (
			SELECT @actualMemberID AS memberid, @membernumber AS membernumber, (
				SELECT columnName AS c
				FROM @tblUpdatedFields
				FOR XML PATH(''), ROOT('updated'),TYPE) 
			FOR XML PATH ('data'));
		
		INSERT INTO @tblSites (memberAdminSiteResourceID)
		SELECT memberAdminSiteResourceID
		FROM dbo.sites
		WHERE orgID = @orgID;

		SELECT @memberAdminSRID = min(memberAdminSiteResourceID) FROM @tblSites;
		WHILE @memberAdminSRID IS NOT NULL BEGIN
			INSERT INTO @tblHookListeners (executionType, objectPath)
			EXEC dbo.hooks_runHook @event='updateMemberData', @siteResourceID=@memberAdminSRID, @dataXML=@dataXML;

			SELECT @memberAdminSRID = min(memberAdminSiteResourceID) FROM @tblSites WHERE memberAdminSiteResourceID > @memberAdminSRID;
		END
	END
	
	-- mark member as done
	EXEC platformQueue.dbo.queue_MemberUpdate_markMemberDone @jobID=@jobID, @jobMemberID=@jobMemberID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_runEarliestLicenseDateRule
@orgID int,
@memberID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpLicenseTypeIDs') IS NOT NULL
		DROP TABLE #tmpLicenseTypeIDs;
	IF OBJECT_ID('tempdb..#tmpLicenseStatusIDs') IS NOT NULL
		DROP TABLE #tmpLicenseStatusIDs;
	CREATE TABLE #tmpLicenseTypeIDs (PLTypeID int);
	CREATE TABLE #tmpLicenseStatusIDs (PLStatusID int);

	DECLARE @activeMemberID int, @columnID int, @columnName varchar(128), @earliestLicenseDate date, @existingBarDate date;
	SELECT @activeMemberID = dbo.fn_getActiveMemberID(@memberID);

	select @columnID = led.columnID, @columnName = mdc.columnName
	from dbo.ams_memberProfessionalLicenseEarliestDate led
	inner join ams_memberDataColumns mdc on mdc.columnId = led.columnID
	where led.orgID = @orgID;

	IF @columnID is null
		RAISERROR('Date column not specified.',16,1);

	INSERT INTO #tmpLicenseTypeIDs (PLTypeID)
	select PLTypeID
	from dbo.ams_memberProfessionalLicenseEarliestDateTypes
	where orgID = @orgID
	and PLTypeID is not null;

	IF @@ROWCOUNT = 0 
		INSERT INTO #tmpLicenseTypeIDs (PLTypeID)
		SELECT PLTypeID 
		FROM dbo.ams_memberProfessionalLicenseTypes 
		WHERE orgID = @orgID;

	INSERT INTO #tmpLicenseStatusIDs (PLStatusID)
	select PLStatusID
	from dbo.ams_memberProfessionalLicenseEarliestDateTypes
	where orgID = @orgID
	and PLStatusID is not null;

	IF @@ROWCOUNT = 0 
		INSERT INTO #tmpLicenseStatusIDs (PLStatusID)
		SELECT PLStatusID 
		FROM dbo.ams_memberProfessionalLicenseStatuses 
		WHERE orgID = @orgID;

	-- update member
	-- when run for a single MemberID, this MUST be called from the context of PMI (via the ams_importMemberFromQueue)
	IF @memberID IS NOT NULL BEGIN
		SELECT @earliestLicenseDate = MIN(mpl.activeDate)
		FROM dbo.ams_memberProfessionalLicenses as mpl
		INNER JOIN #tmpLicenseTypeIDs AS lt ON lt.PLTypeID = mpl.PLTypeID
		INNER JOIN #tmpLicenseStatusIDs AS ls ON ls.PLStatusID = mpl.PLStatusID
		WHERE mpl.memberID = @activeMemberID;

		SELECT @existingBarDate = mdcv.columnValueDate
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			and mdc.columnID = @columnID
		WHERE md.memberID = @activeMemberID;

		IF ISNULL(@earliestLicenseDate,'1/1/1900') <> ISNULL(@existingBarDate,'1/1/1900')
			EXEC dbo.ams_setMemberData @memberID=@activeMemberID, @orgID=@orgID, @columnName=@columnName, 
				@columnValue=@earliestLicenseDate, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		
		-- reprocess any applicable conditions without rolling up error
		IF @byPassQueue = 0 BEGIN			
			BEGIN TRY
				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT @orgID, @memberID, c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
			END TRY
			BEGIN CATCH
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		END ELSE BEGIN
			IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
				INSERT INTO #tmpMCGConditions (conditionID)
				SELECT distinct c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));
			END
		END
	END
	-- use PMI to mass update members
	ELSE BEGIN
		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
		CREATE TABLE #mc_PartialMemImport (membernumber varchar(50));

		DECLARE @sql varchar(max), @importResult xml, @errCount int, @environmentName varchar(50);
		SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

		EXEC ('ALTER TABLE #mc_PartialMemImport ADD [' + @columnName + '] date, rowID int;');

		set @sql = 'select memberNumber, earliestLicenseDate, ROW_NUMBER() over (order by memberNumber)
			from (
				select m.memberNumber, min(mpl.activeDate) as earliestLicenseDate, mdcv.columnValueDate as existingBarDate
				from dbo.ams_members as m
				left outer join dbo.ams_memberProfessionalLicenses as mpl 
					inner join #tmpLicenseStatusIDs as ls on ls.PLStatusID = mpl.PLStatusID 
					inner join #tmpLicenseTypeIDs as lt on lt.PLTypeID = mpl.PLTypeID 
				on mpl.memberid = m.memberid 
				left outer join dbo.ams_memberData as md 
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.columnID = ' + cast(@columnID as varchar(10)) + '
					on md.memberid = m.memberID
				where m.orgID = ' + cast(@orgID as varchar(4)) + ' 
				and m.memberid = m.activememberID 
				and m.status in (''A'',''I'')
				group by m.memberNumber, mdcv.columnValueDate
			) as tmp
			where isnull(earliestLicenseDate,''1/1/1900'') <> isnull(ExistingBarDate,''1/1/1900'')';

		INSERT INTO #mc_PartialMemImport
		EXEC(@sql);

		IF EXISTS (select 1 from #mc_PartialMemImport) BEGIN
			EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Manual Partial Update', 
				@runByMemberID=@recordedByMemberID, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, 
				@bypassRO=1, @finalMSGHeader='MemberCentral automatically', @emailSubject='Earliest License Date Rule Run Report',
				@environmentName=@environmentName, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;

			IF @errCount > 0
				RAISERROR('Error running partial member update.',16,1);
		END

		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
	END

	IF OBJECT_ID('tempdb..#tmpLicenseTypeIDs') IS NOT NULL
		DROP TABLE #tmpLicenseTypeIDs;
	IF OBJECT_ID('tempdb..#tmpLicenseStatusIDs') IS NOT NULL
		DROP TABLE #tmpLicenseStatusIDs;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.job_runDailyMaintenanceJobs
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tier varchar(12), @jobRunOrder tinyint, @procName varchar(100), @todayDOM tinyint, 
		@todayDOW tinyint, @start datetime, @totalMS int;
	DECLARE @tblJobs TABLE (runorder tinyint IDENTITY(1,1) PRIMARY KEY, procName varchar(100), prodOnly bit, domOnly tinyint, dowOnly tinyint);

	SELECT @tier=tier from dbo.fn_getServerSettings();
	set @todayDOM = datepart(d,getdate());
	set @todayDOW = datepart(dw,getdate());

	/* setup jobs */
	INSERT INTO @tblJobs (procName, prodOnly, domOnly, dowOnly)
	VALUES 

	-- highpriority, needs to run as close to midnight as possible
	('ams_advanceMemberDataCompareDates', 0, null, null),
	('sub_advanceRates', 0, null, null),
	('cp_advanceProgramDates', 0, null, null),
	('ams_advanceRollingDateConditionValues', 0, null, null),
	('rpt_advanceRollingDates', 0, null, null),
	('tasks_advanceRollingDates', 0, null, null),
	('cle_advanceCreditDates', 0, null, null),
	('ams_recalcVirtualGroupsBasedOnDateConditions', 0, null, null),
	('ams_memberAddressData_reprocessConditionsBasedOnDateChanges', 0, null, null),
	('sub_job_statusChangeAcceptedToActive', 0, null, null),
	('sub_job_statusChangeActiveInactiveToExpired', 0, null, null),
	('sub_job_statusChangeOfferedToOfferExpired', 0, null, null),
	('tr_autoCloseSystemBatches', 0, null, null),
	('cp_convertDueInstallmentsToSale', 0, null, null),
	('cp_setContributionAsDelinquent', 0, null, null),
	('cp_setContributionAsCancelled', 0, null, null),
	('cp_setContributionAsCurrent', 0, null, null),
	('cp_setContributionAsFulfilled', 0, null, null),
	('ams_populateTrackGroupMembershipHistoryQueue', 0, null, null),
	('ams_autoRunLinkedDateCustomFieldRule', 0, null, null),

	-- tasks that can run a little later
	('tr_autoMarkClosedInvoicesAsPaid', 0, null, null),
	('sub_closePendingInvoices', 0, null, null),
	('tr_autoCloseEmptyOpenInvoices', 0, null, null),
	('tr_makeInvoicesDelinquent', 0, null, null),
	('tr_advanceInvoiceExpectedPayDate', 0, null, null),
	('tr_autoRecognizeDeferred', 0, null, null), 
	('tr_autoPayInvoices', 1, null, null), 
	('tr_populatecache_tr_ARByDayInvoiceProfileChanges', 0, null, null);

	select @jobRunOrder = min(runorder) 
	from @tblJobs
	where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'))
	and (domOnly is null OR (domOnly = @todayDOM))
	and (dowOnly is null OR (dowOnly = @todayDOW));

	while @jobRunOrder is not null begin
		select @procName = procName from @tblJobs where runOrder = @jobRunOrder;

		BEGIN TRY
			SET XACT_ABORT OFF;	

			SET @start = getdate();
			EXEC @procName;
			SET @totalMS = datediff(ms,@start,getdate());

			INSERT INTO platformStatsMC.dbo.job_runtimeLog (procname, timeMS) 
			VALUES (@procName, @totalMS);	

			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH

		-- delay inside the loop to address apparent parallel issue
		WAITFOR DELAY '00:00:01';

		select @jobRunOrder = min(runorder) 
		from @tblJobs
		where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'))
		and (domOnly is null OR (domOnly = @todayDOM))
		and (dowOnly is null OR (dowOnly = @todayDOW))
		and runOrder > @jobRunOrder;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_deleteMemberProfessionalLicense
@memberID int,
@PLID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @PLTypeID int, @orgID int;

	select @PLTypeID = PLTypeID from dbo.ams_memberProfessionalLicenses where plid = @PLID;
	select @orgID = orgID from dbo.ams_members where memberID = @memberID;

	BEGIN TRAN;
		delete from dbo.ams_memberProfessionalLicenses
		where plid = @PLID
		and memberID = @memberID;

		update dbo.ams_members
		set dateLastUpdated = getdate()
		where memberID = @memberID;
	COMMIT TRAN;
	
	-- check for earliest license date rule	
	IF EXISTS (SELECT 1 FROM dbo.ams_memberProfessionalLicenseEarliestDate WHERE orgID = @orgID)
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=@memberid, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	-- reprocess any applicable conditions
	IF @byPassQueue = 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, @memberID, vgc.conditionID
		from dbo.ams_virtualGroupConditions as vgc
		inner join dbo.ams_memberProfessionalLicenseTypes as plt on plt.orgID = vgc.orgID and plt.PLTypeID = @PLTypeID
		where vgc.orgID = @orgID
		and vgc.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\');

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_orgSettingsSetEarliestLicenseDate
@orgID int,
@columnID int,
@earlyLicenseTypes varchar(max),
@earlyLicenseStatuses varchar(max),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @runEarliestLicenseDateRule bit = 0;

	BEGIN TRAN;
		delete from dbo.ams_memberProfessionalLicenseEarliestDateTypes
		where orgID = @orgID;

		delete from dbo.ams_memberProfessionalLicenseEarliestDate
		where orgID = @orgID;

		IF @columnID is not null BEGIN
			insert into dbo.ams_memberProfessionalLicenseEarliestDate (orgID, columnID)
			values (@orgID, @columnID);

			IF ISNULL(@earlyLicenseTypes,'') <> '' OR ISNULL(@earlyLicenseStatuses,'') <> '' BEGIN
				-- to bypass empty varchar list
				IF ISNULL(@earlyLicenseTypes,'') = ''
					set @earlyLicenseTypes = '0';

				insert into dbo.ams_memberProfessionalLicenseEarliestDateTypes (orgID, PLTypeID, PLStatusID)
				select distinct @orgID as orgID, nullif(elt.listitem,0) as PLTypeID, nullif(els.listitem,0) as PLStatusID
				from dbo.fn_varcharListToTable(@earlyLicenseTypes,',') as elt
				outer apply dbo.fn_varcharListToTable(@earlyLicenseStatuses,',') as els;
			END

			SET @runEarliestLicenseDateRule = 1;
		END
	COMMIT TRAN;

	IF @runEarliestLicenseDateRule = 1
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=NULL, @recordedByMemberID=@recordedByMemberID, @bypassQueue=0;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_saveMemberProfessionalLicense
@memberID int,
@PLTypeID int,
@licenseNumber varchar(200),
@activeDate datetime,
@PLStatusID int,
@recordedByMemberID int,
@byPassQueue bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @PLID int;
	select @orgID = orgID from dbo.ams_memberProfessionalLicenseTypes where PLTypeID = @PLTypeID;
	select @PLID = PLID from dbo.ams_memberProfessionalLicenses where memberID = @memberID and PLTypeID = @PLTypeID

	IF @PLID IS NOT NULL
		IF @licenseNumber IS NULL AND @activeDate IS NULL AND @PLStatusID IS NULL
			EXEC dbo.ams_deleteMemberProfessionalLicense @memberID=@memberID, @PLID=@PLID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		ELSE
			update dbo.ams_memberProfessionalLicenses
			set licenseNumber = nullif(@licenseNumber,''),
				activeDate = @activeDate,
				PLStatusID = @PLStatusID
			where memberID = @memberID 
			and PLTypeID = @PLTypeID;
	ELSE
		insert into dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, licenseNumber, activeDate, PLStatusID)
		values (@memberID, @PLTypeID, nullif(@licenseNumber,''), @activeDate, @PLStatusID);

	-- set member as updated
	update dbo.ams_members
	set dateLastUpdated = getdate()
	where memberID = @memberID;

	-- check for earliest license date rule
	IF EXISTS (SELECT 1 FROM dbo.ams_memberProfessionalLicenseEarliestDate where orgID = @orgID)
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=@memberid, @recordedByMemberID=@recordedByMemberID, @byPassQueue=@byPassQueue;

	-- reprocess any applicable conditions
	IF @byPassQueue = 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, @memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\');

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END ELSE BEGIN
		IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
			select @orgid=orgID from dbo.ams_memberProfessionalLicenseTypes where PLTypeID = @PLTypeID;

			INSERT INTO #tmpMCGConditions (conditionID)
			SELECT distinct c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\')
				except 
			select conditionID from #tmpMCGConditions;
		END
	END


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO


-- update linked date columns
UPDATE mdc
SET mdc.linkedDateColumnID = led.columnID,
	mdc.linkedDateCompareDate = led.runDate,
	mdc.linkedDateCompareDateAFID = led.runDateAdvanceDateAFID,
	mdc.linkedDateAdvanceDate = led.advanceDate,
	mdc.linkedDateAdvanceAFID = led.advanceDateAFID
FROM dbo.ams_memberDataColumns as mdc
INNER JOIN dbo.ams_memberProfessionalLicenseEarliestDate AS led ON led.numberOfYearsInLicensureCFColumnID = mdc.columnID;
GO

-- run linked date rules
EXEC dbo.ams_autoRunLinkedDateCustomFieldRule;
GO


-- drop unused columns
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP CONSTRAINT FK_ams_memberProfessionalLicenseEarliestDate_numberOfYearsCF_ams_memberDataColumns;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP CONSTRAINT FK_ams_memberProfessionalLicenseEarliestDate_Advance_af_advanceFormulas;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP CONSTRAINT FK_ams_memberProfessionalLicenseEarliestDate_runDate_af_advanceFormulas;
GO

ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP COLUMN numberOfYearsInLicensureCFColumnID;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP COLUMN runDate;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP COLUMN runDateAdvanceDateAFID;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP COLUMN advanceDate;
ALTER TABLE dbo.ams_memberProfessionalLicenseEarliestDate DROP COLUMN advanceDateAFID;
GO

-- drop unused procs
DROP PROC dbo.ams_orgSettingsSetEarliestLicenseadvanceDates;
DROP PROC dbo.ams_runNumberofYearsinLicensureRule;
GO

-- mdc export/import
USE dataTransfer
GO

DROP TABLE dbo.sync_ams_memberDataColumns;
GO

CREATE TABLE dbo.sync_ams_memberDataColumns (orgCode varchar(10), orgID int, columnID int, columnName varchar(255), columnDesc varchar(255), 
	dataTypeUID uniqueidentifier, dataTypeCode varchar(20), displayTypeCode varchar(20), displayTypeUID uniqueidentifier, allowNewValuesOnImport bit, 
	allowNull bit, defaultValueID int, isReadOnly bit, allowMultiple bit, [uid] uniqueidentifier,  minChars int, maxChars int, minSelected int, 
	maxSelected int, minValueInt int, maxValueInt int, minValueDecimal2 decimal(14, 2), maxValueDecimal2 decimal(14, 2), minValueDate date, maxValueDate date, 
	defaultValue varchar(255), valueID int, fieldValue varchar(255), linkedDateColumnUID uniqueidentifier, linkedDateCompareDate date, 
	linkedDateCompareDateAFUID uniqueidentifier, linkedDateAdvanceDate date, linkedDateAdvanceAFUID uniqueidentifier, finalAction char(1));
GO

USE membercentral
GO

ALTER PROC dbo.ams_exportMemberDataColumnStructure
@orgID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgCode varchar(10), @cmd varchar(4000), @svrName varchar(40);

	SELECT @orgCode = orgCode FROM dbo.organizations WHERE orgID = @orgID;
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgcode = @orgcode;

	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumns (orgCode, orgID, columnID, columnName, columnDesc, dataTypeUID, dataTypeCode, displayTypeCode, displayTypeUID, 
		allowNewValuesOnImport, allowNull, defaultValueID, isReadOnly, allowMultiple, [uid],  minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, 
		minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate,
		linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID)
	select @orgCode, @orgID, f.columnID, f.columnName, f.columnDesc, mdata.uid, mdata.dataTypeCode, mdisp.displayTypeCode, mdisp.uid, f.allowNewValuesOnImport, 
		f.allowNull, f.defaultValueID, f.isReadOnly, f.allowMultiple, f.uid, f.minChars, f.maxChars, f.minSelected, f.maxSelected, f.minValueInt, f.maxValueInt, f.minValueDecimal2, 
		f.maxValueDecimal2, f.minValueDate, f.maxValueDate, 
		case when f.defaultValueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then mdcv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
				when 'DATE' then convert(varchar(10), mdcv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
				else null
				end
		else null
		end, fv.valueID, 
		case when fv.valueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then fv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), fv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), fv.columnValueInteger)
				when 'DATE' then convert(varchar(10), fv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), fv.columnValueBit)
			else null
			end 
		else null
		end as fieldValue,
		ldc.[uid], f.linkedDateCompareDate, af_c.[uid], f.linkedDateAdvanceDate, af_adv.[uid]
	from dbo.ams_memberDataColumns as f
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = f.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = f.defaultValueID and mdcv.columnID = f.columnID
	left outer join dbo.ams_memberDataColumnValues as fv on fv.columnID = f.columnID and mdisp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	left outer join dbo.ams_memberDataColumns as ldc on ldc.orgID = @orgID and ldc.columnID = f.linkedDateColumnID
	left outer join dbo.af_advanceFormulas as af_c on af_c.afID = f.linkedDateCompareDateAFID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.afID = f.linkedDateAdvanceAFID
	where f.orgID = @orgID
	order by f.columnName, fieldValue;

	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumnValues (orgCode, orgID, valueID, columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit)
	select smdc.orgCode, smdc.orgID, fv.valueID, smdc.columnID, fv.columnValueString, fv.columnValueDecimal2 , fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = smdc.defaultValueID and fv.columnID = smdc.columnID
	where smdc.orgID = @orgID
		union
	select smdc.orgCode, smdc.orgID, fv.valueID, smdc.columnID, fv.columnValueString, fv.columnValueDecimal2, fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = smdc.valueID and fv.columnID = smdc.columnID and smdc.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	where smdc.orgID = @orgID;

	-- export to file
	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, columnID, columnName, columnDesc, dataTypeUID, dataTypeCode, displayTypeCode, displayTypeUID, allowNewValuesOnImport, allowNull, defaultValueID, isReadOnly, allowMultiple, [uid],  minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate, linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID, finalAction FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_ams_memberDataColumns.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, useValue, finalAction FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_ams_memberDataColumnValues.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear tables
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_prepareMemberDataColumnImport
@siteID int,
@pathToImport varchar(400),
@importResult XML OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpOrgAddMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgAddMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpNewColumnNames') IS NOT NULL
		DROP TABLE #tmpNewColumnNames;

	CREATE TABLE #tblImportErrors (rowID int IDENTITY(1,1), msg varchar(600), errorCode varchar(30));
	CREATE TABLE #tmpOrgMemberDataColumns (columnID int, [uid] uniqueidentifier, columnName varchar(128), columnDesc varchar(255), 
		dataTypeUID uniqueidentifier, displayTypeUID uniqueidentifier, dataTypeCode varchar(20), displayTypeCode varchar(20), 
		allowNewValuesOnImport bit, allowNull bit, isReadOnly bit, allowMultiple bit, minChars int, maxChars int, minSelected int, 
		maxSelected int, minValueInt int, maxValueInt int, minValueDecimal2 decimal(14,2), maxValueDecimal2 decimal(14,2), 
		minValueDate date, maxValueDate date, defaultValueID int, defaultValue varchar(255), valueID int, fieldValue varchar(255), 
		linkedDateColumnUID uniqueidentifier, linkedDateCompareDate date, linkedDateCompareDateAFUID uniqueidentifier, 
		linkedDateAdvanceDate date, linkedDateAdvanceAFUID uniqueidentifier);
	CREATE TABLE #tmpOrgMemberDataColumnValues (valueID int, columnID int, columnValueString varchar(255), 
		columnValueDecimal2 decimal(14,2), columnValueInteger int, columnvalueDate date, columnValueBit bit, columnValueSiteResourceID int);
	CREATE TABLE #tmpOrgAddMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgUpdateMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgDeleteMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgDeleteMemberDataColumnValues (columnUID uniqueidentifier, columnName varchar(128), valueID int, 
		[value] varchar(255), useCount int);
	CREATE TABLE #tmpNewColumnNames (columnUID uniqueidentifier, columnName varchar(128), isNewColumn bit);

	DECLARE @orgID int, @orgCode varchar(10), @cmd varchar(400), @svrName varchar(40);
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	SELECT @orgID = o.orgID, @orgCode = o.orgCode
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	-- ensure file is present
	IF dbo.fn_FileExists(@pathToImport + 'sync_ams_memberDataColumns.bcp') = 0 OR dbo.fn_FileExists(@pathToImport + 'sync_ams_memberDataColumnValues.bcp') = 0 BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgcode = @orgcode;

	-- import data
	SET @cmd = 'bcp datatransfer.dbo.sync_ams_memberDataColumns in ' + @pathToImport + 'sync_ams_memberDataColumns.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_ams_memberDataColumnValues in ' + @pathToImport + 'sync_ams_memberDataColumnValues.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_ams_memberDataColumns SET orgID = @orgID WHERE orgCode = @orgCode;
	UPDATE datatransfer.dbo.sync_ams_memberDataColumnValues SET orgID = @orgID WHERE orgCode = @orgCode;

	-- existing org member data columns
	INSERT INTO #tmpOrgMemberDataColumns (columnID, [uid], columnName, columnDesc, dataTypeUID, displayTypeUID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, allowNull, 
		isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, 
		defaultValueID, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate,linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID)
	select f.columnID, f.uid, f.columnName, f.columnDesc, mdata.uid, mdisp.uid, mdata.dataTypeCode, mdisp.displayTypeCode, f.allowNewValuesOnImport, f.allowNull, f.isReadOnly, 
		f.allowMultiple, f.minChars, f.maxChars, f.minSelected, f.maxSelected, f.minValueInt, f.maxValueInt, f.minValueDecimal2, f.maxValueDecimal2, f.minValueDate, f.maxValueDate, 
		f.defaultValueID, 
		case when f.defaultValueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then mdcv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
				when 'DATE' then convert(varchar(10), mdcv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
				else null
				end
		else null
		end, fv.valueID, 
		case when fv.valueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then fv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), fv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), fv.columnValueInteger)
				when 'DATE' then convert(varchar(10), fv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), fv.columnValueBit)
			else null
			end 
		else null
		end as fieldValue,
		ldc.[uid], f.linkedDateCompareDate, af_c.[uid], f.linkedDateAdvanceDate, af_adv.[uid]
	from dbo.ams_memberDataColumns as f
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = f.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = f.defaultValueID and mdcv.columnID = f.columnID
	left outer join dbo.ams_memberDataColumnValues as fv on fv.columnID = f.columnID and mdisp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	left outer join dbo.ams_memberDataColumns as ldc on ldc.orgID = @orgID and ldc.columnID = f.linkedDateColumnID
	left outer join dbo.af_advanceFormulas as af_c on af_c.afID = f.linkedDateCompareDateAFID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.afID = f.linkedDateAdvanceAFID
	where f.orgID = @orgID
	order by f.columnName, fieldValue;

	INSERT INTO #tmpOrgMemberDataColumnValues (valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, columnValueSiteResourceID)
	select tmp.valueID, tmp.columnID, fv.columnValueString, fv.columnValueDecimal2 , fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit, fv.columnValueSiteResourceID
	from #tmpOrgMemberDataColumns as tmp
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = tmp.valueID;

	/*** Process the Data for Comparisons ***/
	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column data type UID does not match an existing data type UID', 'INVALIDDATATYPE'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.dataTypeUID
	where smdc.orgID = @orgID
	and mdata.dataTypeID is null;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column display type UID does not match an existing display type UID', 'INVALIDDISPLAYTYPE'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.uid = smdc.displayTypeUID
	where smdc.orgID = @orgID
	and mdisp.displayTypeID is null;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct 'Advance Formula UID [' + CAST(smdc.linkedDateCompareDateAFUID AS varchar(36)) + '] does not match an existing Advance Formula UID', 'INVALIDAFUID'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.af_advanceFormulas as af on af.siteID = @siteID and af.[uid] = smdc.linkedDateCompareDateAFUID
	where smdc.orgID = @orgID
	and smdc.linkedDateCompareDateAFUID is not null
	and af.afID is null;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct 'Advance Formula UID [' + CAST(smdc.linkedDateAdvanceAFUID AS varchar(36)) + '] does not match an existing Advance Formula UID', 'INVALIDAFUID'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.af_advanceFormulas as af on af.siteID = @siteID and af.[uid] = smdc.linkedDateAdvanceAFUID
	where smdc.orgID = @orgID
	and smdc.linkedDateAdvanceAFUID is not null
	and af.afID is null;

	IF EXISTS (SELECT 1 FROM #tblImportErrors)
		GOTO on_done;

	-- new fields
	INSERT INTO #tmpOrgAddMemberDataColumns ([uid], columnName)
	select distinct smdc.[uid], smdc.columnName
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = smdc.[uid]
	where smdc.orgID = @orgID
	and mdc.[uid] is null;

	-- find fields to be updated
	INSERT INTO #tmpOrgUpdateMemberDataColumns ([uid], columnName)
	SELECT DISTINCT [uid], columnName
	from (
		SELECT smdc.[uid], smdc.columnName, smdc.columnDesc, smdc.dataTypeCode, smdc.displayTypeCode, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
			smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
			smdc.minValueDate, smdc.maxValueDate, smdc.defaultValue, smdc.fieldValue, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate,
			smdc.linkedDateCompareDateAFUID, smdc.linkedDateAdvanceDate, smdc.linkedDateAdvanceAFUID
		FROM dataTransfer.dbo.sync_ams_memberDataColumns as smdc
		LEFT OUTER JOIN #tmpOrgAddMemberDataColumns as tmp on tmp.[uid] = smdc.[uid]
		WHERE smdc.orgID = @orgID
		AND tmp.[uid] IS NULL
			EXCEPT
		SELECT tmp.[uid], tmp.columnName, tmp.columnDesc, tmp.dataTypeCode, tmp.displayTypeCode, tmp.allowNewValuesOnImport, tmp.allowNull, tmp.isReadOnly, tmp.allowMultiple, tmp.minChars, tmp.maxChars, 
			tmp.minSelected, tmp.maxSelected, tmp.minValueInt, tmp.maxValueInt, tmp.minValueDecimal2, tmp.maxValueDecimal2, tmp.minValueDate, tmp.maxValueDate, tmp.defaultValue, tmp.fieldValue,
			tmp.linkedDateColumnUID, tmp.linkedDateCompareDate, tmp.linkedDateCompareDateAFUID, tmp.linkedDateAdvanceDate, tmp.linkedDateAdvanceAFUID
		FROM #tmpOrgMemberDataColumns as tmp
		INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
	) tmp;

	-- flip and test incase the org has more fields
	INSERT INTO #tmpOrgUpdateMemberDataColumns ([uid], columnName)
	SELECT DISTINCT [uid], columnName
	FROM (
		SELECT tmp.[uid], tmp.columnName, tmp.columnDesc, tmp.dataTypeCode, tmp.displayTypeCode, tmp.allowNewValuesOnImport, tmp.allowNull, tmp.isReadOnly, tmp.allowMultiple, tmp.minChars, tmp.maxChars, 
			tmp.minSelected, tmp.maxSelected, tmp.minValueInt, tmp.maxValueInt, tmp.minValueDecimal2, tmp.maxValueDecimal2, tmp.minValueDate, tmp.maxValueDate, tmp.defaultValue, tmp.fieldValue,
			tmp.linkedDateColumnUID, tmp.linkedDateCompareDate, tmp.linkedDateCompareDateAFUID, tmp.linkedDateAdvanceDate, tmp.linkedDateAdvanceAFUID
		FROM #tmpOrgMemberDataColumns as tmp
		INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			EXCEPT
		SELECT smdc.[uid], smdc.columnName, smdc.columnDesc, smdc.dataTypeCode, smdc.displayTypeCode, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
			smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
			smdc.minValueDate, smdc.maxValueDate, smdc.defaultValue, smdc.fieldValue, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate,
			smdc.linkedDateCompareDateAFUID, smdc.linkedDateAdvanceDate, smdc.linkedDateAdvanceAFUID
		FROM dataTransfer.dbo.sync_ams_memberDataColumns as smdc
		LEFT OUTER JOIN #tmpOrgAddMemberDataColumns as tmp on tmp.[uid] = smdc.[uid]
		WHERE smdc.orgID = @orgID
		AND tmp.[uid] IS NULL
	) tmp
	WHERE NOT EXISTS (SELECT 1 FROM #tmpOrgUpdateMemberDataColumns WHERE [uid] = tmp.[uid]);

	-- to be deleted fields
	INSERT INTO #tmpOrgDeleteMemberDataColumns ([uid], columnName)
	SELECT DISTINCT mdc.[uid], mdc.columnName
	FROM #tmpOrgMemberDataColumns AS mdc
	LEFT OUTER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID 
		AND smdc.[uid] = mdc.[uid]
	WHERE smdc.columnID IS NULL;

	-- validate new columnNames
	INSERT INTO #tmpNewColumnNames (columnUID, columnName, isNewColumn)
	select [uid], columnName, 1
	from #tmpOrgAddMemberDataColumns
		union all
	select [uid], columnName, 0
	from #tmpOrgUpdateMemberDataColumns;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select 'Duplicate ' + columnName + ' column found', 'DUPLICATECOLNAME'
	from #tmpNewColumnNames
	group by columnName	
	having count(columnName) > 1;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- remove to be deleted columns
	DELETE tmp
	FROM #tmpNewColumnNames as tmp
	INNER JOIN #tmpOrgDeleteMemberDataColumns as tmpD on tmpD.columnName = tmp.columnName;

	-- remove existing column names
	DELETE tmp
	FROM #tmpNewColumnNames as tmp
	INNER JOIN #tmpOrgMemberDataColumns as tmpE on tmpE.columnName = tmp.columnName;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select columnName + ' column name is invalid.', 'INVALIDCOLUMNNAME'
	from #tmpNewColumnNames
	where dbo.fn_ams_isValidNewMemberViewColumn(@orgID,columnName) = 1;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- if changing the display type
	-- err if any expressions are not 1,2,7,8 now that it will be a select
	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the selected display type', 'INVALIDVGCDISPLAYTYPE'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and expressionID not in (1,2,7,8)
	where mdc.dataTypeCode <> 'BIT' 
	and smdc.dataTypeCode <> 'BIT'
	and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
	and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX');

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- if changing the data type
	IF EXISTS (select smdc.columnName 
				from #tmpOrgUpdateMemberDataColumns as tmp
				inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
				inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
				where mdc.dataTypeCode <> smdc.dataTypeCode) BEGIN
		
		-- string to decimal
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueString as decimal(14,2))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Decimal Number (2) data type.','INVALIDSTRINGTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- string to integer
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueInteger = cast(mdcv.columnValueString as int)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'INTEGER';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Whole Number data type.','INVALIDSTRINGTOINTEGER');
			GOTO on_done;
		END CATCH

		-- string to date
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDate = cast(mdcv.columnValueString as date)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'DATE';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Date data type.','INVALIDSTRINGTODATE');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the ' + mdata.dataType + ' data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.displayTypeUID
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (9,10)
		where mdc.dataTypeCode = 'STRING'
		and smdc.dataTypeCode IN ('INTEGER','DECIMAL2','DATE');

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- string to bit
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueBit = cast(mdcv.columnValueString as bit)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'BIT';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Boolean data type.','INVALIDSTRINGTOBIT');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Boolean data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (3,4,5,6,9,10)
		where mdc.dataTypeCode = 'STRING'
		and smdc.dataTypeCode= 'BIT';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- decimal2 to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueDecimal2 as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'DECIMAL2'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are decimal values not compatible with the Text String data type.','INVALIDDECIMAL2TOSTRING');
			GOTO on_done;
		END CATCH

		-- integer to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Text String data type.','INVALIDINTEGERTOSTRING');
			GOTO on_done;
		END CATCH

		-- integer to decimal2
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Decimal Number (2) data type.','INVALIDINTEGERTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- integer to bit
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueBit = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'BIT';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Boolean data type.','INVALIDINTEGERTOBIT');
			GOTO on_done;
		END CATCH
		

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Boolean data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (3,4,5,6)
		where mdc.dataTypeCode = 'INTEGER'
		and smdc.dataTypeCode= 'BIT';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- date to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = convert(varchar(10),mdcv.columnValueDate,101)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'DATE'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are date values not compatible with the Text String data type.','INVALIDDATETOSTRING');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Text String data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (11,12)
		where mdc.dataTypeCode = 'DATE'
		and smdc.dataTypeCode= 'STRING';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- bit to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueBit as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Text String data type.','INVALIDBITTOSTRING');
			GOTO on_done;
		END CATCH

		-- bit to decimal2
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueBit as decimal(14,2))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Text Decimal Number (2) data type.','INVALIDBITTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- bit to integer
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueInteger = cast(mdcv.columnValueBit as int)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'INTEGER';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Whole Number data type.','INVALIDBITTOINTEGER');
			GOTO on_done;
		END CATCH
	END

	-- check custom field validation ranges against existing data
	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minChars is not null and maxChars is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDSTRINGDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'STRING'
		and smdc.minChars is not null 
		and  smdc.maxChars is not null
		and len(mdcv.columnValueString) > 0
		and len(mdcv.columnValueString) not between smdc.minChars and smdc.maxChars;

		IF @@ROWCOUNT > 0
			GOTO on_done;

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDSTRINGDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
		inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
		inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'CONTENTOBJ'
		and smdc.minChars is not null 
		and  smdc.maxChars is not null
		and len(cv.rawContent) > 0
		and len(cv.rawContent) not between smdc.minChars and smdc.maxChars;
		
		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minSelected is not null and maxSelected is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select columnName + ' column has existing members that have field options that are outside the data validation range.', 'INVALIDFIELDOPTS'
		from (
			select md.memberID, count(md.dataID) as optCount, smdc.columnName, smdc.minSelected, smdc.maxSelected
			from #tmpOrgUpdateMemberDataColumns as tmp
			inner join (
				select distinct [uid], columnID
				from #tmpOrgMemberDataColumns 
				where allowMultiple = 1 ) as mdc on mdc.[uid] = tmp.[uid] 
			inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
			inner join (
				select distinct [uid], columnName, minSelected, maxSelected
				from dataTransfer.dbo.sync_ams_memberDataColumns
				where orgID = @orgID 
				and minSelected is not null
				and maxSelected is not null ) as smdc on smdc.[uid] = tmp.[uid]
			group by md.memberID, smdc.columnName, smdc.minSelected, smdc.maxSelected
			having count(*) not between smdc.minSelected and smdc.maxSelected
		) as tmp;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END
	
	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueInt is not null and maxValueInt is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'INTEGER'
		and smdc.minValueInt is not null 
		and smdc.maxValueInt is not null
		and mdcv.columnValueInteger is not null
		and mdcv.columnValueInteger not between smdc.minValueInt and smdc.maxValueInt;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueDecimal2 is not null and maxValueDecimal2 is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'DECIMAL2'
		and smdc.minValueDecimal2 is not null 
		and smdc.maxValueDecimal2 is not null
		and mdcv.columnValueDecimal2 is not null
		and mdcv.columnValueDecimal2 not between smdc.minValueDecimal2 and smdc.maxValueDecimal2;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueDate is not null and maxValueDate is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'DECIMAL2'
		and smdc.minValueDate is not null 
		and smdc.maxValueDate is not null
		and mdcv.columnValueDate is not null
		and mdcv.columnValueDate not between smdc.minValueDate and smdc.maxValueDate;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END
	
	-- if changing the display type from non-options to options we need to create column values for those condition values that dont yet exist as column values
	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumnValues (orgCode, orgID, valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, finalAction)
	select @orgCode, @orgID, null as valueID, smdc.columnID, vgcv.conditionValue, null , null, null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 1
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueString = vgcv.conditionValue
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, cast(vgcv.conditionValue as decimal(14,2)), null, null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 2
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(14,2))
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, null, cast(vgcv.conditionValue as int), null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 3
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueInteger = cast(vgcv.conditionValue as int)
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, null, null, cast(vgcv.conditionValue as date), null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 4
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueDate = cast(vgcv.conditionValue as date)
	where mdcv.valueID is null;

	-- update finalAction
	-- new fields
	UPDATE smdc
	SET smdc.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumns AS smdc
	INNER JOIN #tmpOrgAddMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid]
	WHERE smdc.orgID = @orgID
	AND smdc.finalAction IS NULL;

	-- new field values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'A'
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL;

	-- new field default values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'A'
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL;

	-- update fields
	UPDATE smdc
	SET smdc.finalAction = 'C'
	FROM dataTransfer.dbo.sync_ams_memberDataColumns AS smdc
	INNER JOIN #tmpOrgUpdateMemberDataColumns AS tmp ON tmp.[uid] = smdc.[uid]
	WHERE smdc.orgID = @orgID
	AND smdc.finalAction IS NULL;

	-- update useValue for update field values
	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'STRING' 
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.columnValueString = mdcv.columnValueString;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'STRING' 
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.columnValueString = mdcv.columnValueString COLLATE Latin1_General_CS_AS;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'DECIMAL2' 
	AND smdcv.columnValueDecimal2 = mdcv.columnValueDecimal2;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'INTEGER' 
	AND smdcv.columnValueInteger = mdcv.columnValueInteger;
	
	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'DATE' 
	AND smdcv.columnValueDate = mdcv.columnValueDate;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'BIT' 
	AND smdcv.columnValueBit = mdcv.columnValueBit;

	-- new field values for update fields
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'C'
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL
	AND smdcv.useValue IS NULL;

	-- update finalAction for update field default values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'C'
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL
	AND smdcv.useValue IS NULL;

	-- delete field values
	INSERT INTO #tmpOrgDeleteMemberDataColumnValues (columnUID, columnName, valueID, value, useCount)
	select mdc.[uid], mdc.columnName, mdc.valueID, mdc.fieldValue, count(distinct md.dataID)
	from #tmpOrgMemberDataColumns AS mdc
	inner join #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID 
	inner join dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
		and smdc.finalAction = 'C'
	left outer join dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.columnID = smdc.columnID
		and smdcv.useValue = mdcv.valueID
	left outer join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	where smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	and smdc.dataTypeCode <> 'BIT'
	and smdcv.valueID is null
	group by mdc.[uid], mdc.columnName, mdc.valueID, mdc.fieldValue;

	on_done:
	-- return the xml results
	SELECT @importResult = (
		SELECT GETDATE() AS "@date",

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			FROM dataTransfer.dbo.sync_ams_memberDataColumns
			WHERE orgID = @orgID
			AND finalAction = 'A'
			FOR XML path('field'), ROOT('newfields'), TYPE),'<newfields/>'),

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			FROM dataTransfer.dbo.sync_ams_memberDataColumns
			WHERE orgID = @orgID
			AND finalAction = 'C'
			FOR XML path('field'), ROOT('updatefields'), TYPE),'<updatefields/>'),

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			from #tmpOrgDeleteMemberDataColumns
			FOR XML path('field'), ROOT('removefields'), TYPE),'<removefields/>'),

			ISNULL((SELECT DISTINCT columnUID AS "@uid", columnName AS "@columnName", value AS "@value", useCount AS "@useCount"
			from #tmpOrgDeleteMemberDataColumnValues
			FOR XML path('value'), ROOT('removevalues'), TYPE),'<removevalues/>'),
			
			ISNULL((SELECT dbo.fn_RegExReplace(ISNULL(msg,''),'[^\x20-\x7E]','') AS "@msg", errorCode AS "@errorcode"
			FROM #tblImportErrors
			ORDER BY MSG
			FOR XML path('error'), ROOT('errors'), TYPE),'<errors/>')

		FOR XML PATH('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpOrgAddMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgAddMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpNewColumnNames') IS NOT NULL
		DROP TABLE #tmpNewColumnNames;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_importMemberDataColumns
@siteID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- need this here so we can go in and out of snapshot as needed inside the transaction
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsAdded') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsAdded;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsUpdated') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsUpdated;
	IF OBJECT_ID('tempdb..#tmpDeleteMemberDataColumnValues') IS NOT NULL 
		DROP TABLE #tmpDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#mdcvToAdd') IS NOT NULL 
		DROP TABLE #mdcvToAdd;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tblMDDEFCols') IS NOT NULL 
		DROP TABLE #tblMDDEFCols;
	IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
		DROP TABLE #tblMDDEF;
	IF OBJECT_ID('tempdb..#tblExistingMDDEF') IS NOT NULL 
		DROP TABLE #tblExistingMDDEF;
	IF OBJECT_ID('tempdb..#tmpColumnConditions') IS NOT NULL 
		DROP TABLE #tmpColumnConditions;
	
	CREATE TABLE #tmpMemberDataColumns (columnName varchar(128), dataTypeID int, displayTypeID int, columnDesc varchar(255), allowNewValuesOnImport bit, allowNull bit, 
		isReadOnly bit, allowMultiple bit, [uid] uniqueidentifier,  minChars int, maxChars int, minSelected int, maxSelected int, minValueInt int, maxValueInt int, 
		minValueDecimal2 decimal(14, 2), maxValueDecimal2 decimal(14,2), minValueDate date, maxValueDate date, syncColumnID int, syncDefaultValueID int, 
		linkedDateColumnID int, linkedDateColumnUID uniqueidentifier, linkedDateCompareDate date, linkedDateCompareDateAFID int, linkedDateAdvanceDate date, 
		linkedDateAdvanceAFID int, finalAction char(1));
	CREATE TABLE #tmpUpdateMemberDataColumns (columnID int, columnName varchar(128), oldColumnName varchar(128), dataTypeID int, displayTypeID int, 
		dataTypeCode varchar(20), displayTypeCode varchar(20), oldDataTypeCode varchar(20), oldDisplayTypeCode varchar(20));
	CREATE TABLE #tmpMemberDataColumnsAdded (columnID int, [uid] uniqueidentifier);
	CREATE TABLE #tmpMemberDataColumnsUpdated (columnID int, [uid] uniqueidentifier);
	CREATE TABLE #tmpDeleteMemberDataColumnValues (valueID int);
	CREATE TABLE #mdcvToAdd (valueID int, columnID int, columnUID uniqueidentifier, columnValueString varchar(255), columnValueDecimal2 decimal(14,2), columnValueInteger int, 
		columnvalueDate date, columnValueBit bit, syncColumnID int, syncValueID int);
	CREATE TABLE #tmpMDCDef (columnID int PRIMARY KEY, dataTypeCode varchar(20), displayTypeCode varchar(20));
	CREATE TABLE #tblMDDEFCols (columnID int, valueID int);
	CREATE TABLE #tblMDDEF (memberID int);
	CREATE TABLE #tblExistingMDDEF (memberID int, columnID int);
	CREATE TABLE #tmpColumnConditions (columnID int, conditionID int, dataTypeID int);

	DECLARE @orgID int, @deleteColumnIDList varchar(max), @minValueID int, @minColumnID int;
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	INSERT INTO #tmpMemberDataColumns (columnName, dataTypeID, displayTypeID, columnDesc, allowNewValuesOnImport, allowNull, isReadOnly, allowMultiple, [uid], minChars, maxChars, 
		minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, syncColumnID, syncDefaultValueID, 
		linkedDateColumnUID, linkedDateCompareDate, linkedDateCompareDateAFID, linkedDateAdvanceDate, linkedDateAdvanceAFID, finalAction)
	select distinct smdc.columnName, mdata.dataTypeID, mdisp.displayTypeID, smdc.columnDesc, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
		smdc.[uid], smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
		smdc.minValueDate, smdc.maxValueDate, smdc.columnID, smdc.defaultValueID, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate, af_c.AFID, 
		smdc.linkedDateAdvanceDate, af_adv.AFID, smdc.finalAction
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.dataTypeUID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.[uid] = smdc.displayTypeUID
	left outer join dbo.af_advanceFormulas as af_c on af_c.siteID = @siteID and af_c.[uid] = smdc.linkedDateCompareDateAFUID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.siteID = @siteID and af_adv.[uid] = smdc.linkedDateAdvanceAFUID
	where smdc.orgID = @orgID
	and smdc.finalAction in ('A','C');

	INSERT INTO #tmpUpdateMemberDataColumns (columnID, columnName, oldColumnName, dataTypeID, displayTypeID, dataTypeCode, displayTypeCode, oldDataTypeCode, oldDisplayTypeCode)
	select mdc.columnID, tmp.columnName, mdc.columnName, mdataNew.dataTypeID, mdispNew.displayTypeID, mdataNew.dataTypeCode, mdispNew.displayTypeCode, 
		mdataOld.dataTypeCode, mdispOld.displayTypeCode
	from #tmpMemberDataColumns as tmp
	inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.[uid] = tmp.[uid]
	inner join dbo.ams_memberDataColumnDataTypes as mdataNew on mdataNew.dataTypeID = tmp.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdispNew on mdispNew.displayTypeID = tmp.displayTypeID
	inner join dbo.ams_memberDataColumnDataTypes as mdataOld on mdataOld.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdispOld on mdispOld.displayTypeID = mdc.displayTypeID;

	INSERT INTO #mdcvToAdd (columnID, columnUID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit, syncColumnID, syncValueID)
	select distinct mdc.columnID, smdc.[uid], smdcv.columnValueString, smdcv.columnValueDecimal2, smdcv.columnValueInteger, smdcv.columnvalueDate, smdcv.columnValueBit, 
		smdc.columnID, smdcv.valueID
	from datatransfer.dbo.sync_ams_memberDataColumnValues as smdcv
	inner join datatransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	left outer join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.[uid] = smdc.[uid]
	where smdcv.orgID = @orgID
	and smdcv.finalAction = 'A'
	and smdc.finalAction in ('A','C')
	and smdcv.useValue is null;

	-- delete columns list
	select @deleteColumnIDList =  coalesce(@deleteColumnIDList + ',','') +  cast(mdc.columnID as varchar(10))
	from dbo.ams_memberDataColumns as mdc
	left outer join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
	where mdc.orgID = @orgID
	and smdc.columnID is null
	group by mdc.columnID;

	-- delete field values
	INSERT INTO #tmpDeleteMemberDataColumnValues (valueID)
	select distinct mdcv.valueID
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnValues AS mdcv ON mdc.orgID = @orgID AND mdcv.columnID = mdc.columnID 
	inner join dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
		and smdc.finalAction = 'C'
	left outer join dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.columnID = smdc.columnID
		and smdcv.useValue = mdcv.valueID
	where smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	and smdc.dataTypeCode <> 'BIT'
	and smdcv.valueID is null;

	IF NOT EXISTS (select 1 from #tmpMemberDataColumns) AND NOT EXISTS (select 1 from #tmpDeleteMemberDataColumnValues) AND @deleteColumnIDList IS NULL
		GOTO on_done;

	BEGIN TRAN;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @deleteColumnIDList IS NOT NULL BEGIN
			EXEC dbo.ams_removeMemberDataColumn @siteID=@siteID, @columnIDList=@deleteColumnIDList, @recordedByMemberID=@recordedByMemberID;
			WAITFOR DELAY '00:00:00:500'
		END

		-- new columns
		IF EXISTS (select 1 from #tmpMemberDataColumns where finalAction = 'A') BEGIN
			INSERT INTO dbo.ams_memberDataColumns (orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, defaultValueID, isReadOnly, 
				allowMultiple, minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate)
				OUTPUT Inserted.columnID, Inserted.uid 
				INTO #tmpMemberDataColumnsAdded
			select @orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, NULL AS defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, 
				minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate
			from #tmpMemberDataColumns
			where finalAction = 'A'
				except 
			select orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, 
				minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate
			from dbo.ams_memberDataColumns
			where orgID = @orgID;

			UPDATE mdcv 
			SET mdcv.columnID = tmp.columnID
			FROM #mdcvToAdd as mdcv
			INNER JOIN #tmpMemberDataColumnsAdded as tmp on tmp.[uid] = mdcv.columnUID;
		END

		-- update columns
		IF EXISTS (select 1 from #tmpMemberDataColumns where finalAction = 'C') BEGIN
			UPDATE mdc
			SET mdc.columnName = tmp.columnName,
				mdc.columnDesc = tmp.columnDesc,
				mdc.allowNull = tmp.allowNull,
				mdc.defaultValueID = null,
				mdc.allowNewValuesOnImport = tmp.allowNewValuesOnImport,
				mdc.isReadOnly = tmp.isReadOnly,
				mdc.allowMultiple = tmp.allowMultiple,
				mdc.minChars = tmp.minChars,
				mdc.maxChars = tmp.maxChars,
				mdc.minSelected = tmp.minSelected,
				mdc.maxSelected = tmp.maxSelected,
				mdc.minValueInt = tmp.minValueInt,
				mdc.maxValueInt = tmp.maxValueInt,
				mdc.minValueDecimal2 = tmp.minValueDecimal2,
				mdc.maxValueDecimal2 = tmp.maxValueDecimal2,
				mdc.minValueDate = tmp.minValueDate,
				mdc.maxValueDate = tmp.maxValueDate
					OUTPUT inserted.columnID, inserted.uid
					INTO #tmpMemberDataColumnsUpdated
			FROM #tmpMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.uid = tmp.uid
			WHERE tmp.finalAction = 'C';
		END

		-- create field values
		INSERT INTO #tmpMDCDef (columnID, dataTypeCode, displayTypeCode)
		select c.columnID, dt.dataTypeCode, dp.displayTypeCode
		from (
			select distinct columnID from #mdcvToAdd
		) as tmp
		INNER JOIN dbo.ams_memberDataColumns as c on c.columnID = tmp.columnID
		INNER JOIN dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
		INNER JOIN dbo.ams_memberDataColumnDisplayTypes as dp on dp.displayTypeID = c.displayTypeID;

		IF @@ROWCOUNT > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValueBulk @orgID=@orgID;

			-- update valueID
			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef ON tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'STRING' 
			AND tmpDef.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
			AND tmp.columnValueString = mdcv.columnValueString;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'STRING' 
			AND tmpDef.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
			AND tmp.columnValueString = mdcv.columnValueString COLLATE Latin1_General_CS_AS;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'DECIMAL2' 
			AND tmp.columnValueDecimal2 = mdcv.columnValueDecimal2;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'INTEGER' 
			AND tmp.columnValueInteger = mdcv.columnValueInteger;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'DATE' 
			AND tmp.columnValueDate = mdcv.columnValueDate;
			
			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'BIT' 
			AND tmp.columnValueBit = mdcv.columnValueBit;

			UPDATE smdcv
			SET smdcv.useValue = mdcv.valueID
			FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
			INNER JOIN datatransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
			INNER JOIN #mdcvToAdd AS mdcv ON mdcv.syncValueID = smdc.defaultValueID
			WHERE smdcv.orgID = @orgID
			AND smdcv.useValue IS NULL;
		END

		-- update defaultValue
		IF EXISTS (select 1 from #tmpMemberDataColumns where syncDefaultValueID is not null) BEGIN
			UPDATE mdc 
			SET mdc.defaultValueID = smdcv.useValue
			FROM dbo.ams_memberDataColumns AS mdc 
			INNER JOIN #tmpMemberDataColumns AS tmp ON tmp.[uid] = mdc.[uid]
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.valueID = tmp.syncDefaultValueID
			WHERE mdc.orgID = @orgID;

			INSERT INTO #tblMDDEFCols (columnID, valueID)
			select columnID, defaultValueID
			from dbo.ams_memberDataColumns
			where orgID = @orgID
			and defaultValueID is not null;

			INSERT INTO #tblExistingMDDEF (memberID, columnID)
			select distinct mActive.memberID, mdc.columnID
			from dbo.ams_members as m 
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberData as md on md.memberID = mActive.memberID
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			inner join dbo.ams_memberDataColumns AS mdc on mdc.orgID = @orgID and mdc.columnID = mdcv.columnID
			where mdc.defaultValueID is not null;

			-- Anyone who doesnt have a value for this column needs this value.
			INSERT INTO dbo.ams_memberData (memberID, valueID)
				OUTPUT Inserted.memberID
				INTO #tblMDDEF
			select distinct mActive.memberID, tmp.valueID
			from dbo.ams_members as m 
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			cross join #tblMDDEFCols as tmp
			where not exists (select 1 from #tblExistingMDDEF as def where def.memberID = mActive.memberID and def.columnID = tmp.columnID);

			UPDATE m
			SET m.dateLastUpdated = GETDATE()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID;
		END

		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where displayTypeCode <> olddisplayTypeCode or dataTypeCode <> olddataTypeCode) BEGIN
			UPDATE vgc
			SET processValuesSection = 
						case
						when vge.expression in ('eq','neq') and tmp.dataTypeCode in ('STRING','DECIMAL2','DATE') and tmp.displayTypeCode in ('RADIO','SELECT','CHECKBOX') then 1
						when vge.expression in ('eq','neq','lt','lte','gt','gte') and tmp.dataTypeCode = 'INTEGER' then 1
						when vge.expression in ('datepart','datediff') then 1
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'STRING' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 2
						when vge.expression in ('lt','lte','gt','gte','contains','contains_regex') and tmp.dataTypeCode = 'STRING' then 2
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'BIT' then 3
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'DECIMAL2' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 4
						when vge.expression in ('lt','lte','gt','gte') and tmp.dataTypeCode = 'DECIMAL2' then 4
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'DATE' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 5
						when vge.expression in ('lt','lte','gt','gte') and tmp.dataTypeCode = 'DATE' then 5
						else null end
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			INNER JOIN dbo.ams_virtualGroupExpressions as vge on vge.expressionID = vgc.expressionID
			WHERE tmp.displayTypeCode <> tmp.oldDisplayTypeCode 
			OR tmp.dataTypeCode <> tmp.oldDataTypeCode;
		END

		-- if changing the display type
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where displayTypeCode <> oldDisplayTypeCode) BEGIN
			UPDATE mdc
			SET mdc.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			WHERE mdc.orgID = @orgID;

			UPDATE mf
			SET mf.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE mdc.orgID = @orgID;

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			UPDATE vgcv
			SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.columnID = tmp.columnID
				and tmp.dataTypeCode <> 'BIT' 
				and tmp.oldDataTypeCode <> 'BIT' 
				and tmp.oldDisplayTypeCode in ('RADIO','SELECT','CHECKBOX') 
				and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX')
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
				and vgc.expressionID in (1,2)
			INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			WHERE cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue;
			
			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			INSERT INTO #tmpColumnConditions (columnID, conditionID, dataTypeID)
			SELECT tmp.columnID, vgc.conditionID, vgc.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.columnID = tmp.columnID
				and tmp.dataTypeCode <> 'BIT' 
				and tmp.oldDataTypeCode <> 'BIT' 
				and tmp.displayTypeCode in ('RADIO','SELECT','CHECKBOX') 
				and tmp.oldDisplayTypeCode not in ('RADIO','SELECT','CHECKBOX')
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE vgc.expressionID in (1,2);

			-- get the valueID
			UPDATE vgcv
			SET vgcv.conditionValue = tmp.valueID
			FROM dbo.ams_virtualGroupConditions as vgc
			INNER JOIN #tmpColumnConditions as tmpC on tmpC.conditionID = vgc.conditionID
			INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
			INNER JOIN (
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 1 
				and vgcv.conditionValue = mdcv.columnvalueString
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 2
				and cast(vgcv.conditionValue as decimal(14,2)) = mdcv.columnvalueDecimal2
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 3
				and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 4
				and cast(vgcv.conditionValue as date) = mdcv.columnvalueDate
			) as tmp on tmp.conditionID = vgc.conditionID;

			
			UPDATE vgc
			SET vgc.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));
		END

		-- if changing the data type
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where dataTypeCode <> oldDataTypeCode) BEGIN
			UPDATE mdc
			SET mdc.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			WHERE mdc.orgID = @orgID;

			UPDATE mf
			SET mf.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE mdc.orgID = @orgID;

			-- string to decimal
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueString as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH
			END

			-- string to integer
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'INTEGER') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueInteger = cast(mdcv.columnValueString as int)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'INTEGER';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1);
				END CATCH
			END

			-- string to date
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'DATE') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDate = cast(mdcv.columnValueString as date)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'DATE';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1);
				END CATCH
			END

			-- string to bit
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'BIT') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueBit = cast(mdcv.columnValueString as bit)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'BIT';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueString = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'STRING'
			AND tmp.dataTypeCode <> 'STRING';
			
			-- decimal2 to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'DECIMAL2' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueDecimal2 as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'DECIMAL2'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'DECIMAL2'
			AND tmp.dataTypeCode <> 'DECIMAL2';

			-- integer to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueInteger as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1);
				END CATCH
			END

			-- integer to decimal2
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueInteger as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Decimal data type.', 16, 1);
				END CATCH
			END
			
			-- integer to bit
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'BIT') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueBit = cast(mdcv.columnValueInteger as bit)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'BIT';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueInteger = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'INTEGER'
			AND tmp.dataTypeCode <> 'INTEGER';

			-- date to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'DATE' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = convert(varchar(10),mdcv.columnValueDate,101)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'DATE'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are date values not compatible with the Text string data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueDate = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'DATE'
			AND tmp.dataTypeCode <> 'DATE';
			
			-- bit to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueBit as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Text string data type.', 16, 1);
				END CATCH
			END

			-- bit to decimal2
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueBit as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH
			END

			-- bit to integer
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'INTEGER') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueInteger = cast(mdcv.columnValueBit as int)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'INTEGER';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
						RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueBit = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'BIT'
			AND tmp.dataTypeCode <> 'BIT';

			-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode IN ('STRING','INTEGER','DECIMAL2') and dataTypeCode = 'BIT' and olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX')) BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = mdcv.columnValueBit
				FROM #tmpUpdateMemberDataColumns as tmp
				INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.expressionID in (1,2)
				AND tmp.oldDataTypeCode IN ('STRING','INTEGER','DECIMAL2')
				AND tmp.dataTypeCode = 'BIT'
				AND tmp.oldDisplayTypeCode IN ('RADIO','SELECT','CHECKBOX');
			END

			-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode IN ('STRING','INTEGER','DECIMAL2') and displayTypeCode in ('RADIO','SELECT','CHECKBOX')) BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = mdcv.valueID
				FROM #tmpUpdateMemberDataColumns as tmp
				INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					and mdcv.columnvalueString = vgcv.conditionValue
				WHERE vgc.expressionID in (1,2)
				AND tmp.dataTypeCode IN ('STRING','INTEGER','DECIMAL2')
				AND tmp.oldDataTypeCode = 'BIT'
				AND tmp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX');
			END

			-- update virtual group conditions
			UPDATE vgc
			SET vgc.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
			CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

			UPDATE vgc
			SET vgc.subProc = CASE WHEN e.expression='datediff' then 'MD_DATEDIFF'
								WHEN e.expression='datepart' then 'MD_DATEPART'
								WHEN e.expression='contains' and tmp.dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
								WHEN e.expression='contains_regex' and tmp.dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
								WHEN e.expression='eq' and tmp.dataTypeCode='STRING' then 'MD_EQ_STRING'
								WHEN e.expression='eq' and tmp.dataTypeCode='BIT' then 'MD_EQ_BIT'
								WHEN e.expression='eq' and tmp.dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
								WHEN e.expression='eq' and tmp.dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
								WHEN e.expression='eq' and tmp.dataTypeCode='DATE' then 'MD_EQ_DATE'
								WHEN e.expression='exists' and tmp.dataTypeCode='STRING' then 'MD_EXISTS_STRING'
								WHEN e.expression='exists' and tmp.dataTypeCode='BIT' then 'MD_EXISTS_BIT'
								WHEN e.expression='exists' and tmp.dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
								WHEN e.expression='exists' and tmp.dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
								WHEN e.expression='exists' and tmp.dataTypeCode='DATE' then 'MD_EXISTS_DATE'
								WHEN e.expression='exists' and tmp.dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
								WHEN e.expression='exists' and tmp.dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
								WHEN e.expression='gt' and tmp.dataTypeCode='STRING' then 'MD_GT_STRING'
								WHEN e.expression='gt' and tmp.dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
								WHEN e.expression='gt' and tmp.dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
								WHEN e.expression='gt' and tmp.dataTypeCode='DATE' then 'MD_GT_DATE'
								WHEN e.expression='gte' and tmp.dataTypeCode='STRING' then 'MD_GTE_STRING'
								WHEN e.expression='gte' and tmp.dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
								WHEN e.expression='gte' and tmp.dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
								WHEN e.expression='gte' and tmp.dataTypeCode='DATE' then 'MD_GTE_DATE'
								WHEN e.expression='lt' and tmp.dataTypeCode='STRING' then 'MD_LT_STRING'
								WHEN e.expression='lt' and tmp.dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
								WHEN e.expression='lt' and tmp.dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
								WHEN e.expression='lt' and tmp.dataTypeCode='DATE' then 'MD_LT_DATE'
								WHEN e.expression='lte' and tmp.dataTypeCode='STRING' then 'MD_LTE_STRING'
								WHEN e.expression='lte' and tmp.dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
								WHEN e.expression='lte' and tmp.dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
								WHEN e.expression='lte' and tmp.dataTypeCode='DATE' then 'MD_LTE_DATE'
								WHEN e.expression='neq' and tmp.dataTypeCode='STRING' then 'MD_NEQ_STRING'
								WHEN e.expression='neq' and tmp.dataTypeCode='BIT' then 'MD_NEQ_BIT'
								WHEN e.expression='neq' and tmp.dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
								WHEN e.expression='neq' and tmp.dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
								WHEN e.expression='neq' and tmp.dataTypeCode='DATE' then 'MD_NEQ_DATE'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
							END
				OUTPUT INSERTED.orgID, NULL, INSERTED.conditionID INTO #tblMCQRun (orgID, memberID, conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions AS vgc ON vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON e.expressionID = vgc.expressionID;

			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			-- reprocess conditions based on field
			IF EXISTS (SELECT 1 FROM #tblMCQRun)
				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
		END
		
		
		-- if there was a change in columnname
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where columnName <> oldColumnName COLLATE Latin1_General_CS_AI) BEGIN
			-- update member fields
			UPDATE mf
			SET mf.dbField = tmp.columnName
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			WHERE tmp.columnName <> tmp.oldColumnName COLLATE Latin1_General_CS_AI;

			-- update virtual group conditions
			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			WHERE tmp.columnName <> tmp.oldColumnName COLLATE Latin1_General_CS_AI;
		END

		-- linked date custom fields
		UPDATE tmp
		SET tmp.linkedDateColumnID = mdc.columnID
		FROM #tmpMemberDataColumns AS tmp
		INNER JOIN dbo.ams_memberDataColumns AS mdc ON mdc.orgID = @orgID
			AND mdc.[uid] = tmp.linkedDateColumnUID;

		UPDATE mdc
		SET mdc.linkedDateColumnID = tmp.linkedDateColumnID, 
			mdc.linkedDateCompareDate = tmp.linkedDateCompareDate, 
			mdc.linkedDateCompareDateAFID = tmp.linkedDateCompareDateAFID,
			mdc.linkedDateAdvanceDate = tmp.linkedDateAdvanceDate, 
			mdc.linkedDateAdvanceAFID = tmp.linkedDateAdvanceAFID
		FROM dbo.ams_memberDataColumns AS mdc
		INNER JOIN #tmpMemberDataColumns AS tmp ON tmp.uid = mdc.uid
		WHERE mdc.orgID = @orgID;
	COMMIT TRAN;

	-- check for subscription member date rules we may need to update
	IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where columnName <> oldColumnName and dataTypeCode = 'DATE') BEGIN
		DECLARE @tblDateColumns TABLE (columnID int, columnName varchar(128), oldColumnName varchar(128));
		DECLARE @tblJD TABLE (udid int);

		INSERT INTO @tblDateColumns (columnID, columnName, oldColumnName)
		select columnID, columnName, oldColumnName
		from #tmpUpdateMemberDataColumns 
		where columnName <> oldColumnName 
		and dataTypeCode = 'DATE';

		INSERT INTO @tblJD (udid)
		select distinct udid 
		from customapps.dbo.schedTask_memberJoinDates as jd
		inner join dbo.sites as s on s.orgID = @orgID and s.siteCode = jd.siteCode
		inner join @tblDateColumns as tmp on 
			( tmp.oldColumnName = jd.joinDateFieldName
				or tmp.oldColumnName = jd.rejoinDateFieldName
				or tmp.oldColumnName = jd.droppedDateFieldName 
				or tmp.oldColumnName = jd.paidThruDateFieldName 
				or tmp.oldColumnName = jd.renewalDateFieldName )

		IF @@ROWCOUNT > 0 BEGIN
			UPDATE jd
			SET jd.joinDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.joinDateFieldName;

			UPDATE jd
			SET jd.rejoinDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.rejoinDateFieldName;

			UPDATE jd
			SET jd.droppedDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.droppedDateFieldName;

			UPDATE jd
			SET jd.paidThruDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.paidThruDateFieldName;

			UPDATE jd
			SET jd.renewalDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.renewalDateFieldName;
		END
	END

	SELECT @minValueID = MIN(valueID) FROM #tmpDeleteMemberDataColumnValues;
	WHILE @minValueID IS NOT NULL BEGIN
		EXEC dbo.ams_removeMemberDataColumnValue @valueID=@minValueID, @recordedByMemberID=@recordedByMemberID;
		WAITFOR DELAY '00:00:00:300';

		SELECT @minValueID = MIN(valueID) FROM #tmpDeleteMemberDataColumnValues WHERE valueID > @minValueID;
	END

	-- run linked date custom field rule
	SELECT @minColumnID = MIN(columnID) FROM dbo.ams_memberDataColumns WHERE orgID = @orgID AND linkedDateColumnID IS NOT NULL;
	WHILE @minColumnID IS NOT NULL BEGIN
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@minColumnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

		SELECT @minColumnID = MIN(columnID) 
			FROM dbo.ams_memberDataColumns 
			WHERE orgID = @orgID
			AND linkedDateColumnID IS NOT NULL
			AND columnID > @minColumnID;
	END

	-- recreate vw
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgID = @orgID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsAdded') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsAdded;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsUpdated') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsUpdated;
	IF OBJECT_ID('tempdb..#tmpDeleteMemberDataColumnValues') IS NOT NULL 
		DROP TABLE #tmpDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#mdcvToAdd') IS NOT NULL 
		DROP TABLE #mdcvToAdd;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tblMDDEFCols') IS NOT NULL 
		DROP TABLE #tblMDDEFCols;
	IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
		DROP TABLE #tblMDDEF;
	IF OBJECT_ID('tempdb..#tblExistingMDDEF') IS NOT NULL 
		DROP TABLE #tblExistingMDDEF;
	IF OBJECT_ID('tempdb..#tmpColumnConditions') IS NOT NULL 
		DROP TABLE #tmpColumnConditions;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO