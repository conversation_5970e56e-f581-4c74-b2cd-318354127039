﻿<cfcomponent>
	<cffunction name="getSubcategoriesForCategory" access="public" output="false" returntype="string">
		<cfargument name="catID" type="numeric" required="true">
		<cfset var qryCategories = "">
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
				select c.categoryID, c.categoryName 
				from cms_categories c
				inner join cms_categories pc on pc.categoryID = c.parentCategoryID
				where c.parentCategoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.catID#">
				and c.isActive = 1
				UNION
				SELECT 0, '--- choose a Category ---'
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qryCategories = QueryNew("categoryID,categoryName","integer,varchar")>
		</cfcatch>
		</cftry>
		
		<cfreturn SerializeJSON(qryCategories)>
	</cffunction>

	<cffunction name="getEmailRecipientsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="gridMode" type="string" required="true">

		<cfset var local = structNew()>
		
		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfif arguments.gridMode is 'recipientsGrid'>
			<cfset arrayAppend(local.arrCols,"mrh.ToName #arguments.event.getValue('orderDir')#, mrh.toEmail #arguments.event.getValue('orderDir')#")>
		<cfelseif arguments.gridMode is 'memberActivitiesGrid'>
			<cfset arrayAppend(local.arrCols,"mrh.toEmail #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"em.subject #arguments.event.getValue('orderDir')#, emt.messageType #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"est.status #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"mrh.dateLastUpdated #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"mrh.dateLastUpdated #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"mrh.dateLastUpdated #arguments.event.getValue('orderDir')#")>
		
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryActivity" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpRecipients') IS NOT NULL
					DROP TABLE ##tmpRecipients;
				IF OBJECT_ID('tempdb..##tmpRecipientStats') IS NOT NULL
					DROP TABLE ##tmpRecipientStats;
				CREATE TABLE ##tmpRecipients (recipientID int PRIMARY KEY, messageID int, row int);
				CREATE TABLE ##tmpRecipientStats (recipientID int PRIMARY KEY, [sg_open] int, [sg_click] int, [sg_drop] int,
					[sg_bounce] int, [sg_block] int, [sg_spam] int);
			
				DECLARE @siteID int, @messageCount int, @posStart int, @posStartPlusCount int, @orgID int;
				SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			    SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

				INSERT INTO ##tmpRecipients (recipientID, messageID, row)
				select mrh.recipientID, em.messageID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
				from dbo.email_messageRecipientHistory as mrh
				inner join dbo.email_messages as em on em.siteID = @siteID
					and em.[status] = 'A'
					and em.messageID = mrh.messageID
					<cfif len(arguments.event.getTrimValue('fromName',''))>
						and em.fromName like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getValue('fromName')#%">
					</cfif>
					<cfif len(arguments.event.getTrimValue('replyTo',''))>
						and em.replyToEmail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getValue('replyTo')#%">
					</cfif>
					<cfif len(arguments.event.getTrimValue('subject',''))>
						and em.subject like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getValue('subject')#%">
					</cfif>
					<cfif len(arguments.event.getTrimValue('fDateSentFrom','')) and len(arguments.event.getTrimValue('fDateSentTo',''))>
						and em.sendOnDate between <cfqueryparam value="#dateFormat(arguments.event.getValue('fDateSentFrom'),"m/d/yyyy")#" cfsqltype="CF_SQL_DATE"> and <cfqueryparam value="#dateFormat(arguments.event.getValue('fDateSentTo',now()),"mm/dd/yyyy")# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
					<cfelseif len(arguments.event.getTrimValue('fDateSentFrom',''))>
						and em.sendOnDate >= <cfqueryparam value="#dateFormat(arguments.event.getValue('fDateSentFrom'),"m/d/yyyy")#" cfsqltype="CF_SQL_DATE">
					<cfelseif len(arguments.event.getTrimValue('fDateSentTo',''))>
						and em.sendOnDate <= <cfqueryparam value="#dateFormat(arguments.event.getValue('fDateSentTo'),"m/d/yyyy")#" cfsqltype="CF_SQL_TIMESTAMP">
					</cfif>
				inner join dbo.email_statuses as est on est.statusID = mrh.emailStatusID
					<cfif len(arguments.event.getTrimValue('statusCode',''))>
						and est.statuscode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#urldecode(arguments.event.getValue('statusCode'))#">)
					</cfif>
				inner join dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID
					<cfif val(arguments.event.getTrimValue('recipientMessageType',0))>
						and emt.messageTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('recipientMessageType',0))#">
					</cfif>
				<cfif arguments.gridMode is 'recipientsGrid' and (arguments.event.getValue('fAssociatedMemberID',0) gt 0 or arguments.event.getValue('fAssociatedGroupID',0) gt 0)>
					inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and m.memberID = mrh.memberID
					inner join membercentral.dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activememberID
					<cfif arguments.event.getValue('fAssociatedMemberID',0) gt 0>
						and mActive.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fAssociatedMemberID')#">
					</cfif>
					<cfif arguments.event.getValue('fAssociatedGroupID',0) gt 0>
						inner join memberCentral.dbo.cache_members_groups as mg on mg.orgID = @orgID
							and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fAssociatedGroupID')#">
							and mg.memberID = mActive.memberID 
					</cfif>
				<cfelseif arguments.gridMode is 'memberActivitiesGrid'>
					inner join membercentral.dbo.ams_members as m on m.orgID = @orgID and m.memberID = mrh.memberID
					inner join membercentral.dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activememberID
						and mActive.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('selectedMemberID',0)#">
				</cfif>
				where mrh.siteID = @siteID
				<cfif len(arguments.event.getTrimValue('emailTo',''))>
					and mrh.toEmail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getValue('emailTo')#%">
				</cfif>;

				SET @messageCount = @@ROWCOUNT;

				DELETE FROM ##tmpRecipients
				WHERE NOT (row > @posStart AND row <= @posStartPlusCount);

				INSERT INTO ##tmpRecipientStats (recipientID, sg_open, sg_click, sg_drop, sg_bounce, sg_block, sg_spam)
				select pivottable.recipientID, isnull(pivottable.sg_open,0), isnull(pivottable.sg_click,0), isnull(pivottable.sg_drop,0), 
					isnull(pivottable.sg_bounce,0), isnull(pivottable.sg_block,0), isnull(pivottable.sg_spam,0)
				from (
					select r.recipientID, rtst.statusCode, count(*) as pivotvalue
					from ##tmpRecipients as r
					inner join dbo.email_messageRecipientHistoryTracking rt 
						on rt.siteID = @siteID
						and rt.recipientID = r.recipientID
					inner join dbo.email_statuses rtst on rtst.statusID = rt.statusID
						and rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_bounce','sg_spam')
					group by r.recipientID,rtst.statusCode
				) as dataToPivot
				PIVOT (sum(pivotvalue) for statusCode in ([sg_open],[sg_click],[sg_drop],[sg_bounce],[sg_block],[sg_spam])) as pivottable;

				select data.messageID, data.recipientID, data.ToName, data.toEmail, data.dateLastUpdated, data.status, data.statusCode,
					data.row, data.company, data.membernumber, data.memberID, data.membername, stats.sg_open, stats.sg_click, 
					(stats.sg_drop + stats.sg_bounce + stats.sg_block + stats.sg_spam) as problems_unique, data.subject, data.messageType,
					data.allowAdminResend, @messageCount as messageCount
				from (
					select em.messageID, mrh.recipientID, mrh.ToName, mrh.toEmail, mrh.dateLastUpdated, s.status, s.statusCode, r.row, 
						mActive.company, mActive.membernumber, mActive.memberID, 
						mActive.lastname + ', ' + mActive.firstname + isnull(' ' + mActive.middlename,'') + ' (' + mActive.membernumber + ')' as memberName,
						CASE WHEN emt.allowAdminView = 1 THEN em.subject ELSE ''+ emt.messageType +': For security, the content of this message is not viewable' END as subject,
						emt.messageType, emt.allowAdminResend
					from ##tmpRecipients as r
					inner join dbo.email_messages as em on em.siteID = @siteID 
						and em.status = 'A'
					inner join dbo.email_messageRecipientHistory as mrh on mrh.siteID = @siteID 
						and em.messageID = mrh.messageID
						and mrh.recipientID = r.recipientID
					inner join dbo.email_statuses as s on s.statusID = mrh.emailStatusID
					inner join dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID
					inner join membercentral.dbo.ams_members as m on m.memberID = mrh.memberID
					inner join membercentral.dbo.ams_members as mActive on mActive.memberID = m.activememberID
				) as data
				left outer join ##tmpRecipientStats as stats on stats.recipientID = data.recipientID
				order by data.row;

				IF OBJECT_ID('tempdb..##tmpRecipients') IS NOT NULL
					DROP TABLE ##tmpRecipients;
				IF OBJECT_ID('tempdb..##tmpRecipientStats') IS NOT NULL
					DROP TABLE ##tmpRecipientStats;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryActivity>
	</cffunction>

	<cffunction name="getEmailMessageTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryEmailMessageTypes = "">

		<cfquery name="qryEmailMessageTypes" datasource="#application.dsn.platformMail.dsn#">
			select distinct mt.messageTypeID, mt.messageType
			from dbo.email_messageTypes as mt
			inner join dbo.email_messages as m 
				on m.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and m.status ='A'
				and m.messageTypeID = mt.messageTypeID
			order by mt.messageType
		</cfquery>

		<cfreturn qryEmailMessageTypes>
	</cffunction>

	<cffunction name="resendRecipientEmail" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="toEmail" type="string" required="true">

		<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_resendRecipientEmail">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recipientID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.toEmail#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="insertFooter" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="footerName" type="string" required="true">
		<cfargument name="footerContent" type="string" required="true">
		<cfargument name="footerStatus" type="string" required="true">

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @resourceTypeID int, @parentSiteResourceID int, @siteResourceStatusID int, @isHTML bit, @languageID int, 
					@isActive bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawContent varchar(MAX), @contentID int, 
					@siteResourceID int, @toolType varchar(200), @memberID int, @footerStatus varchar(50) ;

				SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_integer">;
				SET @contentTitle = <cfqueryparam value="#arguments.footerName#" cfsqltype="cf_sql_varchar">;
				SET @rawContent = <cfqueryparam value="#arguments.footerContent#" cfsqltype="cf_sql_longvarchar">;
				SET @memberID = <cfqueryparam value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)#" cfsqltype="cf_sql_integer">;
				SET @footerStatus = <cfqueryparam value="#arguments.footerStatus#" cfsqltype="cf_sql_varchar">;

				SET @toolType = 'EmailBlast';
				SET @isHTML = 1;
				SET @languageID = 1;
				SET @isActive = 1;
				SET @contentDesc = '';
			
				SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				SELECT @siteResourceStatusID = siteResourceStatusID FROM dbo.cms_siteResourceStatuses WHERE siteResourceStatusDesc = 'Active';
			
				SELECT @parentSiteResourceID = st.siteResourceID 
				FROM dbo.admin_tooltypes tt
				INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
					AND st.siteID = @siteID
					AND tt.toolType = @toolType
				INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
					AND sr.siteResourceStatusID = @siteResourceStatusID;

				SELECT @siteResourceStatusID = siteResourceStatusID FROM dbo.cms_siteResourceStatuses WHERE siteResourceStatusDesc = @footerStatus ;

				EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
					@siteResourceStatusID=@siteResourceStatusID, @isHTML=@isHTML, @languageID=@languageID, @isActive=@isActive, @contentTitle=@contentTitle, 
					@contentDesc=@contentDesc, @rawContent=@rawContent, @memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateFooter" access="public" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="contentID" type="numeric" required="true">
		<cfargument name="footerName" type="string" required="true">
		<cfargument name="footerContent" type="string" required="true">
		<cfargument name="footerStatus" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @isHTML bit, @languageID int, @contentTitle varchar(200), @contentDesc varchar(400), @rawContent varchar(MAX),
					@contentID int, @memberID int, @siteResourceID int, @footerStatus varchar(50) ;
			
				SET @contentID = <cfqueryparam value="#arguments.contentID#" cfsqltype="CF_SQL_integer">;
				SET @contentTitle = <cfqueryparam value="#arguments.footerName#" cfsqltype="cf_sql_varchar">;
				SET @rawContent = <cfqueryparam value="#arguments.footerContent#" cfsqltype="cf_sql_longvarchar">;
				SET @memberID = <cfqueryparam  value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)#" cfsqltype="cf_sql_integer">;
				SET @footerStatus = <cfqueryparam value="#arguments.footerStatus#" cfsqltype="cf_sql_varchar">;
				SET @siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_integer">;
				SET @isHTML = 1;
				SET @languageID = 1;
				SET @contentDesc = '';
			
				EXEC dbo.cms_updateContent @contentID, @languageID, @isHTML, @contentTitle, @contentDesc, @rawContent, @memberID;

				EXEC dbo.cms_updateSiteResourceStatus @siteResourceStatusDesc=@footerStatus, @siteResourceID=@siteResourceID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="scheduleBlast" access="public" output="false" returntype="void">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="emailDateScheduled" type="string" required="true">
		<cfargument name="afID" type="numeric" required="true">
		<cfargument name="endRunDate" type="string" required="true">

		<cfset var local = structNew()>

		<cfif len(trim(arguments.emailDateScheduled))>
			<cfset arguments.emailDateScheduled = ParseDateTime("#replace(arguments.emailDateScheduled,' - ',' ')#")>
		</cfif>
		
		<cfif len(trim(arguments.endRunDate))>
			<cfset arguments.endRunDate = ParseDateTime("#replace(arguments.endRunDate,' - ',' ')#")>
		</cfif>

		<cfset var qryUpdateBlast = "">
		
		<cfquery name="qryUpdateBlast" datasource="#application.dsn.membercentral.dsn#">
			update dbo.email_emailBlasts 
			set <cfif len(arguments.emailDateScheduled)>
					emailDateScheduled = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.emailDateScheduled#">
				<cfelse>
					emailDateScheduled = null
				</cfif>
				<cfif arguments.afID GT 0 >
					,afID =	 <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.afID#">
				<cfelse>
					,afID = null
				</cfif>
				<cfif len(arguments.endRunDate) AND arguments.afID GT 0>
					,endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endRunDate#">
				<cfelse>
					,endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="yes">
				</cfif>
			where blastID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blastID#">
		</cfquery>
	</cffunction>

	<cffunction name="getStatuses" access="public" output="false" returntype="query">
		<cfset var local = structNew()>

		<cfquery name="local.qryStatuses" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select statusID, statusCode, status, statusOrder
			from dbo.email_statuses 
			order by statusorder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryStatuses>
	</cffunction>
	
	<cffunction name="getAdvanceFormulas" access="public" output="false" returntype="query">
		<cfargument name="SiteID" type="numeric" required="true">
		
		<cfset var qryAdvanceFormulas = "">
		
		<cfquery name="qryAdvanceFormulas" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT AFID, siteID, datePart, dateNum, adjustTerm, nextWeekday, weekNumber, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="integer">
			ORDER BY afName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryAdvanceFormulas>
	</cffunction>

	<cffunction name="getFooter" access="public" output="false" returntype="query">
		<cfargument name="SiteID" type="numeric" required="true">
		<cfargument name="contentID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryFooter" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @toolType varchar(100), @contentID int;
			
			SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="integer">;
			SET @contentID = <cfqueryparam value="#arguments.contentID#" cfsqltype="integer">;
			SET @toolType = 'EmailBlast';
			
			SELECT c.contentID, cv.rawContent, cl.contentTitle, cl.contentLanguageID, childSR.siteResourceStatusID, srs.siteResourceStatusDesc, childSR.siteResourceID
			FROM dbo.admin_tooltypes as tt
			INNER JOIN dbo.admin_siteTools as st ON st.toolTypeID = tt.toolTypeID
				AND st.siteID = @siteID
				AND tt.toolType = @toolType
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
				AND  sr.siteResourceStatusID = 1
			INNER JOIN dbo.cms_siteResources childSR ON childSR.parentSiteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceTypes srt ON srt.resourceTypeID = childSR.resourceTypeID AND srt.resourceType = 'ApplicationCreatedContent'
			INNER JOIN dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = childSR.siteResourceStatusID
			INNER JOIN dbo.cms_content c ON c.siteID = @siteID
				AND c.siteResourceID = childSR.siteResourceID
			INNER JOIN dbo.cms_contentLanguages cl ON cl.siteID = @siteID
				and cl.contentID = c.contentID
			INNER JOIN dbo.cms_contentVersions cv ON cv.siteID = @siteID
				AND cv.contentID = cl.contentID
				AND cv.contentLanguageID = cl.contentLanguageID
				AND cv.isActive = 1
			WHERE c.contentID = @contentID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryFooter>
	</cffunction>
	
	<cffunction name="getFooters" access="public" output="false" returntype="query">
		<cfargument name="SiteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryFooters" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @toolType varchar(100);
			
			SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="integer">;
			SET @toolType = 'EmailBlast';
			
			SELECT c.contentID, cv.rawContent, cl.contentTitle, cl.contentLanguageID, srs.siteResourceStatusDesc
			FROM dbo.admin_tooltypes as tt
			INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
				AND st.siteID = @siteID
				AND tt.toolType = @toolType
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
				AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.cms_siteResources childSR ON childSR.parentSiteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceTypes srt ON srt.resourceTypeID = childSR.resourceTypeID
				 AND srt.resourceType = 'ApplicationCreatedContent'
			INNER JOIN dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = childSR.siteResourceStatusID
			INNER JOIN dbo.cms_content c ON c.siteID = @siteID
				AND c.siteResourceID = childSR.siteResourceID
			INNER JOIN dbo.cms_contentLanguages cl ON cl.siteID = @siteID 
				AND cl.contentID = c.contentID
			INNER JOIN dbo.cms_contentVersions cv ON cv.siteID = @siteID
				AND cv.contentID = cl.contentID
				AND cv.contentLanguageID = cl.contentLanguageID
				AND cv.isActive = 1
			order by srs.siteResourceStatusDesc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryFooters>
	</cffunction>

	<cffunction name="getCustomFonts" access="public" output="false" returntype="query">
		<cfargument name="SiteID" type="numeric" required="true">	
		<cfset var local = structNew()>
		
		<cfquery name="local.qryCustomFont" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @searchvalue varchar(300);
			
			SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">;		
			SELECT 
				customFontID,
				name,
				fontCssUrl,
				fontFamily 
			FROM dbo.email_customFonts as cf		
			WHERE  cf.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryCustomFont>
	</cffunction>

	<cffunction name="insertCustomFont" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="name" type="string" required="true">
		<cfargument name="fontCssUrl" type="string" required="true">
		<cfargument name="fontFamily" type="string" required="true">

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int,  @customFontID int, @name varchar(100), @fontCssUrl varchar(250), @fontFamily varchar(100);

				SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_integer">;
				SET @name = <cfqueryparam value="#arguments.name#" cfsqltype="cf_sql_varchar">;
				SET @fontCssUrl = <cfqueryparam value="#arguments.fontCssUrl#" cfsqltype="cf_sql_varchar">;
				SET @fontFamily = <cfqueryparam value="#arguments.fontFamily#" cfsqltype="cf_sql_varchar">;
				INSERT INTO dbo.email_customFonts (siteID, name, fontCssUrl, fontFamily)
						VALUES (@siteID, @name, @fontCssUrl, @fontFamily);
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateCustomFont" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="customFontID" type="numeric" required="true">
		<cfargument name="name" type="string" required="true">
		<cfargument name="fontCssUrl" type="string" required="true">
		<cfargument name="fontFamily" type="string" required="true">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int,  @customFontID int, @name varchar(100), @fontCssUrl varchar(250), @fontFamily varchar(100);
				SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_integer">;
				SET @customFontID = <cfqueryparam value="#arguments.customFontID#" cfsqltype="CF_SQL_integer">;
				SET @name = <cfqueryparam value="#arguments.name#" cfsqltype="cf_sql_varchar">;
				SET @fontCssUrl = <cfqueryparam value="#arguments.fontCssUrl#" cfsqltype="cf_sql_varchar">;
				SET @fontFamily = <cfqueryparam value="#arguments.fontFamily#" cfsqltype="cf_sql_varchar">;
				
				UPDATE dbo.email_customFonts
				SET name = @name,
					fontCssUrl = @fontCssUrl,
					fontFamily = @fontFamily
				WHERE customFontID = @customFontID 
				AND siteID = @siteID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getCustomFontDetails" access="public" output="false" returntype="query">
		<cfargument name="SiteID" type="numeric" required="true">	
		<cfargument name="customFontID" type="numeric" required="true">
		<cfset var local = structNew()>
		
		<cfquery name="local.qryCustomFontDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @customFontID int;
			SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">;	
			SET @customFontID = <cfqueryparam value="#arguments.customFontID#" cfsqltype="cf_sql_integer">;		

			SELECT customFontID, name, fontCssUrl, fontFamily 
			FROM dbo.email_customFonts
			WHERE siteID = @siteID
			AND customFontID = @customFontID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryCustomFontDetails>
	</cffunction>

	<cffunction name="getBlastInfo" access="package" output="false" returntype="query">
		<cfargument name="blastID" type="numeric" required="yes">

		<cfset var qryBlast = "">
		
		<cfquery name="qryBlast" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select eb.blastID, eb.ruleID, m.activeMemberID as createdByMemberID, eb.blastName, eb.fromName, eb.fromEmail, eb.replyTo, eb.deliveryReportEmail,
				c.categoryID as categoryID, c.categoryName as categoryName, subc.categoryID as subCategoryID, subc.categoryName as subCategoryName,
				eb.contentID, cl.contentTitle, cl.contentDesc, cv.rawContent, isnull(eb.footerContentID,0) as footerContentID, eb.emailDateScheduled, eb.endRunDate, eb.emailDateSent,
				eb.siteID, s.sitecode, isnull(eb.afID,0) as afID, s.useRemoteLogin, eb.utm_autoappend, eb.utm_campaign, eb.includeAttachmentListAsAttachment,
				eb.editorID, te.editorCode, eb.supportingContentID, sc.rawContent as supportingContent, eb.orgIdentityID, af.afName,
				(select count(emailBlastConsentListID) from dbo.email_emailBlastConsentLists where blastID = eb.blastID) as consentListCount
			from dbo.email_emailBlasts as eb
			inner join dbo.sites as s on s.siteID = eb.siteID
			inner join dbo.cms_contentLanguages as cl on cl.siteID = s.siteID
				AND cl.contentID = eb.contentID 
				AND cl.languageID = 1
			inner join dbo.cms_contentVersions as cv on cv.siteID = s.siteID
				AND cv.contentID = cl.contentID
				AND cv.contentLanguageID = cl.contentLanguageID 
				AND cv.isActive = 1
			inner join dbo.ams_members as m on m.memberid = eb.memberID
			inner join dbo.template_editors as te on te.editorID = eb.editorID
			inner join dbo.cms_categories as c on c.categoryID = eb.categoryID
			left outer join dbo.cms_categories as subc on subc.categoryID = eb.subcategoryID
			left outer join dbo.af_advanceFormulas af on af.AFID = eb.afID
			outer apply dbo.fn_getContent(eb.supportingContentID,1) as sc
			where eb.blastID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlast>
	</cffunction>

	<cffunction name="getBlastSendingHistorySummary" access="package" output="false" returntype="query">
		<cfargument name="blastID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qrySummary = "">
		
		<cfquery name="qrySummary" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @messageID int, @messageCount int;

			SELECT @messageCount=count(*), @messageID=max(m.messageID)
			FROM dbo.email_messages m
			where m.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND m.[status]='A'
			AND m.referenceType = 'EmailBlast' 
			AND m.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">;

			SELECT @messageCount AS messageCount, e.sendOnDate, mActive.firstName + ' ' + mActive.lastName as recentSentByMember
			FROM dbo.email_messages AS e
			LEFT OUTER JOIN memberCentral.dbo.ams_members AS m
				INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				ON m.memberid = e.recordedByMemberID
			WHERE e.messageID = @messageID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySummary>
	</cffunction>

	<cffunction name="getBlastEmailTypes" access="public" output="false" returntype="query">
		<cfargument name="blastId" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryBlastEmailTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select emailTypeID
			from dbo.email_emailBlastEmailTypes
			where blastID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blastID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryBlastEmailTypes>
	</cffunction>

	<cffunction name="getBlastEmailTagTypes" access="public" output="false" returntype="query">
		<cfargument name="blastId" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryBlastEmailTagTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select emailTagTypeID
			from dbo.email_emailBlastEmailTagTypes
			where blastID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blastID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryBlastEmailTagTypes>
	</cffunction>

	<cffunction name="getEmailBlastAttachments" access="public" output="false" returntype="query">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="orderBy" type="string" required="false" default="0">
		<cfargument name="orderDir" type="string" required="false" default="asc">

		<cfset var qryBlastAttachments = "">

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dl.docTitle")>
		<cfset arrayAppend(local.arrCols,"dv.fileName")>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfif NOT listFindNoCase('asc,desc',arguments.orderDir)>
			<cfset arguments.orderDir = "asc">
		</cfif>

		<cfquery name="qryBlastAttachments" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ed.autoID, ed.documentID, dl.docTitle, dv.fileName,
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.orderDir#) AS rowNum
			FROM dbo.email_emailBlastDocuments as ed
			INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			WHERE ed.blastID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">
			ORDER BY rowNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlastAttachments>
	</cffunction>

	<cffunction name="getBlastsForCalendar" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="subCategoryIDList" type="string" required="true">
		<cfargument name="fBlastKeyword" type="string" required="true">
		<cfargument name="fBlastMessage" type="string" required="true">
		<cfargument name="fBlastRecurring" type="numeric" required="true">
		<cfargument name="fBlastTestMsg" type="string" required="true">
		<cfargument name="dsp" type="string" required="true">
		<cfargument name="start" type="date" required="true">
		<cfargument name="end" type="date" required="true">

		<cfset var local = structNew()>
		<cfset local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.siteID)>
		<cfif len(arguments.fBlastMessage)>
			<cfset local.searchterms = createObject("component","model.search.bucket").prepareSearchString(stringToClean=arguments.fBlastMessage)>
			<cfif len(local.searchterms)>
				<cfset local.searchterms = local.searchterms & " AND (mcsitecode#local.siteCode#xxx AND mcresourcetypeApplicationCreatedContentxxx)">
			</cfif>
		</cfif>
		<cfquery name="local.qryBlasts" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@startDate datetime = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#dateformat(arguments.start,"m/d/yyyy")#">,
					@endDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#dateformat(arguments.end,"m/d/yyyy")# 23:59:59.997">,
					@nowDate datetime = getdate(), @resourceTypeID int = dbo.fn_getResourceTypeID('ApplicationCreatedContent');;
				
				IF OBJECT_ID('tempdb..##tmpCalEntries') IS NOT NULL
					DROP TABLE ##tmpCalEntries;
				IF OBJECT_ID('tempdb..##tmpRecurringBlasts') IS NOT NULL
					DROP TABLE ##tmpRecurringBlasts;
				CREATE TABLE ##tmpCalEntries (rowID int IDENTITY(1,1), blastID int, sendDate datetime, sendTime varchar(8));
				CREATE TABLE ##tmpRecurringBlasts (rowID int IDENTITY(1,1), blastID int, scheduleDate datetime, [datePart] varchar(11),
					dateNum smallint, adjustTerm varchar(12), nextWeekday bit, weekNumber varchar(4));
				<cfif len(arguments.fBlastMessage)>
					DECLARE @fullTextKeys TABLE (searchKey int PRIMARY KEY, rank int, contentLanguageID int INDEX fullTextKeys_contentLang);
					DECLARE @fulltextkeywords varchar(8000) = '#local.searchterms#';

					INSERT INTO @fullTextKeys(searchKey, rank)
					SELECT sclsearch.[key], rank
					FROM containstable(searchMC.dbo.cms_contentLanguages,searchtext,@fulltextkeywords) as sclsearch;

					UPDATE sclsearch 
					SET contentLanguageID = cl.contentLanguageID
					FROM searchMC.dbo.cms_contentLanguages AS cl
					INNER JOIN @fullTextKeys AS sclsearch 
						ON cl.siteID=@siteID
						and cl.resourceTypeID = @resourceTypeID
						and sclsearch.searchKey = cl.id
				</cfif>

				-- dates earlier than NOW (individual emal blasts that have been sent)
				INSERT INTO ##tmpCalEntries(blastID, sendDate, sendTime)
				SELECT m.referenceID, m.dateEntered, FORMAT(m.dateEntered, 'hh:mm tt')
				FROM platformmail.dbo.email_messages AS m
				INNER JOIN platformmail.dbo.email_messageRecipientHistory AS mrh ON mrh.siteID = @siteID
					AND m.messageID = mrh.messageID
				WHERE m.siteID = @siteID
				AND m.[status] = 'A'
				AND m.referenceType = 'EmailBlast'
				AND m.dateEntered BETWEEN @startDate AND @endDate
				<cfif arguments.fBlastTestMsg EQ 0>
					AND m.isTestMessage = 0
				<cfelseif arguments.fBlastTestMsg EQ 1>
					AND m.isTestMessage = 1
				<cfelse>
					AND m.isTestMessage in (0,1)
				</cfif>
				GROUP BY m.referenceID, m.dateEntered
				ORDER BY m.dateEntered;

				-- dates scheduled, but not recurring
				INSERT INTO ##tmpCalEntries(blastID, sendDate, sendTime)
				SELECT blastID, emailDateScheduled, FORMAT(emailDateScheduled, 'hh:mm tt')
				FROM dbo.email_EmailBlasts
				WHERE siteID = @siteID
				AND afID IS NULL
				AND emailDateScheduled BETWEEN @startDate AND @endDate
				AND (endRunDate IS NULL OR (endRunDate IS NOT NULL AND emailDateScheduled <= endRunDate));

				-- dates scheduled and recurring
				INSERT INTO ##tmpRecurringBlasts (blastID, scheduleDate, [datePart], dateNum, adjustTerm, nextWeekday, weekNumber)
				SELECT eb.blastID, eb.emailDateScheduled, af.[datePart], af.dateNum, af.adjustTerm, af.nextWeekday, af.weekNumber
				FROM dbo.email_EmailBlasts AS eb
				INNER JOIN dbo.af_advanceFormulas AS af ON af.siteID = @siteID
					AND af.AFID = eb.afID
				WHERE eb.siteID = @siteID
				AND eb.afID IS NOT NULL
				AND eb.emailDateScheduled IS NOT NULL
				AND eb.emailDateScheduled <= @endDate
				AND (eb.endRunDate IS NULL OR (eb.endRunDate IS NOT NULL AND eb.emailDateScheduled <= eb.endRunDate))
				ORDER BY eb.emailDateScheduled;

				WITH tmpRecurringDates AS (
					SELECT blastID, scheduleDate, [datePart], dateNum, adjustTerm, nextWeekday, weekNumber
					FROM ##tmpRecurringBlasts
						UNION ALL
					SELECT blastID, dbo.fn_af_getAFDate(scheduleDate, [datePart], dateNum, adjustTerm, nextWeekday, weekNumber) AS scheduleDate,
						[datePart], dateNum, adjustTerm, nextWeekday, weekNumber
					FROM tmpRecurringDates
					WHERE scheduleDate <= @endDate
					AND scheduleDate <= DATEADD(year, 1, @nowDate)
				)
				INSERT INTO ##tmpCalEntries(blastID, sendDate, sendTime)
				SELECT blastID, scheduleDate, FORMAT(scheduleDate, 'hh:mm tt')
				FROM tmpRecurringDates
				WHERE scheduleDate > @nowDate
				AND scheduleDate BETWEEN @startDate AND @endDate
				ORDER BY blastID, scheduleDate
				OPTION (MAXRECURSION 1200);

				SELECT DISTINCT tmp.sendDate, tmp.sendTime, eb.blastID, eb.blastName, c.categoryName,
					subc.categoryName AS subCategoryName, mActive.lastName, mActive.firstName
				FROM ##tmpCalEntries AS tmp
				INNER JOIN dbo.email_EmailBlasts AS eb ON eb.blastID = tmp.blastID 
					and eb.siteID = @siteID
					AND (eb.endRunDate IS NULL OR (eb.endRunDate IS NOT NULL AND tmp.sendDate <= eb.endRunDate))
				inner join dbo.cms_contentLanguages as cl on cl.siteID = @siteID
					and cl.contentID = eb.contentID
					and cl.languageID = 1
				inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
					and cv.contentID = cl.contentID
					and cv.contentLanguageID = cl.contentLanguageID 
					and cv.isActive = 1
				<cfif len(arguments.fBlastMessage)>
					inner join @fullTextKeys as fsearch on fsearch.contentLanguageID = cl.contentLanguageID
				</cfif>
				INNER JOIN dbo.ams_members AS m ON m.memberID = eb.memberID
					AND m.orgID IN (@orgID,1)
				INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
					AND mActive.orgID = m.orgID
				INNER JOIN dbo.cms_categories AS c ON c.categoryID = eb.categoryID
				LEFT OUTER JOIN dbo.cms_categories AS subc ON subc.categoryID = eb.subcategoryID
				WHERE 1 = 1
				<cfif arguments.dsp neq "all">
					AND mActive.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
				</cfif>
				<cfif arguments.categoryID GT 0>
					AND eb.categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
				</cfif>
				<cfif listLen(arguments.subCategoryIDList)>
					AND eb.subCategoryID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.subCategoryIDList#">)
				</cfif>
				<cfif listLen(arguments.fBlastKeyword)>
					AND eb.blastName like <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="%#arguments.fBlastKeyword#%">
				</cfif>
				<cfif arguments.fBlastRecurring eq 1>
					and eb.AFID is not null
				</cfif>
				ORDER BY sendDate, sendTime;

				IF OBJECT_ID('tempdb..##tmpCalEntries') IS NOT NULL
					DROP TABLE ##tmpCalEntries;
				IF OBJECT_ID('tempdb..##tmpRecurringBlasts') IS NOT NULL
					DROP TABLE ##tmpRecurringBlasts;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrBlasts = arrayNew(1)>
		<cfloop query="local.qryBlasts">
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div>
					<h4>#local.qryBlasts.blastName#</h4>
					<table>
						<tr valign="top"><td nowrap="true"><b>Send Time:</b></td><td rowspan="5" width="10">&nbsp;</td><td>#DateFormat(local.qryBlasts.sendDate, "mmmm d, yyyy")# #local.qryBlasts.sendTime#</td></tr>
						<tr valign="top"><td><b>Category:</b></td><td>#encodeForHTML(local.qryBlasts.categoryName)#<cfif len(local.qryBlasts.subCategoryName)> / #encodeForHTML(local.qryBlasts.subCategoryName)#</cfif></td></tr>
						<cfif arguments.dsp eq "all">
							<tr valign="top"><td><b>Created By:</b></td><td>#local.qryBlasts.firstName# #local.qryBlasts.lastName#</td></tr>
						</cfif>
						<tr valign="top"><td>&nbsp;</td><td><a href="javascript:goToBlast(#local.qryBlasts.blastID#);">View blast in a new window</a></td></tr>
					</table>
				</div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.tmpStr = structNew() >
			<cfset local.tmpStr['start'] =  dateTimeFormat(local.qryBlasts.sendDate,"yyyy-mm-dd'T'HH:nn:ss'Z'") >
			<cfset local.tmpStr['end'] = local.tmpStr['start']>
			<cfset local.tmpStr['allDay'] = 0>
			<cfset local.tmpStr['title'] = local.qryBlasts.blastName>
			<cfset local.tmpStr['description'] = htmleditformat(trim(local.data))>
			
			<cfset arrayAppend(local.arrBlasts,local.tmpStr)>
		</cfloop>
		
		<cfreturn SerializeJSON(local.arrBlasts)>
	</cffunction>

	<cffunction name="getBlastConsentLists" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		
		<cfset var qryBlastConsentLists = "">

		<cfquery name="local.qryBlastConsentLists" datasource="#application.dsn.membercentral.dsn#">
			SELECT 0 AS emailBlastConsentListID, cl.consentListID, clt.consentListTypeName, cl.consentListName, m.modeName, count(clm.consentListMemberID) AS memberCount, 1 AS isGlobal, 0 as isPrimary
			FROM platformMail.dbo.email_consentLists cl
			INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
				AND clt.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">
				AND clt.consentListTypeName = 'Global Lists'
				AND cl.[status] = 'A'
			INNER JOIN platformMail.dbo.email_consentListModes m ON m.consentListModeID = cl.consentListModeID
				AND m.modeName = 'GlobalOptOut'
			LEFT OUTER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID = cl.consentListID
			GROUP BY cl.consentListID, clt.consentListTypeName, cl.consentListName, m.modeName
				UNION
			SELECT bcl.emailBlastConsentListID, cl.consentListID, clt.consentListTypeName, cl.consentListName, m.modeName, count(clm.consentListMemberID) AS memberCount, 0 AS isGlobal, bcl.isPrimary
			FROM dbo.email_emailBlastConsentLists AS bcl
			INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListID = bcl.consentListID
				AND cl.[status] = 'A'
			INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
				AND clt.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">
			INNER JOIN platformMail.dbo.email_consentListModes m ON m.consentListModeID = cl.consentListModeID
			LEFT OUTER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID = bcl.consentListID
			WHERE bcl.blastID = <cfqueryparam value="#arguments.blastID#" cfsqltype="cf_sql_integer">
			GROUP BY bcl.emailBlastConsentListID, cl.consentListID, clt.consentListTypeName, cl.consentListName, m.modeName, bcl.isPrimary
			ORDER BY isGlobal DESC, consentListTypeName, consentListName
		</cfquery>

		<cfreturn qryBlastConsentLists>
	</cffunction>

	<cffunction name="getValidFromEmailByMessageType" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageTypeCode" type="string" required="true">
		<cfargument name="fromEmail" type="string" required="true">
		
		<cfset var qryFromEmail = "">

		<cfquery name="local.qryFromEmail" datasource="#application.dsn.platformMail.dsn#">
			DECLARE @fromEmail varchar(200), @messageTypeID int, @allowedDomainsRegex varchar(1050), @defaultSendingHostname varchar(100);

			SET @fromEmail = <cfqueryparam value="#arguments.fromEmail#" cfsqltype="cf_sql_varchar">;

			SELECT @messageTypeID = messageTypeID
			FROM dbo.email_messageTypes
			WHERE messageTypeCode = <cfqueryparam value="#arguments.messageTypeCode#" cfsqltype="cf_sql_varchar">;

			SELECT @allowedDomainsRegex = '[.@](' + replace(STRING_AGG(suds.sendingHostname,'|'),'.','\.') + ')$', 
				@defaultSendingHostname = (SELECT sendingHostname FROM dbo.sendgrid_subuserDomains WHERE subuserDomainID = su.activeSubuserDomainID)
			FROM dbo.sendgrid_subusers su
			INNER JOIN dbo.sendgrid_subuserDomains suds
				ON suds.subuserID = su.subuserID
				AND su.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">
			INNER JOIN dbo.sendgrid_subuserMailstreams sums
				ON sums.subuserID = su.subuserID 
			INNER JOIN dbo.email_mailstreams ms
				ON sums.mailstreamID = ms.mailStreamID
			INNER JOIN dbo.email_messageTypes mt 
				ON mt.mailStreamID = ms.mailStreamID
				AND mt.messageTypeID=@messageTypeID
			GROUP BY su.activeSubuserDomainID;

			IF membercentral.dbo.fn_RegexMatch (@fromEmail,@allowedDomainsRegex) = 0 BEGIN
				SET @fromEmail = 'noreply@'+@defaultSendingHostname;
			END

			SELECT @fromEmail as fromEmail;
		</cfquery>

		<cfreturn qryFromEmail.fromEmail>
	</cffunction>

	<cffunction name="getMessagesFromFilters" access="public" output="false" returntype="query">
		<cfargument name="messageTypeID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="subCategoryIDList" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="fromName" type="string" required="true">
		<cfargument name="replyToEmail" type="string" required="true">
		<cfargument name="subject" type="string" required="true">
		<cfargument name="fSentFrom" type="string" required="true">
		<cfargument name="fSentTo" type="string" required="true">
		<cfargument name="emailTypeIDList" type="string" required="true">
		<cfargument name="msgCountFrom" type="string" required="true">
		<cfargument name="msgCountTo" type="string" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="posStart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="0">
		<cfargument name="orderby" type="string" required="false" default="dateEntered desc">
		<cfargument name="folderPathUNC" type="string" required="false">
		<cfargument name="reportFileName" type="string" required="false">
		<cfargument name="fBlastTestMsg" type="string" required="false" default="0">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryActivity">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @totalCount int, @posStart int, @posStartAndCount int,@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				<cfif arguments.mode eq "grid">
					set @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;	
					set @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMessages') IS NOT NULL
					DROP TABLE ##tblMessages;
				IF OBJECT_ID('tempdb..##tblMessagesSubset') IS NOT NULL
					DROP TABLE ##tblMessagesSubset;
				IF OBJECT_ID('tempdb..##tblPivotKeys') IS NOT NULL
					DROP TABLE ##tblPivotKeys;
				IF OBJECT_ID('tempdb..##tblMessageStats') IS NOT NULL
					DROP TABLE ##tblMessageStats;
				IF OBJECT_ID('tempdb..##tblMessagesFinal') IS NOT NULL
					DROP TABLE ##tblMessagesFinal;
				CREATE TABLE ##tblMessages (messageID int PRIMARY KEY, MsgCount int, row int);	
				CREATE TABLE ##tblMessagesSubset (messageID int PRIMARY KEY, MsgCount int, row int);	
				CREATE TABLE ##tblPivotKeys (statuscode varchar(10), suffix varchar(10), pivotkey varchar(20));	
				CREATE TABLE ##tblMessageStats (messageID int PRIMARY KEY, 
					[sg_open_total] int,[sg_click_total] int, [sg_drop_total] int,sg_bounce_total int, sg_block_total int, sg_spam_total int, suppressed_total int, optout_total int, 
					sg_open_unique int, sg_click_unique int, sg_drop_unique int, sg_bounce_unique int, sg_block_unique int, sg_spam_unique int, suppressed_unique int,  optout_unique int);	

				insert into ##tblPivotKeys (statuscode,suffix,pivotkey)	
				select statusesToTrack.listitem as statuscode, suffixes.listitem as suffix, statusesToTrack.listitem + suffixes.listitem as pivotkey	
				from membercentral.dbo.fn_varCharListToTable('sg_open,sg_click,sg_drop,sg_bounce,sg_block,sg_spam,suppressed,optout',',') statusesToTrack	
				cross join membercentral.dbo.fn_varCharListToTable('_total,_unique',',') as suffixes;

				insert into ##tblMessages (messageID, MsgCount, row)	
				select messageID, MsgCount, ROW_NUMBER() OVER (ORDER BY #arguments.orderby#) as row 	
				from (	
					select em.messageID, em.dateEntered, em.subject, count(emrh.recipientID) as MsgCount	
					from dbo.email_messages as em	
					<cfif arguments.messageTypeID gt 0>	
						inner join dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID	
							and emt.messageTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.messageTypeID#">	
						<cfif arguments.messageTypeID EQ 3 AND arguments.categoryID gt 0>	
							inner join membercentral.dbo.email_EmailBlasts as eb ON eb.blastID = em.referenceID AND em.referenceType = 'EmailBlast'	
								and eb.categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">	
							<cfif arguments.subCategoryIDList neq 0>	
								and eb.subCategoryID in (0#arguments.subCategoryIDList#)	
							</cfif>	
						</cfif>	
					</cfif>	
					left outer join dbo.email_messageRecipientHistory as emrh on emrh.siteID = @siteID and emrh.messageID = em.messageID	
					where em.siteID = @siteID
					and em.status = 'A'	
					<cfif len(arguments.fromname)>
						and em.fromName like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.fromname#%">
					</cfif>
					<cfif len(arguments.replytoemail)>
						and em.replyToEmail like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.replytoemail#%">
					</cfif>
					<cfif len(arguments.subject)>
						and em.subject like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.subject#%">
					</cfif>
					<cfif len(arguments.fSentFrom)>
						and em.sendOnDate >= <cfqueryparam value="#arguments.fSentFrom#" cfsqltype="CF_SQL_TIMESTAMP">
					</cfif>
					<cfif len(arguments.fSentTo)>
						and em.sendOnDate <= <cfqueryparam value="#arguments.fSentTo#" cfsqltype="CF_SQL_TIMESTAMP">
					</cfif>
					<cfif ListLen(arguments.emailTypeIDList)>
						and emrh.emailTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.emailTypeIDList#">)
					</cfif>
					<cfif arguments.fBlastTestMsg EQ 1>
						AND em.isTestMessage = 1
					<cfelseif arguments.fBlastTestMsg EQ 0>
						AND em.isTestMessage = 0
					<cfelse>
						AND em.isTestMessage in (0,1)
					</cfif>
					group by em.messageID, em.dateEntered, em.subject	
					having 1=1	
					<cfif len(arguments.msgCountFrom)>
						and count(emrh.recipientID) >= <cfqueryparam value="#arguments.msgCountFrom#" cfsqltype="cf_sql_integer">
					</cfif>
					<cfif len(arguments.msgCountTo)>
						and count(emrh.recipientID) <= <cfqueryparam value="#arguments.msgCountTo#" cfsqltype="cf_sql_integer">
					</cfif>
				) as innerTmp;

				SELECT @totalCount = @@ROWCOUNT;

				insert into ##tblMessagesSubset (messageID, MsgCount, row)	
				select messageID, MsgCount, row 	
				from ##tblMessages 	
				<cfif arguments.mode eq "grid">
					where row > @posStart 	
					and row <= @posStartAndCount
				</cfif>;

				insert into ##tblMessageStats (
					messageID,
					sg_open_total,
					sg_click_total,
					sg_drop_total,
					sg_bounce_total,
					sg_block_total,
					sg_spam_total,
					suppressed_total,
					optout_total,
					sg_open_unique,
					sg_click_unique,
					sg_drop_unique,
					sg_bounce_unique,
					sg_block_unique,
					sg_spam_unique,
					suppressed_unique,
					optout_unique
				)

				select 
					pivottable.messageID, 
					isnull(pivottable.[sg_open_total],0),
					isnull(pivottable.[sg_click_total],0),
					isnull(pivottable.[sg_drop_total],0),
					isnull(pivottable.[sg_bounce_total],0),
					isnull(pivottable.[sg_block_total],0),
					isnull(pivottable.[sg_spam_total],0),
					isnull(pivottable.[suppressed_total],0),
					isnull(pivottable.[optout_total],0),
					isnull(pivottable.[sg_open_unique],0),
					isnull(pivottable.[sg_click_unique],0),
					isnull(pivottable.[sg_drop_unique],0), 
					isnull(pivottable.[sg_bounce_unique],0),
					isnull(pivottable.[sg_block_unique],0),
					isnull(pivottable.[sg_spam_unique],0),
					isnull(pivottable.[suppressed_unique],0),
					isnull(pivottable.[optout_unique],0)
				from (
					select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
					from ##tblMessagesSubset msg 
					inner join dbo.email_messageRecipientHistory mrh 
						on mrh.siteID = @siteID
						and msg.messageID = mrh.messageID
					inner join dbo.email_messageRecipientHistoryTracking rt
						on rt.siteID = @siteID
						and mrh.recipientID = rt.recipientID
					inner join dbo.email_statuses rtst on rtst.statusID = rt.statusID
						and rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_bounce','sg_block','sg_spam')
					inner join ##tblPivotKeys pk on pk.statusCode = rtst.statuscode
					group by mrh.messageID, pk.pivotkey, pk.suffix

					union 

					-- summary for statuses where recipient remains in final status, no entries in email_messageRecipientHistoryTracking
					select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
					from ##tblMessagesSubset msg 
					inner join dbo.email_messageRecipientHistory mrh 
						on mrh.siteID = @siteID
						and mrh.messageID = msg.messageID
					inner join dbo.email_statuses rtst 
						on rtst.statusID = mrh.emailStatusID
						and rtst.statusCode in ('suppressed','optout')
					inner join ##tblPivotKeys pk on pk.statusCode = rtst.statuscode
					group by mrh.messageID, pk.pivotkey, pk.suffix
				) as dataToPivot
				PIVOT (
					sum(pivotvalue) for pivotkey in (
					[sg_open_total],[sg_click_total],[sg_drop_total],[sg_bounce_total],[sg_block_total],[sg_spam_total],[suppressed_total],[optout_total],
					[sg_open_unique],[sg_click_unique],[sg_drop_unique],[sg_bounce_unique],[sg_block_unique],[sg_spam_unique],[suppressed_unique],[optout_unique]) 
				) as pivottable;


				select data.row, data.messageID, data.messageType, data.dateEntered, data.sendOnDate, data.fromName, data.replyToEmail,
					data.subject, data.messageCount, stats.sg_open_total, stats.sg_click_total, stats.optout_total, stats.sg_open_unique, stats.sg_click_unique,
					(stats.sg_drop_unique + stats.suppressed_unique) as suppressed_unique,
					(stats.sg_bounce_unique + stats.sg_block_unique + stats.sg_spam_unique) as problems_unique, @totalCount as totalCount
				<cfif arguments.mode eq "export">
					into ##tblMessagesFinal
				</cfif>
				from (
					select em.messageID, emt.messageType, em.dateEntered, em.sendOnDate, em.fromName, em.replyToEmail,
						CASE WHEN emt.allowAdminView = 1 THEN em.subject ELSE ''+ emt.messageType +': For security, the content of this message is not viewable' END as subject,
						tmp.MsgCount as messageCount, tmp.row
					from ##tblMessagesSubset tmp
					inner join dbo.email_messages as em on em.status='A' and em.siteID = @siteID and em.messageID = tmp.messageID	
					inner join dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID	
				) as data
				left outer join ##tblMessageStats stats on stats.messageID = data.messageID
				order by data.row;

				<cfif arguments.mode eq "export">
					DECLARE @selectsql varchar(max) = '
						SELECT MessageID, MessageType, dateEntered as MessageCreatedDate, sendOnDate as MessageSentDate, FromName, ReplyToEmail, Subject, 
							isnull(messageCount,0) as Recipients, isnull(sg_open_total,0) as TotalOpens, isnull(sg_open_unique,0) as UniqueOpens, 
							isnull(sg_click_total,0) as TotalClicks, isnull(sg_click_unique,0) as UniqueClicks, isnull(suppressed_unique,0) as RecipientsSuppressed, isnull(problems_unique,0) as RecipientsWithProblems, 
							ROW_NUMBER() OVER(order by row) as mcCSVorder
						*FROM* ##tblMessagesFinal';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMessages') IS NOT NULL
					DROP TABLE ##tblMessages;
				IF OBJECT_ID('tempdb..##tblMessagesSubset') IS NOT NULL
					DROP TABLE ##tblMessagesSubset;
				IF OBJECT_ID('tempdb..##tblPivotKeys') IS NOT NULL
					DROP TABLE ##tblPivotKeys;
				IF OBJECT_ID('tempdb..##tblMessageStats') IS NOT NULL
					DROP TABLE ##tblMessageStats;
				IF OBJECT_ID('tempdb..##tblMessagesFinal') IS NOT NULL
					DROP TABLE ##tblMessagesFinal;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfif arguments.mode EQ 'grid'>
			<cfreturn local.qryActivity>
		<cfelse>
			<!---Returning junk Query--->
			<cfreturn QueryNew("categoryID,categoryName","integer,varchar")>
		</cfif>
	</cffunction>

</cfcomponent>