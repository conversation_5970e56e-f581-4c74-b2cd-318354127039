ALTER PROC dbo.ts_forwardIncomingDepoConnectMessage
@receiverParticipantID int,
@senderParticipantID int,
@emailContent varchar(max),
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @messageTypeID int, @adminToolSiteResourceID int,@newContentID int, @newResourceID int,
		@resourceTypeID int, @nowDate datetime = getdate(), @contentTitle varchar(250), @contentVersionID int,
		@conversationSubject varchar(400), @languageID int, @fromEmail varchar(400), @sentOnBehalfOfName varchar(200), @fromFirm varchar(200),
		@fromNameAndFirm varchar(200), @messageStatusIDInserting int, @tier varchar(12),
		@defaultMarketingSubUserID int, @replyToEmail varchar(200), @emailTypeID int, @fieldID int,
		@conversationID int, @receiverRoleCode varchar(20), @approvedByDepoMemberDataID int, @senderRoleCode varchar(20),
		@sentOnBehalfOfParticipantID int, @onBehalfOfReceiverDepoMemberID int, @onBehalfOfReceiverMemberID int,
		@delegateName varchar(200), @requestorName varchar(200), @respondentName varchar(200), @orgSysMemberID int,
		@itemGroupUID uniqueidentifier, @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
		@inquiryOptOutListID int, @globalOptOutListID int, @messageLogEmail varchar(100) = '<EMAIL>', @consentListIDs VARCHAR(MAX);

	DECLARE @tblRecipientRoles TABLE (roleCode varchar(20));

	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;
	CREATE TABLE #tmpRecipientEmails (email varchar(200), recipientName varchar(200));
	CREATE TABLE #tmpRecipientsAdded (recipientID INT, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));

	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @languageID = membercentral.dbo.fn_getLanguageID('en');
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = 'TS';
	SELECT @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EXPERTINQUIRY';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @defaultMarketingSubUserID = defaultMarketingSubUserID FROM membercentral.dbo.platform_environments WHERE environmentName = @tier;
	SELECT @fromEmail = 'noreply@' + sendingHostName FROM platformMail.dbo.sendgrid_subuserDomains WHERE subuserID = @defaultMarketingSubUserID;
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @adminToolSiteResourceID = ast.siteResourceID
	FROM membercentral.dbo.admin_siteTools ast
	INNER JOIN membercentral.dbo.admin_toolTypes att ON att.tooltypeID = ast.toolTypeID
 		AND att.toolType = 'TrialSmithTools'
	WHERE ast.siteID = @siteID;

	SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
	FROM dbo.expertConnectInquirySettings;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
		AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	DECLARE @initialQueuePriority int, @expectedRecipientCount int;
	SELECT @expectedRecipientCount = COUNT(*) FROM #tmpRecipientEmails;
	SELECT @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	SELECT @conversationID =  p.conversationID, @receiverRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @receiverParticipantID;

	IF @receiverRoleCode IN ('Requestor','RequestorDelegate')
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Requestor'), ('RequestorDelegate');
	ELSE
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Respondent'), ('RespondentDelegate');
	
	-- get recipient emails
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	SELECT email, LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,'')))
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN @tblRecipientRoles AS tmp ON tmp.roleCode = r.roleCode
	WHERE p.conversationID = @conversationID;

	-- add cc recipient
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	VALUES(@messageLogEmail, 'DepoConnect Message Logs');

	DELETE tmp
	FROM #tmpRecipientEmails AS tmp
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
		AND clm.email = tmp.email;

	IF NOT EXISTS (SELECT 1 FROM #tmpRecipientEmails)
		GOTO on_done;

	-- get memberID for recipients
	IF @receiverRoleCode IN ('Requestor','Respondent')
		SELECT @onBehalfOfReceiverDepoMemberID = depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants
		WHERE participantID = @receiverParticipantID;
	ELSE BEGIN
		SELECT @onBehalfOfReceiverDepoMemberID = p.depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @receiverRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode='TS',
		@depomemberdataid=@onBehalfOfReceiverDepoMemberID, @memberID=@onBehalfOfReceiverMemberID OUTPUT;

	IF @onBehalfOfReceiverMemberID = 0
		SET @onBehalfOfReceiverMemberID = @orgSysMemberID;
	
	-- prep contentTitle, conversationSubject, fromName & replyToEmail
	SELECT @senderRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @senderParticipantID;

	IF @senderRoleCode IN ('Requestor','Respondent')
		SET @sentOnBehalfOfParticipantID = @senderParticipantID;
	ELSE BEGIN
		SELECT @sentOnBehalfOfParticipantID = p.participantID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @senderRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	SELECT @contentTitle = 'Re: ' + LTRIM(RTRIM(ISNULL(i.expertFirstName,'') + ' ' + ISNULL(i.expertLastName,''))) + ' - ' + i.topic,
		@delegateName = CASE
				WHEN @sentOnBehalfOfParticipantID <> @senderParticipantID
					THEN (
						SELECT LTRIM(RTRIM(ISNULL(p2.firstName,'') + ' ' + ISNULL(p2.lastName,'')))
						FROM dbo.expertConnectInquiryConversationParticipants AS p2
						WHERE p2.participantID = @senderParticipantID
					)
				ELSE ''
			END,
		@sentOnBehalfOfName = LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,''))),
		@fromFirm = CASE WHEN r.roleCode = 'Requestor' THEN i.companyName ELSE d.BillingFirm END
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN dbo.depomemberdata AS d ON d.depoMemberDataID = p.depoMemberDataID
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = p.inquiryID
	WHERE p.participantID = @sentOnBehalfOfParticipantID;

	SET @fromNameAndFirm = @sentOnBehalfOfName + ' - ' + @fromFirm;
	IF LEN(@delegateName) > 0
		SET @fromNameAndFirm = @delegateName + ' on behalf of ' + @fromNameAndFirm;

	SELECT @requestorName = req.firstName + ' ' + req.lastName,
		@respondentName = resp.firstName + ' ' + resp.lastName,
		@replyToEmail = i.emailAddressSlug + '_' + CAST(i.inquiryID as varchar(10)) + '_' +
			CASE 
				WHEN @senderRoleCode IN ('Requestor','RequestorDelegate') THEN CAST(req.participantID as varchar(10))
				ELSE CAST(resp.participantID as varchar(10))
			END + '@depoconnect.trialsmith.com'
	FROM dbo.expertConnectInquiryConversations c
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS req
		INNER JOIN dbo.expertConnectInquiryRoles AS r1 ON r1.roleID = req.roleID
			AND r1.roleCode = 'Requestor'
		INNER JOIN dbo.depomemberdata AS d1 ON d1.depoMemberDataID = req.depoMemberDataID
		ON req.conversationID = c.conversationID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS resp
		INNER JOIN dbo.expertConnectInquiryRoles AS r2 ON r2.roleID = resp.roleID
			AND r2.roleCode = 'Respondent'
		INNER JOIN dbo.depomemberdata AS d2 ON d2.depoMemberDataID = resp.depoMemberDataID
		ON resp.conversationID = c.conversationID
	WHERE c.conversationID = @conversationID;

	SET @emailContent = REPLACE(@emailContent,'[[replyToEmailAddress]]',@replyToEmail);

	SET @conversationSubject = @contentTitle + ' (' + @requestorName + ' and ' + @respondentName + ')';

	BEGIN TRAN;

		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID,
			@parentSiteResourceID=@adminToolSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=@languageID, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@emailContent,
			@memberID=@orgSysMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

		SELECT TOP 1 @contentVersionID = cv.contentVersionID
		FROM membercentral.dbo.cms_content AS c 
		INNER JOIN membercentral.dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID
			AND cl.languageID = @languageID
		INNER JOIN membercentral.dbo.cms_contentVersions AS cv ON cv.contentLanguageID = cl.contentLanguageID
			AND cv.isActive = 1
		WHERE c.contentID = @newContentID;

		-- get consentListIDS
		SELECT @consentListIDs = cast(NULLIF(@inquiryOptOutListID,0) as varchar(10));

		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=NULL,
			@sendingSiteResourceID=@adminToolSiteResourceID, @isTestMessage=0, @sendOnDate=@nowDate, @recordedByMemberID=@orgSysMemberID,
			@fromName=@fromNameAndFirm, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',  @subject=@conversationSubject,
			@contentVersionID=@contentVersionID, @messageWrapper='', @referenceType='EXPERTINQUIRY_REPLY',
			@referenceID=@conversationID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

		-- add recipients
		INSERT INTO platformMail.dbo.email_messageRecipientHistory(messageID, memberID, dateLastUpdated,
			toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
			OUTPUT INSERTED.recipientID
			INTO #tmpRecipientsAdded (recipientID)
		SELECT @messageID, CASE WHEN email = @messageLogEmail THEN @orgSysMemberID ELSE @onBehalfOfReceiverMemberID END,
			@nowDate, recipientName, email,
			@messageStatusIDInserting, NULL, NULL, @emailTypeID, @siteID, @initialQueuePriority
		FROM #tmpRecipientEmails;

		EXEC platformMail.dbo.email_insertMetadataField @fieldName='emailOptOutURL', @isMergeField=1, @fieldID=@fieldID OUTPUT;

		SET @itemGroupUID = NEWID();
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

		-- queue recipient details with extended merge code [[emailOptOutURL]]
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		SELECT itemUID, @insertingQueueStatusID
		FROM #tmpRecipientsAdded;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, tmp.recipientID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCRecipientID'
			UNION
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, @messageID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID
		FROM platformMail.dbo.email_metadataFields AS mdf
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCExtMergeCodeFieldID'
		CROSS JOIN #tmpRecipientsAdded AS tmp
		WHERE mdf.fieldName = 'emailOptOutURL';

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = GETDATE()
		FROM platformQueue.dbo.tblQueueItems AS qi
		INNER JOIN #tmpRecipientsAdded AS tmp ON tmp.itemUID = qi.itemUID;

	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
