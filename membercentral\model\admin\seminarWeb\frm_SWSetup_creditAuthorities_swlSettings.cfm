<cfsavecontent variable="local.swlSettingsJS">
	<cfoutput>
	<script type="text/javascript">
		function validateAndSaveAuthoritySWL(){
			var arrReq = new Array();
			var authorityForm = $('##frmAuthoritySWL');
			mca_hideAlert('ca_swl_frm_err');

			var swlPromptInterval = $('##swlPromptInterval').val();
			var daysToCompleteExam = $('##daysToCompleteExam').val();
			var daysToCompleteEvaluation = $('##daysToCompleteEvaluation').val();

			if (swlPromptInterval == '') arrReq[arrReq.length] = 'Enter the SeminarWeb Live! prompt interval. Enter 0 if not required.';
			else if (isNaN(swlPromptInterval) || Number(swlPromptInterval) != parseInt(swlPromptInterval)) arrReq[arrReq.length] = 'Enter a valid SeminarWeb Live! prompt interval. Enter 0 if not required.';

			if (daysToCompleteExam == '') arrReq[arrReq.length] = 'Enter the SeminarWeb Live days to complete exams. Enter 0 if not applicable.';
			else if (isNaN(daysToCompleteExam) || Number(daysToCompleteExam) != parseInt(daysToCompleteExam)) arrReq[arrReq.length] = 'Enter the SeminarWeb Live days to complete exams. Enter 0 if not applicable.';

			if (daysToCompleteEvaluation == '')  arrReq[arrReq.length] = 'Enter the SeminarWeb Live days to complete evaluations. Enter 0 if not applicable.';
			else if (isNaN(daysToCompleteEvaluation) || Number(daysToCompleteEvaluation) != parseInt(daysToCompleteEvaluation)) arrReq[arrReq.length] = 'Enter a valid SeminarWeb Live days to complete evaluations. Enter 0 if not applicable.';


			if(arrReq.length){
				mca_showAlert('ca_swl_frm_err', arrReq.join('<br/>'), true);
				return false;
			}

			var fd = authorityForm.serializeArray();
			$('##divAuthoritySWLForm').hide();
			var loadingHTML = $('##divAuthoritySWLSaveLoading').html();

			$("##divAuthoritySWLFormSubmitArea").html(loadingHTML).show().load('#local.saveCreditAuthoritySWLLink#', fd, function() { onAuthoritySWLSaveComplete(); });
			$("html, body").animate({ scrollTop: 0 }, "slow");

			$('##btnSaveAuthoritySWL').attr('disabled', true);
			return true;
		}
		function onAuthoritySWLSaveComplete() {
			editCreditAuthority(sw_creditauthorityid,'swlSettings');	
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.swlSettingsJS)#">
<cfoutput>

<div id="divAuthoritySWLForm">
	<form name="frmAuthoritySWL" id="frmAuthoritySWL">
		<input type="hidden" id="swlMustAttend" name="swlMustAttend" value="1">
		<input type="hidden" id="swlMustAttendMinutes" name="swlMustAttendMinutes" value="0">

		<div id="ca_swl_frm_err" class="alert alert-danger mb-2 d-none"></div>

		<h5 class="my-3">Requirements for Earning Credit with this Authority</h5>
		<div class="form-group row">
			<label for="swlPromptTypeID" class="col-sm-5 col-form-label-sm font-size-md">Type of Prompting Required *</label>
			<div class="col-sm-7">
				<select name="swlPromptTypeID" id="swlPromptTypeID" class="form-control form-control-sm">
					<option value="">No prompting required</option>
					<cfloop query="local.qryPromptTypes">
						<option value="#local.qryPromptTypes.promptTypeID#" <cfif local.qryPromptTypes.promptTypeID eq local.qryAuthority.SWLPromptTypeID>selected</cfif>>#local.qryPromptTypes.promptType#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<div class="form-group row">
			<label for="swlPromptInterval" class="col-sm-5 col-form-label-sm font-size-md">Prompting Interval (in minutes) *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="swlPromptInterval" id="swlPromptInterval" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.SWLpromptInterval)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if prompting is not required, enter 0 </i>)</span>
			</div>
		</div>

		<h5 class="my-3">Forms By Load Point</h5>

		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>PRETEST</b> - Must Pass all Exams to Earn Credit *</div>
			<div class="col-sm">
				<div class="form-check-inline">
					<input type="radio" name="preExamRequired" id="preExamRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.swlPreExamRequired) is 0>checked</cfif>>
					<label for="preExamRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="preExamRequired" id="preExamRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.swlPreExamRequired) is 1>checked</cfif>>
					<label for="preExamRequired_1" class="form-check-label">Yes, registrant must pass all exams.</label>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>POSTTEST</b> - Must Pass all Exams to Earn Credit *</div>
			<div class="col-sm">
				<div class="form-check-inline">
					<input type="radio" name="examRequired" id="examRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.SWLExamRequired) is 0>checked</cfif>>
					<label for="examRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="examRequired" id="examRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.SWLExamRequired) is 1>checked</cfif>>
					<label for="examRequired_1" class="form-check-label">Yes, registrant must pass all exams.</label>
				</div>
			</div>
		</div>
		<div class="form-group row">
			<label for="daysToCompleteExam" class="col-sm-5 col-form-label-sm font-size-md">Num Days to Complete Exams to Earn Credit *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="daysToCompleteExam" id="daysToCompleteExam" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.SWLdaysToCompleteExam)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if not applicable, enter 0 </i>)</span>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>EVALUATION</b> - Must Complete all Evaluations to Earn Credit *</div>
			<div class="col-sm">				
				<div class="form-check-inline">
					<input type="radio" name="evaluationRequired" id="evaluationRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.SWLEvaluationRequired) is 0>checked</cfif>>
					<label for="evaluationRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="evaluationRequired" id="evaluationRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.SWLEvaluationRequired) is 1>checked</cfif>>
					<label for="evaluationRequired_1" class="form-check-label">Yes, registrant must complete all evaluations.</label>
				</div>
			</div>
		</div>
		<div class="form-group row">
			<label for="daysToCompleteEvaluation" class="col-sm-5 col-form-label-sm font-size-md">Num Days to Complete Exams to Earn Credit *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="daysToCompleteEvaluation" id="daysToCompleteEvaluation" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.SWLdaysToCompleteEvaluation)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if not applicable, enter 0 </i>)</span>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5">* indicates a required field</div>
			<div class="col-sm-7">
				<button type="button" id="btnSaveAuthoritySWL" name="btnSaveAuthoritySWL" class="btn btn-sm btn-primary" onclick="validateAndSaveAuthoritySWL();">Save Details</button> 
				<button type="button" id="btnCancelAuthoritySWL" name="btnCancelAuthoritySWL" class="btn btn-sm btn-secondary" onclick="cancelAuthorityForm();">Cancel</button>
			</div>
		</div>
	</form>
</div>

<div id="divAuthoritySWLFormSubmitArea"></div>
<div id="divAuthoritySWLSaveLoading" class="d-none">
	<h4>Edit Credit Authority</h4>
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
</cfoutput>