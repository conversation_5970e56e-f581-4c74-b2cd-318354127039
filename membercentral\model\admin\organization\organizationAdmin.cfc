<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.edit = buildCurrentLink(arguments.event,"edit");
			this.link.saveBasic = buildCurrentLink(arguments.event,"saveBasic");
			this.link.saveCredit = buildCurrentLink(arguments.event,"saveCredit");
			this.link.removeProLicenseStatus = buildCurrentLink(arguments.event,"removeProLicenseStatus");
			this.link.removeOrgIdentity = buildCurrentLink(arguments.event,"removeOrgIdentity");

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
		
	<cffunction name="edit" access="public" output="false" returntype="struct" hint="Edit">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objOrg = CreateObject("component","model.admin.organization.organization")>

		<cfscript>
		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		appendBreadCrumbs(arguments.event,{ link='', text="Edit Settings" });
		</cfscript>
		
		<!--- tab setup --->
		<cfset var currTab = arguments.event.getValue('tab','basic')>
		<cfif currTab eq ''><cfset currTab = 'basic'></cfif>
		<cfset local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','')>
		<cfset local.arrTabs = arrayNew(1)>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Basic', id='basic', fn='frm_organization_basic' }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.qryOrganization = local.objOrg.getSettings(orgid=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryMemberNumberOptions = local.objOrg.getMemberNumberOptions()>
			<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="defaultOrgIdentityID", selectedValueID=val(local.qryOrganization.defaultOrgIdentityID), allowBlankOption=false)>
		</cfif>
		
		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Record Types', id='rectype', fn='frm_organization_rectype' }>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Demographics', id='memfields', fn='frm_organization_memfields' }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.qryOrganization = local.objOrg.getSettings(orgid=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(arguments.event.getValue('mc_siteinfo.orgid'))>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Addresses', id='address', fn='frm_organization_address' }>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Phones', id='phone', fn='frm_organization_phone' }>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Emails', id='email', fn='frm_organization_email' }>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Websites', id='website', fn='frm_organization_website' }>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Prof Licenses', id='license', fn='frm_organization_license' }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.qryOrganization = local.objOrg.getSettings(orgid=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>

			<cfquery name="local.qryMemberDataDateCustomFields" datasource="#application.dsn.membercentral.dsn#">
				SELECT mdc.columnID, mdc.columnName
				FROM dbo.ams_memberDataColumns as mdc
				INNER JOIN dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
				WHERE mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
				AND mdt.dataTypeCode = 'DATE'
				ORDER BY mdc.columnName
			</cfquery>
			
			<cfquery name="local.qryMemberDataNumberCustomFields" datasource="#application.dsn.membercentral.dsn#">
				SELECT mdc.columnID, mdc.columnName
				FROM dbo.ams_memberDataColumns as mdc
				INNER JOIN dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
				WHERE mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
				AND mdt.dataTypeCode = 'INTEGER'
				ORDER BY mdc.columnName
			</cfquery>
			
			<cfquery name="local.qryOrgEarlyLicenseSettings" datasource="#application.dsn.membercentral.dsn#">
				SELECT pld.columnID, plt.PLTypeID, plt.PLStatusID
				FROM dbo.ams_memberProfessionalLicenseEarliestDate pld
				LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseEarliestDateTypes plt on pld.orgID = plt.orgID
				WHERE pld.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
				ORDER BY plt.PLTypeID, plt.PLStatusID
			</cfquery>
			<cfquery name="local.earlyProfLicenseTypes" dbtype="query">
				SELECT DISTINCT PLTypeID
				FROM [local].qryOrgEarlyLicenseSettings
				WHERE PLTypeID IS NOT NULL
			</cfquery>
			<cfquery name="local.earlyProfLicenseStatuses" dbtype="query">
				SELECT DISTINCT PLStatusID
				FROM [local].qryOrgEarlyLicenseSettings
				WHERE PLStatusID IS NOT NULL
			</cfquery>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Districting', id='district', fn='frm_organization_district' }>
		
		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Credit Types', id='credit', fn='frm_organization_credit' }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreditSponsor">
				select top 1 sponsorID, statementAppProvider, statementAppProgram, statementPendProgram
				from dbo.crd_sponsors
				where orgID = #arguments.event.getValue('mc_siteinfo.orgid')#
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreditAuthorities">
				select cas.ASID, ca.authorityID, ca.authorityCode, ca.authorityName, cas.certificateMessage, cas.creditMessage, cas.affirmationFileName
				from dbo.crd_authoritySponsors as cas
				inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
				where cas.sponsorID = #val(local.qryCreditSponsor.sponsorID)#
				order by ca.authorityName
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreditAuthorityTypes">
				select typeID, authorityID, typeCode, typeName
				from dbo.crd_authorityTypes
				where authorityID IN (0#valueList(local.qryCreditAuthorities.authorityID)#)
				order by authorityID, typeName
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreditAuthoritySponsorTypes">
				select ASID, typeID, ovtypeName, LiveApprovedCertificateID
				from dbo.crd_authoritySponsorTypes
				where ASID IN (0#ValueList(local.qryCreditAuthorities.ASID)#)
				order by ASID, typeID
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCertificates">
				select cer.certificateID, cer.certFileName, cer.friendlyName, cast(cer.certificateID as varchar(10)) + ' - ' + cer.certFileName as certFileNameShort,
				isPortrait, case when isportrait = 0 then 'landscape' 
				else 'portrait' 
				end as orientation
				from dbo.crd_certificates as cer
				inner join dbo.sites as s on s.siteID = cer.siteID
				where s.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#
				order by cer.certFileName
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllCreditAuthorities">
				select ca.authorityID, ca.authorityName
				from dbo.crd_authorities as ca
				where ca.authorityID not IN (0#valueList(local.qryCreditAuthorities.authorityID)#)
				order by ca.authorityName
			</cfquery>
		</cfif>

		<cfset local.ap = ArrayLen(local.arrTabs)+1>
		<cfset local.arrTabs[local.ap] = { title='Identities', id='identity', fn='frm_organization_identity' }>
		<cfif currTab EQ local.arrTabs[local.ap].id>
			<cfset local.editOrgIdentityLink = buildCurrentLink(arguments.event,"editOrgIdentity") & "&mode=direct">
			<cfset local.previewOrgIdentityLink = buildCurrentLink(arguments.event,"previewOrgIdentity") & "&mode=stream">
			<cfset local.listOrgIdentitiesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=organizationJSON&meth=getOrgIdentities&mode=stream">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_organization.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveBasic" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var qryUpdate = "">
		<cfset var emailImportResults = arguments.event.getTrimValue('emailImportResults')>

		<cfscript>
			if (len(emailImportResults)) {
				var stReplyTo = replace(replace(emailImportResults,',',';','ALL'),' ','','ALL');
				var replyToEmailArr = listToArray(stReplyTo,';');
				for (var i=1; i lte arrayLen(replyToEmailArr); i++) {
					if (len(replyToEmailArr[i]) and not isValid("regex",replyToEmailArr[i],application.regEx.email)) {
						arrayDeleteAt(replyToEmailArr,i);
					}
				}
				emailImportResults = arrayToList(replyToEmailArr,';');
			}
		</cfscript>

		<cfquery name="local.qryGetDefaultOrgIdentityID" datasource="#application.dsn.memberCentral.dsn#">
			SELECT defaultOrgIdentityID
			FROM dbo.organizations
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdate">
			UPDATE dbo.organizations
			SET emailImportResults = <cfqueryparam cfsqltype="cf_sql_varchar" value="#emailImportResults#">,
				memNumPrefixGuest = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('memNumPrefixGuest')#">,
				memNumPrefixUser = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('memNumPrefixUser')#">,
				memberNumberOptionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('memNumOption')#">,
				autoGenMemNumMax = 
					<cfif arguments.event.getValue('memNumOption') eq 2>
						<cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('autoGenMemNumMax'))#">
					<cfelse>
						null
					</cfif>,
				allowedAdminCount = 
					<cfif len(arguments.event.getValue('allowedAdminCount'))>
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('allowedAdminCount')#">
					<cfelse>
						<cfqueryparam cfsqltype="cf_sql_integer" null="true">
					</cfif>,
				defaultOrgIdentityID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('defaultOrgIdentityID')#">
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
		</cfquery>

		<cflocation url="#this.link.edit#" addtoken="no">
	</cffunction>

	<cffunction name="saveCredit" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif len(arguments.event.getTrimValue('ASIDList',''))>
			<cfset local.strSponsor = structNew()>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAuthorityTypes">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT cat.typeID, cas.ASID
				FROM dbo.crd_authoritySponsors AS cas 
				INNER JOIN dbo.crd_authorities AS ca ON ca.authorityID = cas.authorityID
				INNER JOIN dbo.crd_authorityTypes AS cat ON cat.authorityID = ca.authorityID
				WHERE cas.ASID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.event.getTrimValue('ASIDList')#">)
				AND cas.sponsorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('sponsorID'))#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfloop list="#arguments.event.getTrimValue('ASIDList')#" index="local.thisASID">
				<cfquery name="local.qryThisAuthorityTypes" dbtype="query">
					SELECT typeID
					FROM [local].qryAuthorityTypes
					WHERE ASID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisASID#">
				</cfquery>

				<cfset local.strSponsor[local.thisASID] = { "strAuthorityTypes":{} }>

				<cfloop query="local.qryThisAuthorityTypes">
					<cfif arguments.event.getTrimValue('authorityType_#local.thisASID#_#local.qryThisAuthorityTypes.typeID#',0) IS 1>
						<cfset local.strSponsor[local.thisASID]["strAuthorityTypes"][local.qryThisAuthorityTypes.typeID] = {
							"authorityTypeOV": arguments.event.getTrimValue('authorityTypeOV_#local.thisASID#_#local.qryThisAuthorityTypes.typeID#',''),
							"authorityTypeLiveAppCertID": val(arguments.event.getTrimValue('authorityTypeLiveAppCertID_#local.thisASID#_#local.qryThisAuthorityTypes.typeID#',0))
						}>
					<cfelse>
						<cfset local.strSponsor[local.thisASID]["strAuthorityTypes"][local.qryThisAuthorityTypes.typeID] = {}>
					</cfif>
				</cfloop>
			</cfloop>
		</cfif>

		<cfquery name="local.qrySaveCredit" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('mc_siteinfo.orgid')#">, 
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('mc_siteinfo.siteid')#">,
					@sponsorID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('sponsorID'))#">,
					@ASTID int;
				
				BEGIN TRAN;
					<!--- certificates --->
					<cfloop list="#arguments.event.getTrimValue('CertList','')#" index="local.thisCertID">
						UPDATE dbo.crd_certificates
						SET friendlyName = ISNULL(NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('certDesc_#local.thisCertID#','')#">,''),'Unnamed Certificate'),
							certFileName = ISNULL(NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('certName_#local.thisCertID#','')#">,''),'#local.thisCertID#'),
							isPortrait = ISNULL(NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('certOrientation_#local.thisCertID#','')#">,''),'#local.thisCertID#')
						WHERE certificateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCertID#">;
					</cfloop>
					
					<cfif len(arguments.event.getTrimValue('certName_x',''))>
						insert into dbo.crd_certificates (friendlyName, siteID, certFileName,isPortrait)
						values (<cfqueryparam value="#arguments.event.getTrimValue('certDesc_x')#" cfsqltype="CF_SQL_VARCHAR">, 
							@siteID,
							<cfqueryparam value="#arguments.event.getTrimValue('certName_x')#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.event.getTrimValue('certOrientation_x')#" cfsqltype="CF_SQL_VARCHAR">
						);
					</cfif>
					
					<!--- sponsor information --->
					UPDATE dbo.crd_sponsors
					SET statementAppProvider = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('statementAppProvider','')#">,
						statementAppProgram = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('statementAppProgram','')#">,
						statementPendProgram = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('statementPendProgram','')#">
					WHERE sponsorID = @sponsorID
					AND orgID = @orgID;
					
					<!--- authorities --->
					<cfloop list="#arguments.event.getTrimValue('ASIDList','')#" index="local.thisASID">
						UPDATE dbo.crd_authoritySponsors
						SET certificateMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('certificateMessage_#local.thisASID#','')#">,
							creditMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('creditMessage_#local.thisASID#','')#">,
							affirmationFileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('affirmationFilename_#local.thisASID#','')#">
						WHERE ASID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisASID#">
						AND sponsorID = @sponsorID;
		
						<cfloop collection="#local.strSponsor[local.thisASID]["strAuthorityTypes"]#" item="local.typeID">
							SET @ASTID = NULL;
							
							SELECT @ASTID = ASTID
							FROM dbo.crd_authoritySponsorTypes
							WHERE ASID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisASID#">
							AND typeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.typeID#">;

							<cfif NOT structIsEmpty(local.strSponsor[local.thisASID]["strAuthorityTypes"][local.typeID])>
								IF @ASTID IS NULL BEGIN
									INSERT INTO dbo.crd_authoritySponsorTypes (ASID, typeID, ovTypeName, liveApprovedCertificateID, liveDeniedCertificateID) 
									values (<cfqueryparam value="#local.thisASID#" cfsqltype="CF_SQL_INTEGER">, 
										<cfqueryparam value="#local.typeID#" cfsqltype="CF_SQL_INTEGER">,
										NULLIF(<cfqueryparam value="#local.strSponsor[local.thisASID]["strAuthorityTypes"][local.typeID].authorityTypeOV#" cfsqltype="CF_SQL_VARCHAR">,''),
										NULLIF(<cfqueryparam value="#local.strSponsor[local.thisASID]["strAuthorityTypes"][local.typeID].authorityTypeLiveAppCertID#" cfsqltype="CF_SQL_INTEGER">,0),
										NULL
									);
								END ELSE BEGIN
									UPDATE dbo.crd_authoritySponsorTypes
									SET ovTypeName = nullIf(<cfqueryparam value="#local.strSponsor[local.thisASID]["strAuthorityTypes"][local.typeID].authorityTypeOV#" cfsqltype="CF_SQL_VARCHAR">,''),
										liveApprovedCertificateID = nullIf(<cfqueryparam value="#local.strSponsor[local.thisASID]["strAuthorityTypes"][local.typeID].authorityTypeLiveAppCertID#" cfsqltype="CF_SQL_INTEGER">,0)
									WHERE ASTID = @ASTID;
								END
							<cfelse>
								IF @ASTID IS NOT NULL BEGIN
									IF EXISTS (SELECT 1 FROM dbo.crd_offeringTypes WHERE ASTID = @ASTID)
										RAISERROR('Authority Sponsor Type in use and cannot be deleted.',16,1);

									DELETE FROM dbo.cms_myCEPageCreditTypes
									WHERE ASTID = @ASTID;

									DELETE FROM dbo.crd_authoritySponsorTypes
									WHERE ASTID = @ASTID;
								END
							</cfif>
						</cfloop>
					</cfloop>
		
					<cfif len(arguments.event.getTrimValue('newASID',''))>
						INSERT INTO dbo.crd_authoritySponsors (authorityID, sponsorID, certificateMessage, creditMessage, affirmationFileName)
						VALUES (<cfqueryparam value="#arguments.event.getTrimValue('newASID')#" cfsqltype="CF_SQL_INTEGER">,  @sponsorID, '', '', NULL);
					</cfif>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
			IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	
		<cflocation url="#this.link.edit#&tab=credit" addtoken="no">
	</cffunction>

	<cffunction name="removeProLicenseStatus" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryOrgProLicensesStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>

		<cfquery name="local.qryCurrStatus" dbtype="query">
			select PLStatusID, StatusName
			from [local].qryOrgProLicensesStatuses
			where PLStatusID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('plsid',0)#">
		</cfquery>

		<cfquery name="local.qryNewStatuses" dbtype="query">
			select PLStatusID, StatusName
			from [local].qryOrgProLicensesStatuses
			where PLStatusID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('plsid',0)#">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_removeLicenseStatus.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrgIdentity" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryStates = application.objCommon.getStates()>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('oid',0))>
		<cfset local.orgIdentityID = val(local.qryOrgIdentity.orgIdentityID)>
		<cfset local.usageMode = arguments.event.getValue('usageMode','OrgAdmin')>
		<cfset local.orgIdToEdit = arguments.event.getValue('orgIdToEdit',0)>
		<cfif local.orgIdToEdit eq 0>
			<cfset local.orgIdToEdit = arguments.event.getValue('mc_siteinfo.orgID')>
		</cfif>
		
		<cfif local.orgIdentityID GT 0>
			<cfset local.qryOrgIdentityUsages = CreateObject("component","organization").getOrgIdentityUsages(orgID=local.orgIdToEdit, orgIdentityID=local.orgIdentityID)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editOrgIdentity.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="previewOrgIdentity" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('oid',0))>
		<cfset local.qryOrgIdentityUsages = CreateObject("component","organization").getOrgIdentityUsages(orgID=local.qryOrgIdentity.orgID, orgIdentityID=local.qryOrgIdentity.orgIdentityID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_previewOrgIdentity.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>