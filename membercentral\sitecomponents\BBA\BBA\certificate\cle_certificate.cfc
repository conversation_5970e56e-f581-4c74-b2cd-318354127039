<cfcomponent output="false">

	<cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
		<cfscript>
			var strCertMergeCodes = {
				"member": "prefix,firstname,middlename,lastname,suffix,professionalsuffix,Alabama_licenseNumber"
			};
			return strCertMergeCodes;
		</cfscript>
	</cffunction>

	<cffunction name="generateCertBody" access="public" output="false" returntype="string">
		<cfargument name="strCertMergeCodes" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.arrSpeakers = ArrayNew(1)> 	  
		<cfif structKeyExists(arguments.strCertMergeCodes.event,"qryEventRoles")>
			<cfloop query="arguments.strCertMergeCodes.event.qryEventRoles">
				<cfif FindNoCase("Speaker",arguments.strCertMergeCodes.event.qryEventRoles.categoryName)>
					<cfset ArrayAppend(local.arrSpeakers,arguments.strCertMergeCodes.event.qryEventRoles.fullName)>	
				</cfif>
			</cfloop>	
		</cfif> 
		   
		<cfsavecontent variable="local.registrantName">
			<cfoutput>#UCASE(arguments.strCertMergeCodes.member.firstname & " " & arguments.strCertMergeCodes.member.middlename & " " & arguments.strCertMergeCodes.member.lastname & " " & arguments.strCertMergeCodes.member.suffix)#</cfoutput>
		</cfsavecontent>

        <cfset local.generalCreditAvailTotal = 0>
		<cfset local.EthicsCreditAvailTotal = 0>
		<cfloop query="arguments.strCertMergeCodes.credit.qryPossibleCredits">
			<cfif arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType eq 'General'>
				<cfset local.generalCreditAvailTotal = local.generalCreditAvailTotal + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue />
			<cfelseif arguments.strCertMergeCodes.credit.qryPossibleCredits.creditType eq "Ethics">
				<cfset local.EthicsCreditAvailTotal = local.EthicsCreditAvailTotal + arguments.strCertMergeCodes.credit.qryPossibleCredits.creditValue />
			</cfif>
		</cfloop>
		<cfset local.creditAvailTotal = local.generalCreditAvailTotal + local.EthicsCreditAvailTotal/>
		<cfset local.creditAvailTotal = numberFormat(local.creditAvailTotal,'__.00')>

        <cfset local.totalEthnicCreditAwarded = 0>
        <cfset local.totalGeneralCreditAwarded = 0>
        <cfloop query="arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate">                   
            <cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>        
                <cfif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.originalCreditType EQ 'General'>
                    <cfset local.totalGeneralCreditAwarded = local.totalGeneralCreditAwarded + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
                <cfelseif arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.originalCreditType EQ 'Ethics'>
                    <cfset local.totalEthnicCreditAwarded = local.totalEthnicCreditAwarded + arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate.creditValueAwarded>
                 </cfif>    
            </cfif>
        </cfloop>
        <cfset local.totalCreditAwarded = local.totalGeneralCreditAwarded + local.totalEthnicCreditAwarded>
		<cfset local.totalCreditAwarded = numberFormat(local.totalCreditAwarded,'__.00')>
        <cfsavecontent variable="local.data">
			<cfoutput>
			<html>
				<head>
				<title>BBA CLE Certificate</title>
				<style>
					.borders{position:relative; z-index:10; margin:4px;  border:2px solid ##740730; }
					p.MsoNormal, td.MsoNormal, li.MsoNormal, div.MsoNormal { margin:0in; margin-bottom:.0001pt;font-size:12.0pt;font-family:"Times New Roman", Times, serif; }
					.Section1{ size:8.5in 11.0in;  padding:15px; margin-right:30px;}
                    .Section2{ size:8.5in 11.0in;  padding-left:15px; padding-right:15px; padding-top:5px; margin-right:30px;}
					div.Section1{ page:Section1; }
					tr { vertical-align:top; }
					div.MsoNormal, td.MsoNormal { font-size:8.0pt; font-family:"Times New Roman", Times, serif; }
					table { page-break-inside:auto }
					tr    { page-break-inside:avoid; page-break-after:auto }
					
					.text-center{
						text-align:center;
					}
					.text-right{
						text-align:right;
					}
					.text-left{
						text-align:left;
					}
                    .b{
                        font-weight:bold;
                    }
                    .pb7{
                        padding-bottom:7px;
                    }
                    .pl30{
                        padding-left:30px;
                    }
                    .sign-img {
                            width: 200px; /* Adjust as needed */
                            height: auto;
                        }
                    .square-img{
                         width: 680px; /* Adjust as needed */
                        
                       } 
                    .contentFirst  {
                    font-size:18px; 
                    }
					div.MsoNormal, td.MsoNormal { font-size:7.2pt; }
					body { height:11in;width:8.5in;padding:.2in;font-family: "Times New Roman", Times, serif;}
					
				</style>
				</head>
				<body>
                    <div class="Section1">	
                        <p  align="center" style="margin:0px;"><img src="#arguments.strCertMergeCodes.certificate.imagesurl#logo.png" height="50px"></p>
                    </div>
                   
                    <div class="Section1">	
                        <p  align="center" class="b" style="margin:0px;">CERTIFICATE OF ATTENDANCE <br>FOR CONTINUING LEGAL EDUCATION
                        </p>
                    </div>	
                    <table width="90%" class="text-left pl30"  cellpadding="0" cellspacing="1" > 
                        <tr>
                            <td class="text-left pb7"  width="30%">
                                <div class="contentFirst"><span>Sponsor:</span> </div>
                            </td>
                            <td class="text-left pb7" >
                                <div class="contentFirst"> Birmingham Bar Association  </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left pb7" width="30%">
                                <div class="contentFirst"><span>Title of Seminar:</span></div>
                            </td>
                            <td class="text-left pb7">
                                <div class="contentFirst"> #arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle#</div>
                            </td>
                        </tr>
                        <cfif arraylen(local.arrSpeakers)>
                            <tr>
                                <td>Speaker(s):#arrayToList(local.arrSpeakers,"; ")#</td>
                            </tr>
                        </cfif>
                        <tr>
                            <td class="text-left pb7" width="30%">
                                <div class="contentFirst">Date:</div>
                            </td>
                            <td class="text-left pb7" >
                                <div class="contentFirst">#DateFormat(arguments.strCertMergeCodes.event.qryEventTimes.startTime,"dddd, mmm dd, yyy ")#</div>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-left pb7" width="30%">
                                <div class="contentFirst">Location:</div>
                            </td>
                            <td class="text-left pb7" >
                                <div class="contentFirst">#arguments.strCertMergeCodes.event.qryEventMeta.locationContentTitle#</div>
                            </td>
                        </tr>
                    </table>
                    <br>                   
                    <table width="100%" cellpadding="1" class="text-left pl30" cellspacing="4" > 
                        
                        <tr>
                            <td>
                                <div>This program has been approved by the Alabama State Bar for a total of:<br><span class="b">#local.creditAvailTotal# CLE Credit hour (based on 60-minute hour)</span> </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div>Of this total, </div>
                            </td>
                        </tr> 
                        <tr>
                            <td>
                                <div><span class="b"> #local.EthicsCreditAvailTotal# CLE Credit</span> hour of this program was devoted to instruction in Ethics.</div>
                            </td>
                        </tr> 
                        <tr>
                            <td>
                                <div><span class="b"> #local.generalCreditAvailTotal# CLE Credit</span> hour of this program was devoted to instruction in General.</div>
                            </td>
                        </tr> 
                        <tr>
                            <td>
                                <div>Introductory remarks, keynote addresses, business meetings, meals, breaks, receptions, etc., are <br>not included in the computation of credit.</div>
                            </td>
                        </tr>                     
                    </table> 
                    <br>
                    <table width="65%" cellpadding="0" cellspacing="1" class="text-left pl30">   
                        <tr>  
                            <td class="text-left">
                                <img src="#arguments.strCertMergeCodes.certificate.imagesurl#square_lines.png">
                            </td>
                        </tr>                   
                    </table
                    <br>
                    <br>
                    <table width="100%" cellpadding="1" cellspacing="4" class="text-left pl30"> 
                        <tr>
                            <td>
                                <div>By signing below, I certify that I attended the activity described above and am entitled to claim<br>
                                   <span class="b">#local.totalCreditAwarded#</span> total CLE Credit hour, which was considered <span class="b">#local.totalEthnicCreditAwarded#</span> Ethics  and <span class="b">#local.totalGeneralCreditAwarded#</span> General credit hours by the Alabama State Bar.
                                </div>
                            </td>
                        </tr>                     
                    </table> 
                    <br>
                    <br>
                    <table width="100%" cellpadding="0" cellspacing="4" class="text-left pl30"> 
                        <tr>
                            <td class="text-left b" >Attendee's Name:#local.registrantName#	<br>  <cfif len(arguments.strCertMergeCodes.member.Alabama_licenseNumber) > Attendee's Bar Number(s): #arguments.strCertMergeCodes.member.Alabama_licenseNumber#</cfif></td>
                        </tr>
                        <tr>
                            <td class="text-left" >
                                <div class="attendeeDetail">
                                <span>_______________________________</span><br/>
                                <span>Signature of attendee</span>
                                </div>
                            </td>
                        
                            <td class="text-left" >
                                <div class="attendeeDetail">
                                <span>___________________________________</span><br/>
                                <span>Date</span>
                                </div>
                            </td>
                        </tr>
                    </table> 
                    <br>
                    <br>
                    <table width="65%" cellpadding="0" cellspacing="1" class="text-left pl30"> 
                        <tr>  
                            <td class="text-left">Acknowledged by:</td> 
                            <td class="text-left"><span>	Jim Wilson, Executive Director <br>Ginny Thomas, CLE Program Manager </span>		
                            </td>
                        </tr>                        
                        <tr>  
                            <td class="text-left" colspan="2">    
                                <p class="text-left">Signature of Sponsor Representative:</p>		
                            </td>
                        </tr> 
                        <tr>  
                            <td class="text-left">
                                <img   class="sign-img"  src="#arguments.strCertMergeCodes.certificate.imagesurl#signature.png">
                                <p class="text-left">Date: #DateFormat(arguments.strCertMergeCodes.event.qryEventTimes.endTime,"mm/dd/yyyy")#</p>		
                            </td>
                        </tr>                   
                    </table				
				</body>
			</html> 
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.data>
	</cffunction>
</cfcomponent>
