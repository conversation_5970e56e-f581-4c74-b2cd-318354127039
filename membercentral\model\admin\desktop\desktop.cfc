<cfcomponent output="no">

	<cffunction name="getSites" access="public" output="false" returntype="query">
		<cfset var qrySites = "">
	
		<cfquery name="qrySites" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select s.siteID, s.siteCode, s.siteName
			from dbo.sites as s
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			order by s.siteCode

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qrySites>
	</cffunction>

	<cffunction name="getOrganizations" access="public" output="false" returntype="query">
		<cfset var qryOrgs = "">

		<cfquery name="qryOrgs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct o.orgID, o.orgCode, oi.organizationName as orgName
			from dbo.organizations o
			inner join dbo.sites s on s.orgID = o.orgID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			order by o.orgCode, oi.organizationName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryOrgs>
	</cffunction>

	<cffunction name="getPermissionsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="operationMode" type="string" required="true">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var local = structNew()>

		<cfif arguments.operationMode eq 'grid'>
			<cfset local.orderDir = arguments.event.getValue('orderDir')>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"srt.resourceType #local.orderDir#, srf.functionName #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"srf.functionName #local.orderDir#, srtf.displayName #local.orderDir#")>
			<cfset local.orderby = local.arrCols[arguments.event.getValue('orderBy')+1]>
		</cfif>

		<cfquery name="local.qryPermissions" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @totalCount INT;

				IF OBJECT_ID('tempdb..##tblPermissionsSearch') IS NOT NULL
					DROP TABLE ##tblPermissionsSearch;

				CREATE TABLE ##tblPermissionsSearch (resourceTypeID INT, applicationTypeID INT, toolTypeID INT, resourceTypeFunctionID INT);

				INSERT INTO ##tblPermissionsSearch (resourceTypeID, applicationTypeID, toolTypeID, resourceTypeFunctionID)
				SELECT DISTINCT srt.resourceTypeID, apt.applicationTypeID, toolTypeID, srtf.resourceTypeFunctionID
				FROM dbo.cms_siteResourceTypeClasses srtc
				INNER JOIN dbo.cms_siteResourceTypes srt ON srtc.resourceTypeClassID = srt.resourceTypeClassID
					<cfif arguments.event.getTrimValue('fResourceType','') NEQ ''>
						AND srt.resourceTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fResourceType')#" list="true">)
					</cfif>
				INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.resourceTypeID = srt.resourceTypeID
				INNER JOIN dbo.cms_siteResourceFunctions srf ON srf.functionID = srtf.functionID
				<cfif arguments.event.getTrimValue('fRole','') NEQ ''>
					INNER JOIN dbo.cms_siteResourceRoleFunctions AS srrf ON srrf.resourceTypeFunctionID = srtf.resourceTypeFunctionID
						AND srrf.roleID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fRole')#" list="true">)
				</cfif>
				LEFT OUTER JOIN dbo.cms_applicationTypes apt ON apt.resourceTypeID = srt.resourceTypeID
				LEFT OUTER JOIN dbo.admin_toolTypes tt ON tt.resourceTypeID = srt.resourceTypeID
				WHERE 1 = 1
				<cfif arguments.event.getTrimValue('fToolType','') NEQ ''>
					AND tt.toolTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fToolType')#" list="true">)
				</cfif>
				<cfif arguments.event.getTrimValue('fApplicationType','') NEQ ''>
					AND apt.applicationTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fApplicationType')#" list="true">)
				</cfif>;

				<cfif arguments.operationMode EQ "grid">
					IF OBJECT_ID('tempdb..##tblPermissions') IS NOT NULL
						DROP TABLE ##tblPermissions;
					CREATE TABLE ##tblPermissions (resourceTypeClassName VARCHAR(100), resourceType VARCHAR(100), functionName VARCHAR(100),
						displayName VARCHAR(100), applicationType VARCHAR(100), toolCFC VARCHAR(100), rolesList VARCHAR(MAX),row INT);

					INSERT INTO ##tblPermissions (resourceTypeClassName, resourceType, functionName, displayName, applicationType, toolCFC, rolesList, row)
					SELECT DISTINCT srtc.resourceTypeClassName, srt.resourceType, srf.functionName, srtf.displayName,
					CASE WHEN LEN(apt.applicationTypeName) > 0 THEN apt.applicationTypeName ELSE ISNULL(tt.toolType,'') END applicationType, ISNULL(tt.toolCFC,'') AS toolCFC,
					rolesList = ISNULL(STUFF((
								SELECT ',' + srroles.roleName
								FROM dbo.cms_siteResourceRoles AS srroles
								INNER JOIN dbo.cms_siteResourceRoleFunctions AS srrf ON srroles.roleID = srrf.roleID AND srroles.roleTypeID = 2
								WHERE srrf.resourceTypeFunctionID = srtf.resourceTypeFunctionID
								FOR XML PATH ('')
						),1,1,''),''),
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
					FROM ##tblPermissionsSearch as tmp
					INNER JOIN dbo.cms_siteResourceTypes srt ON srt.resourceTypeID = tmp.resourceTypeID
					INNER JOIN dbo.cms_siteResourceTypeClasses srtc ON srtc.resourceTypeClassID = srt.resourceTypeClassID
					INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.resourceTypeFunctionID = tmp.resourceTypeFunctionID
					INNER JOIN dbo.cms_siteResourceFunctions srf ON srf.functionID = srtf.functionID
					LEFT OUTER JOIN dbo.cms_applicationTypes apt ON apt.applicationTypeID = tmp.applicationTypeID
					LEFT OUTER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = tmp.toolTypeID;

					SELECT @totalCount = @@ROWCOUNT;
				
					DECLARE @posStart INT, @posStartAndCount INT;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					SELECT resourceTypeClassName, resourceType, functionName, displayName, applicationType, toolCFC, rolesList, row, @totalCount as totalCount
					FROM ##tblPermissions
					WHERE row > @posStart
					AND row <= @posStartAndCount
					ORDER BY row;

					IF OBJECT_ID('tempdb..##tblPermissions') IS NOT NULL
						DROP TABLE ##tblPermissions;
				<cfelseif arguments.operationMode EQ "export">
					DECLARE @roleList VARCHAR(MAX), @roleListWithNullCheck VARCHAR(MAX), @permissionssql VARCHAR(MAX), @selectsql VARCHAR(MAX);

					IF OBJECT_ID('tempdb..####tblPermissions') IS NOT NULL
						DROP TABLE ####tblPermissions;

					SELECT	@roleList = COALESCE(@roleList + ',', '') + QUOTENAME(roleName), 
							@roleListWithNullCheck = COALESCE(@roleListWithNullCheck + ',', '') + 'ISNULL(results.'+ QUOTENAME(roleName) +',0) AS ' + QUOTENAME(roleName)
					FROM cms_siteResourceRoles srroles
					INNER JOIN cms_siteResourceRoleTypes srrt ON srrt.roleTypeID = srroles.roleTypeID
						AND srrt.roleTypeName='UniversalRole';

					SET @permissionssql = '	
						SELECT ROW_NUMBER() OVER (ORDER BY results.resourceTypeClassName, results.resourceType, results.functionName, results.displayName) AS rowID,
							results.resourceTypeClassName, results.resourceType, results.functionName, results.displayName,
							' + @roleListWithNullCheck + ',
							ISNULL(apt.applicationTypeName,'''') as applicationTypeName, ISNULL(apt.applicationTypeDesc,'''') AS applicationTypeDesc, 
							ISNULL(tt.toolType,'''') AS toolType, ISNULL(tt.toolDesc,'''') AS toolDesc, ISNULL(tt.toolCFC,'''') AS toolCFC
						INTO ####tblPermissions
						FROM (
							SELECT resourceTypeClassName, resourceTypeID, resourceType, resourceTypeFunctionID, functionName, displayName, ' + @roleList + '
							FROM (
								SELECT srtc.resourceTypeClassName, srt.resourceTypeID, srt.resourceType, srtf.resourceTypeFunctionID, srf.functionName, srtf.displayName, srroles.roleName, 
									CASE WHEN srrf.roleFunctionID IS NULL THEN 0 ELSE 1 END AS assigned
								FROM dbo.cms_siteResourceTypeClasses srtc 
								INNER JOIN dbo.cms_siteResourceTypes srt ON srtc.resourceTypeClassID = srt.resourceTypeClassID
								INNER JOIN dbo.cms_siteResourceTypeFunctions srtf ON srtf.resourceTypeID = srt.resourceTypeID
								INNER JOIN dbo.cms_siteResourceFunctions srf ON srf.functionID = srtf.functionID
								LEFT OUTER JOIN dbo.cms_siteResourceRoleFunctions srrf
									INNER JOIN dbo.cms_siteResourceRoles srroles ON srroles.roleID = srrf.roleID AND srroles.roleTypeID = 2
								ON srrf.resourceTypeFunctionID = srtf.resourceTypeFunctionID
							) AS data
							PIVOT (MIN(data.assigned) FOR data.roleName in (' + @roleList + ')) AS pvt
						) AS results
						INNER JOIN ##tblPermissionsSearch AS tmp ON tmp.resourceTypeID = results.resourceTypeID
							AND tmp.resourceTypeFunctionID = results.resourceTypeFunctionID
						LEFT OUTER JOIN dbo.cms_applicationTypes apt ON apt.applicationTypeID = tmp.applicationTypeID
						LEFT OUTER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = tmp.toolTypeID;
					';

					EXEC(@permissionssql);

					SET @selectsql = 'SELECT resourceTypeClassName, resourceType, functionName, displayName, ' + @roleList + ',
						applicationTypeName, applicationTypeDesc, toolType, toolDesc, toolCFC, rowID AS mcCSVorder
						*FROM* ####tblPermissions';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.reportFileName#', @returnColumns=1;
				
					IF OBJECT_ID('tempdb..####tblPermissions') IS NOT NULL
						DROP TABLE ####tblPermissions;
				</cfif>

				IF OBJECT_ID('tempdb..##tblPermissionsSearch') IS NOT NULL
					DROP TABLE ##tblPermissionsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryPermissions>
	</cffunction>

	<cffunction name="getResourceTypes" access="public" output="No" returntype="query">
		<cfset var qryResourceTypes = "">
	
		<cfquery name="qryResourceTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT resourceTypeID, resourceType
			FROM dbo.cms_siteResourceTypes
			ORDER BY resourceType;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryResourceTypes>
	</cffunction>

	<cffunction name="getRoles" access="public" output="No" returntype="query">
		<cfset var qryRoles = "">
	
		<cfquery name="qryRoles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT r.roleID, r.roleName
			FROM dbo.cms_siteResourceRoles r
			INNER JOIN dbo.cms_siteResourceRoleTypes rt ON rt.roleTypeID = r.roleTypeID
				AND rt.roleTypeName='UniversalRole'
			ORDER BY r.roleName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryRoles>
	</cffunction>

	<cffunction name="getToolTypes" access="public" output="No" returntype="query">
		<cfset var qryToolTypes = "">
	
		<cfquery name="qryToolTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT toolTypeID, toolType
			FROM dbo.admin_toolTypes
			ORDER BY toolType;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryToolTypes>
	</cffunction>

	<cffunction name="getApplicationTypes" returntype="query" access="public" output="No">
		<cfset var qryApplicationTypes = "">
	
		<cfquery name="qryApplicationTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT applicationTypeID, applicationTypeName
			FROM dbo.cms_applicationTypes
			ORDER BY applicationTypeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryApplicationTypes>
	</cffunction>

	<cffunction name="filterSiteMapStats" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="siteIDList" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		<cfargument name="count" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		
		<cfif arguments.mcproxy_siteCode neq "MC">
			<cfset arguments.siteIDList = arguments.mcproxy_siteID>
		</cfif>

		<cfquery name="local.strReturn.arrData" datasource="#application.dsn.platformstatsMC.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @count int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
			IF @count not in (25,50,100)
				SET @count = 25;

			SELECT TOP (@count) s.siteid, s.sitecode, s.sitename, count(sl.logID) as numruns, avg(sl.timeMS) as avgms, 
				sum(sl.timeMS) as summs, max(sl.startDate) as lastrun
			FROM dbo.site_sitemapLog as sl
			INNER JOIN memberCentral.dbo.sites as s on s.siteID = sl.siteID
			inner join memberCentral.dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			WHERE 1 = 1
			<cfif listLen(arguments.siteIDList)>
				and sl.siteID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.siteIDList#">)
			</cfif>
			<cfif len(trim(arguments.dateFrom))>
				and sl.startDate >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(trim(arguments.dateTo))>
				and sl.startDate <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
			</cfif>
			GROUP BY s.siteID, s.siteCode, s.siteName
			<cfswitch expression="#arguments.orderBy#">
				<cfcase value="site">
					ORDER BY sitecode asc, sitename asc
				</cfcase>
				<cfcase value="avg">
					ORDER BY avgms desc
				</cfcase>
				<cfcase value="sum">
					ORDER BY summs desc
				</cfcase>
				<cfcase value="numruns">
					ORDER BY numruns desc
				</cfcase>
				<cfcase value="lastrun">
					ORDER BY lastrun desc
				</cfcase>
				<cfdefaultcase>
					ORDER BY avgms desc
				</cfdefaultcase>
			</cfswitch>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strReturn['arrdata'].each(function(thisRow) {
			arguments.thisRow.lastrun = dateTimeformat(arguments.thisRow.lastrun,"mmm d - h:nn tt");
		})>
		
		<cfset local.strReturn.success = true>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getMerchantProfilesListFromFilters" access="public" output="false" returntype="array">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var local = structNew()>
		<cfset local.fSiteID = arguments.event.getValue('fSiteID',0)>
		<cfset local.fenableMCPay = arguments.event.getValue('fenableMCPay',0)>
		<cfset local.fenablePFD = arguments.event.getValue('fenablePFD',0)>
		<cfset local.fenableSurcharge = arguments.event.getValue('fenableSurcharge',0)>
		<cfset local.fPaymentTypes = arguments.event.getValue('fPaymentTypes','')>
		<cfset local.fDateFrom = arguments.event.getValue('fDateFrom')>
		<cfset local.fDateTo = arguments.event.getValue('fDateTo')>
		<cfset arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))))>
		<cfset arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))))>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfquery name="local.arrMerchantProfiles" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
				IF OBJECT_ID('tempdb..##tmpMerchantProfileList') IS NOT NULL
					DROP TABLE ##tmpMerchantProfileList;
				IF OBJECT_ID('tempdb..##tmpMPCounts') IS NOT NULL
					DROP TABLE ##tmpMPCounts;
				IF OBJECT_ID('tempdb..##tmpMPFCounts') IS NOT NULL
					DROP TABLE ##tmpMPFCounts;
				CREATE TABLE ##tmpMerchantProfileList (profileID int PRIMARY KEY, profileName varchar(100), profileCode varchar(20), status char(1),
					allowPayments bit, allowPayInvoicesOnline bit, allowRefunds bit, maxFailedAutoAttempts int, 
					daysBetweenAutoAttempts int, minDaysFailedCleanup int, enableProcessingFeeDonation bit, processFeeDonationFeePercent decimal(5,2), 
					enableApplePay bit , enableGooglePay bit, enableMCPay bit, enableAmericanExpress bit, 
					enableDiscover bit, enableMasterCard bit, enableVISA bit, enableSurcharge bit, surchargePercent decimal (5,2),
					siteCode varchar(10), siteName varchar(60), gatewayID int, row int);
				CREATE TABLE ##tmpMPCounts (profileID int PRIMARY KEY, SumPayments decimal(18,2), CountPayments int, SumRefunds decimal(18,2), CountRefunds int);
				CREATE TABLE ##tmpMPFCounts (profileID int PRIMARY KEY, CountFailedPayments int);

				DECLARE @authorizeGatewayID int, @totalCount int, @posStart int, @posStartAndCount int, @dateFrom datetime, @dateTo datetime;
				SET @authorizeGatewayID = dbo.fn_mp_getGatewayID('AuthorizeCCCIM');
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				SET @dateFrom = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fDateFrom#">;
				SET @dateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fDateTo# 23:59:59.997">;

				INSERT INTO ##tmpMerchantProfileList (profileID, profileName, profileCode, status, allowPayments, allowPayInvoicesOnline, allowRefunds, 
					maxFailedAutoAttempts, daysBetweenAutoAttempts, minDaysFailedCleanup, enableProcessingFeeDonation, 
					processFeeDonationFeePercent, enableApplePay, enableGooglePay, enableMCPay, enableAmericanExpress, 
					enableDiscover, enableMasterCard, enableVISA, enableSurcharge, surchargePercent, siteCode, siteName, row)
				SELECT mp.profileID, mp.profileName, mp.profileCode, mp.status, mp.allowPayments, mp.allowPayInvoicesOnline, mp.allowRefunds, 
					mp.maxFailedAutoAttempts, mp.daysBetweenAutoAttempts, mp.minDaysFailedCleanup, 
					mp.enableProcessingFeeDonation, mp.processFeeDonationFeePercent, mp.enableApplePay,
					mp.enableGooglePay, mp.enableMCPay,
					CASE WHEN EXISTS(
						SELECT 1
						FROM dbo.mp_profileCardTypes mpct
						INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
						WHERE ct.cardType = 'American Express' 
						AND mpct.profileID = mp.profileID) THEN 1 ELSE 0 END as enableAmericanExpress,
					CASE WHEN EXISTS(
						SELECT 1
						FROM dbo.mp_profileCardTypes mpct
						INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
						WHERE ct.cardType = 'Discover' 
						AND mpct.profileID = mp.profileID) THEN 1 ELSE 0 END as enableDiscover,
					CASE WHEN EXISTS(
						SELECT 1
						FROM dbo.mp_profileCardTypes mpct
						INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
						WHERE ct.cardType = 'MasterCard' 
						AND mpct.profileID = mp.profileID) THEN 1 ELSE 0 END as enableMasterCard,
					CASE WHEN EXISTS(
						SELECT 1
						FROM dbo.mp_profileCardTypes mpct
						INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
						WHERE ct.cardType = 'VISA' 
						AND mpct.profileID = mp.profileID) THEN 1 ELSE 0 END as enableVISA,
					mp.enableSurcharge, mp.surchargePercent,
					s.siteCode, s.siteName, ROW_NUMBER() OVER (ORDER BY s.siteCode, mp.profileName)
				FROM dbo.mp_profiles AS mp
				INNER JOIN dbo.sites s ON mp.siteID = s.siteID
				where mp.gatewayID = @authorizeGatewayID
				AND mp.status IN ('A','I')
				<cfif local.fSiteID gt 0>
					AND mp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fSiteID#">
				</cfif>
				<cfif local.fenableMCPay EQ 1>
					AND mp.enableMCPay = 1
				</cfif>
				<cfif local.fenablePFD EQ 1>
					AND mp.enableProcessingFeeDonation = 1
				</cfif>
				<cfif local.fenableSurcharge EQ 1>
					AND mp.enableSurcharge = 1
				</cfif>
				<cfif ListLen(local.fPaymentTypes)>
					AND (
					<cfloop list="#local.fPaymentTypes#" item="local.paymentType" index="local.paymentIndex">
						<cfif local.paymentType EQ 'A'>
							EXISTS (
								SELECT 1
								FROM dbo.mp_profileCardTypes mpct
								INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
								WHERE ct.cardType = 'American Express' 
								AND mpct.profileID = mp.profileID
							)
						</cfif>
						<cfif local.paymentType EQ 'D'>
							EXISTS (
								SELECT 1
								FROM dbo.mp_profileCardTypes mpct
								INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
								WHERE ct.cardType = 'Discover' 
								AND mpct.profileID = mp.profileID
							)
						</cfif>
						<cfif local.paymentType EQ 'M'>
							EXISTS (
								SELECT 1
								FROM dbo.mp_profileCardTypes mpct
								INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
								WHERE ct.cardType = 'MasterCard' 
								AND mpct.profileID = mp.profileID
							)
						</cfif>
						<cfif local.paymentType EQ 'V'>
							EXISTS (
								SELECT 1
								FROM dbo.mp_profileCardTypes mpct
								INNER JOIN dbo.mp_cardTypes ct ON mpct.cardTypeID = ct.cardTypeID
								WHERE ct.cardType = 'VISA' 
								AND mpct.profileID = mp.profileID
							)
						</cfif>
						<cfif local.paymentType EQ 'AP'>
							mp.enableApplePay = 1
						</cfif>
						<cfif local.paymentType EQ 'GP'>
							mp.enableGooglePay = 1
						</cfif>
						<cfif local.paymentIndex neq listLen(local.fPaymentTypes)>
							OR
						</cfif>
					</cfloop>
					)
				</cfif>
				;			
				<cfif arguments.mode EQ "grid">
					
					SET @totalCount = @@ROWCOUNT;

					DELETE FROM ##tmpMerchantProfileList
					WHERE row <= @posStart 
					OR row >@posStartAndCount;
				</cfif>

				INSERT INTO ##tmpMPCounts (profileID, SumPayments, CountPayments, SumRefunds, CountRefunds)
				select tmp.profileID, 
					sum(case when t.typeid = 2 then t.amount else 0 end) as SumPayments,
					sum(case when t.typeid = 2 then 1 else 0 end) as CountPayments,
					sum(case when t.typeid = 4 then t.amount*-1 else 0 end) as SumRefunds,
					sum(case when t.typeid = 4 then 1 else 0 end) as CountRefunds
				from ##tmpMerchantProfileList as tmp
				inner join dbo.tr_transactionPayments as tp on tp.profileID = tmp.profileID
				inner join dbo.tr_transactions as t on t.transactionID = tp.transactionID
					and t.typeID in (2,4)
					and t.statusID = 1
					and t.dateRecorded between @dateFrom and @dateTo
				group by tmp.profileID;

				INSERT INTO ##tmpMPFCounts (profileID, CountFailedPayments)
				select tmp.profileID, count(ph.historyID) as CountFailedPayments
				from ##tmpMerchantProfileList as tmp
				inner join dbo.tr_paymentHistory as ph on ph.profileID = tmp.profileID
					and ph.isSuccess = 0
					and ph.paymentType = 'payment'
					and ph.datePaid between @dateFrom and @dateTo
				group by tmp.profileID;

				<cfif arguments.mode EQ "grid">
					SELECT tmp.profileID, tmp.profileName, tmp.profileCode, tmp.status, 
						CASE WHEN tmp.allowPayments = 1 THEN 'Yes' ELSE 'No' END as allowPayments, 
						CASE WHEN tmp.allowPayInvoicesOnline = 1 THEN 'Yes' ELSE 'No' END allowPayInvoicesOnline,
						CASE WHEN tmp.allowRefunds = 1 THEN 'Yes' ELSE 'No' END as allowRefunds, 
						ISNULL(tmp.maxFailedAutoAttempts,0) as maxFailedAutoAttempts,
						ISNULL(tmp.daysBetweenAutoAttempts,0) as daysBetweenAutoAttempts, 
						tmp.minDaysFailedCleanup,
						CASE WHEN tmp.enableProcessingFeeDonation = 1 THEN 'Yes' ELSE 'No' END as enableProcessingFeeDonation, 
						tmp.processFeeDonationFeePercent, 
						CASE WHEN tmp.enableSurcharge = 1 THEN 'Yes' ELSE 'No' END as enableSurcharge, 
						tmp.surchargePercent, 
						CASE WHEN tmp.enableMCPay = 1 THEN 'Yes' ELSE 'No' END as enableMCPay,
						tmp.enableApplePay, tmp.enableGooglePay, tmp.enableAmericanExpress, tmp.enableDiscover, 
						tmp.enableMasterCard, tmp.enableVISA, tmp.siteCode, tmp.siteName, @totalCount AS totalCount, 
						ISNULL(tmpMPC.sumPayments,0) as SumPayments, ISNULL(tmpMPC.countPayments,0) as CountPayments,
						ISNULL(tmpMPC.sumRefunds,0) as SumRefunds, ISNULL(tmpMPC.countRefunds,0) as CountRefunds,
						ISNULL(tmpMPFC.countFailedPayments,0) as CountFailedPayments,
						'paymentProfileRow_' + CAST(tmp.profileID AS varchar(10)) AS DT_RowId
					FROM ##tmpMerchantProfileList as tmp
					LEFT OUTER JOIN ##tmpMPCounts as tmpMPC on tmpMPC.profileID = tmp.profileID
					LEFT OUTER JOIN ##tmpMPFCounts as tmpMPFC on tmpMPFC.profileID = tmp.profileID
					ORDER BY tmp.row;
				<cfelseif arguments.mode EQ "export">
					DECLARE  @selectsql VARCHAR(MAX);
					SET @selectsql = 'SELECT tmp.profileID, tmp.siteCode, tmp.siteName, tmp.profileName, tmp.profileCode, tmp.status, 
						CASE WHEN tmp.allowPayments = 1 THEN ''Yes'' ELSE ''No'' END as allowPayments, 
						CASE WHEN tmp.allowPayInvoicesOnline = 1 THEN ''Yes'' ELSE ''No'' END allowPayInvoicesOnline,
						CASE WHEN tmp.allowRefunds = 1 THEN ''Yes'' ELSE ''No'' END as allowRefunds, 
						ISNULL(tmp.maxFailedAutoAttempts,0) as maxFailedAutoAttempts,
						ISNULL(tmp.daysBetweenAutoAttempts,0) as daysBetweenAutoAttempts, 
						tmp.minDaysFailedCleanup,
						CASE WHEN tmp.enableMCPay = 1 THEN ''Yes'' ELSE ''No'' END as enableMCPay,
						CASE WHEN tmp.enableProcessingFeeDonation = 1 THEN ''Yes'' ELSE ''No'' END as enableProcessingFeeDonation, 
						tmp.processFeeDonationFeePercent, 
						CASE WHEN tmp.enableSurcharge = 1 THEN ''Yes'' ELSE ''No'' END as enableSurcharge, 
						tmp.surchargePercent, tmp.enableVISA, tmp.enableMasterCard, tmp.enableAmericanExpress, tmp.enableDiscover, 
						tmp.enableApplePay, tmp.enableGooglePay, 
						ISNULL(tmpMPC.sumPayments,0) as SumPayments, 
						ISNULL(tmpMPC.countPayments,0) as CountPayments,
						ISNULL(tmpMPFC.countFailedPayments,0) as CountFailedPayments, 
						ISNULL(tmpMPC.sumRefunds,0) as SumRefunds, 
						ISNULL(tmpMPC.countRefunds,0) as CountRefunds,
						ROW_NUMBER() OVER(ORDER BY tmp.row) as mcCSVorder 
					*FROM* ##tmpMerchantProfileList as tmp
					LEFT OUTER JOIN ##tmpMPCounts as tmpMPC on tmpMPC.profileID = tmp.profileID
					LEFT OUTER JOIN ##tmpMPFCounts as tmpMPFC on tmpMPFC.profileID = tmp.profileID';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.reportFileName#', @returnColumns=1;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpMerchantProfileList') IS NOT NULL
					DROP TABLE ##tmpMerchantProfileList;
				IF OBJECT_ID('tempdb..##tmpMPCounts') IS NOT NULL
					DROP TABLE ##tmpMPCounts;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.arrMerchantProfiles>
	</cffunction>

</cfcomponent>