ALTER PROC dbo.email_reorderConsentLists
@consentListTypeID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (newOrderNum int NOT NULL, consentListID int NOT NULL, orderNum int NOT NULL);

	INSERT INTO @tmp (consentListID, orderNum, newOrderNum)
	SELECT consentListID, orderNum, ROW_NUMBER() OVER(ORDER BY orderNum) as newOrderNum
	FROM dbo.email_consentLists
	WHERE consentListTypeID = @consentListTypeID
	AND [status] = 'A';

	UPDATE rg
	SET rg.orderNum = t.newOrderNum
	FROM dbo.email_consentLists as rg 
	INNER JOIN @tmp as t on rg.consentListID = t.consentListID
	WHERE rg.[status] = 'A';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHand<PERSON> @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
