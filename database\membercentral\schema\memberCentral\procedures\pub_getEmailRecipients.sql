ALTER PROC dbo.pub_getEmailRecipients
@publicationID INT,
@mode TINYINT,
@emailTypeID INT,
@receiveFID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	CREATE TABLE #tmpMembers (memberID INT);
	CREATE TABLE #tmpRecipients (memberID INT, memberName VARCHAR(200), memberCompany VARCHAR(200), memberEmail VARCHAR(200), emailTypeID INT);
	CREATE TABLE #tmpFinalRecipients (memberID INT, memberName VARCHAR(200), memberCompany VARCHAR(200), memberEmail VARCHAR(200), emailTypeID INT);

	DECLARE @emailConsentListID INT, @emailConsentListMode VARCHAR(25), @orgID INT, @siteID INT, @globalOptOutListID INT;

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.pub_publications AS p
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE p.publicationID = @publicationID;

	SELECT @emailConsentListID = pub.emailConsentListID, @emailConsentListMode = m.modeName
	FROM dbo.pub_publications AS pub
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListID = pub.emailConsentListID
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes AS m ON m.consentListModeID = cl.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
	WHERE pub.publicationID = @publicationID;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	-- get members with ReceiveEditionEmails perms
	INSERT INTO #tmpMembers (memberID)
	SELECT DISTINCT m.activeMemberID
	FROM dbo.pub_publications AS p
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		and ai.applicationInstanceID = p.applicationInstanceID 
	INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'publications'
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID 
		and sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
		and srfrp.siteResourceID = sr.siteResourceID 
		AND srfrp.functionID = @receiveFID 
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
		and srfrp.rightPrintID = gprp.rightPrintID
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
		and m.groupPrintID = gprp.groupPrintID
	WHERE p.publicationID = @publicationID;

	-- get recipient member details
	INSERT INTO #tmpRecipients (memberID, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT DISTINCT m.memberID, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company, me.email, me.emailTypeID
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.memberID = tmp.memberID
		INNER JOIN dbo.pub_publicationEmailTagTypes as pet on pet.publicationID = @publicationID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
			AND metag.memberID = m.memberID 
			AND pet.emailTagTypeID = metag.emailTagTypeID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
			AND me.memberID = m.memberID
			AND metag.emailTypeID = me.emailTypeID
			AND me.email <> '';

	INSERT INTO #tmpFinalRecipients (memberID, memberName, memberCompany, memberEmail, emailTypeID)
	select memberID, memberName, memberCompany, memberEmail, emailTypeID
	from #tmpRecipients;
	
	IF @emailConsentListMode = 'Opt-Out' BEGIN
		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		INNER JOIN platformMail.dbo.email_consentListMembers AS clm
			ON clm.consentListID IN (@emailConsentListID,@globalOptOutListID)
			AND clm.email = tmp.memberEmail;
	END

	IF @emailConsentListMode = 'Opt-In' BEGIN
		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		LEFT OUTER JOIN platformMail.dbo.email_consentListMembers clm
			ON clm.consentListID = @emailConsentListID
			AND clm.email = tmp.memberEmail
		WHERE clm.consentListMemberID is null;
	END
	
	-- summary counts	 
	IF @mode = 0 BEGIN
		SELECT summaryRow, summaryItem, emailTypeID, summaryItemCount, summaryLinkText = 
			CASE 
			WHEN summaryRow IN (1,2,3,4) AND summaryItemCount = 0 then '0 members'
			WHEN summaryRow IN (1,2,3,4) AND summaryItemCount > 0 then 'view ' + cast(summaryItemCount AS VARCHAR(10)) + ' members'
			WHEN summaryRow IN (5) AND summaryItemCount = 0 then '0 recipients'
			WHEN summaryRow IN (5) AND summaryItemCount > 0 then 'view ' + cast(summaryItemCount AS VARCHAR(10)) + ' recipients'
			END
		FROM (
			SELECT 1 AS summaryRow, 'Members with Receive Email Editions permission' AS summaryItem, 0 AS emailTypeID, COUNT(memberID) AS summaryItemCount
			FROM #tmpMembers
				UNION ALL
			SELECT 2, 'Members using e-mail type: ' + met.emailType, met.emailTypeID, COUNT(tmp.memberid)
			FROM #tmpRecipients AS tmp
			INNER JOIN dbo.ams_memberEmailTypes AS met ON met.emailTypeID = tmp.emailTypeID
			GROUP BY met.emailType, met.emailTypeID
				UNION ALL
			SELECT 3, 'Members with no e-mail address', 0, COUNT(tmp.memberID)
			FROM #tmpMembers AS tmp
			LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
			WHERE tmpR.memberID IS NULL
				UNION ALL
			SELECT 4, 'Members within '+ @emailConsentListMode +' list', 0, COUNT(DISTINCT tmpR.memberID)
			FROM platformMail.dbo.email_consentListMembers AS clm
			INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
			WHERE clm.consentListID = @emailConsentListID OR (@emailConsentListMode = 'Opt-Out' AND clm.consentListID IN (@emailConsentListID,@globalOptOutListID))
				UNION ALL
			SELECT 5, 'Total messages to be sent', 0, COUNT(memberID)
			FROM #tmpFinalRecipients
		) AS outertmp
		ORDER BY summaryRow;

		GOTO on_done;
	END
	
	-- Members with Receive Email Editions permission
	IF @mode = 1 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
		ORDER BY memberName;

		GOTO on_done;
	END	
	
	-- Members using an Email-Type
	IF @mode = 2 BEGIN
		SELECT DISTINCT memberName, memberCompany, memberEmail
		FROM #tmpRecipients AS tmp
		WHERE emailTypeID = @emailTypeID
		ORDER BY memberName;

		GOTO on_done;
	END
	
	-- Members with no e-mail address
	IF @mode = 3 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
		WHERE tmpR.memberID IS NULL
		ORDER BY memberName;

		GOTO on_done;
	END
	
	-- Members within Opt-In/Opt-Out list
	IF @mode = 4 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany, clm.email AS memberEmail
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.ams_members AS m on m.memberID = tmpR.memberID
		WHERE clm.consentListID = @emailConsentListID OR (@emailConsentListMode = 'Opt-Out' AND clm.consentListID IN (@emailConsentListID,@globalOptOutListID))
		ORDER BY memberName;

		GOTO on_done;
	END	
	
	-- Recipient Members
	IF @mode IN (5,6) BEGIN
		SELECT memberID, memberName, memberCompany, memberEmail, emailTypeID
		FROM #tmpFinalRecipients
		ORDER BY memberName, memberCompany, memberEmail;
		
		GOTO on_done;
	END

	on_done:

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN -1;
END CATCH
GO
