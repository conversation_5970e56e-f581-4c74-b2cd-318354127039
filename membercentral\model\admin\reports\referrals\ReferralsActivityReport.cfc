<cfcomponent extends="model.admin.reports.report" output="false">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
			
		// call common report controller
		reportController(event=arguments.event);
		local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
		variables.qryGetReferralSettings =  local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript> 
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfset var local = structNew() />
		
		<cfset local.showForm = Val(arguments.event.getValue('qryReportInfo').reportID) />
		
		<cfif local.showForm>
			
			<cfscript>
				local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
				local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
				local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
				/* Get referralID */
				local.referralID = variables.qryGetReferralSettings.referralID;
				local.allowFeeDiscrepancy = variables.qryGetReferralSettings.allowFeeDiscrepancy;
				
				/* Populate "What is your preferred language?" drop-down */ 
				local.qryGetLanguages = local.objAdminReferrals.getReferralLanguages(referralID=local.referralID);
				/* Populate "Sources" drop-down */ 
				local.qryGetReferralSources = local.objAdminReferrals.getReferralSources(referralID=local.referralID);
				/* Populate "Status" drop-down */ 
				local.qryGetReferralStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.referralID, isAgency=0, isDeleted=0).qryStatus;
				/* Populate "Fee Discrepancy Status" drop-down */ 
				if(local.allowFeeDiscrepancy)
					local.qryGetFeeDiscrepancyStatuses = local.objAdminReferrals.getFeeDiscrepancyStatuses();
				/* Populate "Counselors" drop-down */ 
				local.qryGetCounselors = local.objAdminReferrals.getInterviewers(referralID=local.referralID,orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				/* Populate "Fee Type" drop-down */ 
				local.qryGetFeeTypes = local.objAdminReferrals.getFeeTypes(referralID=local.referralID);

				local.refFieldFilterControls = local.objAdminReferrals.getReferralCustomFieldFilterControlsForReport(siteID=local.siteID);
				
				local.qryReportViews = getReportViews();
				
				local.reportInfoXML = arguments.event.getValue('qryReportInfo').otherXML;
				local.referralDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/referraldatefrom/text())");
				local.referralDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/referraldateto/text())");
				local.callDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/calldatefrom/text())");
				local.callDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/calldateto/text())");
				local.lastUpdatedDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/lastupdateddatefrom/text())");
				local.lastUpdatedDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/lastupdateddateto/text())");
				local.lastUpdatedDateExcludeFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/lastupdateddateexcludefrom/text())");
				local.lastUpdatedDateExcludeTo = XMLSearch(local.reportInfoXML,"string(/report/extra/lastupdateddateexcludeto/text())");				
				local.lastUpdatedDate = XMLSearch(local.reportInfoXML,"string(/report/extra/lastupdateddate/text())");
				local.caseOpenDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/caseopendatefrom/text())");
				local.caseOpenDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/caseopendateto/text())");
				local.caseCloseDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/caseclosedatefrom/text())");
				local.caseCloseDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/caseclosedateto/text())");
				local.retainedCase = XMLSearch(local.reportInfoXML,"string(/report/extra/retainedcase/text())");
				local.clientReferralSourceID = XMLSearch(local.reportInfoXML,"string(/report/extra/clientreferralsourceid/text())");
				local.feeTypeID = XMLSearch(local.reportInfoXML,"string(/report/extra/feetypeid/text())");
				local.clientReferralStatusID = XMLSearch(local.reportInfoXML,"string(/report/extra/clientreferralstatusid/text())");
				if(local.allowFeeDiscrepancy)
					local.feeDiscrepancyStatusID = XMLSearch(local.reportInfoXML,"string(/report/extra/feediscrepancystatusid/text())");
				local.intMemberID = XMLSearch(local.reportInfoXML,"string(/report/extra/intmemberid/text())");
				local.includeFeesData = XMLSearch(local.reportInfoXML,"string(/report/extra/includefeesdata/text())");
				local.referralLanguageID = XMLSearch(local.reportInfoXML,"string(/report/extra/referrallanguageid/text())");
				local.receiveSurveys = XMLSearch(local.reportInfoXML,"string(/report/extra/receivesurveys/text())");
				local.receiveNewsletters = XMLSearch(local.reportInfoXML,"string(/report/extra/receivenewsletters/text())");
				local.attorneyFilterCustomFields = XMLSearch(local.reportInfoXML,"string(/report/extra/attorneyfiltercustomfields/text())");
				local.clientReferralsFilterCustomFields = XMLSearch(local.reportInfoXML,"string(/report/extra/clientreferralsfiltercustomfields/text())");
				local.reportView = XMLSearch(local.reportInfoXML,"string(/report/extra/reportview/text())");
				local.paymentDateFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/paymentdatefrom/text())"); 
				local.paymentDateTo = XMLSearch(local.reportInfoXML,"string(/report/extra/paymentdateto/text())");
				local.strAllFilterCustomFieldValues = {};
				if(len(trim(local.attorneyFilterCustomFields))){
					local.attorneyFilterCustomFields = deserializeJSON(local.attorneyFilterCustomFields);
					StructAppend(local.strAllFilterCustomFieldValues, local.attorneyFilterCustomFields);
				}
				if(len(trim(local.clientReferralsFilterCustomFields))){
					local.clientReferralsFilterCustomFields = deserializeJSON(local.clientReferralsFilterCustomFields);
					StructAppend(local.strAllFilterCustomFieldValues, local.clientReferralsFilterCustomFields);
				}

				local.arrAvailableCustomFieldIDs = arrayNew(1);
				local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.siteID);
				local.arrRefFieldSection = [
					{ "resourceType": "Referrals", "areaName": "Attorney" },
					{ "resourceType": "ClientReferrals", "areaName": "ClientReferrals"}
				];
				for (local.thisSection in local.arrRefFieldSection) {
					local.qryThisCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(
						siteID=local.siteID, resourceType=local.thisSection.resourceType, areaName=local.thisSection.areaName,
						csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0);
					local.thisCustomFieldsXML = xmlParse(local.qryThisCustomFieldsXML.returnXML).xmlRoot;

					for (local.thisfield in local.thisCustomFieldsXML.xmlChildren) {
						arrayAppend(local.arrAvailableCustomFieldIDs, local.thisfield.xmlattributes.fieldID);
					}
				}

				/* for the referral dates, we want to default from one month prior the first time the page is loaded (before the extra node is saved to the report) */
				if (arrayLen(XMLSearch(local.reportInfoXML, "/report/extra/referraldatefrom")) eq 0)
					local.referralDateFrom = dateFormat(dateAdd("m", -1, now()), "m/d/yyyy");
				if (arrayLen(XMLSearch(local.reportInfoXML, "/report/extra/referraldateto")) eq 0)
					local.referralDateTo = dateFormat(now(), "m/d/yyyy");
				
				// remember which radio buttons were selected
				local.receiveSurveysCheckedBlank = false;
				local.receiveSurveysChecked1 = false;
				local.receiveSurveysChecked0 = false;
				if (local.receiveSurveys EQ 0) {
					local.receiveSurveysChecked0 = true;
				} else if (local.receiveSurveys EQ 1) {
					local.receiveSurveysChecked1 = true;
				} else {
					local.receiveSurveysCheckedBlank = true;
				}
				
				local.receiveNewslettersCheckedBlank = false;
				local.receiveNewslettersChecked1 = false;
				local.receiveNewslettersChecked0 = false;
				if (local.receiveNewsletters EQ 0) {
					local.receiveNewslettersChecked0 = true;
				} else if (local.receiveNewsletters EQ 1) {
					local.receiveNewslettersChecked1 = true;
				} else {
					local.receiveNewslettersCheckedBlank = true;
				}
			</cfscript>			
		</cfif>

		<cfsavecontent variable="local.dataHead">
			<cfoutput>
				<script language="javascript">
					function forceDateRange() {
						var fgResult = true;
						var s1 = $('##referralDateFrom').val();
						var e1 = $('##referralDateTo').val();
						var s2 = $('##callDateFrom').val();
						var e2 = $('##callDateTo').val();
						var hasValidRange1 = s1 && e1;
						var hasValidRange2 = s2 && e2;					

						if (!hasValidRange1 && !hasValidRange2) {
							rptShowAlert("Either Referral Date or Call Date ranges must be provided.");
							fgResult=false; 
						}

						/* Each entered start date must have an end date, and vice versa*/
						if (!isCompleteRange(s1, e1)) {
							rptShowAlert("Referral Date From and To must be provided together.");
							fgResult=false; 
						}
						if (!isCompleteRange(s2, e2)) {
							rptShowAlert("Call Date From and To must be provided together.");
							fgResult=false; 
						}

						if (hasValidRange1 && !isWithinOneYear(s1, e1)) {
							rptShowAlert("Referral Date Range cannot exceed 1 year.");
							fgResult=false; 
						}
						if (hasValidRange2 && !isWithinOneYear(s2, e2)) {
							rptShowAlert("Call Date Range cannot exceed 1 year.");
							fgResult=false; 
						}		
						return fgResult;
					}
					$(document).ready(function() {
						$('.tbody_ovfsimg').hide();
						
						var reportView = $('##reportView');
						var fieldsetChooser = $('div##stepFieldsetsDIVfieldsets');
						var fileTypeButtons = $('button##btnReportBarscreen, button##btnReportBarpdf');
						var loadingMessage = $('##divReportShowScreenLoading');
						var loadingScreen = $('##divReportShowScreen');
						
						var caseCheckbox = $('##retainedCase');
						var retainedCaseFields = $('.retainedCaseFields');
						var includeFeesCheckbox = $('##includeFeesData');
						var includeFeesFields = $('.includeFeesFields');
						
						setupRptFilterDateRange('referralDateFrom','referralDateTo');
						setupRptFilterDateRange('callDateFrom','callDateTo');
						setupRptFilterDateRange('lastUpdatedDateFrom','lastUpdatedDateTo');
						setupRptFilterDateRange('lastUpdatedDateExcludeFrom','lastUpdatedDateExcludeTo');
						setupRptFilterDateRange('caseOpenDateFrom','caseOpenDateTo');
						setupRptFilterDateRange('caseCloseDateFrom','caseCloseDateTo');
						setupRptFilterDateRange('paymentDateFrom','paymentDateTo');
						mca_setupCalendarIcons('frmReport');
						mca_setupSelect2();
						
						/* show/hide based on if its checked already upon page load */
						if (caseCheckbox.is(":checked")) {
							retainedCaseFields.show();
						} else {
							$("##caseOpenDateFrom").val("");
							$("##caseOpenDateTo").val("");
							$("##caseCloseDateFrom").val("");
							$("##caseCloseDateTo").val("");
							retainedCaseFields.hide(); 
						};
						
						caseCheckbox.click(function() {
							retainedCaseFields.toggle();
						});
					
						if (includeFeesCheckbox.is(":checked")) {
							includeFeesFields.show();
						} else {
							includeFeesFields.hide();
						};
						
						includeFeesCheckbox.click(function() {
							includeFeesFields.toggle();
						});

						$('input[type=radio][name=lastUpdatedDate]').change(function() {
							$("##lastUpdatedDateFrom").val("");
							$("##lastUpdatedDateTo").val("");
							$("##lastUpdatedDateExcludeFrom").val("");
							$("##lastUpdatedDateExcludeTo").val("");							
						});						
						
						/* toggle based on value of reportView selection */
						reportView.toggleFieldsetsAndLoading( fieldsetChooser, loadingMessage, loadingScreen );
						reportView.toggleFileTypeButtons( fileTypeButtons );
						
						reportView.change(function() {
							reportView.toggleFieldsetsAndLoading( fieldsetChooser, loadingMessage, loadingScreen );
							reportView.toggleFileTypeButtons( fileTypeButtons );
							if (reportView.val() == 'raw') $('.tbody_ovfsmn,.tbody_ovfsmc').hide(); 
							else $('.tbody_ovfsmn,.tbody_ovfsmc').show(); 
						});					
					});
					<cfif local.showForm>
						function mccf_changeExpression(el) {
							if (el.attr('id')) {
								var expElementID = el.attr('id');
								var targetElementID = expElementID.replace('_exp', '');;
								var sel = el.val();
								if (sel == 'exists' || sel == 'not_exists' || sel == ''){
									$('##' + targetElementID).val('').hide();
									$('##' + targetElementID).find('input').each(function(key){
										$(this).val('').hide();
									});
								}
								else{ 
									$('##' + targetElementID).show();
									$('##' + targetElementID).find('input').each(function(key){
										$(this).show();
									});
								}
							}
						}
						function prefillData() {
							var objPrefill = new Object();
							<cfloop collection="#local.strAllFilterCustomFieldValues#" item="local.thisKey">
								<cfif arrayFind(local.arrAvailableCustomFieldIDs, getToken(local.thisKey,2,"_"))>
									#toScript(local.strAllFilterCustomFieldValues[local.thisKey],"objPrefill.#local.thisKey#")#
								</cfif>
							</cfloop>

							for (var key in objPrefill) {
								$('##'+key).val(objPrefill[key]);
								mccf_changeExpression($('##'+key));
							}
						}
						$(document).ready(function() {
							prefillData();
						});
						function clearLastUpdatedOptions(){
							$("##lastUpdatedDateFrom").val("");
							$("##lastUpdatedDateTo").val("");
							$("##lastUpdatedDateExcludeFrom").val("");
							$("##lastUpdatedDateExcludeTo").val("");
							$('input[name="lastUpdatedDate"]').prop('checked', false);
						}
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div id="reportDefs">
					#showCommonTop(event=arguments.event)# 
					<cfif local.showForm>
						<cfform name="frmReport" id="frmReport" method="post">
							<input type="hidden" name="reportAction" id="reportAction" value="" />
							#showStepReferralPanelCriteria(event=arguments.event, title="Optionally Define Panel Filter", desc="Optionally filter the panels appearing on this report using the defined criteria below.<br>")#
							#showStepMemberCriteria(event=arguments.event, title="Optionally Define Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")# 
							
							<div class="mb-5 stepDIV">
								<h5>Define Extra Options</h5>
								<div class="row mt-2">
									<div class="col-sm-12">
										<div class="form-group row">
											<label for="referralDateFrom" class="col-md-4 col-sm-12 col-form-label">Referral Date between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="referralDateFrom" id="referralDateFrom" value="#local.referralDateFrom#" mcrdtxt="Referral Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="referralDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 px-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="referralDateTo" id="referralDateTo" value="#local.referralDateTo#" mcrdtxt="Referral End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="referralDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto pl-md-2">
														<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('referralDateFrom','referralDateTo');">clear</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="callDateFrom" class="col-md-4 col-sm-12 col-form-label">Call Date between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="callDateFrom" id="callDateFrom" value="#local.callDateFrom#" mcrdtxt="Call Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="callDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 px-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="callDateTo" id="callDateTo" value="#local.callDateTo#" mcrdtxt="Call Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="callDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto pl-md-2">
														<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('callDateFrom','callDateTo');">clear</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="receiveSurveys" class="col-md-4 col-sm-12 col-form-label">Last Updated</label>
											<div class="col-md-8 col-sm-12">
												<div class="form-check">
													<input class="form-check-input" type="radio" name="lastUpdatedDate" id="lastUpdatedDateFromOpt" value="include" <cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "include">checked</cfif>>
													<label class="form-check-label" for="lastUpdatedDateFromOpt">Include between</label>
													<div class="col-md-8 col-sm-12">
														<div class="row">
															<div class="col-md col-sm-12 pr-md-0">
																<div class="input-group input-group-sm">
																	<input type="text" name="lastUpdatedDateFrom" id="lastUpdatedDateFrom" value="#local.lastUpdatedDateFrom#" mcrdtxt="Last Updated Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
																	<div class="input-group-append">
																		<span class="input-group-text cursor-pointer calendar-button" data-target="lastUpdatedDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	</div>
																</div>
															</div>
															<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
															<div class="col-md col-sm-12 px-md-0">
																<div class="input-group input-group-sm">
																	<input type="text" name="lastUpdatedDateTo" id="lastUpdatedDateTo" value="#local.lastUpdatedDateTo#" mcrdtxt="Last Updated End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
																	<div class="input-group-append">
																		<span class="input-group-text cursor-pointer calendar-button" data-target="lastUpdatedDateTo"><i class="fa-solid fa-calendar"></i></span>
																	</div>
																</div>
															</div>
															<div class="col-md-auto pl-md-2">
																<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('lastUpdatedDateFrom','lastUpdatedDateTo');">clear</button>
															</div>
														</div>
													</div>													
												</div>
												<div class="form-check">
													<input class="form-check-input" type="radio" name="lastUpdatedDate" id="lastUpdatedDateExcludeFromOpt" value="exclude" <cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "exclude">checked</cfif>>
													<label class="form-check-label" for="lastUpdatedDateExcludeFromOpt">Not updated between</label>
													<div class="col-md-8 col-sm-12">
														<div class="row">
															<div class="col-md col-sm-12 pr-md-0">
																<div class="input-group input-group-sm">
																	<input type="text" name="lastUpdatedDateExcludeFrom" id="lastUpdatedDateExcludeFrom" value="#local.lastUpdatedDateExcludeFrom#" mcrdtxt="Last Updated Exclude Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
																	<div class="input-group-append">
																		<span class="input-group-text cursor-pointer calendar-button" data-target="lastUpdatedDateExcludeFrom"><i class="fa-solid fa-calendar"></i></span>
																	</div>
																</div>
															</div>
															<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
															<div class="col-md col-sm-12 px-md-0">
																<div class="input-group input-group-sm">
																	<input type="text" name="lastUpdatedDateExcludeTo" id="lastUpdatedDateExcludeTo" value="#local.lastUpdatedDateExcludeTo#" mcrdtxt="Last Updated Exclude End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
																	<div class="input-group-append">
																		<span class="input-group-text cursor-pointer calendar-button" data-target="lastUpdatedDateExcludeTo"><i class="fa-solid fa-calendar"></i></span>
																	</div>
																</div>
															</div>
															<div class="col-md-auto pl-md-2">
																<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('lastUpdatedDateExcludeFrom','lastUpdatedDateExcludeTo');">clear</button>
															</div>
														</div>
													</div>													
												</div>
												<div class="float-left pt-md-2">
													<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-lastupdatedate" onclick="clearLastUpdatedOptions();">Clear Date Selections</button>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="retainedCase" class="col-md-4 col-sm-12 col-form-label">Retained Case</label>
											<div class="col-md-8 col-sm-12 pt-2">
												<input type="checkbox" name="retainedCase" id="retainedCase" value="1" <cfif Val(local.retainedCase)>checked</cfif>>
											</div>
										</div>
										<div class="form-group row retainedCaseFields">
											<label for="caseOpenDateFrom" class="col-md-4 col-sm-12 col-form-label">Case Opened between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="caseOpenDateFrom" id="caseOpenDateFrom" value="#local.caseOpenDateFrom#" mcrdtxt="Case Opened Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="caseOpenDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 px-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="caseOpenDateTo" id="caseOpenDateTo" value="#local.caseOpenDateTo#" mcrdtxt="Case Opened End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="caseOpenDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto pl-md-2">
														<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('caseOpenDateFrom','caseOpenDateTo');">clear</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row retainedCaseFields">
											<label for="caseCloseDateFrom" class="col-md-4 col-sm-12 col-form-label">Case Closed between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="caseCloseDateFrom" id="caseCloseDateFrom" value="#local.caseCloseDateFrom#" mcrdtxt="Case Closed Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="caseCloseDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 px-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="caseCloseDateTo" id="caseCloseDateTo" value="#local.caseCloseDateTo#" mcrdtxt="Case Closed End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="caseCloseDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto pl-md-2">
														<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('caseCloseDateFrom','caseCloseDateTo');">clear</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row retainedCaseFields">
											<label for="includeFeesData" class="col-md-4 col-sm-12 col-form-label">Include Fees' Data</label>
											<div class="col-md-8 col-sm-12">
												<input type="checkbox" name="includeFeesData" id="includeFeesData" value="1" <cfif Val(local.includeFeesData)>checked</cfif>>
											</div>
										</div>
										<div class="form-group row includeFeesFields">
											<label for="paymentDateFrom" class="col-md-4 col-sm-12 col-form-label">Payment between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="paymentDateFrom" id="paymentDateFrom" value="#local.paymentDateFrom#" mcrdtxt="Payment Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="paymentDateFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 px-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="paymentDateTo" id="paymentDateTo" value="#local.paymentDateTo#" mcrdtxt="Payment End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="paymentDateTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto pl-md-2">
														<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('paymentDateFrom','paymentDateTo');">clear</button>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="clientReferralSourceID" class="col-md-4 col-sm-12 col-form-label">Source</label>
											<div class="col-md-8 col-sm-12">
												<select name="clientReferralSourceID" id="clientReferralSourceID" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All">
													<cfloop query="local.qryGetReferralSources">
														<option value="#local.qryGetReferralSources.clientReferralSourceID#" <cfif listFind(local.clientReferralSourceID, local.qryGetReferralSources.clientReferralSourceID) gt 0>selected</cfif>>#local.qryGetReferralSources.clientReferralSource#</option>
													</cfloop>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="clientReferralStatusID" class="col-md-4 col-sm-12 col-form-label">Referral Status</label>
											<div class="col-md-8 col-sm-12">
												<select name="clientReferralStatusID" id="clientReferralStatusID" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All">
													<cfoutput query="local.qryGetReferralStatus" group="primaryStatus">
														<optgroup label="#local.qryGetReferralStatus.primaryStatus#">
															<cfoutput>
																<option 
																	<cfif listFind(local.clientReferralStatusID, local.qryGetReferralStatus.clientReferralStatusID) gt 0>selected</cfif>
																	value="#local.qryGetReferralStatus.clientReferralStatusID#" 
																	title="#local.qryGetReferralStatus.statusName#">
																	#local.qryGetReferralStatus.statusName#<cfif local.qryGetReferralStatus.isActive eq 0> (Inactive)</cfif>
																</option>
															</cfoutput>
														</optgroup>
													</cfoutput>
												</select>
											</div>
										</div>
										<cfif local.allowFeeDiscrepancy>
											<div class="form-group row">
												<label for="feeDiscrepancyStatusID" class="col-md-4 col-sm-12 col-form-label">Fee Discrepancy Status</label>
												<div class="col-md-8 col-sm-12">
													<select name="feeDiscrepancyStatusID" id="feeDiscrepancyStatusID" class="form-control form-control-sm"  multiple="yes" data-toggle="custom-select2" placeholder="All">
														<cfloop query="local.qryGetFeeDiscrepancyStatuses">
															<option value="#local.qryGetFeeDiscrepancyStatuses.feeDiscrepancyStatusID#" <cfif local.qryGetFeeDiscrepancyStatuses.feeDiscrepancyStatusID eq local.feeDiscrepancyStatusID>selected</cfif>>#local.qryGetFeeDiscrepancyStatuses.statusName#</option>
														</cfloop>
													</select>
												</div>
											</div>
										</cfif>
										<div class="form-group row">
											<label for="intMemberID" class="col-md-4 col-sm-12 col-form-label">Counselor</label>
											<div class="col-md-8 col-sm-12">
												<select name="intMemberID" id="intMemberID" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All">
													<cfloop query="local.qryGetCounselors">
														<option value="#local.qryGetCounselors.intMemberID#" <cfif listFind(local.intMemberID, local.qryGetCounselors.intMemberID) gt 0>selected</cfif>>#local.qryGetCounselors.interviewerName#</option>
													</cfloop>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="referralFeeTypeID" class="col-md-4 col-sm-12 col-form-label">Fee Type</label>
											<div class="col-md-8 col-sm-12">
												<select name="referralFeeTypeID" id="referralFeeTypeID" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All">
													<cfloop query="local.qryGetFeeTypes">
														<option value="#local.qryGetFeeTypes.feeTypeID#" <cfif listFind(local.feeTypeID, local.qryGetFeeTypes.feeTypeID) gt 0>selected</cfif>>#local.qryGetFeeTypes.feeTypeName#</option>
													</cfloop>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="legalDescription" class="col-md-4 col-sm-12 col-form-label">Legal Description</label>
											<div class="col-md-8 col-sm-12">
												<input type="text" name="legalDescription" id="legalDescription" value="" class="form-control form-control-sm">
											</div>
										</div>
										<div class="form-group row">
											<label for="referralLanguageID" class="col-md-4 col-sm-12 col-form-label">What is your preferred language?</label>
											<div class="col-md-8 col-sm-12">
												<select name="referralLanguageID" id="referralLanguageID" class="form-control form-control-sm">
													<option value=""></option>
													<cfloop query="local.qryGetLanguages">
														<option value="#local.qryGetLanguages.languageID#" <cfif local.qryGetLanguages.languageID eq local.referralLanguageID>selected</cfif>>#local.qryGetLanguages.languageName#</option>
													</cfloop>
												</select>
											</div>
										</div>
										<div class="form-group row">
											<label for="receiveSurveys" class="col-md-4 col-sm-12 col-form-label">Receive Surveys</label>
											<div class="col-md-8 col-sm-12">
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveSurveys" id="receiveSurveysBlank" value="ALL" <cfif local.receiveSurveysCheckedBlank>checked</cfif>>
													<label class="form-check-label" for="receiveSurveysBlank">All records</label>
												</div>
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveSurveys" id="receiveSurveys1" value="1" <cfif local.receiveSurveysChecked1>checked</cfif>>
													<label class="form-check-label" for="receiveSurveys1">Yes</label>
												</div>
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveSurveys" id="receiveSurveys0" value="0" <cfif local.receiveSurveysChecked0>checked</cfif>>
													<label class="form-check-label" for="receiveSurveys0">No</label>
												</div>
											</div>
										</div>
										<div class="form-group row mt-2">
											<label for="receiveNewsletters" class="col-md-4 col-sm-12 col-form-label">Receive Newsletters</label>
											<div class="col-md-8 col-sm-12">
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveNewsletters" id="receiveNewslettersBlank" value="ALL" <cfif local.receiveNewslettersCheckedBlank>checked</cfif>>
													<label class="form-check-label" for="receiveNewslettersBlank">All records</label>
												</div>
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveNewsletters" id="receiveNewsletters1" value="1" <cfif local.receiveNewslettersChecked1>checked</cfif>>
													<label class="form-check-label" for="receiveNewsletters1">Yes</label>
												</div>
												<div class="form-check">
													<input class="form-check-input" type="radio" name="receiveNewsletters" id="receiveNewsletters0" value="0" <cfif local.receiveNewslettersChecked0>checked</cfif>>
													<label class="form-check-label" for="receiveNewsletters0">No</label>
												</div>
											</div>
										</div>
										#local.refFieldFilterControls#
										<div class="form-group row">
											<label for="reportView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
											<div class="col-md-8 col-sm-12">
												<select name="reportView" id="reportView" class="form-control form-control-sm">
													<cfloop query="local.qryReportViews">
														<option value="#local.qryReportViews.type#" <cfif local.qryReportViews.type eq local.reportView>selected</cfif>>#local.qryReportViews.name#</option>
													</cfloop>
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>

							#showStepRollingDates(event=arguments.event)#
							#showStepFieldsets(event=arguments.event, desc='Fieldsets will be included in the report only when "Raw Data Behind Report" is selected as the Report View.')#
							#showButtonBar(event=arguments.event,validateFunction='forceDateRange')#
						</cfform>
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="getReportViews" access="private" output="false" returntype="query">
		<cfargument name="includeFieldsets" required="false" default="ALL" />
		<cfscript>
			var qryReportViews = queryNew("name, type, includeFieldsets", "varchar, varchar, bit");
			queryAddRow(qryReportViews, 10);
			querySetCell(qryReportViews, "name", "Group by Panel", 1);
			querySetCell(qryReportViews, "type", "groupByPanel", 1);
			querySetCell(qryReportViews, "includeFieldsets", 0, 1);
			querySetCell(qryReportViews, "name", "Group by Panel / SubPanel", 2);
			querySetCell(qryReportViews, "type", "groupByPanelSubPanel", 2);
			querySetCell(qryReportViews, "includeFieldsets", 0, 2);
			querySetCell(qryReportViews, "name", "Group by Panel w/ Detail", 3);
			querySetCell(qryReportViews, "type", "groupByPanelWithDetail", 3);
			querySetCell(qryReportViews, "includeFieldsets", 0, 3);
			querySetCell(qryReportViews, "name", "Group by Member", 4);
			querySetCell(qryReportViews, "type", "groupByMember", 4);
			querySetCell(qryReportViews, "includeFieldsets", 1, 4);
			querySetCell(qryReportViews, "name", "Group by Member w/ Detail", 5);
			querySetCell(qryReportViews, "type", "groupByMemberWithDetail", 5);
			querySetCell(qryReportViews, "includeFieldsets", 1, 5);
			querySetCell(qryReportViews, "name", "Group by Source", 6);
			querySetCell(qryReportViews, "type", "groupBySource", 6);
			querySetCell(qryReportViews, "includeFieldsets", 0, 6);
			querySetCell(qryReportViews, "name", "Group by Source w/ Detail", 7);
			querySetCell(qryReportViews, "type", "groupBySourceWithDetail", 7);
			querySetCell(qryReportViews, "includeFieldsets", 0, 7);
			querySetCell(qryReportViews, "name", "Group by Counselor", 8);
			querySetCell(qryReportViews, "type", "groupByCounselor", 8);
			querySetCell(qryReportViews, "includeFieldsets", 0, 8);
			querySetCell(qryReportViews, "name", "Group by Counselor w/ Detail", 9);
			querySetCell(qryReportViews, "type", "groupByCounselorWithDetail", 9);
			querySetCell(qryReportViews, "includeFieldsets", 0, 9);
			querySetCell(qryReportViews, "name", "Raw Data Behind Report", 10);
			querySetCell(qryReportViews, "type", "raw", 10);
			querySetCell(qryReportViews, "includeFieldsets", 1, 10);		
		</cfscript>
		
		<cfif NOT arguments.includeFieldsets EQ "ALL">
			
			<cfquery name="qryReportViews" dbtype="query">
				SELECT *
				FROM qryReportViews
				WHERE includeFieldsets = #arguments.includeFieldsets#
			</cfquery>
			
		</cfif>
		
		<cfreturn qryReportViews />
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);
			
			local.strFields = structNew();
			
			local.strFields.referralDateFrom = { label="Referral Start Date", value=arguments.event.getValue('referralDateFrom','') };
			local.strFields.referralDateTo = { label="Referral End Date", value=arguments.event.getValue('referralDateTo','') };
			local.strFields.callDateFrom = { label="Call Date Start", value=arguments.event.getValue('callDateFrom','') };
			local.strFields.callDateTo = { label="Call Date End", value=arguments.event.getValue('callDateTo','') };
			local.strFields.lastUpdatedDateFrom = { label="Last Updated Start Date", value=arguments.event.getValue('lastUpdatedDateFrom','') };
			local.strFields.lastUpdatedDateTo = { label="Last Updated End Date", value=arguments.event.getValue('lastUpdatedDateTo','') };
			local.strFields.lastUpdatedDateExcludeFrom = { label="Last Updated Exclude Start Date", value=arguments.event.getValue('lastUpdatedDateExcludeFrom','') };
			local.strFields.lastUpdatedDateExcludeTo = { label="Last Updated Exclude End Date", value=arguments.event.getValue('lastUpdatedDateExcludeTo','') };
			local.strFields.lastUpdatedDate = { label="Filter By Last Updated Date", value=arguments.event.getValue('lastUpdatedDate','') };
			local.strFields.caseOpenDateFrom = { label="Case Opened Start Date", value=arguments.event.getValue('caseOpenDateFrom','') };
			local.strFields.caseOpenDateTo = { label="Case Opened End Date", value=arguments.event.getValue('caseOpenDateTo','') };
			local.strFields.caseCloseDateFrom = { label="Case Closed Start Date", value=arguments.event.getValue('caseCloseDateFrom','') };
			local.strFields.caseCloseDateTo = { label="Case Closed End Date", value=arguments.event.getValue('caseCloseDateTo','') };
			local.strFields.retainedCase = { label="Retained Case", value=arguments.event.getValue('retainedCase','') };
			local.strFields.clientReferralSourceID = { label="Source", value=arguments.event.getValue('clientReferralSourceID','') };
			local.strFields.feeTypeID = { label="Fee Type", value=arguments.event.getValue('referralFeeTypeID','') };
			local.strFields.legalDescription = { label="Legal Description", value=arguments.event.getValue('legalDescription','') };
			local.strFields.clientReferralStatusID = { label="Referral Status", value=arguments.event.getValue('clientReferralStatusID','') };
			if(variables.qryGetReferralSettings.allowFeeDiscrepancy)
				local.strFields.feeDiscrepancyStatusID = { label="Fee Discrepancy Status", value=arguments.event.getValue('feeDiscrepancyStatusID','') };
			local.strFields.intMemberID = { label="Counselor", value=arguments.event.getValue('intMemberID','') };
			local.strFields.includeFeesData = { label="Include Fees Data", value=arguments.event.getValue('includeFeesData','ALL') };
			local.strFields.paymentDateFrom = { label="Payment Start Date", value=arguments.event.getValue('paymentDateFrom','') }; 
			local.strFields.paymentDateTo = { label="Payment End Date", value=arguments.event.getValue('paymentDateTo','') };
			local.strFields.referralLanguageID = { label="Preferred Language", value=arguments.event.getValue('referralLanguageID','') };
			local.strFields.receiveSurveys = { label="Receive Surveys", value=arguments.event.getValue('receiveSurveys','ALL') };
			local.strFields.receiveNewsletters = { label="Receive Newsletters", value=arguments.event.getValue('receiveNewsletters','ALL') };
			local.strFields.reportView = { label="Report View", value=arguments.event.getValue('reportView','') };
			local.strFields.rpList = { label="Panel List", value=XMLSearch(local.otherXML,'string(/report/extra/rplist/text())') };

			local.strFilterFormValuesCF = CreateObject("component","model.admin.referrals.referrals").getReferralCustomFieldFilterFormValuesForReport(requestCollection=arguments.event.getCollection());
			local.strFields.attorneyFilterCustomFields = { label="Attorney Custom Fields", value=SerializeJSON(local.strFilterFormValuesCF.attorneyFilterCustomFields) };
			local.strFields.clientReferralsFilterCustomFields = { label="Client Custom Fields", value=SerializeJSON(local.strFilterFormValuesCF.clientReferralsFilterCustomFields) };

			reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
			return returnAppStruct('','echo');
		</cfscript> 
	</cffunction>
	
	<cffunction name="generateData" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="csvfilename" type="string" required="false" default="">
		
		<cfscript>
		var local = structNew();
		local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
		local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode);

		local.qryGetReferralSettings = local.objAdminReferrals.getReferralSettings(siteID=local.mc_siteInfo.siteID);
		local.referralID = local.qryGetReferralSettings.referralID;

		local.clientReferralSourceID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/clientreferralsourceid/text())");
		local.feeTypeID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/feetypeid/text())");
		local.clientReferralStatusID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/clientreferralstatusid/text())");
		if(local.qryGetReferralSettings.allowFeeDiscrepancy)
				local.feeDiscrepancyStatusID =  XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/feediscrepancystatusid/text())");
		local.legalDescription = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/legaldescription/text())");
		local.intMemberID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/intmemberid/text())");
		local.referralLanguageID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referrallanguageid/text())");
		local.receiveSurveys = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/receivesurveys/text())");
		local.receiveNewsletters = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/receivenewsletters/text())");
		local.reportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportview/text())");
		local.referralDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldatefrom/text())");
		local.referralDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldateto/text())");
		local.callDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldatefrom/text())");
		local.callDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldateto/text())");
		local.lastUpdatedDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/lastupdateddatefrom/text())");
		local.lastUpdatedDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/lastupdateddateto/text())");
		local.lastUpdatedDateExcludeFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/lastupdateddateexcludefrom/text())");
		local.lastUpdatedDateExcludeTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/lastupdateddateexcludeto/text())");
		local.lastUpdatedDate = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/lastupdateddate/text())");
		local.caseOpenDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/caseopendatefrom/text())");
		local.caseOpenDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/caseopendateto/text())");
		local.caseCloseDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/caseclosedatefrom/text())");
		local.caseCloseDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/caseclosedateto/text())");
		local.retainedCase = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/retainedcase/text())");
		local.includeFeesData = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/includefeesdata/text())");
		local.paymentDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/paymentdatefrom/text())"); 
		local.paymentDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/paymentdateto/text())");
		local.attorneyFilterCustomFields = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/attorneyfiltercustomfields/text())");
		local.clientReferralsFilterCustomFields = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/clientreferralsfiltercustomfields/text())");
		local.rsFieldsetReportViews = getReportViews(includeFieldsets=1);
		local.thisViewIncludesFieldsets = listFindNoCase(valueList(local.rsFieldsetReportViews.type), local.reportView);
		local.isRetainedCaseChecked = local.retainedCase is 1;
		local.isIncludeFeesDataChecked = local.includeFeesData is 1;
		local.includeFeeData = local.isRetainedCaseChecked AND local.isIncludeFeesDataChecked;
		local.includeDetails = false;
		if (FindNoCase("WithDetail", local.reportView))
			local.includeDetails = true;
		if (len(trim(local.attorneyFilterCustomFields)))
			local.attorneyFilterCustomFields = deserializeJSON(local.attorneyFilterCustomFields);
		if (len(trim(local.clientReferralsFilterCustomFields)))
			local.clientReferralsFilterCustomFields = deserializeJSON(local.clientReferralsFilterCustomFields);

		if (NOT isStruct(local.attorneyFilterCustomFields)) local.attorneyFilterCustomFields = {};
		if (NOT isStruct(local.clientReferralsFilterCustomFields)) local.clientReferralsFilterCustomFields = {};

		local.hasPanelFilters = ListLen(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/rplist/text())")) GT 0;
		</cfscript>

		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.strItemIDSQLVariable = { "AttorneyCustom"="cr.clientReferralID" }>
		<cfset local.strAttorneyFields = local.objResourceCustomFields.generateFieldFilterSQL(rc=local.attorneyFilterCustomFields, fieldIDPrefix='aFieldID', fieldExpPrefix='ARF', strItemIDSQLVariable=local.strItemIDSQLVariable)>
		
		<cfset local.strItemIDSQLVariable = { "ClientRefCustom"="cr.clientReferralID" }>
		<cfset local.strClientReferralFields = local.objResourceCustomFields.generateFieldFilterSQL(rc=local.clientReferralsFilterCustomFields, fieldIDPrefix='rFieldID', fieldExpPrefix='RF', strItemIDSQLVariable=local.strItemIDSQLVariable)>

		<cfif arguments.reportAction eq "customcsv" and local.reportView EQ "raw">
			<cfset local.arrCustomFieldSelectList = local.objAdminReferrals.getReferralCustomFieldSelectListArrayForReport(siteID=local.mc_siteInfo.siteID)>
		</cfif>

		<cfset local.tempReportTableName = "rpt#getTickCount()#">
		<cftry>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(local.strAttorneyFields.filterSetupSQL)>#PreserveSingleQuotes(local.strAttorneyFields.filterSetupSQL)#</cfif>
				<cfif len(local.strClientReferralFields.filterSetupSQL)>#PreserveSingleQuotes(local.strClientReferralFields.filterSetupSQL)#</cfif>

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>

				declare @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">;
				declare @referralID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referralID#">;

				declare @refClientTypeClient int;
				select @refClientTypeClient = clientTypeID from dbo.ref_clientTypes where clientType = 'Client';

				<cfif local.hasPanelFilters>
					declare @tblOrigRP table (panelID int PRIMARY KEY, panelParentID int);

					insert into @tblOrigRP (panelID, panelParentID)
					select panelID, panelParentID
					from @tblRP;

					-- add sub-panels if panel is included in filters
					insert into @tblRP (panelID, panelParentID)
					select p.panelID, p.panelParentID
					from dbo.ref_panels as p
					inner join @tblRP as tblRP on tblRP.panelID = p.panelParentID
					inner join dbo.ref_panelStatus as ps on ps.panelStatusID = p.statusID
						and ps.statusName in ('Inactive','Active')
						and ps.referralID = @referralID
					where tblRP.panelParentID is null
					and p.referralID = @referralID
						except
					select panelID, panelParentID
					from @tblRP
					where panelParentID is not null;

					-- add panel if sub-panel included in filters
					insert into @tblRP (panelID, panelParentID)
					select panelID, NULL
					from (
						select panelParentID as panelID
						from @tblRP
						where panelParentID is not null
							except
						select panelID
						from @tblRP
					) tmp;
				</cfif>
			
				IF OBJECT_ID('tempdb..###local.tempReportTableName#Filtered') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#Filtered;
				CREATE TABLE ###local.tempReportTableName#Filtered (clientID int, clientParentID int null, clientReferralID int);

				IF OBJECT_ID('tempdb..###local.tempReportTableName#CteClient') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#CteClient;
				CREATE TABLE ###local.tempReportTableName#CteClient (clientID int, clientParentID int null, clientReferralID int);

				IF OBJECT_ID('tempdb..###local.tempReportTableName#ordTrans') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#ordTrans;
				CREATE TABLE ###local.tempReportTableName#ordTrans (saleTID int, transactionID int, cache_amountAfterAdjustment decimal(18,2), amtToBePaid decimal(18,2));

				IF OBJECT_ID('tempdb..###local.tempReportTableName#_Payments') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_Payments;
				CREATE TABLE ###local.tempReportTableName#_Payments (clientReferralID int, paymentTotal decimal(18,2), qryCode int);

				IF OBJECT_ID('tempdb..###local.tempReportTableName#_Payments2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_Payments2;
				CREATE TABLE ###local.tempReportTableName#_Payments2 (clientReferralID int, paymentTotal decimal(18,2));

				IF OBJECT_ID('tempdb..###local.tempReportTableName#') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#Reorg') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#Reorg;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1reorg') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1reorg;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_2Final') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_2Final;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1_2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1_2;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1_2Final') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1_2Final;

				-- filtered client referrals
				INSERT INTO ###local.tempReportTableName#Filtered (clientID, clientParentID, clientReferralID)
				select c.clientID, c.clientParentID, cr.clientReferralID
				from dbo.ref_clients c 
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID 
				inner join dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberid 
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID 
					and m2.status <> 'D'
				<cfif listLen(local.intMemberID)>
					inner join dbo.ams_members m3 on m3.orgid = @orgID and m3.memberID = cr.enteredByMemberID
					inner join dbo.ams_members counselors on counselors.orgID = @orgID and counselors.memberID = m3.activeMemberID
						and counselors.activeMemberID in (<cfqueryparam value="#local.intMemberID#" cfsqltype="cf_sql_integer" list="true" />)
				</cfif>
				<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
					<cfif NOT Len(local.feeDiscrepancyStatusID)>
						left join dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = cr.feeDiscrepancyStatusID and fds.isActive = 1
					<cfelse>
						inner join dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = cr.feeDiscrepancyStatusID and fds.isActive = 1
					</cfif>
				</cfif>
				where c.referralID = @referralID
				and c.typeID = @refClientTypeClient
				<cfif Len(local.referralDateFrom)>
					and cr.clientReferralDate >= <cfqueryparam value="#local.referralDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.referralDateTo)>				 
					and cr.clientReferralDate < <cfqueryparam value="#dateAdd('d', 1, local.referralDateTo)#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.callDateFrom)>
					and cr.dateCreated >= <cfqueryparam value="#local.callDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.callDateTo)>				 
					and cr.dateCreated < <cfqueryparam value="#dateAdd('d', 1, local.callDateTo)#" cfsqltype="cf_sql_date">
				</cfif>						
				<cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "include" and len(local.lastUpdatedDateFrom)>
					and cr.dateLastUpdated >= <cfqueryparam value="#local.lastUpdatedDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "include" and len(local.lastUpdatedDateTo)>				 
					and cr.dateLastUpdated < <cfqueryparam value="#dateAdd('d', 1, local.lastUpdatedDateTo)#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "exclude" and len(local.lastUpdatedDateExcludeFrom) and len(local.lastUpdatedDateExcludeTo)> 
					and (cr.dateLastUpdated not between <cfqueryparam value="#local.lastUpdatedDateExcludeFrom#" cfsqltype="cf_sql_date"> 
					and  <cfqueryparam value="#local.lastUpdatedDateExcludeTo#" cfsqltype="cf_sql_date"> 
					or cr.dateLastUpdated is null)
				</cfif>	
				<cfif Len(local.clientReferralSourceID)>	
					and cr.sourceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.clientReferralSourceID#" list="yes">)
				</cfif>					
				<cfif Len(local.feeTypeID)>	
					and cr.feeTypeID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.feeTypeID#" list="yes">)
				</cfif>
				<cfif Len(local.legalDescription)>	
					and cr.issueDesc like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.legalDescription#%">
				</cfif>			
				<cfif Len(local.referralLanguageID)>	
					and cr.communicateLanguageID = <cfqueryparam value="#local.referralLanguageID#" cfsqltype="cf_sql_integer"> 
				</cfif>						
				<cfif Len(local.clientReferralStatusID)>	
					and cr.statusID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.clientReferralStatusID#" list="yes">)
				</cfif>						
				<cfif local.receiveSurveys NEQ "ALL">	
					and cr.sendSurvey = <cfqueryparam value="#local.receiveSurveys#" cfsqltype="cf_sql_bit"> 
				</cfif>						
				<cfif local.receiveNewsletters NEQ "ALL">	
					and cr.sendNewsBlog = <cfqueryparam value="#local.receiveNewsletters#" cfsqltype="cf_sql_bit"> 
				</cfif>
				<cfif local.qryGetReferralSettings.allowFeeDiscrepancy AND Len(local.feeDiscrepancyStatusID)>
					and fds.feeDiscrepancyStatusID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.feeDiscrepancyStatusID#" list="yes">)
				</cfif>
				;

				-- filtered client referrals and their trees
				WITH cte_client as (
					select clientID, clientParentID, clientReferralID
					from ###local.tempReportTableName#Filtered
						union all
					select c.clientID, c.clientParentID, clients.clientReferralID
					from cte_client as clients
					inner join dbo.ref_clients c on c.clientID = clients.clientParentID and c.referralID = @referralID
				)
				INSERT INTO ###local.tempReportTableName#CteClient (clientID, clientParentID, clientReferralID)
				SELECT clientID, clientParentID, clientReferralID
				FROM cte_client;

				<cfif local.includeFeeData>	
					<cfif Len(local.paymentDateFrom) or Len(local.paymentDateTo)>					
						DECLARE @ARGLAID int;
						EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;

						insert into ###local.tempReportTableName#_Payments (clientReferralID, qryCode)
						select cr.clientReferralID, 1
						from dbo.ref_collectedFees cf 
						inner join dbo.ref_cases c on c.referralID = @referralID and c.caseID = cf.caseID
						inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientReferralID = c.clientReferralID
						inner join dbo.ref_clients cTbl on cTbl.referralID = @referralID and cr.clientID = cTbl.clientID
						left outer join dbo.tr_applications tra on tra.orgID = @orgID 
							and cf.collectedFeeID = tra.itemID
							and tra.itemType = 'referralfee'
							and tra.status = 'A'
						where cf.referralID = @referralID 
						and tra.itemID is null
						<cfif Len(local.paymentDateFrom)>
							and cf.collectedFeeDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
						</cfif>
						<cfif Len(local.paymentDateTo)>				 
							and cf.collectedFeeDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
						</cfif>
						group by cr.clientReferralID;

						update rpt
						set rpt.paymentTotal = tmp.paymentTotal
						from ###local.tempReportTableName#_Payments rpt
						inner join (
							select rp.clientReferralID, sum((cf.collectedFee*cf.referralFeePercent)/100) as paymentTotal
							from dbo.ref_collectedFees cf 
							inner join dbo.ref_cases c on c.referralID = @referralID and c.caseID = cf.caseID
							inner join ###local.tempReportTableName#_Payments rp on rp.clientReferralID = c.clientReferralID
							left outer join dbo.tr_applications tra on tra.orgID = @orgID and cf.collectedFeeID = tra.itemID
								and tra.itemType = 'referralfee'
								and tra.status = 'A'
							where cf.referralID = @referralID
							and tra.itemID is null
							<cfif Len(local.paymentDateFrom)>
								and cf.collectedFeeDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
							</cfif>
							<cfif Len(local.paymentDateTo)>				 
								and cf.collectedFeeDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
							</cfif>	
							group by rp.clientReferralID
							) as tmp on tmp.clientReferralID = rpt.clientReferralID;
						
						insert into ###local.tempReportTableName#_Payments (clientReferralID, paymentTotal, qryCode)
						select c.clientReferralID, sum(tmp2.allocAmt) as paymentTotal, 2
						from dbo.ref_clients cTbl
						inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = cTbl.clientID
						inner join dbo.ref_cases c on c.referralID = @referralID and c.clientReferralID = cr.clientReferralID
						inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and c.caseID = cf.caseID
						inner join (
							select collectedFeeID, sum(allocAmt) as allocAmt
							from (
								-- allocations to sales in case
								select cf.collectedFeeID, case when alloc.creditGLAccountID_alloc = @ARGLAID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocAmt
								from dbo.ref_collectedFees as cf
							  	inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.itemID = cf.collectedFeeID and tra.itemType = 'referralfee' and tra.status = 'A'
								inner join dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = tra.transactionID and tSale.typeID = 1
								inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = tSale.transactionID
								inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
								inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								<cfif Len(local.paymentDateFrom)>
									and b.depositDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
								</cfif>
								<cfif Len(local.paymentDateTo)>				 
									and b.depositDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
								</cfif>	
								where cf.referralID = @referralID
									union all
								-- allocations to adjustments of sales in case
								select cf.collectedFeeID, case when alloc.creditGLAccountID_alloc = @ARGLAID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocAmt
								from dbo.ref_collectedFees as cf
								inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.itemID = cf.collectedFeeID and tra.itemType = 'referralfee' and tra.status = 'A'
								inner join dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = tra.transactionID and tSale.typeID = 1
								inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.AppliedToTransactionID = tSale.transactionID and rAdj.typeID = 1
								inner join dbo.tr_transactions as tAdj on tAdj.ownedByOrgID = @orgID and tAdj.transactionID = rAdj.transactionID and tAdj.typeID = 3
								inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = tAdj.transactionID
								inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
								inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								<cfif Len(local.paymentDateFrom)>
									and b.depositDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
								</cfif>
								<cfif Len(local.paymentDateTo)>				 
									and b.depositDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
								</cfif>	
								where cf.referralID = @referralID
									union all
								-- voided allocations to sales in case
								select cf.collectedFeeID, case when alloc.creditGLAccountID_alloc = @ARGLAID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocAmt
								from dbo.ref_collectedFees as cf
								inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.itemID = cf.collectedFeeID and tra.itemType = 'referralfee' and tra.status = 'A'
								inner join dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = tra.transactionID and tSale.typeID = 1
								inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = tSale.transactionID
								inner join dbo.tr_relationships as rVO on rVO.orgID = @orgID and rVO.appliedToTransactionID = alloc.transactionID_alloc and rVO.typeID = 8
								inner join dbo.tr_transactions as tVO on tVO.ownedByOrgID = @orgID and tVO.transactionID = rVO.transactionID and tVO.typeID = 8
								inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = tVO.transactionID
								inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								where cf.referralID = @referralID
								<cfif Len(local.paymentDateFrom)>
									and b.depositDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
								</cfif>
								<cfif Len(local.paymentDateTo)>				 
									and b.depositDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
								</cfif>	
									union all
								-- voided allocations to adjustments of sales in case
								select cf.collectedFeeID, case when alloc.creditGLAccountID_alloc = @ARGLAID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocAmt
								from dbo.ref_collectedFees as cf
								inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.itemID = cf.collectedFeeID and tra.itemType = 'referralfee' and tra.status = 'A'
								inner join dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = tra.transactionID and tSale.typeID = 1
								inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.AppliedToTransactionID = tSale.transactionID and rAdj.typeID = 1
								inner join dbo.tr_transactions as tAdj on tAdj.ownedByOrgID = @orgID and tAdj.transactionID = rAdj.transactionID and tAdj.typeID = 3
								inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = tAdj.transactionID
								inner join dbo.tr_relationships as rVO on rVO.orgID = @orgID and rVO.appliedToTransactionID = alloc.transactionID_alloc and rVO.typeID = 8
								inner join dbo.tr_transactions as tVO on tVO.ownedByOrgID = @orgID and tVO.transactionID = rVO.transactionID and tVO.typeID = 8
								inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = tVO.transactionID
								inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								<cfif Len(local.paymentDateFrom)>
									and b.depositDate >= <cfqueryparam value="#local.paymentDateFrom#" cfsqltype="cf_sql_date">
								</cfif>
								<cfif Len(local.paymentDateTo)>				 
									and b.depositDate < <cfqueryparam value="#dateAdd('d', 1, local.paymentDateTo)#" cfsqltype="cf_sql_date">
								</cfif>	
								where cf.referralID = @referralID
							) as tmp
							group by collectedFeeID
							) as tmp2 on tmp2.collectedFeeID = cf.collectedFeeID
						where cTbl.referralID = @referralID
						group by c.clientReferralID;
						
						-- get rid of duplicates
						insert into ###local.tempReportTableName#_Payments2 (clientReferralID, paymentTotal)
						select clientReferralID, sum(paymentTotal)
						from ###local.tempReportTableName#_Payments
						group by clientReferralID;
					</cfif>				 			
					
					declare @legacyTrans table (rowID int identity (1,1), caseID int, saleTID int, transactionID int, cache_amountAfterAdjustment decimal(18,2), 
						amtToBePaid decimal(18,2), collectedFee decimal(18,2));
					declare @t_Tax int = dbo.fn_tr_getTypeID('Sales Tax');
					
					insert into @legacyTrans (caseID, saleTID, transactionID, cache_amountAfterAdjustment, amtToBePaid, collectedFee)
					select cf.caseID, 0 as saleTID, 0 as transactionID, cf.importedCollectedFee as cache_amountAfterAdjustment, 0 as amtToBePaid, cf.collectedFee
					from dbo.ref_collectedFees cf 
					inner join dbo.ref_cases as c on c.referralID = @referralID and c.caseID = cf.caseID
					inner join dbo.ref_clientReferrals as cr on cr.referralID = @referralID and cr.clientReferralID = c.clientReferralID
					inner join dbo.ref_clients as cl on cl.clientID = cr.clientID and cl.referralID = @referralID
					left outer join dbo.tr_applications tra on tra.orgID = @orgID and cf.collectedFeeID = tra.itemID
						and tra.itemType = 'referralfee'
						and tra.status = 'A'
					where cf.referralID = @referralID
					and tra.itemID is null;
					
					WITH ordTrans as (
						select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment, 
							ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
						from dbo.tr_applications tra
						inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
						inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID					
						inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
						where tra.orgID = @orgID
						and tra.itemType = 'referralfee'
						and tra.status = 'A'
							union all
						select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment, 
							ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
						from dbo.tr_transactions as t
						inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tr.transactionID = t.transactionID
						inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
						inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
						where t.typeID = @t_Tax
						and t.ownedByOrgID = @orgID
					)
					INSERT INTO ###local.tempReportTableName#ordTrans (saleTID, transactionID, cache_amountAfterAdjustment, amtToBePaid)
					SELECT saleTID, transactionID, cache_amountAfterAdjustment, amtToBePaid
					FROM ordTrans;
				</cfif>

				<cfswitch expression="#local.reportView#">
					<cfcase value="groupByPanel,groupByPanelWithDetail">
						<cfif local.reportView EQ "groupByPanelWithDetail">	
							select panelID = case when p.panelParentID is not null then p.panelParentID else p.panelID end
							, panel = case when p2.name is not null then p2.name else p.name end
							, [Panel Code] = case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end
							, 1 as ttlrow							
							, 1 as subCount
							, crsource.clientReferralSource as Source
							, cr.clientReferralID as ReferralID
							, cr.clientReferralDate as [Referral Date]
							, crs.statusName as [Referral Status]
							, crs.statusCode as [Status Code]
							<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								, fds.statusName as [Fee Discrepancy Status]
							</cfif>
						<cfelse>
							select panelID = p.panelID							
							, panel = case when p2.name is not null then concat(p2.name,' / ', p.name) else p.name end
							, [Panel Code] = case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end
							, case when p2.name is not null then 0 else 1 end as ttlrow			
							, count(distinct cr.clientReferralID) as subCount
						</cfif>	
					</cfcase>
					<cfcase value="groupByPanelSubPanel">
						select 1 as ttlrow	
						, panelID =  p.panelID
						, [Sub-Panel] = case when p2.name is not null then concat(p2.name,' / ',p.name) else p.name end
						, [Sub-Panel Code] = case when p.shortDesc is not null then p.shortDesc else p2.shortDesc end
						, count(distinct cr.clientReferralID) as subCount
					</cfcase>					
					<cfcase value="groupByMember,groupByMemberWithDetail">
						select m.memberid, m.lastname + ', ' + m.firstName + ' ('+m.membernumber+')' as [Member], m.Company, 
						m.firstName as [First Name], m.lastname as [Last Name], m.MemberNumber, 1 as ttlrow
						<cfif local.reportView EQ "groupByMemberWithDetail">
							, 1 as subCount
							, panelID = case when p.panelParentID is not null then p.panelParentID else p.panelID end
							, panel = case when p2.name is not null then p2.name else p.name end
							, [Panel Code] = case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end
							, crsource.clientReferralSource as Source
							, cr.clientReferralID as ReferralID
							, cr.clientReferralDate as [Referral Date]
							, crs.statusName as [Referral Status]
							, crs.statusCode as [Status Code]
							<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								,fds.statusName as [Fee Discrepancy Status]
							</cfif>
							, c.firstName as [Client First Name]
							, c.middleName as [Client Middle Name]
							, c.lastName as [Client Last Name]
							, c.businessName as [Client Business Name]
							, c.address1 as [Client Address 1]
							, c.address2 as [Client Address 2]
							, c.city as [Client City]
							, s.code as [Client State]
							, c.postalCode as [Client Zip Code]
							, c.countryID as [Client Country]
							, c.email as [Client Email]
							, c.homePhone as [Client Home Phone]
							, c.cellPhone as [Client Cell Phone]
							, c.alternatePhone as [Client Alternate Phone]
						<cfelse>
							, count(distinct cr.clientReferralID ) as subCount
						</cfif>
					</cfcase>
					<cfcase value="groupBySource,groupBySourceWithDetail">
						select crsource.clientReferralSourceID as sourceID, crsource.clientReferralSource as Source, 1 as ttlrow
						<cfif local.reportView EQ "groupBySourceWithDetail">
							, 1 as subCount
							, cr.clientReferralID as ReferralID
							, cr.clientReferralDate as [Referral Date]
							, crs.statusName as [Referral Status]
							, crs.statusCode as [Status Code]
							<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								, fds.statusName as [Fee Discrepancy Status]
							</cfif>
						<cfelse>
							,  count(distinct cr.clientReferralID ) as subCount
						</cfif>	
					</cfcase>
					<cfcase value="raw">
						select panel = case when p2.name is not null then p2.name else p.name end,
							[Panel Code] = case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end,
							[Sub-Panel] = case 
								when isnull(h.subPanelID1,'') <> '' then 
									(
										select replace(STUFF((SELECT  '|' + pp.name
										from dbo.ref_panels pp
										where pp.panelID in (
											select listitem 
											from dbo.fn_varcharListToTable(h.subPanelID1,',') 
											where TRY_CONVERT(int,listitem) is not null 
										)
										order by pp.name
										FOR XML PATH('')), 1, 1, '') , '&amp;', '&') 
									)
								else '' end, 
							[Sub-Panel Code] = case
								when isnull(h.subPanelID1,'') <> '' then
									(
										select replace(STUFF((SELECT  '|' + pp.shortDesc
										from dbo.ref_panels pp
										where pp.panelID in (
											select listitem 
											from dbo.fn_varcharListToTable(h.subPanelID1,',') 
											where TRY_CONVERT(int,listitem) is not null 
										)
										order by pp.name
										FOR XML PATH('')), 1, 1, '') , '&amp;', '&') 
									)
								else '' end, 
						crsource.clientReferralSource as Source,
						crftypes.feeTypeName,
						cr.clientReferralID as ReferralID,
						cr.clientReferralDate as [Referral Date],
						cr.dateCreated as [Call Date],
						rc.dateCaseOpened as [Open Case Date],
						rc.dateCaseClosed as [Close Case Date],
						cr.dateLastUpdated as [Last Updated Date],				
						crs.statusName as [Referral Status],
						crs.statusCode as [Status Code],
						<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
							fds.statusName as [Fee Discrepancy Status],
						</cfif>
						c.firstName as [Client First Name],
						c.middleName as [Client Middle Name],
						c.lastName as [Client Last Name],
						c.businessName as [Client Business Name],
						c.address1 as [Client Address 1],
						c.address2 as [Client Address 2],
						c.city as [Client City],
						s.code as [Client State],
						c.postalCode as [Client Postal Code],
						c.email as [Client Email],
						c.homePhone as [Client Home Phone],
						c.cellPhone as [Client Cell Phone],
						c.alternatePhone as [Client Alternate Phone],
						m.memberID,
						m.firstName as [First Name],			
						m.lastname as [Last Name],						
						m.MemberNumber,
						m.Company,							
						counselors.lastname + ', ' + counselors.firstName [Counselor Name],
						STUFF((SELECT   '|' + CONVERT(varchar,n.createdDate,1 ) + ' '+ n.referralNote  
												from dbo.ref_notes n
												where  n.clientReferralID = cr.clientReferralID and n.noteType = 'C'
												order by n.createdDate desc
												FOR XML PATH('')), 1, 1, '') as [Counselor Notes],
						cr.issueDesc as [Legal Description],
						STUFF((SELECT   '|' + CONVERT(varchar,n.createdDate,1 ) + ' '+ n.referralNote  
												from dbo.ref_notes n
												where  n.clientReferralID = cr.clientReferralID and n.noteType = 'A'
												order by n.createdDate desc
												FOR XML PATH('')), 1, 1, '') as [Lawyer Notes],
						cr.importClientReferralID as [Import Case Number],
						rl.languageName as [Language]
					</cfcase>
					<cfcase value="groupByCounselor,groupByCounselorWithDetail">
						select counselors.activeMemberID as counselorsID,
							counselors.lastname + ', ' + counselors.firstName as [Counselor Name],
							1 as ttlrow
							<cfif local.reportView EQ "groupByCounselorWithDetail">
								, 1 as subCount
								, cr.clientReferralID as ReferralID
								, cr.clientReferralDate as [Referral Date]
								, crs.statusName as [Referral Status]
								, crs.statusCode as [Status Code]
								<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								,fds.statusName as [Fee Discrepancy Status]
								</cfif>
							<cfelse>
								, count(distinct cr.clientReferralID ) as subCount
							</cfif>
					</cfcase>
				</cfswitch>

				<cfif local.includeFeeData>
					<!--- report views that are raw show one per row; other views sum() the fees --->
					<cfif local.reportView EQ "raw">
						, rc.caseFees as [Fees Reported by Client]
						, fees.ReferralDues as [Referral Fees]
						, fees.amtToBePaid as [Amount to be Paid]
						, fees.collectedFee as [Fees Collected by Attorney]
						<cfif Len(local.paymentDateFrom) or Len(local.paymentDateTo)>
							, rpt.paymentTotal as [Fees Paid Total]				
						</cfif>						
					<cfelse>
						, sum(fees.ReferralDues) as [Total Referral Fees]
						, sum(fees.amtToBePaid) as [Total Amount To Be Paid]
						, sum(fees.collectedFee) as [Total Fees Collected by Attorney]
					</cfif>
				</cfif>

				<cfif arguments.reportAction eq "customcsv" and local.reportView EQ "raw" and len(trim(ArrayToList(local.arrCustomFieldSelectList)))>
					, #ArrayToList(local.arrCustomFieldSelectList)#
				</cfif>					
				into ###local.tempReportTableName#
				from ###local.tempReportTableName#Filtered as tmp
				inner join dbo.ref_clients c on c.clientID = tmp.clientID and c.referralID = @referralID
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				inner join ###local.tempReportTableName#CteClient as cParent on cParent.clientReferralID = cr.clientReferralID and cParent.clientParentID is null
				inner join dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID and crs.isAgency = 0 and crs.referralID = @referralID
				left join dbo.ref_clientReferralSources crsource on crsource.clientReferralSourceID = cr.sourceID and crsource.referralID = @referralID
				<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
					<cfif Len(local.feeDiscrepancyStatusID)>
						inner join dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = cr.feeDiscrepancyStatusID and fds.isActive = 1
					<cfelse>
						left join dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = cr.feeDiscrepancyStatusID and fds.isActive = 1
					</cfif>
				</cfif>
				inner join dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = cr.memberid
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID 
					and m.isProtected = 0
					and m.status <> 'D'	
				<cfif listLen(local.intMemberID) or local.reportView EQ "groupByCounselor" or local.reportView EQ "groupByCounselorWithDetail">
					inner join dbo.ams_members m3 on m3.orgID = @orgID and m3.memberID = cr.enteredByMemberID
					inner join dbo.ams_members counselors on counselors.orgID = @orgID and counselors.memberID = m3.activeMemberID
						and counselors.isProtected = 0
						<cfif listLen(local.intMemberID)>
							and counselors.activeMemberID in (<cfqueryparam value="#local.intMemberID#" cfsqltype="cf_sql_integer" list="true" />)
						</cfif>
				<cfelseif local.reportView EQ "raw">
					left outer join dbo.ams_members m3 on m3.orgID = @orgID and m3.memberID = cr.enteredByMemberID
					left outer join dbo.ams_members counselors on counselors.orgID = @orgID and counselors.memberID = m3.activeMemberID
						and counselors.isProtected = 0
				</cfif>
				inner join dbo.ref_languages rl on rl.languageID = cr.communicateLanguageID	and rl.referralID = @referralID
				inner join searchMC.dbo.tblSearchReferralHistory h on h.clientID = cParent.clientID
				left outer join dbo.ams_states s on s.stateid = c.state
				left outer join dbo.ref_clientReferralFeeTypes crftypes on crftypes.feeTypeID = cr.feeTypeID and crftypes.referralID = @referralID
				left outer join dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID
				left outer join dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				left outer join dbo.ref_panels p on
					p.referralID = @referralID
					<cfif local.hasPanelFilters>
						and p.panelID 
							<cfif local.reportView EQ "groupByPanel">
								in ( 
									select listitem 
									from dbo.fn_varcharListToTable(cast(h.panelID1 as varchar(10)) + isnull(','+h.subPanelID1,''),',') 
									where TRY_CONVERT(int, listitem) is not null
								)
							<cfelseif local.reportView EQ "groupByPanelSubPanel" OR local.reportView EQ "raw">
								in ( 
									select listitem 
									from dbo.fn_varcharListToTable(h.subPanelID1,',') 
									where TRY_CONVERT(int, listitem) is not null 
										union
									select panelID
									from @tblOrigRP
									where panelID = h.panelID1
									and panelParentID is null
								)
							<cfelse>
								= h.panelID1
							</cfif>
					<cfelseif local.reportView eq "groupByPanelSubPanel">
						and p.panelID in (
							select listitem
							from dbo.fn_varcharListToTable(cast(h.panelID1 as varchar(10)) + isnull(','+h.subPanelID1,''),',') 
							where TRY_CONVERT(int, listitem) is not null
						)
					<cfelse>
						and p.panelID = h.panelID1
					</cfif>
				outer apply (select p2.name,p2.shortDesc from dbo.ref_panels p2 where p2.referralID = @referralID and p2.panelID = p.panelParentID) as p2
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>
					#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#
				</cfif>
				<cfif local.includeFeeData>
					/* INCLUDE THIS JOIN ONLY WHEN "INCLUDE FEES DATA" and "RETAINED CASES" CHECKBOXES ARE SELECTED */
					left outer join (
						select caseID, sum(ReferralDues) as ReferralDues, sum(amtToBePaid) as amtToBePaid, sum(collectedFee) as collectedFee
						from (
							select cf.caseID, sum(ot.ReferralDues) as ReferralDues, sum(ot.amtToBePaid) as amtToBePaid, sum(cf.collectedFee) as collectedFee
							from dbo.tr_applications tra
							inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
							inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID					
							inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
							inner join (
								select saleTID, sum(cache_amountAfterAdjustment) as ReferralDues, sum(amtToBePaid) as amtToBePaid
								from ###local.tempReportTableName#ordTrans
								group by saleTID
								) ot on ot.saleTID = tra.transactionID 
							where tra.orgID = @orgID
							and ts.cache_amountAfterAdjustment > 0.00											
							group by cf.caseID
								union
							select lt.caseID, sum(lt.cache_amountAfterAdjustment) as ReferralDues, sum(lt.amtToBePaid), sum(lt.collectedFee)
							from @legacyTrans lt
							group by lt.caseID
						) as tmp
						group by caseID
					) as fees on fees.caseID = rc.caseID

					<cfif Len(local.paymentDateFrom) or Len(local.paymentDateTo)>
						inner join ###local.tempReportTableName#_Payments2 rpt on rpt.clientReferralID = cr.clientReferralID						
					</cfif>										
				</cfif> 
				<cfif arguments.reportAction eq "customcsv" and local.reportView EQ "raw" and len(trim(ArrayToList(local.arrCustomFieldSelectList)))>
					left outer join (
						SELECT * from (			
							SELECT DISTINCT fd.itemID, CASE WHEN fd.itemType = 'AttorneyCustom' THEN 'attorney_' ELSE 'client_' END + f.fieldText AS fieldText, 
								answer = replace(replace((
												(select replace(case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
																	when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
																	when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(15))
																	when ft.dataTypeCode = 'BIT' then case when fv.valueBit = 1 then 'Yes' else 'No' end
																	when ft.dataTypeCode = 'DATE' then convert(varchar(12), fv.valueDate, 101)
																else '' end,' ','^~~~^') as [data()] 
												from dbo.cf_fieldValues as fv
												inner join dbo.cf_fieldData as fd2 on fd2.valueID = fv.valueID
												where fd2.itemID = fd.itemID
												and fd2.itemType in ('AttorneyCustom','ClientRefCustom')
												and fv.fieldID = f.fieldID
												FOR XML PATH (''),TYPE).value('(./text())[1]','nvarchar(max)')
											), ' ', ', '),'^~~~^',' ')
							FROM dbo.cf_fieldData as fd 				
							INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID 
							INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID AND ft.displayTypeCode <> 'LABEL'
							inner join ###local.tempReportTableName#CteClient ctc on ctc.clientReferralID = fd.itemID
							WHERE fd.itemType in ('AttorneyCustom','ClientRefCustom')
							AND f.isActive = 1
							) A
							PIVOT  
							(  
								Max(answer)  
								FOR fieldText IN (#ArrayToList(local.arrCustomFieldSelectList)#)
							) AS PivotTable
					) pv ON pv.itemID = cr.clientReferralID
				</cfif>									
				WHERE c.referralID = @referralID
				<cfif Len(local.referralDateFrom)>
					and cr.clientReferralDate >= <cfqueryparam value="#local.referralDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.referralDateTo)>				 
					and cr.clientReferralDate < <cfqueryparam value="#dateAdd('d', 1, local.referralDateTo)#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.callDateFrom)>
					and cr.dateCreated >= <cfqueryparam value="#local.callDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif Len(local.callDateTo)>				 
					and cr.dateCreated < <cfqueryparam value="#dateAdd('d', 1, local.callDateTo)#" cfsqltype="cf_sql_date">
				</cfif>	
				<cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "include" and len(local.lastUpdatedDateFrom)>
					and cr.dateLastUpdated >= <cfqueryparam value="#local.lastUpdatedDateFrom#" cfsqltype="cf_sql_date">
				</cfif>
				<cfif len(local.lastUpdatedDate) and local.lastUpdatedDate eq "include" and len(local.lastUpdatedDateTo)>				 
					and cr.dateLastUpdated < <cfqueryparam value="#dateAdd('d', 1, local.lastUpdatedDateTo)#" cfsqltype="cf_sql_date">
				</cfif>			
				
				/* INCLUDE THESE CONDITIONS ONLY WHEN "RETAINED CASES" CHECKBOX IS SELECTED */
				<cfif Len(local.caseOpenDateFrom)>
					and rc.dateCaseOpened >= <cfqueryparam value="#local.caseOpenDateFrom#" cfsqltype="cf_sql_timestamp">
				</cfif>
				<cfif Len(local.caseOpenDateTo)>				 
					and rc.dateCaseOpened < <cfqueryparam value="#dateAdd('d', 1, local.caseOpenDateTo)#" cfsqltype="cf_sql_timestamp">
				</cfif>
				<cfif Len(local.caseCloseDateFrom)>
					and rc.dateCaseClosed >= <cfqueryparam value="#local.caseCloseDateFrom#" cfsqltype="cf_sql_timestamp">
				</cfif>
				<cfif Len(local.caseCloseDateTo)> 
					and rc.dateCaseClosed < <cfqueryparam value="#dateAdd('d', 1, local.caseCloseDateTo)#" cfsqltype="cf_sql_timestamp">
				</cfif>
				<cfif Len(local.clientReferralSourceID)>	
					and cr.sourceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.clientReferralSourceID#" list="yes">)					
				</cfif>
				<cfif Len(local.feeTypeID)>	
					and cr.feeTypeID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.feeTypeID#" list="yes">)					
				</cfif>
				<cfif Len(local.legalDescription)>	
					and cr.issueDesc like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#local.legalDescription#%">
				</cfif>
				<cfif Len(local.referralLanguageID)>	
					and cr.communicateLanguageID = <cfqueryparam value="#local.referralLanguageID#" cfsqltype="cf_sql_integer"> 
				</cfif>
				<cfif Len(local.clientReferralStatusID)>	
					and cr.statusID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.clientReferralStatusID#" list="yes">)					
				</cfif>
				<cfif local.receiveSurveys NEQ "ALL">	
					and cr.sendSurvey = <cfqueryparam value="#local.receiveSurveys#" cfsqltype="cf_sql_bit"> 
				</cfif>
				<cfif local.receiveNewsletters NEQ "ALL">	
					and cr.sendNewsBlog = <cfqueryparam value="#local.receiveNewsletters#" cfsqltype="cf_sql_bit"> 
				</cfif>
				<cfif local.isRetainedCaseChecked>
					and rc.caseID is not null
				</cfif>
				<cfif local.qryGetReferralSettings.allowFeeDiscrepancy AND Len(local.feeDiscrepancyStatusID)>
					and fds.feeDiscrepancyStatusID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.feeDiscrepancyStatusID#" list="yes">)
				</cfif>
				#PreserveSingleQuotes(local.strAttorneyFields.filterSQL)#
				#PreserveSingleQuotes(local.strClientReferralFields.filterSQL)#

				<cfswitch expression="#local.reportView#">
					<cfcase value="groupByPanel">
						group by p.panelID, case when p2.name is not null then concat(p2.name,' / ', p.name) else p.name end, case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end,
							case when p2.name is not null then 0 else 1 end
					</cfcase>
					<cfcase value="groupByPanelSubPanel">
						group by p.panelID, case when p2.name is not null then concat(p2.name,' / ',p.name) else p.name end, case when p.shortDesc is not null then p.shortDesc else p2.shortDesc end
					</cfcase>
					<cfcase value="groupByPanelWithDetail">
						group by case when p.panelParentID is not null then p.panelParentID else p.panelID end, 
							case when p2.name is not null then p2.name else p.name end, case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end,								
							crsource.clientReferralSource, cr.clientReferralDate, cr.clientReferralID, crs.statusName, crs.statusCode
							<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								,fds.statusName
							</cfif>
					</cfcase>
					<cfcase value="groupByMember">
						group by m.memberid, m.lastname, m.firstName, m.membernumber, m.company, cr.clientReferralID 
					</cfcase>
					<cfcase value="groupByMemberWithDetail">
						group by m.memberid, m.lastname, m.firstName, m.membernumber, m.company,
							case when p.panelParentID is not null then p.panelParentID else p.panelID end, 
							case when p2.name is not null then p2.name else p.name end, case when p2.shortDesc is not null then p2.shortDesc else p.shortDesc end,
							crsource.clientReferralSource, cr.clientReferralID, cr.clientReferralDate, crs.statusName, crs.statusCode
							<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
								,fds.statusName
							</cfif>
							,c.firstName, c.middleName, c.lastName, c.businessName, c.address1, c.address2, c.city, s.code, c.postalCode, c.countryID,
							c.email, c.homePhone, c.cellPhone, c.alternatePhone
					</cfcase>
					<cfcase value="groupBySource">
						group by crsource.clientReferralSourceID, crsource.clientReferralSource			
					</cfcase>
					<cfcase value="groupBySourceWithDetail">
						group by crsource.clientReferralSourceID, crsource.clientReferralSource, cr.clientReferralID, cr.clientReferralDate, crs.statusName, crs.statusCode
						<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
							,fds.statusName
						</cfif>
					</cfcase>
					<cfcase value="groupByCounselor">
						group by counselors.activeMemberID, counselors.lastname + ', ' + counselors.firstName
					</cfcase>
					<cfcase value="groupByCounselorWithDetail">
						group by counselors.activeMemberID, counselors.lastname + ', ' + counselors.firstName, cr.clientReferralID, cr.clientReferralDate, crs.statusName, crs.statusCode
						<cfif local.qryGetReferralSettings.allowFeeDiscrepancy>
							,fds.statusName
						</cfif>
					</cfcase>
				</cfswitch>;
				
				<cfif local.thisViewIncludesFieldsets>
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					/* we need a second temp table to store data from fieldsets as well */
					<cfset local.finalTempTableName = "#local.tempReportTableName#_1">

					DECLARE @outputFieldsXML xml;
					
					CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempReportTableName# (memberID);

					-- get fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
						@membersTableName='###local.tempReportTableName#', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
						@mode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.reportAction eq 'customcsv' ? 'export' : 'report'#">, 
						@outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					EXEC tempdb..sp_rename '##tmpMembersFS.memberID', 'MCMemberIDTemp', 'COLUMN';
				
					SELECT DISTINCT tm.*, m.*
					INTO ###local.tempReportTableName#_1
					FROM ###local.tempReportTableName# AS tm
					INNER JOIN ##tmpMembersFS AS m ON m.MCMemberIDTemp = tm.memberID;

					ALTER TABLE ###local.tempReportTableName#_1 DROP COLUMN MCMemberIDTemp;

					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
				<cfelse>
					<cfset local.finalTempTableName = "#local.tempReportTableName#">
				</cfif>
				
				<cfif local.reportView NEQ "raw">
					#makeTempTableAcceptNULLs(local.finalTempTableName)#
				</cfif>

				<cfset local.reportTotalLabel = "Report Total"/>
				<cfset local.fieldsToDisplay = "">
				<cfset local.additionalFields = "">
				<cfif local.qryGetReferralSettings.allowFeeDiscrepancy and ListContainsNoCase("raw,customcsv",arguments.reportAction)>
					<cfset local.additionalFields = ",[Fee Discrepancy Status]">
				</cfif>
				<cfif local.reportView EQ "groupByPanel">
					<cfset local.args.fieldsInSelectClause = "PanelID, Panel">
					<cfset local.args.fieldsInOrderClause = "Panel">
					<cfset local.fieldsToDisplay = "Panel,[Panel Code]">
					<cfset local.reportTotalLabel = "Report Total (Primary Panels Only)"/>
					<cfset local.args.reportTotalLabel = local.reportTotalLabel>

				<cfelseif local.reportView EQ "groupByPanelSubPanel">

					<cfset local.args.fieldsInSelectClause = "PanelID,[Sub-Panel]">
					<cfset local.args.fieldsInOrderClause = "[Sub-Panel]">
					<cfset local.fieldsToDisplay = "[Sub-Panel],[Sub-Panel Code]">

				<cfelseif local.reportView EQ "groupByPanelWithDetail">
					
					<cfset local.args.fieldsInSelectClause = "panelID, Panel">
					<cfset local.args.fieldsInOrderClause = "Panel, [Referral Date] desc">
					<cfset local.fieldsToDisplay = "Panel, [Panel Code], Source, ReferralID, [Referral Date], [Referral Status], [Status Code]" &local.additionalFields>

				<cfelseif local.reportView EQ "groupBySource">

					<cfset local.args.fieldsInSelectClause = "sourceID, Source">
					<cfset local.args.fieldsInOrderClause = "Source">
					<cfset local.fieldsToDisplay = "Source">

				<cfelseif local.reportView EQ "groupBySourceWithDetail">
					
					<cfset local.args.fieldsInSelectClause = "sourceID, Source">
					<cfset local.args.fieldsInOrderClause = "Source, [Referral Date] desc">
					<cfset local.fieldsToDisplay = "Source, ReferralID, [Referral Date], [Referral Status],[Status Code]" &local.additionalFields>
					
				<cfelseif local.reportView EQ "groupByMember">
					
					<cfset local.args.fieldsInSelectClause = "memberID, [Member]">
					<cfset local.args.fieldsInOrderClause = "[Member]">				
					<cfset local.fieldsToDisplay = "[Member], [First Name], [Last Name], MemberNumber">			

				<cfelseif local.reportView EQ "groupByMemberWithDetail">
				
					<cfset local.args.fieldsInSelectClause = "memberID, [Member]">
					<cfset local.args.fieldsInOrderClause = "[Member], [Referral Date] desc">
					<cfset local.fieldsToDisplay = "[Member],[First Name], [Last Name], MemberNumber, Source, ReferralID, [Referral Date], [Referral Status],[Status Code]" &local.additionalFields&", [Client First Name], [Client Middle Name], [Client Last Name], [Client Business Name],">
					<cfset local.fieldsToDisplay &= "[Client Address 1], [Client Address 2], [Client City], [Client State], [Client Zip Code], [Client Country], [Client Email],">
					<cfset local.fieldsToDisplay &= "[Client Home Phone], [Client Cell Phone], [Client Alternate Phone]">	

				<cfelseif local.reportView EQ "raw">
				
					<cfset local.args.fieldsInSelectClause = "*">
					<cfset local.args.fieldsInOrderClause = "[Last Name]">
					<cfset local.fieldsToDisplay = "ReferralID,[Referral Date],Panel,[Panel Code],[Sub-Panel],[Sub-Panel Code],Source,[Referral Status],[Status Code]" &local.additionalFields&",[Client First Name],[Client Middle Name],[Client Last Name],[Client Business Name],[Client Address 1],[Client Address 2],[Client City],[Client State],[Client Postal Code],[Client Email],[Client Home Phone],[Client Cell Phone],[Client Alternate Phone],[First Name],[Last Name],MemberNumber,Company,[Counselor Name],[Counselor Notes],[Import Case Number],[Language]" />					
		
				<cfelseif local.reportView EQ "groupByCounselor">

					<cfset local.args.fieldsInSelectClause = "counselorsID, [Counselor Name]">
					<cfset local.args.fieldsInOrderClause = "[Counselor Name]">
					<cfset local.fieldsToDisplay = "[Counselor Name]">

				<cfelseif local.reportView EQ "groupByCounselorWithDetail">
					
					<cfset local.args.fieldsInSelectClause = "counselorsID, [Counselor Name]">
					<cfset local.args.fieldsInOrderClause = "[Counselor Name], [Referral Date] desc">
					<cfset local.fieldsToDisplay = "[Counselor Name], ReferralID, [Referral Date], [Referral Status],[Status Code]"&local.additionalFields>
					
				</cfif>

				<cfif local.reportView NEQ "raw">
					<cfset local.args.tempTableName = local.finalTempTableName>
					<cfset local.args.includeFeeData = local.includeFeeData>
					<cfset local.args.hasDetails = local.includeDetails>
					<cfset local.args.reportView = local.reportView> 
					#reorgDataLayout(ArgumentCollection=local.args)#
				</cfif>

				<cfif arguments.reportAction eq "customcsv">
					<cfset local.finalTempTableName2 = local.finalTempTableName & "_2">
					<cfset local.rowOrderField = ListFirst(local.args.fieldsInOrderClause)>
					<cfset local.rowOrderField_SQLSafe = REReplace(local.rowOrderField,"[[:punct:]]", "", "ALL")>
					<cfset local.rowOrderField_SQLSafe = ReplaceNoCase(local.rowOrderField_SQLSafe, "desc", "", "ALL")>
					<cfset local.rowOrderField_NoSort = ReplaceNoCase(local.rowOrderField, "desc", "", "ALL")>
					<cfset local.otherFieldsToDisplay = ListDeleteAt(local.fieldsToDisplay, 1)>
					
					SELECT 
						<cfif local.reportView NEQ "raw">
						ROW_NUMBER() OVER (	order by 
												case 
													/* change it to zzzzzzzzzzz to put it last in sort order */
													when #local.rowOrderField_NoSort# = '#local.reportTotalLabel#' then 'zzzzzzzzzzz' 
													else #local.rowOrderField_NoSort# end,											
												ttlrow) AS row, 
						</cfif>
						*
					into ###local.finalTempTableName2#
					<cfif local.reportView NEQ "raw">
						from ###local.finalTempTableName#Reorg
					<cfelse>
						from ###local.finalTempTableName#
					</cfif>

					<cfif local.reportView EQ "raw">
						/* Remove duplicates */
						;WITH CTE AS ( 
							SELECT ROW_NUMBER() OVER 
							(PARTITION BY
										panel,
										[Panel Code],
										[Sub-Panel],
										[Sub-Panel Code],
										Source,
										feeTypeName,
										ReferralID,
										[Referral Date],
										[Referral Status],
										[Status Code],
										<cfif local.qryGetReferralSettings.allowFeeDiscrepancy and ListContainsNoCase("raw,customcsv",arguments.reportAction)>
										[Fee Discrepancy Status],
										</cfif>
										[Client First Name],
										[Client Middle Name],
										[Client Last Name],
										[Client Business Name],
										[Client Address 1],
										[Client Address 2],
										[Client City],
										[Client State],
										[Client Postal Code],
										[Client Email],
										[Client Home Phone],
										[Client Cell Phone],
										[Client Alternate Phone],
										memberID,
										[First Name],			
										[Last Name], 										
										[MemberNumber],
										Company,
										[Counselor Name],
										[Counselor Notes],
										[Legal Description],
										[Lawyer Notes],
										[Import Case Number],
										[Language]
							Order BY 
									ReferralID ASC
						) 
						AS RowNumber, 
								Source,
								feeTypeName,
								ReferralID,
								[Referral Date],									
								[Referral Status],
								<cfif local.qryGetReferralSettings.allowFeeDiscrepancy and ListContainsNoCase("raw,customcsv",arguments.reportAction)>
									[Fee Discrepancy Status],
								</cfif>
								[Client First Name],
								[Client Middle Name],
								[Client Last Name],
								[Client Business Name],
								[Client Address 1],
								[Client Address 2],
								[Client City],
								[Client State],
								[Client Postal Code],
								[Client Email],
								[Client Home Phone],
								[Client Cell Phone],
								[Client Alternate Phone],
								memberID,	
								[First Name],		
								[Last Name],								
								[MemberNumber],
								Company,
								[Counselor Name],
								[Counselor Notes],
								[Legal Description],
								[Lawyer Notes],
								[Import Case Number],
								[Language]
						FROM ###local.finalTempTableName2# tbl )
						DELETE FROM CTE Where RowNumber > 1;
					</cfif>

					IF OBJECT_ID('tempdb..#####local.finalTempTableName2#Final') IS NOT NULL
						DROP TABLE #####local.finalTempTableName2#Final;

					<cfif local.thisViewIncludesFieldsets>
						DECLARE @fsCols varchar(max);

						SELECT @fsCols = COALESCE(@fsCols + ', ', '') + quotename(fieldLabel)
						FROM (
							SELECT x.XmlCol.value('(@fieldLabel)[1]','VARCHAR(100)') AS fieldLabel
							FROM @outputFieldsXML.nodes('/fields/field') x(XmlCol)
						) tmp;
					</cfif>
					
					EXEC('SELECT MCROWID = ROW_NUMBER() OVER(order by <cfif local.reportView NEQ "raw">row<cfelse>ReferralID</cfif>), 
							<cfif local.reportView NEQ "raw">
									ttlrow,
									case 
										when (ttlrow = 99999998 AND #local.rowOrderField_NoSort# != ''#local.reportTotalLabel#'') then null 
										when ttlrow = 99999999 then null 
										else #local.rowOrderField_NoSort# end	
										AS [#local.rowOrderField_SQLSafe#],
									<cfif Len(local.otherFieldsToDisplay)>
										#local.otherFieldsToDisplay#,
									</cfif>
								<cfif local.includeDetails>
									case 
										when ttlrow = 99999998 then subCount 
										else null end 
										AS Count
								<cfelse>								
									subCount AS Count
								</cfif>
								
								<cfif local.thisViewIncludesFieldsets>
									, ' + @fsCols + '
								</cfif>
							<cfelse>
								tm.*
							</cfif>
							<cfif local.includeFeeData>
								<cfif local.reportView NEQ "raw">
									, [Total Referral Fees]
									, [Total Amount To Be Paid]
									, [Total Fees Collected by Attorney]
								</cfif>
							</cfif>
						INTO #####local.finalTempTableName2#Final
						FROM ###local.finalTempTableName2# tm;
					');
					
					<cfif local.reportView EQ "groupByPanel">
						delete from #####local.finalTempTableName2#Final where ttlrow is null;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupByPanelSubPanel">
						delete from #####local.finalTempTableName2#Final where ttlrow is null;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupByPanelWithDetail">
						delete from #####local.finalTempTableName2#Final where ttlrow >= 99999998;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow,Count")#
					<cfelseif local.reportView EQ "groupByMember">
						delete from #####local.finalTempTableName2#Final where ttlrow is null;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupByMemberWithDetail">
						delete from #####local.finalTempTableName2#Final where ttlrow >= 99999998;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow,Count")#
					<cfelseif local.reportView EQ "groupBySource">
						delete from #####local.finalTempTableName2#Final where ttlrow is null;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupBySourceWithDetail">
						delete from #####local.finalTempTableName2#Final where ttlrow >= 99999998;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow,Count")#
					<cfelseif local.reportView EQ "raw">
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupByCounselor">
						delete from #####local.finalTempTableName2#Final where ttlrow is null;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow")#
					<cfelseif local.reportView EQ "groupByCounselorWithDetail">
						delete from #####local.finalTempTableName2#Final where ttlrow >= 99999998;
						#generateFinalBCPTable(tblName="#####local.finalTempTableName2#Final", dropFields="MCROWID,ttlrow,Count")#
					</cfif>

					IF OBJECT_ID('tempdb..#####local.finalTempTableName2#Final') IS NOT NULL
						DROP TABLE #####local.finalTempTableName2#Final;
				<cfelse>
					select * <cfif local.thisViewIncludesFieldsets>, case when rowNum = 1 then @outputFieldsXML else null end as mc_outputFieldsXML</cfif>
					from ###local.finalTempTableName#Reorg
					order by rowNum;
				</cfif>

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_Payments') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_Payments;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_Payments2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_Payments2;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#Filtered') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#Filtered;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#CteClient') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#CteClient;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#ordTrans') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#ordTrans;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#Reorg') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#Reorg;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1reorg') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1reorg;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_2;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_2Final') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_2Final;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1_2') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1_2;
				IF OBJECT_ID('tempdb..###local.tempReportTableName#_1_2Final') IS NOT NULL
					DROP TABLE ###local.tempReportTableName#_1_2Final;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset local.errMsg = "Error Generating Data.">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
		<cfreturn local>
	</cffunction>
	
	<cffunction name="reorgDataLayout" access="private" output="false" returntype="string">
		<cfargument name="tempTableName" required="true" type="string" />
		<cfargument name="fieldsInSelectClause" required="true" type="string" />
		<cfargument name="fieldsInOrderClause" required="true" type="string" />
		<cfargument name="reportTotalLabel" required="false" type="string" default="Report Total" />
		<cfargument name="includeFeeData" required="false" type="boolean" default="false" />
		<cfargument name="feesFields" required="false" type="string" default="[Total Referral Fees], [Total Amount To Be Paid], [Total Fees Collected by Attorney]" />
		<cfargument name="hasDetails" required="false" type="boolean" default="false" />
		<cfargument name="hasFieldsets" required="false" type="boolean" default="false" />
		<cfargument name="reportView" required="false" type="string" default="" />
		
		<cfset var local = structNew() />
		
		<cfset local.feesFieldsWithSUM = "">
		
		<cfloop list="#arguments.feesFields#" index="local.feeField">
			<cfset local.feesFieldsWithSUM = ListAppend(local.feesFieldsWithSUM, "SUM(#local.feeField#)")>
		</cfloop>
		
		<cfset local.first2Fields = ListGetAt(arguments.fieldsInSelectClause, 1) & "," & ListGetAt(arguments.fieldsInSelectClause, 2)>
		<cfset local.rollupField = ListFirst(arguments.fieldsInSelectClause)>
		
		<cfsavecontent variable="local.returnSQL">
			
			<cfoutput>
				
			/* reorganize data rows to be in a format that can easily be formatted for screen, PDF, or CVS
			 *	99999998 = a 'Report Total' or subtotal row
			 *	99999999 = an empty row, for display purposes only
			 */
			
			<cfif arguments.hasDetails>
				
				<!--- subcount row --->
				insert into ###arguments.tempTableName# (ttlrow, subCount, #arguments.fieldsInSelectClause#
					<cfif arguments.includeFeeData>, #arguments.feesFields#</cfif>
					)
				select '99999998', sum(subCount), #arguments.fieldsInSelectClause#
					<cfif arguments.includeFeeData>, #local.feesFieldsWithSUM#</cfif>
				from ###arguments.tempTableName#
				group by #arguments.fieldsInSelectClause#
				
				<!--- blank row --->
				insert into ###arguments.tempTableName# (ttlrow, subCount, #arguments.fieldsInSelectClause#
					<cfif arguments.includeFeeData>, #arguments.feesFields#</cfif>
					)
				select '99999999', null, #arguments.fieldsInSelectClause#
					<cfif arguments.includeFeeData>, null, null, null</cfif>
				from ###arguments.tempTableName#
				group by #arguments.fieldsInSelectClause#
				
				<!--- GRAND TOTAL --->
				insert into ###arguments.tempTableName# (ttlrow, subCount, #local.first2Fields#
					<cfif arguments.includeFeeData>, #arguments.feesFields#</cfif>
					)
				select 99999998, sum(subCount), '99999998', '#arguments.reportTotalLabel#'
					<cfif arguments.includeFeeData>, #local.feesFieldsWithSUM#</cfif>
				from ###arguments.tempTableName#
				where ttlrow = 1
	
				<!--- return the new data table layout --->
				select *, ROW_NUMBER() OVER(order by case when #local.rollupField# = 99999998 then 0 else 1 end desc, 
				
					<cfif arguments.hasFieldsets>
						#arguments.fieldsInSelectClause#
						, case when ttlrow >= 99999998 then 0 else 1 end desc, #arguments.fieldsInOrderClause#
					<cfelse>
						#arguments.fieldsInOrderClause#
					</cfif>
					, subCount desc) as rowNum
				into ###arguments.tempTableName#Reorg
				from ###arguments.tempTableName#
			<cfelse>

				<!--- GRAND TOTAL --->	
				<cfif arguments.reportView neq "groupByPanelSubPanel">
					insert into ###arguments.tempTableName# (ttlrow,subCount, #local.first2Fields#
						<cfif arguments.includeFeeData>, #arguments.feesFields#</cfif>
						)
					select 99999998, sum(subCount), '99999998', '#arguments.reportTotalLabel#'
						<cfif arguments.includeFeeData>, #local.feesFieldsWithSUM#</cfif>
					from ###arguments.tempTableName#
					where ttlrow = 1
				</cfif>
				
				<!--- return the new data table layout --->
				select *, ROW_NUMBER() OVER(order by case when #local.rollupField# = 99999998 then 0 else 1 end desc, #arguments.fieldsInOrderClause#) as rownum
				into ###arguments.tempTableName#Reorg
				from ###arguments.tempTableName#
			</cfif>
			
			</cfoutput>
			
		</cfsavecontent>
		
		<cfreturn Trim(local.returnSQL)>
	</cffunction>
	
	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			
			<cfset local.reportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportview/text())")>
			<cfif local.reportView EQ "raw">
				<cfthrow detail="This report view is not supported." type="RptError">
			</cfif>

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m", 
				dropTblName="##tblReferralActivity_1")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<!--- get extra fields --->
			<cfset local.retainedCase = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/retainedcase/text())")>
			<cfset local.includeFeesData = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/includefeesdata/text())")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>
			<cfset local.referralDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldatefrom/text())")>
			<cfset local.referralDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldateto/text())")>
			<cfset local.callDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldatefrom/text())")>
			<cfset local.callDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldateto/text())")>

			<cfset local.isRetainedCaseChecked = local.retainedCase is 1>
			<cfset local.isIncludeFeesDataChecked = local.includeFeesData is 1>
			<cfset local.includeFeeData = local.isRetainedCaseChecked AND local.isIncludeFeesDataChecked>
		
			<cfset local.rsFieldsetReportViews = getReportViews(includeFieldsets=1)>
			<cfset local.thisViewIncludesFieldsets = listFindNoCase(valueList(local.rsFieldsetReportViews.type), local.reportView)>
		
			<cfset local.includeDetails = false>
			<cfif FindNoCase("WithDetail", local.reportView)>
				<cfset local.includeDetails = true>
			</cfif>
			<cfset local.strReturnValidation = validateReferralDateRange(referralDateFrom=local.referralDateFrom, referralDateTo=local.referralDateTo, callDateFrom=local.callDateFrom, callDateTo=local.callDateTo)>
			<cfif local.strReturnValidation.isErr EQ 1>
				<cfthrow message="#local.strReturnValidation.errMsg#">
			</cfif>
			
			<cfset local.Report = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			<cfset local.qry = local.Report.qryData>
			
			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfif local.qry.recordcount AND local.thisViewIncludesFieldsets>
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.qry.mc_outputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where fieldcodeSect NOT IN ('mc','m')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated')
				</cfquery>
			</cfif>
	
			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
					<div>Referral Date Range: #local.referralDateFrom# - #local.referralDateTo#</div>
					<br/>
				</cfoutput>

				<cfif local.qry.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfswitch expression="#local.reportView#">
						<cfcase value="groupByPanel">
							<cfset local.outputFieldsList = "Panel,Panel Code">
							<cfset local.groupedID = "PanelID">
							<cfset local.groupedColumn = "Panel">
						</cfcase>
						<cfcase value="groupByPanelSubPanel">
							<cfset local.outputFieldsList = "Sub-Panel,Sub-Panel Code">
							<cfset local.groupedID = "PanelID">
							<cfset local.groupedColumn = "Sub-Panel">
						</cfcase>
						<cfcase value="groupByPanelWithDetail">
							<cfset local.outputFieldsList = "ttlrow,Panel,Panel Code,Source,ReferralID,Referral Date,Referral Status,Status Code">
							
							<cfset local.groupedID = "PanelID">
							<cfset local.groupedColumn = "Panel">
						</cfcase>
						<cfcase value="groupBySource">
							<cfset local.outputFieldsList = "Source">
							<cfset local.groupedID = "SourceID">
							<cfset local.groupedColumn = "Source">
						</cfcase>
						<cfcase value="groupBySourceWithDetail">
							<cfset local.outputFieldsList = "ttlrow,Source,ReferralID,Referral Date,Referral Status,Status Code">
							<cfset local.groupedID = "SourceID">
							<cfset local.groupedColumn = "Source">
						</cfcase>
						<cfcase value="groupByMember">
							<cfset local.outputFieldsList = "Member">
							<cfset local.groupedID = "MemberID">
							<cfset local.groupedColumn = "Member">
						</cfcase>
						<cfcase value="groupByMemberWithDetail">
							<cfset local.outputFieldsList = "ttlrow,Member,Panel,Panel Code,Source,ReferralID,Referral Date,Referral Status,Status Code">
							<cfset local.groupedID = "MemberID">
							<cfset local.groupedColumn = "Member">
						</cfcase>
						<cfcase value="groupByCounselor">
							<cfset local.outputFieldsList = "Counselor Name">
							<cfset local.groupedID = "counselorsID">
							<cfset local.groupedColumn = "Counselor Name">
						</cfcase>
						<cfcase value="groupByCounselorWithDetail">
							<cfset local.outputFieldsList = "ttlrow,Counselor Name,ReferralID,Referral Date,Referral Status,Status Code">
							<cfset local.groupedID = "counselorsID">
							<cfset local.groupedColumn = "Counselor Name">
						</cfcase>
					</cfswitch>
					
					<cfif local.includeFeeData>
						<cfset local.feesFieldsList = "Total Referral Fees,Total Amount To Be Paid,Total Fees Collected by Attorney">
					<cfelse>
						<cfset local.feesFieldsList = "">
					</cfif>
					
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
						<tr>
							<!--- column headers --->
							<cfloop from="1" to="#ListLen(local.outputFieldsList)#" index="local.fieldNumber">
								<cfset local.fieldName = ListGetAt(local.outputFieldsList, local.fieldNumber)>
								<cfif local.fieldName NEQ "ttlrow">
									<th class="text-left"><b>#local.fieldName#</b></th>
								</cfif> 
							</cfloop>

							<th class="text-right"><b>Count</b></th>
							
							<cfif structKeyExists(local, "feesFieldsList")>
								<cfloop list="#local.feesFieldsList#" index="local.fieldName">
									<th class="text-right"><b>#local.fieldName#</b></th>
								</cfloop>
							</cfif>
						</tr>
					</thead>
					
					<!--- table of data --->
					<cfset local.resetGroupedFieldValue = "">
					<cfloop query="local.qry">
						
						<cfset local.groupedFieldValueIsReset = false>
						
						<cfif local.qry[local.groupedID][local.qry.CurrentRow] EQ "99999998">
							<cfset local.ttlRow = "font-weight-bold">
							<tr style="line-height:10px;"><td colspan="20">&nbsp;</td></tr>
						<cfelse>
							<cfset local.ttlRow = "">
						</cfif>
						
						<tr>
							
							<cfloop from="1" to="#ListLen(local.outputFieldsList)#" index="local.fieldNumber">
								<cfset local.fieldName = ListGetAt(local.outputFieldsList, local.fieldNumber)>
								<cfset local.fieldValue = local.qry[local.fieldName][local.qry.CurrentRow]>
								
								<cfif local.fieldName EQ local.groupedColumn>
									<cfset local.groupedFieldValue = local.fieldValue>
									
									<cfif local.resetGroupedFieldValue NEQ local.groupedFieldValue>
										<cfset local.groupedFieldValueIsReset = true>
									</cfif>
									
									<cfset local.resetGroupedFieldValue = local.groupedFieldValue>
								</cfif>
								
								<cfif local.groupedFieldValueIsReset IS false AND local.fieldName EQ local.groupedColumn>
									
									<cfif ListFindNoCase("99999998", local.qry.ttlrow[local.qry.CurrentRow])>
										<cfset local.ttlRow = "font-weight-bold">
									</cfif>
									<td class="#local.ttlRow#">&nbsp;</td>
								
								<cfelseif local.fieldName NEQ "ttlrow">
									<td class="align-top #local.ttlRow#">
										<cfif isDate(local.fieldValue)>
											#DateFormat(local.fieldValue, "m/d/yyyy")#
										<cfelse>
											<cfif local.fieldName eq "Member">
												<cfif arguments.reportAction eq "screen" and local.qry.memberid neq 99999998>
													<a href="#local.memberLink#&memberid=#local.qry.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qry["Extended MemberNumber"]#<cfelse>#local.qry["Extended Name"]#</cfif></a><br/>
												<cfelse>
													<b><cfif local.frmShowMemberNumber is 1>#local.qry["Extended MemberNumber"]#<cfelse>#local.qry["Extended Name"]#</cfif></b><br/>
												</cfif>
												<cfif local.frmShowCompany is 1 AND len(local.qry.company)>#local.qry.company#<br/></cfif>
											<cfelseif listFindNoCase("99999998", local.qry.ttlrow[local.qry.CurrentRow]) and listFindNoCase("groupByPanelSubPanel", local.reportView)>
												&nbsp;
											<cfelse>
												#local.qry[local.fieldName][local.qry.CurrentRow]#
											</cfif>
										</cfif>
										
										<!--- optional grouped fieldset data --->
										<cfif local.fieldName EQ local.groupedColumn 
												AND local.groupedFieldValueIsReset
												AND local.thisViewIncludesFieldsets>
										
											<div class="pl-3">
												<cfloop query="local.qryOutputFieldsForLoop">
													<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
														#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qry[local.qryOutputFieldsForLoop.fieldlabel][local.qry.currentrow])#<br/>
													<cfelseif len(local.qry[local.qryOutputFieldsForLoop.fieldLabel][local.qry.currentrow])>
														<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
															#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qry[local.qryOutputFieldsForLoop.fieldlabel][local.qry.currentrow], "m/d/yyyy")#<br/>
														<cfelse>
															#local.qryOutputFieldsForLoop.fieldlabel#: #local.qry[local.qryOutputFieldsForLoop.fieldlabel][local.qry.currentrow]#<br/>
														</cfif>
														
													</cfif>
												</cfloop>
											</div>
										</cfif>
									</td>
								</cfif>
							</cfloop>
							
							<td class="align-top text-right #local.ttlRow#">
								<cfif local.qry.ttlrow[local.qry.CurrentRow] EQ "99999998"
										OR local.qry[local.groupedID][local.qry.CurrentRow] EQ "99999998"
										OR NOT local.includeDetails>
									<cfif listFindNoCase("groupByPanel,groupByPanelSubPanel", local.reportView)>
										<cfif local.qry.ttlrow[local.qry.CurrentRow] EQ 1>
											<b>#local.qry.subCount#</b>
										<cfelseif local.qry.ttlrow[local.qry.CurrentRow] EQ "99999998" and listFindNoCase("groupByPanelSubPanel", local.reportView)>
											&nbsp;
										<cfelse>
											#local.qry.subCount#
										</cfif>
									<cfelse>
										#local.qry.subCount#
									</cfif>
								</cfif>
							</td>
							
							<cfif structKeyExists(local, "feesFieldsList")>
								<cfloop list="#local.feesFieldsList#" index="local.fieldName">
									<cfset local.fieldValue = local.qry[local.fieldName][local.qry.CurrentRow]>
									
									<td class="align-top text-right #local.ttlRow#">
										<cfif Len(local.fieldValue)>#dollarformat(local.fieldValue)#</cfif>
									</td>
								</cfloop>
							</cfif>
						</tr>
					</cfloop>
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.Report.qryData", strQryResult=local.Report.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>

		<cfcatch type="RptError">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
			<cfset local.strReturn.errMsg = cfcatch.message>
		</cfcatch>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m", 
				dropTblName="placeholder")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.reportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportview/text())")>
			<cfset local.referralDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldatefrom/text())")>
			<cfset local.referralDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/referraldateto/text())")>
			<cfset local.callDateFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldatefrom/text())")>
			<cfset local.callDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/calldateto/text())")>
			<cfset local.strReturnValidation = validateReferralDateRange(referralDateFrom=local.referralDateFrom, referralDateTo=local.referralDateTo, callDateFrom=local.callDateFrom, callDateTo=local.callDateTo)>
			<cfif local.strReturnValidation.isErr EQ 1>
				<cfthrow message="#local.strReturnValidation.errMsg#">
			</cfif>
			<cfscript>
			local.strReport = generateData(strSQLPrep=local.strSQLPrep, reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode);
			
			local.arrInitialReportSort = arrayNew(1);
			if (listFindNoCase("groupByPanel,groupByPanelWithDetail",local.reportView)) {
				local.strTemp = { field='Panel', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (listFindNoCase("groupByPanelSubPanel",local.reportView)) {
				local.strTemp = { field='[Sub-Panel]', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (listFindNoCase("groupByMember,groupByMemberWithDetail",local.reportView)) {
				local.strTemp = { field='Member', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (listFindNoCase("groupBySource,groupBySourceWithDetail",local.reportView)) {
				local.strTemp = { field='Source', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (local.reportView eq "raw") {
				local.strTemp = { field='ReferralID', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (listFindNoCase("groupByCounselor,groupByCounselorWithDetail",local.reportView)) {
				local.strTemp = { field='[Counselor Name]', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			}
			local.strReportQry = { qryReportFields=local.strReport.qryData, strQryResult=local.strReport.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
	
		<cfset var local = structNew()>
		
		<cfset local.reportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/reportview/text())")>
		<cfif local.reportView eq "groupByMemberWithDetail">
			<cfset local.pdfOrientation = "landscape">
		<cfelse>
			<cfset local.pdfOrientation = "portrait">
		</cfif>

		<cfreturn SUPER.pdfReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode, pdfOrientation=local.pdfOrientation)>
	</cffunction>

</cfcomponent>