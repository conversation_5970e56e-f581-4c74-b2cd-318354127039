<cfcomponent extends="model.admin.admin" output="no">
	<cfset this.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listConsentLists" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objEmailPreferences = createObject("component","emailPreferences");
			
			local.consentListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailPreferencesJSON&meth=getConsentLists&mode=stream";
			local.editConsentListTypeLink = buildCurrentLink(arguments.event,"editConsentListType") & '&mode=direct';
			local.editConsentListLink = buildCurrentLink(arguments.event,"editConsentList") & '&mode=direct';
			
			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');

			/* Opt-Out Lists */
			local.optOutListMembersLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailPreferencesJSON&meth=getConsentListMembers&listMode=Opt-Out&mode=stream";
			local.addMemberToOptOutListLink = "#buildCurrentLink(arguments.event,"addMemberToList")#&listMode=Opt-Out&mode=direct";
			local.deleteEntriesFromOptOutListLink = buildCurrentLink(arguments.event,"deleteEntriesFromConsentList") & '&listMode=Opt-Out&mode=stream';
			local.qryOptOutLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out");
			local.exportOptOutListMembersLink = buildCurrentLink(arguments.event,"exportConsentListMembers") & '&listMode=Opt-Out&mode=stream';

			/* Opt-In Lists */
			local.optInListMembersLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailPreferencesJSON&meth=getConsentListMembers&listMode=Opt-In&mode=stream";
			local.addMemberToOptInListLink = "#buildCurrentLink(arguments.event,"addMemberToList")#&listMode=Opt-In&mode=direct";
			local.deleteEntriesFromOptInListLink = buildCurrentLink(arguments.event,"deleteEntriesFromConsentList") & '&listMode=Opt-In&mode=stream';
			local.qryOptInLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-In");
			local.exportOptInListMembersLink = buildCurrentLink(arguments.event,"exportConsentListMembers") & '&listMode=Opt-In&mode=stream';

			local.qryOrgIdentities = application.objOrgInfo.getOrgIdentities(orgID=local.orgID);
			local.qryOrgConsentLists = application.objOrgInfo.getOrgConsentLists(orgID=local.orgID);
		
			/* Common Links */
			local.importConsentListMembersPromptLink = buildCurrentLink(arguments.event,"importConsentListMembersPrompt") & '&mode=direct';
			local.consentListMemberHistoryLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailPreferencesJSON&meth=getConsentListMemberHistory&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_consentListManagement.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editConsentListType" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryConsentListType = createObject("component","emailPreferences").getConsentListType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), consentListTypeID=arguments.event.getValue('tid',0))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_consentListType.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editConsentList" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objEmailPreferences = createObject("component","emailPreferences")>

		<cfset local.qryConsentList = local.objEmailPreferences.getConsentList(consentListID=arguments.event.getValue('lid',0))>
		<cfset local.qryOrgConsentListTypes = local.objEmailPreferences.getOrgConsentListTypes(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.qryConsentListModes = local.objEmailPreferences.getConsentListModes(modeNameList="Opt-In,Opt-Out")>
		<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="orgIdentityID", selectedValueID=val(local.qryConsentList.orgIdentityID), allowBlankOption=false)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_consentList.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addMemberToList" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objEmailPreferences = createObject("component","emailPreferences")>
		<cfset local.listMode = arguments.event.getValue('listMode')>
		<cfset local.qryConsentLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode=local.listMode)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.listMode EQ 'Opt-Out'>
					<cfinclude template="frm_optOutList.cfm">
				<cfelse>
					<cfinclude template="frm_optInList.cfm">
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getListMemberHistoryDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListMemberID" type="numeric" required="true">
		<cfargument name="listMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		
		<cftry>
			<cfset local.qryListMemberDetails = CreateObject("component","emailPreferences").getListMemberDetails(orgID=arguments.mcproxy_orgID, consentListMemberID=arguments.consentListMemberID)>
			<cfif local.qryListMemberDetails.recordCount>
				<cfsavecontent variable="local.returnStruct.data">
					<cfoutput> 
						<cfif val(local.qryListMemberDetails.memberID)>
							<div class="row mb-3">
								<div class="col-auto">Associated Member#local.qryListMemberDetails.recordCount gt 1 ? 's' : ''#:</div>
								<div class="col">
									<cfloop query="local.qryListMemberDetails">
										<a href='javascript:editMember(#local.qryListMemberDetails.memberID#)'>#local.qryListMemberDetails.memberDisplayName#</a>#local.qryListMemberDetails.currentRow NEQ local.qryListMemberDetails.recordCount ? '; ' : ''#
									</cfloop>
								</div>
							</div>
						</cfif>
						<div>
							<b><cfif arguments.listMode EQ 'Opt-Out'>Opt-Out<cfelse>Opt-In</cfif> List Member History</b>
							<div class="pt-1 my-2">
								<table id="consentListMemHistoryTable" class="table table-striped table-bordered" style="width:100%">
									<thead>
										<tr>
											<th>Date</th>
											<th>Action</th>
											<th>Email Address</th>
											<th>Entered By Member</th>
										</tr>
									</thead>
								</table>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteEntriesFromConsentList" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
			local.data = "The items could not be deleted. Contact MemberCentral for assistance.";
			local.objEmailPreferences = createObject("component","emailPreferences");
			local.listMode = arguments.event.getValue('listMode','');

			if(local.listMode EQ 'Opt-Out'){
				arguments.event.setValue('fConsentListId',arguments.event.getValue('fOptOutConsentListID',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptOutCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptOutCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptOutAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptOutAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptOutEmailAddressMode',''));
			} else {
				arguments.event.setValue('fConsentListId',arguments.event.getValue('fOptInConsentListID',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptInCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptInCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptInAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptInAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptInEmailAddressMode',''));
			}
		</cfscript>

		<cfset local.qryConsentListMembers = createObject("component","emailPreferences").getConsentListMembersFromFilters(event=arguments.event, operationMode="massDelete", listMode=local.listMode)>
		<cfif valueList(local.qryConsentListMembers.consentListMemberID) gt 0>
			<cfset local.result = local.objEmailPreferences.deleteConsentListMember(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), consentListMemberIDs=valueList(local.qryConsentListMembers.consentListMemberID))>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					<cfif local.listMode EQ 'Opt-Out'>
						reloadAndShowOptOutListMembersGrid();showOptOutListFilters();filterOptOutListsGrid();
					<cfelse>
						reloadAndShowOptInListMembersGrid();showOptInListFilters();filterOptInListsGrid();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportConsentListMembers" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.listMode = arguments.event.getValue('listMode');

			if(local.listMode EQ 'Opt-Out'){
				arguments.event.setValue('fConsentListId',arguments.event.getValue('fOptOutConsentListId',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptOutCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptOutCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptOutAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptOutAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptOutEmailAddressMode',''));

				local.reportFileName = "OptOutListMembers-#DateFormat(Now(),'yyyymmdd')#.csv";
			}
			else {
				arguments.event.setValue('fConsentListId',arguments.event.getValue('fOptInConsentListId',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptInCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptInCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptInAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptInAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptInEmailAddressMode',''));

				local.reportFileName = "OptInListMembers-#DateFormat(Now(),'yyyymmdd')#.csv";
			}
		</cfscript>

		<cfset local.listLink = buildCurrentLink(arguments.event,"listConsentLists")>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>
		
		<cfset local.qryConsentListMembers = createObject("component","emailPreferences").getConsentListMembersFromFilters(event=arguments.event, operationMode="exportEntries", listMode=local.listMode)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExportConsentListMembers">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..####tmpListMembersExport#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpListMembersExport#local.tmpSuffix#;
				
				DECLARE @orgID INT, @selectsql VARCHAR(max);
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				
				-- list members matching filters				
				SELECT mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' AS [List Member],
					l.consentListName as [List Name], lm.email as [List Email Address], FORMAT(lm.dateCreated,'M/dd/yyyy h:mm tt') + ' CT' AS [Date Added]
				INTO ####tmpListMembersExport#local.tmpSuffix#
				FROM platformMail.dbo.email_consentListMembers AS lm
				INNER JOIN platformMail.dbo.email_consentLists AS l ON l.consentListID = lm.consentListID
					AND l.[status] = 'A'
				INNER JOIN platformMail.dbo.email_consentListModes AS clm ON clm.consentListModeID = l.consentListModeID
				LEFT OUTER JOIN memberCentral.dbo.ams_memberEmails AS me 
						INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = me.memberID
						INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
						ON me.orgID = @orgID
						AND me.email = lm.email
				WHERE lm.consentListMemberID IN (0#ValueList(local.qryConsentListMembers.consentListMemberID)#)
				GROUP BY mActive.lastName, mActive.firstName, mActive.memberNumber, l.consentListName, lm.email, lm.dateCreated;

				SET @selectsql = '
					SELECT *, ROW_NUMBER() OVER(order by [List Email Address], [List Name], [Date Added]) as mcCSVorder
					*FROM* ####tmpListMembersExport#local.tmpSuffix#';

				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

				IF OBJECT_ID('tempdb..####tmpListMembersExport#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpListMembersExport#local.tmpSuffix#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#local.listLink#" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="importConsentListMembersPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('listMode','') is 'optin'>
			<cfset local.mode = 'Opt-In'>
		<cfelseif arguments.event.getValue('listMode','') is 'optout'>
			<cfset local.mode = 'Opt-Out'>
		<cfelse>
			<cfset local.mode = ''>
		</cfif>

		<cfset local.qryConsentLists = createObject("component","emailPreferences").getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode=local.mode)>
		<cfset local.importConsentListMembersLink = buildCurrentLink(arguments.event,"processConsentListMembersImport") & '&listMode=#arguments.event.getValue('listMode')#&mode=direct'>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importConsentListMembers.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processConsentListMembersImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objEmailPreferences = CreateObject("component","emailPreferences")>
		<cfset local.doAgainURL = local.importConsentListMembersPromptLink = buildCurrentLink(arguments.event,"importConsentListMembersPrompt") & '&listMode=#arguments.event.getValue('listMode')#&mode=direct'>

		<cfset local.refreshGridFn = 'closeBox'>
		<cfif arguments.event.getValue('listMode','') is 'optin'>
			<cfset local.refreshGridFn = 'reloadOptInTable'>
		<cfelseif arguments.event.getValue('listMode','') is 'optout'>
			<cfset local.refreshGridFn = 'reloadOptOutTable'>
		</cfif>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objEmailPreferences.importConsentListMembers(event=arguments.event)>
		<cfset local.data = local.objEmailPreferences.showImportResults(strResult=local.processResult, doAgainURL=local.doAgainURL, refreshGridFn=local.refreshGridFn)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageMemberConsentLists" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objEmailPreferences = CreateObject("component","emailPreferences")>

		<cfset local.listMode = arguments.event.getValue('listMode','Opt-Out')>
		<cfset local.memberID = arguments.event.getValue('mID',0)>
		<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		
		<cfset local.dtRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailPreferencesJSON&listMode=#local.listMode#&#local.listMode EQ 'Opt-Out' ? 'fOptOutAssignedMemberID' : 'fOptInAssignedMemberID'#=#local.memberID#&mode=stream">
		<cfset local.consentListMembersLink = "#local.dtRootLink#&meth=getConsentListMembers&opMode=memberConsentLists">
		<cfset local.consentListMemberHistoryLink = "#local.dtRootLink#&meth=getConsentListMemberHistory&opMode=memberConsentListHistory">
		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>

		<cfquery name="local.qryDistinctMemberEmails" dbtype="query">
			SELECT DISTINCT email
			FROM local.qryMemberEmails
			ORDER BY email
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_manageMemberConsentLists.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>