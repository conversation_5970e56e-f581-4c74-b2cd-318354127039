<cfsavecontent variable="local.swodSettingsJS">
	<cfoutput>
	<script type="text/javascript">
		function validateAndSaveAuthoritySWOD(){
			var arrReq = new Array();
			var authorityForm = $('##frmAuthoritySWOD');
			mca_hideAlert('ca_swod_frm_err');

			var swodPromptInterval = $('##swodPromptInterval').val();
			var mediaRequiredPct = $('##mediaRequiredPct').val();
			var daysToComplete = $('##daysToComplete').val();
			var mustAttendMinutes = $('##mustAttendMinutes').val();

			if (swodPromptInterval == '') arrReq[arrReq.length] = 'Enter the SeminarWeb On Demand prompt interval. Enter 0 if not required.<br/>';
			else if (isNaN(swodPromptInterval) || Number(swodPromptInterval) != parseInt(swodPromptInterval)) arrReq[arrReq.length] = 'Enter a valid SeminarWeb On Demand prompt interval. Enter 0 if not required.<br/>';

			if (mediaRequiredPct == '' || isNaN(mediaRequiredPct) || parseInt(mediaRequiredPct) < 0 || parseInt(mediaRequiredPct) > 100) arrReq[arrReq.length] = 'Enter a valid SeminarWeb On Demand media percentage requirement. Enter 0 if not required.<br/>';

			if (daysToComplete == '') arrReq[arrReq.length] = 'Enter the SeminarWeb On Demand days to complete. Enter 0 if not applicable.<br/>';
			else if (isNaN(daysToComplete) || Number(daysToComplete) != parseInt(daysToComplete)) arrReq[arrReq.length] = 'Enter a valid SeminarWeb On Demand days to complete. Enter 0 if not applicable.<br/>';

			if (mustAttendMinutes == '')  arrReq[arrReq.length] = 'Enter the SeminarWeb On Demand attendance length requirement. Enter 0 if there is no time requirement.<br/>';
			else if (isNaN(mustAttendMinutes) || Number(mustAttendMinutes) != parseInt(mustAttendMinutes)) arrReq[arrReq.length] = 'Enter a valid SeminarWeb On Demand attendance length requirement. Enter 0 if there is no time requirement.<br/>';     

			if(arrReq.length){
				mca_showAlert('ca_swod_frm_err', arrReq.join('<br/>'), true);
				return false;
			}

			var fd = authorityForm.serializeArray();			
			$('##divAuthoritySWODForm').hide();
			var loadingHTML = $('##divAuthoritySWODSaveLoading').html();

			$("##divAuthoritySWODFormSubmitArea").html(loadingHTML).show().load('#local.saveCreditAuthoritySWODLink#', fd, function() { onAuthoritySWODSaveComplete(); });
			$("html, body").animate({ scrollTop: 0 }, "slow");

			$('##btnSaveAuthoritySWOD').attr('disabled', true);
			return true;
		}
		function onAuthoritySWODSaveComplete() {
			editCreditAuthority(sw_creditauthorityid,'swodSettings');	
		}
		$(function() {
			mca_setupCalendarIcons('frmAuthoritySWOD');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.swodSettingsJS)#">
<cfoutput>

<div id="divAuthoritySWODForm">
	<form name="frmAuthoritySWOD" id="frmAuthoritySWOD">
		<div id="ca_swod_frm_err" class="alert alert-danger mb-2 d-none"></div>

		<h5 class="my-3">Requirements for Earning Credit with this Authority</h5>
		<div class="form-group row">
			<label for="swodPromptTypeID" class="col-sm-5 col-form-label-sm font-size-md">Type of Prompting Required *</label>
			<div class="col-sm-7">
				<select name="swodPromptTypeID" id="swodPromptTypeID" class="form-control form-control-sm">
					<option value="">No prompting required</option>
					<cfloop query="local.qryPromptTypes">
						<option value="#local.qryPromptTypes.promptTypeID#" <cfif local.qryPromptTypes.promptTypeID eq local.qryAuthority.SWODPromptTypeID>selected</cfif>>#local.qryPromptTypes.promptType#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<div class="form-group row">
			<label for="swodPromptInterval" class="col-sm-5 col-form-label-sm font-size-md">Prompting Interval (in minutes) *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="swodPromptInterval" id="swodPromptInterval" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.SWODpromptInterval)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if prompting is not required, enter 0 </i>)</span>
			</div>
		</div>

		<h5 class="my-3">Forms By Load Point</h5>

		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>PRETEST</b> - Must Pass all Exams to Earn Credit *</div>
			<div class="col-sm">
				<div class="form-check-inline">
					<input type="radio" name="preExamRequired" id="preExamRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.swodPreExamRequired) is 0>checked</cfif>>
					<label for="preExamRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="preExamRequired" id="preExamRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.swodPreExamRequired) is 1>checked</cfif>>
					<label for="preExamRequired_1" class="form-check-label">Yes, must pass all exams</label>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>POSTTEST</b> - Must Pass all Exams to Earn Credit *</div>
			<div class="col-sm">
				<div class="form-check-inline">
					<input type="radio" name="examRequired" id="examRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.swodExamRequired) is 0>checked</cfif>>
					<label for="examRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="examRequired" id="examRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.swodExamRequired) is 1>checked</cfif>>
					<label for="examRequired_1" class="form-check-label">Yes, must pass all exams</label>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5"><b>EVALUATION</b> - Must Complete all Evaluations to Earn Credit *</div>
			<div class="col-sm">
				<div class="form-check-inline">
					<input type="radio" name="evaluationRequired" id="evaluationRequired_0" value="0" class="form-check-input" <cfif val(local.qryAuthority.swodEvaluationRequired) is 0>checked</cfif>>
					<label for="evaluationRequired_0" class="form-check-label">No</label>
				</div>
				<div class="form-check-inline">
					<input type="radio" name="evaluationRequired" id="evaluationRequired_1" value="1" class="form-check-input" <cfif val(local.qryAuthority.swodEvaluationRequired) is 1>checked</cfif>>
					<label for="evaluationRequired_1" class="form-check-label">Yes, must complete all evaluations</label>
				</div>
			</div>
		</div>

		<h5 class="mt-4 mb-3">SWOD Settings</h5>

		<div class="form-group row">
			<label for="mediaRequiredPct" class="col-sm-5 col-form-label-sm font-size-md">Percentage that Each Video/Audio File must be Viewed *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="mediaRequiredPct" id="mediaRequiredPct" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.swodmediaRequiredPct)#" autocomplete="off"> %
				<span class="ml-2 text-nowrap">(<i> if no % requirement, enter 0 </i>)</span>
			</div>
		</div>		
		<div class="form-group row mt-3">
			<label for="daysToComplete" class="col-sm-5 col-form-label-sm font-size-md">Number of Days to Complete the Program to Earn Credit *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="daysToComplete" id="daysToComplete" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.swoddaysToComplete)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if N/A, enter 0 </i>)</span>
			</div>
		</div>
		<div class="form-group row">
			<label for="mustAttendMinutes" class="col-sm-5 col-form-label-sm font-size-md">Length of Time Must Attend to Earn Credit (in minutes) *</label>
			<div class="col-sm-7 d-flex align-items-center">
				<input type="text" name="mustAttendMinutes" id="mustAttendMinutes" class="form-control form-control-sm" maxlength="3" value="#val(local.qryAuthority.swodmustAttendMinutes)#" autocomplete="off">
				<span class="ml-2 text-nowrap">(<i> if no requirement, enter 0 </i>)</span>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col-sm-5">* indicates a required field</div>
			<div class="col-sm-7">
				<button type="button" id="btnSaveAuthoritySWOD" name="btnSaveAuthoritySWOD" class="btn btn-sm btn-primary" onclick="validateAndSaveAuthoritySWOD();">Save Details</button> 
				<button type="button" id="btnCancelAuthoritySWOD" name="btnCancelAuthoritySWOD" class="btn btn-sm btn-secondary" onclick="cancelAuthorityForm();">Cancel</button>
			</div>
		</div>
	</form>
</div>

<div id="divAuthoritySWODFormSubmitArea"></div>
<div id="divAuthoritySWODSaveLoading" class="d-none">
	<h4>Edit Credit Authority</h4>
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
</cfoutput>