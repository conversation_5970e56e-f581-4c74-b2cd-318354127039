ALTER PROC dbo.email_moveConsentList
@consentListID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orderNum int, @consentListTypeID int;
	
	SELECT @orderNum = orderNum, @consentListTypeID = consentListTypeID
	FROM dbo.email_consentLists
	WHERE consentListID = @consentListID
	AND [status] = 'A';

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum + 1
			WHERE consentListTypeID = @consentListTypeID
			AND orderNum >= @orderNum - 1
			AND [status] = 'A';
		
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum - 2
			WHERE consentListID = @consentListID
			AND [status] = 'A';
		END
		ELSE BEGIN
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum - 1
			WHERE consentListTypeID = @consentListTypeID
			AND orderNum <= @orderNum + 1
			AND [status] = 'A';

			UPDATE dbo.email_consentLists
			SET orderNum = orderNum + 2
			WHERE consentListID = @consentListID
			AND [status] = 'A';
		END

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH
GO
