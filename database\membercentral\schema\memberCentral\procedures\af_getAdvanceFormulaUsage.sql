ALTER PROC dbo.af_getAdvanceFormulaUsage
@afID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @siteID int;
	DECLARE @resourceTypes TABLE (usageName varchar(200), usageCount int);

	SELECT @siteID = siteID
	FROM dbo.af_advanceFormulas
	WHERE afID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Subscription Rates', COUNT(rateID)
	FROM dbo.sub_rates
	WHERE rateStartDateAFID = @afID
	OR rateEndDateAFID = @afID
	OR termStartDateAFID = @afID
	OR termEndDateAFID = @afID
	OR graceAFID = @afID
	OR recogStartDateAFID = @afID
	OR recogEndDateAFID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Custom Fields', COUNT(columnID)
	FROM dbo.ams_memberDataColumns
	WHERE linkedDateCompareDateAFID = @afID
	OR linkedDateAdvanceAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Email Blasts', COUNT(blastID)
	FROM dbo.email_EmailBlasts
	WHERE afID = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Group Assignment Conditions', COUNT(DISTINCT conditionID)
	FROM dbo.ams_virtualGroupConditionValues
	WHERE afID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'MyCE Pages', COUNT(clePageID)
	FROM dbo.cms_myCEPages
	WHERE creditFromAdvanceDateAFID = @afID
	OR creditToAdvanceDateAFID = @afID
	OR advanceDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Contribution Programs', COUNT(programID)
	FROM dbo.cp_programs
	WHERE advanceAFDateAFID = @afID
	OR nextAdvancementDateAFID = @afID
	OR nextPaymentDateAFID = @afID
	OR payThruDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Referral Scheduled Reports', COUNT(scheduleReportID)
	FROM dbo.ref_scheduledReports
	WHERE startDateAFID = @afID
	OR fromRefDateAFID = @afID
	OR toRefDateAFID = @afID
	OR cutoffDateAFID = @afID
	OR lastUpdatedDateAFID = @afID
	OR fromFollowUpDateAFID = @afID
	OR toFollowUpDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Events', COUNT(seriesID)
	FROM dbo.ev_recurringSeries
	WHERE afid = @afID;

	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Scheduled Tasks', COUNT(taskID)
	FROM dbo.scheduledTasks
	WHERE periodDateAFID = @afID;
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Saved Reports', COUNT(reportID)
	FROM dbo.rpt_SavedReports
	WHERE siteID = @siteID
	AND (
		-- report extra
		otherXML.exist('/report/extra/*[@afid = sql:variable("@afID")]') = 1
		-- report sub widget under d
		OR otherXML.exist('/report/subrule//condition/d/*[@afid = sql:variable("@afID")]') = 1
		-- report sub widget d
		OR otherXML.exist('/report/subrule//condition/d[@afid = sql:variable("@afID")]') = 1
	);
	
	INSERT INTO @resourceTypes (usageName, usageCount)
	SELECT 'Tasks Automations', COUNT(ta.automationID)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.runAFID = @afID
	OR ta.filterXML.exist('/filter/field[@afid = sql:variable("@afID")]') = 1
	OR ta.actionXML.exist('/action/field[@afid = sql:variable("@afID")]') = 1;

	DELETE FROM @resourceTypes WHERE usageCount = 0;

	SELECT usageName, usageCount
	FROM @resourceTypes
	ORDER BY usageCount DESC, usageName;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
