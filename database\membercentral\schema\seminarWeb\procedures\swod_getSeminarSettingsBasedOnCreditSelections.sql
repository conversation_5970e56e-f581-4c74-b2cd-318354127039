ALTER PROC dbo.swod_getSeminarSettingsBasedOnCreditSelections
@enrollmentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select 
		isnull(min(promptInterval),0) as promptInterval,
		isnull(min(promptTypeID),1) as promptTypeID,
		isnull(max(preExamRequired),0) as preExamRequired,
		isnull(max(examRequired),0) as examRequired,
		isnull(max(evaluationRequired),0) as evaluationRequired,
		isnull(max(mediaRequiredPct),0) as mediaRequiredPct,
		isnull(max(mustAttendMinutes),0) as mustAttendMinutes
	FROM (
		SELECT csaa.authorityID, 
			cast(casod.preexamRequired as smallint) as preexamRequired,
			cast(casod.examRequired as smallint) as examRequired,
			cast(casod.evaluationRequired as smallint) as evaluationRequired,
			casod.promptInterval, casod.promptTypeID, casod.mediaRequiredPct, 
			casod.mustAttendMinutes
		FROM dbo.tblEnrollmentsAndCredit AS eac 
		INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csaa ON sac.CSALinkID = csaa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON csaa.authorityID = casod.authorityID
		WHERE eac.enrollmentID = @enrollmentID
	) as authBest;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
