(function() {

	angular.module('module_1').controller('cmpltController', function ($scope, $rootScope, $http, $window, $timeout, cssData, dataBase, components, SeminarDataService,APIRequestService,AppManager,PostTestTimerService,TimerService) {
	var self = this;

	var loadPoint_postTest_complete = false;
	var loadPoint_evaluation_complete = false;
	var isPreTestPending = false;
	var isPostTestPending = false;
	var isEvaluationPending = false;

	$scope.evaluationObj ={};
	this.posttestObj ={};
	this.gradeObj = {};
	this.certificateURL = "";
	this.endOfSeminarText = "";
	this.mcq = [];
	this.evaluationScreenOrder = 1;
	this.showEvaluationLoading = 0;
	$rootScope.evaluationFormId = 0;
	$scope.evaluationSection = 0;
	$scope.failedExam = false;
	$scope.hidePercentScore = false;
	$scope.evaluationPage = 0;
	$scope.pageObj = {};
	$scope.evaluationAnswers = {};
	$scope.isEvaluationAnswerValid = false;
	this.fileRulesArray = null;
	$scope.seminarRulesArray = null;
	this.userAccepted = false;
	this.totalTimeSpentString;
	this.postTestFailed = false;
	$scope.hoursStepper = 0;
	$scope.minutesStepper =0;
	$scope.chancesLeft = 1;
	$scope.certifiedStatementMsg = 0;
	$scope.evaluationView = 0;
	this.retakeExam = false;
	$rootScope.maxTimeAllowed = 0;
	var maxTimeAllowedActualValue = 0;
	$rootScope.sessionCounter = 0;
	$rootScope.sessionPageNumber = 1;
	$rootScope.sessionStart = false;
	$rootScope.nextLoadPoint = 0;
	$rootScope.nextFormID = 0;
	$scope.strFormsMessage = '';
	$rootScope.closeConfirmPopUp = 1;
	
		/*getting json data*/
		dataBase.jsonData.then(function (data) {
			$scope.data = data.data.tabs.tab4;
		});

		// call init() on tab-click from main-controller
		$scope.init = function() {
			if (AppManager.isIpad) {
				angular.element('.seminarCompleteProcess .returnScreen .img').css('right','-26px');
			}
			if (self.postTestLoaded) {
				AppManager.testInProgress = true;
				if(PostTestTimerService.resumeTimer > 0){
					PostTestTimerService.time = PostTestTimerService.resumeTimer;
					if(PostTestTimerService.timerStatus == 0){					
						PostTestTimerService.start();	
					}	
				}
				return;
			}
            APIRequestService.getSeminarforPlayer().then(function (response) {
				if(response.userLoggedIn == 1) {
					self.supportPhone = AppManager.seminarDetailsObj.supportPhone;
					self.supportEmail = AppManager.seminarDetailsObj.supportEmail;
					self.certificateURL = AppManager.seminarDetailsObj.certificateURL;
					self.endOfSeminarText = AppManager.seminarDetailsObj.endOfSeminarText;
					self.fileRulesArray = AppManager.seminarCompletionCheckObj.fileRulesArray[0];
					self.totalTimeSpentString = AppManager.totalTimeSpentString;
					$scope.seminarRulesArray = AppManager.seminarCompletionCheckObj.seminarRulesArray;
					var mateiralLen = SeminarDataService.materialPairs.length;
						
					$scope.replayVideoLink = response.returnData.replayVideoLink;
					$scope.canViewReplay = response.returnData.canViewReplay;
					var total = 0;
					for (var i=0; i<mateiralLen; i++) {
						var materialType = SeminarDataService.materialPairs[i].type;
						// show viewed percent for video and audio only
						if(materialType.indexOf('video') > -1 || materialType.indexOf('audio') > -1) {
							//show mateiral percent
							var streamAccessDetails = SeminarDataService.materialPairs[i].accessDetails.join("");
							if (streamAccessDetails.lastIndexOf('1') > -1) {
								var noOfOccurencesOfOne = 0;
								var materialAccessDetails = SeminarDataService.materialPairs[i].accessDetails;
								for (var k=0; k<materialAccessDetails.length; k++) {
									if (materialAccessDetails[k] == 1) {
										noOfOccurencesOfOne++;
									}
								}
								var curPercent =  (noOfOccurencesOfOne / streamAccessDetails.length) *100;
								$rootScope.materialsCompletedPercent[i] = Math.round(curPercent);
								total += Math.round(curPercent);
							}
						}
					}

					$rootScope.materialsCompletedPercentAvg = total/$rootScope.materialsCompletedPercent.length;
					
					isPreTestPending 	= (AppManager.semninarHasPreTest == 1  && AppManager.allPreTestCompleted  == 0) ? true : false;
					isPostTestPending 	= (AppManager.semninarHasPostTest == 1 && AppManager.allPostTestCompleted == 0) ? true : false;
					isEvaluationPending = (AppManager.semninarHasEvaluation == 1 && AppManager.allEvaluationCompleted == 0) ? true : false;

					//console.log("isPreTestPending   " + isPreTestPending + "isPostTestPending  " + isPostTestPending  + "isEvaluationPending  " + isEvaluationPending)
					
					if(AppManager.swlLaunch != 0){
						var mediaRequiredPct = AppManager.seminarCompletionCheckObj.mediaRequiredPct;
						if (AppManager.isSeminarFinalCompleted == 1) {
							processCompletion(false);
						} else if ((AppManager.isCompleted == 1) && (AppManager.allPostTestCompleted) && (AppManager.allEvaluationCompleted)) {
							// FinalCheckSeminarforCompletion.
							processCompletion();
						} else if ((AppManager.isCompleted == 1) && (AppManager.allPostTestCompleted) && (!AppManager.allEvaluationCompleted)) {
							startLoadPoint('evaluation');
						} else if ((AppManager.isCompleted == 1) && (!AppManager.allPostTestCompleted)) {
							if (!self.postTestFailed || AppManager.allPostTestCompleted == false) {
								startLoadPoint('posttest');
							} else {
								$scope.mcqShow = false;
								if(AppManager.seminarDetailsObj.offerCertificate == 1) {
									processCompletion();
								} else {
									angular.element('.seminarCompleteProcess .elements').displayHideShow('.failureScreen');								
								}
								angular.element('#right .tab').removeClass('disable');
							}
						} else {						
							angular.element('.seminarCompleteProcess .elements').displayHideShow('.returnScreen');	
							AppManager.checkCompletionForSeminar();
						}
					}
				} else{
					AppManager.disconnectedFromServer();
				}
			});
		}	

		function startLoadPoint(loadpoint) {
			// pause media during posttest, and evaluation.
			try {videoJS.mediaPause()} catch (e) {}	
			if( loadpoint.toLowerCase() == 'posttest' ) {
				APIRequestService.loadFormsByLoadPoint( loadpoint ).then( function( data ) {
					if( data.userLoggedIn == 1 ) {
						if ( data.returnData.examObj[0] || data.returnData.strFormsMessage[0]) {
							$scope.attemptsRemaining = '';
								if ( data.returnData.examObj[0] ) {
									$scope.attemptsRemaining = data.returnData.examObj[0].ATTEMPTSREMAINING;
								}else if ( data.returnData.strFormsMessage[0] ) {
									$scope.attemptsRemaining = data.returnData.strFormsMessage[0].ATTEMPTSREMAINING;
								}

							
							if($scope.attemptsRemaining < -1 || $scope.attemptsRemaining == 0){
								showExamFail(data);		
							}else{
								$scope.isRequired = data.returnData.examObj[0].ISREQUIRED;
								AppManager.testInProgress = true;
								var formID = data.returnData.examObj[0].FORMID;
								self.showImmediateAnswer = data.returnData.examObj[0].SHOWIMMEDIATEANSWER;
								$scope.showGradeButton = self.showImmediateAnswer;
								$scope.isRequired = data.returnData.examObj[0].ISREQUIRED;
								$rootScope.maxTimeAllowed = data.returnData.examObj[0].MAXMINUTESALLOWED;
								$scope.certifiedStatementMsg = data.returnData.examObj[0].CERTIFIEDSTATEMENT;
								$scope.allowSkipBackward = data.returnData.examObj[0].ALLOWSKIPBACKWARD;
								self.ENFORCEQREQSTATUSCORRECTMAXTRIES = data.returnData.examObj[0].ENFORCEQREQSTATUSCORRECTMAXTRIES;
								self.MAXTRIESPERQUESTION = data.returnData.examObj[0].MAXTRIESPERQUESTION;
								self.blankOnInactivity = 1;//Always show pop up while focus away from exam
								maxTimeAllowedActualValue = $rootScope.maxTimeAllowed;
								$rootScope.nextLoadPoint=loadpoint;
								$rootScope.nextFormID=formID;
								if(data.returnData.examObj[0].OVERRIDEMESSAGE != undefined && data.returnData.examObj[0].OVERRIDEMESSAGE != null && data.returnData.examObj[0].OVERRIDEMESSAGE != ''){
										self.strFormsMessage = data.returnData.examObj[0].OVERRIDEMESSAGE;
									}else{
										self.strFormsMessage = '';
									}
								//if (!self.retakeExam) {
									if(seminarType != 'swl'){
										angular.element( '.seminarCompleteProcess .elements' ).displayHideShow( '.continueScreen' );
									}else{
										
										angular.element('.seminarCompleteProcess .elements').displayHideShow('.startScreen'); 
									}
									self.posttestObj.examObj = data.returnData.examObj[0];
									self.posttestObj.examObj.formtitle = self.posttestObj.examObj.FORMTITLE;
									self.posttestObj.examObj.formintro = self.posttestObj.examObj.FORMINTRO;
								//}	
							}
						} else if( isEvaluationPending ) {
							startLoadPoint( 'evaluation' );
						} else {
							if( AppManager.allPostTestCompleted == false ) {
								showExamFail(data);								
							} else {
								processCompletion();
							}
						}
					} else {
						AppManager.disconnectedFromServer();
					}
				})
			} else if( loadpoint.toLowerCase() == 'evaluation' ) {
				self.showEvaluationLoading = 1;
				AppManager.testInProgress = true;
				if(seminarType == 'swl'){
					$rootScope.currentLoadPoint = 'evaluation';
				}
				angular.element( '.seminarCompleteProcess .elements' ).displayHideShow( '.evaluationScreen' );
				APIRequestService.loadFormsByLoadPoint( loadpoint ).then( function( data ) {
					if( data.userLoggedIn == 1 ) {
						if( data.returnData.examObj.length > 0 ) {
							$rootScope.evaluationFormId = data.returnData.examObj[0].FORMID ;
							var promise = APIRequestService.loadForm( loadpoint,data.returnData.examObj[0].FORMID );
							promise.then( function( data ) {
								self.showEvaluationLoading = 0;
								$scope.evaluationObj = data.returnData.examObj;
								if( data.returnData.examObj.sectionObj ) {
									self.populateEvaluation();         		
								}
							});
						} else {
							if(seminarType == 'swl'){
								processCompletion();
							}else{
								self.showEvaluationLoading = 0;
								processCompletion();
							}
						}
					} else {
						AppManager.disconnectedFromServer();
					}
				});
			}
		}
		function showExamFail(data) {
			if ( data.returnData.strFormsMessage[0] ) {
				if(data.returnData.strFormsMessage[0].OVERRIDEMESSAGE != undefined && data.returnData.strFormsMessage[0].OVERRIDEMESSAGE != null && data.returnData.strFormsMessage[0].OVERRIDEMESSAGE != ''){
					self.strFormsMessage = data.returnData.strFormsMessage[0].OVERRIDEMESSAGE;
				}else{
					self.strFormsMessage = '';
				}
			}else{
				self.strFormsMessage = '';
			} 
			
			$scope.mcqShow = false;
			$scope.failedExam = true;
			self.postTestFailed = true;
			$scope.hidePercentScore =true;								
			angular.element( '.seminarCompleteProcess .elements' ).displayHideShow( '.scoreScreen' );
			
			if(data.returnData.strFormsMessage[0].NUMCORRECT != undefined && data.returnData.strFormsMessage[0].NUMGIVEN != undefined && data.returnData.strFormsMessage[0].PASSINGPCT != undefined){
				setTimeout(function(){
					angular.element( '.scoreData span' ).remove();
					angular.element( '.scoreData' ).prepend( '<span>You correctly answered '+data.returnData.strFormsMessage[0].NUMCORRECT+' out of '+data.returnData.strFormsMessage[0].NUMGIVEN+' questions for a score of '+data.returnData.strFormsMessage[0].PASSINGPCT+'%.<br/><span>' );
					
				},500);
			}
		}
		function processCompletion(runCheck) {
			var runCheck = typeof runCheck !== 'undefined' ? runCheck : true;
			self.hideAllPatches();
			if (runCheck) {
				if(seminarType != 'swl'){
					AppManager.finalCheckSeminarforCompletion(AppManager.userReportedTimeSpent);
				}
			}
			if (AppManager.seminarDetailsObj.offerCertificate == 1) {
				showCertificate();
			} else {
				showComplete();
			}
			$rootScope.closeConfirmPopUp = 1;
			$rootScope.sideBarClick = 0;
			sideBarScope = angular.element('.sidebar-controller').scope();
			if(sideBarScope != undefined){
				sideBarScope.init();
			}
		}

		function showCertificate() {
			angular.element('.seminarCompleteProcess .elements').displayHideShow('.successScreen');
			var objParams = {enrollmentID:AppManager.enrollmentID,emailToUse:AppManager.email,signUpOrgCode:AppManager.signuporgcode};
			var promise = APIRequestService.emailCertificate(objParams);
			promise.then( function( data ) {});				
		}

		function showComplete() {
			if(seminarType == 'swl'){
				angular.element('.seminarCompleteProcess .elements').displayHideShow('.successScreenNoCertSwl');
			}else{
				angular.element('.seminarCompleteProcess .elements').displayHideShow('.successScreenNoCert');				
			}
		}

		function populateQuestions(data) {
			self.postTestLoaded = true;
			self.mcq=[];
			$scope.sectionNoArray = [];
			$rootScope.sessionPageIndex = [];
			$scope.sectionObj = data.returnData.examObj.sectionObj;
			var sectionLength = $scope.sectionObj.length;
			for (var z=0; z<sectionLength; z++) {
				var qLen = $scope.sectionObj[z].pagesArr.length;
				for (var i=0; i<qLen; i++) {
					var qObj ={};
					var ques = $scope.sectionObj[z].pagesArr[i];
					qObj.question = ques.questiontext;
					qObj.options =[];
					for (var j=0; j<ques.optionsarr.length; j++) {
							qObj.options.push({optionid:ques.optionsarr[j].optionid,optiontext:ques.optionsarr[j].optiontext});
					}
					qObj.responseid = data.returnData.examObj.responseid;
					qObj.controlfield = ques.controlfield;
					self.mcq[self.mcq.length]= qObj;
				}
				$scope.sectionNoArray.push(self.mcq.length);
				$rootScope.sessionPageIndex.push(qLen);
			}
			$scope.mcqLength = self.mcq.length;
			$rootScope.postTestPercent = data.returnData.examObj.passingpct;
			//console.log($scope);
			//console.log($rootScope);
		}

		this.populateEvaluation = function() {
			$scope.pageObj = $scope.evaluationObj.sectionObj[$scope.evaluationSection].pagesArr[$scope.evaluationPage];
			$scope.evaluationView = $scope.pageObj.questiontypeid;
			if ($scope.evaluationView == 3 || $scope.evaluationView == 7) {
				var optionsarr = $scope.pageObj.optionsarr;

				$scope.liveSeminarRows=[];
				$scope.liveSeminarCols=[];

				angular.forEach(optionsarr, function (option,i) {
					var rowid = "q_" + $scope.pageObj.questionid + "_" + $scope.pageObj.questiontypeid +"_" + option.optionid + "_opt"; 
					$scope.liveSeminarRows.push({optionid: option.optionid, id:rowid, rowtext:option.optiontext, showinput:option.showinput, inputtext:option.inputtext});
				});      

				var optionsxArr= optionsarr[0].optionsxArr;
				angular.forEach(optionsxArr, function (optionsx,j) {
					$scope.liveSeminarCols.push(optionsx);
				})
			}
			$scope.checkEvaluationAnswer();
		}	
		/*Evaluation Screen Navigation*/
		$scope.evaluationNextClick = function (e) {
			var _this = e.currentTarget;
			self.evaluationStepSkip = false;
			if (!angular.element(_this).hasClass('disabled')) {
				var responseArray= [];
				var responseid = $scope.evaluationObj.responseid;
				var textName = "";
				var tempObj = {};
				switch (parseInt($scope.pageObj.questiontypeid)) {
					case 1:
					case 2:
						textName = "q_" + $scope.pageObj.questionid + "_" + $scope.pageObj.questiontypeid + "_txt";
						tempObj = {name:textName, value:$scope.evaluationAnswers[$scope.pageObj.questionid]};
						responseArray.push(tempObj);
						break;
					case 3:
						textName = "q_" + $scope.pageObj.questionid + "_" + $scope.pageObj.questiontypeid + "_opt";
						tempObj = {name:textName, value:$scope.evaluationAnswers[$scope.pageObj.questionid]};
						responseArray.push(tempObj);
						if (angular.isDefined($scope.evaluationAnswers[$scope.evaluationAnswers[$scope.pageObj.questionid]])) {
							textName = "q_" + $scope.pageObj.questionid + "_" + $scope.pageObj.questiontypeid + "_" + $scope.evaluationAnswers[$scope.pageObj.questionid] + "_txt";
							tempObj = {name:textName, value:$scope.evaluationAnswers[$scope.evaluationAnswers[$scope.pageObj.questionid]]};
							responseArray.push(tempObj);
						}
						break;
					case 7:
						$("#matrixView input[type='radio']:checked").each(function() {
							var tempObj = {name:$(this).attr('name'), value:$(this).val()};
							responseArray.push(tempObj);
						})
						break;
					default:
				}

				var objParams = {json: JSON.stringify({responseID:responseid, responseArray:responseArray})};
				var promise = APIRequestService.saveFormResponse(objParams);
				promise.then( function( data ) {
					if( data.success == true ) {
						console.log( "Successfully saved your response!");
					} else {
						console.log("Error: We encountered a problem saving your response. Try again.");
					}
				});

				if (($scope.evaluationPage + 1) < $scope.evaluationObj.sectionObj[$scope.evaluationSection].pagesArr.length) {
					$scope.evaluationPage++;
					self.evaluationScreenOrder++;
					self.populateEvaluation();
				} else if (($scope.evaluationSection + 1) < $scope.evaluationObj.sectionObj.length) {
					$scope.evaluationSection++;
					$scope.evaluationPage=0;
					self.populateEvaluation();
				} else {
					if(seminarType != 'swl'){
						$scope.evaluationSubmit();
					}else{
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.successScreenSWLEval');		
					}
				}
			}
			if(self.evaluationScreenOrder == 3 && ($scope.evaluationObj.formtitle.length == 0 || $scope.evaluationObj.sectionObj[$scope.evaluationSection].sectiontitle.length == 0)){
				self.evaluationStepSkip = true;
			}	
		}

		$scope.evaluationPreviousClick = function (e) {
			var _this = e.currentTarget;
			if (!angular.element(_this).hasClass('disabled')) {

				

				if ($scope.evaluationPage > 0) {
					$scope.evaluationPage--;
					self.evaluationScreenOrder--;
					self.populateEvaluation();
					if(self.evaluationScreenOrder == 3 && ($scope.evaluationObj.formtitle.length == 0 || $scope.evaluationObj.sectionObj[$scope.evaluationSection].sectiontitle.length == 0)){
						self.evaluationStepSkip = true;
					}
				} else {
					if($scope.evaluationObj.formtitle.length!=0 && $scope.evaluationObj.sectionObj[$scope.evaluationSection].sectiontitle.length != 0) {
						self.evaluationScreenOrder = 2;
					} else { 
						self.evaluationStepSkip = true;
						self.evaluationScreenOrder = 1;
					}
				}
			}
		}
		$scope.evaluationPreviousClickAfterComplete = function (e) {
			$scope.evaluationSection = $scope.evaluationObj.sectionObj.length-1;
			$scope.evaluationPage =   $scope.evaluationObj.sectionObj[$scope.evaluationSection].pagesArr.length-1;
			self.populateEvaluation();
			
		}
		
		$scope.evaluationSubmit = function () {
			var responseid = $scope.evaluationObj.responseid;
			APIRequestService.gradeExam(responseid).then(function (data) {
				if( data.userLoggedIn == 1 ) {
					AppManager.addActivityLogEntry("User pressed Exam Grade Page Button",false);
					AppManager.allEvaluationCompleted = 1;
					AppManager.testInProgress = false;
					self.hideAllPatches();
					if(seminarType != 'swl'){
						APIRequestService.completeSeminarAfterEvaluation($rootScope.evaluationFormId,$scope.evaluationObj.responseid).then(function (data) {});
						$rootScope.closeConfirmPopUp = 1;
					}
				} else {
					AppManager.disconnectedFromServer();
				}
			});
			processCompletion();
		}

		$scope.checkEvaluationAnswer = function() {
			if ($scope.pageObj.isrequired) {
				if ($scope.pageObj.questiontypeid == 7) {
					for (var i = 0, len = $scope.liveSeminarRows.length; i < len; i++) {
						if (!angular.isDefined($scope.evaluationAnswers[$scope.liveSeminarRows[i].optionid])) {
							$scope.isEvaluationAnswerValid = false;
							break;
						}
						$scope.isEvaluationAnswerValid = true;
					}
				} else if ($scope.pageObj.questiontypeid == 3) {
					if (angular.isDefined($scope.evaluationAnswers[$scope.pageObj.questionid])) {
						$scope.isEvaluationAnswerValid = true;
					} else {
						$scope.isEvaluationAnswerValid = false;
					}
				} else {
					if (angular.isDefined($scope.evaluationAnswers[$scope.pageObj.questionid]) && $scope.evaluationAnswers[$scope.pageObj.questionid] != '' ) {
						$scope.isEvaluationAnswerValid = true;
					} else {
						$scope.isEvaluationAnswerValid = false;
					}
				}
			} else {
				$scope.isEvaluationAnswerValid = true;
			}
		}

		/*'Continue to Next Step' button*/
		$scope.continueClick = function (e) {
			var _this = e.currentTarget;
			$scope.chances = $scope.chancesLeft;
			self.showAllPatches();
			angular.element('.seminarCompleteProcess .elements').displayHideShow('.startScreen');  
			$rootScope.closeConfirmPopUp = 0;          
		}
		/*Start button*/
		$scope.startClick = function (e) {
			var startProceed = 0;
			if($scope.certifiedStatementAccepted != undefined && $scope.certifiedStatementAccepted){
				
				var startProceed = 1;
			}else if($scope.certifiedStatementMsg.trim().length == 0){
				var startProceed = 1;
				
			}
			if(startProceed == 1){
				var promise = APIRequestService.loadForm($rootScope.nextLoadPoint,$rootScope.nextFormID);
				promise.then (function (data) {
					if( data.userLoggedIn == 1 ) {
						AppManager.addActivityLogEntry("User started the exam",false);
						self.posttestObj = data.returnData;
						if(TimerService.timespent == 0){
							TimerService.start();
						}
						if (data.returnData.examObj.formid) {
							populateQuestions(data);
							//if (!self.retakeExam) {
								continueStart(e);
							//}
						} else if (isEvaluationPending) {
							startLoadPoint('evaluation');
						} else {
							processCompletion();
						}    
					} else {
						AppManager.disconnectedFromServer();
					}
				});
			
			}
		}

		function continueStart(e) {
			if (maxTimeAllowedActualValue) {
                PostTestTimerService.time = maxTimeAllowedActualValue*60;
				if(PostTestTimerService.timerStatus == 0){					
					PostTestTimerService.start();	
				}			                	
            }
			var _this = e.currentTarget;			
			if (!angular.element(_this).hasClass('disable')) {
				$rootScope.sectionEnd = false;
				$rootScope.sessionStart = true;
		
				if(self.posttestObj.examObj.formtitle.length == 0 || self.posttestObj.examObj.sectionObj[$rootScope.sessionCounter].sectiontitle.length == 0 )
					$scope.sessionEndNextClick();
				else angular.element('.seminarCompleteProcess .elements').displayHideShow('#sectionAfterEnd');
			
				self.showAllPatches();
				AppManager.testInProgress = true;				
			}
		}

		/* Next Button */
		$scope.nextClick = function (e) {
			var _this = e.currentTarget;
			var evnt = {
				currentTarget: angular.element('.seminarCompleteProcess .elements .retake')
			}
			$scope.retakeClick(evnt);
			self.showAllPatches();
		}
		self.validateQtnsAnswered = function() {
			var mcqAnsweredLength = 0; 
			$scope.attemptedQuestionCount = 0;
			angular.element('.seminarCompleteProcess #postTest .mcq').each(function() {
				if ($(this).attr('validationDone')) {
					mcqAnsweredLength += 1; 
					$scope.attemptedQuestionCount++;
				}
			});
			return true;
		}
		$scope.qtnsAnsweredPrevClick = function() {
			$scope.postTestQnIndex--;
			angular.element('.seminarCompleteProcess .elements').displayHideShow('#postTest');
			angular.element('.seminarCompleteProcess #postTest .mcq').addRemoveClassI('displayHide', $scope.postTestQnIndex-1);
			var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));
		}
		$scope.sessionEndPrevClick = function() {
			$rootScope.sessionPageNumber = ($scope.sectionNoArray[$rootScope.sessionCounter - 1])
			$scope.postTestQnIndex--;
			$rootScope.sessionCounter--;
			angular.element('.seminarCompleteProcess .elements').displayHideShow('#postTest');
			angular.element('.seminarCompleteProcess #postTest .mcq').addRemoveClassI('displayHide', $scope.postTestQnIndex-1);
			var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));
			
		}
		$scope.sessionEndNextClick = function() {
			var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));
			$rootScope.sessionPageNumber = 0;
			$rootScope.sessionPageNumber++;
			if ($rootScope.sessionCounter == 0) {
				$scope.postTestQnIndex = 1;
			}
			if ($(".checkBox").hasClass('select')) {
				angular.element('.seminarCompleteProcess #postTest .nextPage').removeClass('disable');
			}
			if (angular.element('.seminarCompleteProcess #postTest .mcq').find('.checkBox').hasClass('select')) {
				angular.element('.seminarCompleteProcess #postTest .gradePage').removeClass('disable');
			}
			angular.element('.seminarCompleteProcess .elements').displayHideShow('#postTest');
			angular.element('.seminarCompleteProcess #postTest .mcq').addRemoveClassI('displayHide', $scope.postTestQnIndex-1);
		}

		
        /*Invoked from Appmanager*/
		$rootScope.setSWLExam = function () {
			$rootScope.showExamDisp = 0;
			angular.element('#swlCompleteWrap .seminarCompleteProcess.elementsSem').removeClass('displayHide').removeClass('hide');
			var scope = angular.element('#swlCompleteWrap .seminarCompleteProcess').scope();
			angular.element('#swlCompleteWrap .seminarCompleteProcess .closeExamPopup').remove();
			_height = angular.element('.playerWell').height() - 10;
			angular.element('#swlCompleteWrap .content-popup').height(_height+'px');
			AppManager.swlLaunch = 1;
			scope.init();  
			setTimeout(function(){
				hideShowPopUpElements();
			},1000);
			if( angular.element(window).width() < 1024){
				angular.element(document.getElementById("tab_0")).trigger('click'); 
			}
		}
		// Calling on posttest Timer	
		$rootScope.callGradeExam = function() {
		
			$scope.postTestTimeElapsed = PostTestTimerService.time;
			
			APIRequestService.gradeExam(self.posttestObj.examObj.responseid).then(function (data) {
				if( data.userLoggedIn == 1 ) {
					$scope.failedExam = false;
					self.gradeObj = data.returnData;
					AppManager.addActivityLogEntry("User pressed Exam Grade Page Button",false);
					$scope.chancesLeft = self.gradeObj.attemptsremaining;       			
					AppManager.checkCompletionForSeminar();
					AppManager.testInProgress = false;
					if (self.gradeObj.passfail == "Failed" && $scope.chancesLeft > 0) {
						$scope.mcqShow = false;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.retakeScreen');
					} else if (self.gradeObj.passfail == "Failed" && $scope.chancesLeft == 0) {
						$scope.mcqShow = false;
						$scope.failedExam = true;
						self.postTestFailed = true;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.scoreScreen');
					} else if (self.gradeObj.passfail == "Passed") {
						self.postTestLoaded = false;
							angular.element('.seminarCompleteProcess .elements').displayHideShow('.scoreScreen');
							angular.element('.seminarCompleteProcess .hidePercentScoreTitle').displayHide();
					}
					self.hideAllPatches();					
				} else {
					AppManager.disconnectedFromServer();
				}
			});
		
		}
		$rootScope.submitAnswersClick = function(postTestTimeElapsed,noQuestionsRequired) {
			$scope.postTestTimeElapsed = postTestTimeElapsed;
			if(!postTestTimeElapsed){
				PostTestTimerService.stop();
			}
			
			if ((!$scope.isRequired && !postTestTimeElapsed && !self.validateQtnsAnswered()) || ($scope.attemptedQuestionCount != $scope.mcqLength)) {
				return;
			}
			APIRequestService.gradeExam(self.posttestObj.examObj.responseid).then(function (data) {
				if (data.userLoggedIn == 1) {
					$scope.failedExam = false;
					self.gradeObj = data.returnData;
					AppManager.addActivityLogEntry("User pressed Exam Grade Page Button",false);
					$scope.chancesLeft = self.gradeObj.attemptsremaining;       			
					AppManager.checkCompletionForSeminar();
					AppManager.testInProgress = false;
					if (self.gradeObj.passfail == "Failed" && ($scope.chancesLeft > 0 || $scope.chancesLeft== '-1')) {
						$scope.mcqShow = false;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.retakeScreen');
					} else if (self.gradeObj.passfail == "Failed" && $scope.chancesLeft <= 0) {
						$scope.mcqShow = false;
						$scope.failedExam = true;
						self.postTestFailed = true;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.scoreScreen');
					} else if (self.gradeObj.passfail == "Passed") {
						self.postTestLoaded = false;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('.scoreScreen');
						angular.element('.seminarCompleteProcess .hidePercentScoreTitle').displayHide();
					}
					sideBarScope = angular.element('.sidebar-controller').scope();
					if(sideBarScope != undefined){
						sideBarScope.init();
					}
					$rootScope.closeConfirmPopUp = 1;
					self.hideAllPatches();					
				}
			});
	
		}
		self.hideAllPatches = function() {
			angular.element('#left .patch').removeClass('showPatch');
			angular.element('#left .box > .patch').displayHide();
			angular.element('#right .tab').removeClass('disable');
		}
		self.showAllPatches = function() {
			if (self.blankOnInactivity) {
				angular.element('#left .patch').addClass('showPatch');
				angular.element('#left .box > .patch').displayShow();
				angular.element('#right .tab').addClass('disable');				
			}
		}
		$scope.resultScreenContinueClick = function( e ) {
			if ( !AppManager.allPostTestCompleted ) {
				startLoadPoint( 'posttest' );
			} else if ( isEvaluationPending ) {
				self.showAllPatches();
				startLoadPoint( 'evaluation' );
			} else {
				processCompletion();
			}
		}
		$scope.closeExamPopup = function( e ) {
			if($rootScope.closeConfirmPopUp == 1){
						
				angular.element('.seminarCompleteProcess').show();
				
				hideShowPopUpElements();
				angular.element("#examWrapper").hide();
				AppManager.testInProgress = false;
				resetExamScroll();

			}else{

				hideShowPopUpElements('.confirmExamClose');

				angular.element('.seminarCompleteProcess ').hide();
				resetExamScroll();
			}
		}

		/*post test check box selection*/
		$scope.checkBoxClick = function (e, i, j) {
			var _this = e.currentTarget;
			if (angular.element(_this).hasClass('disable')) {
				return;
			}
			AppManager.addActivityLogEntry("User answered an exam question",false);
			var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + i);
			mcq.find('.feedback').html('');
			mcq.find('.feedback').displayHide();
			mcq.find('.checkBox').removeAddClassEle('select', _this);
			if (mcq.find('.checkBox.select').length && (!$scope.isRequired || !self.showImmediateAnswer)) {
				angular.element('.seminarCompleteProcess #postTest .nextPage').removeClass('disable');
			} else {
				angular.element('.seminarCompleteProcess #postTest .nextPage').addClass('disable');
			}
			var mcqObj = self.mcq[i];
			var responseArray= [];
			var tempObj = {name:mcqObj.controlfield, value:mcqObj.options[j].optionid};
			responseArray.push(tempObj);
			objParams = { json: JSON.stringify({responseID:mcqObj.responseid, responseArray:responseArray})};
			mcq.attr('answerUpdated',true);
			if($scope.showGradeButton){
				angular.element('.seminarCompleteProcess #postTest .gradePage').removeClass('disable');	
			}
		}
		self.validateUserAnswer = function(mcq,callback) {
			mcq.find('.checkBox').addClass('disable');
			mcq.attr('validationDone',true);
			mcq.removeAttr('answerUpdated');
			if (self.ENFORCEQREQSTATUSCORRECTMAXTRIES && self.MAXTRIESPERQUESTION) {
				if (mcq.attr('MAXTRIESPERQUESTION')) {
					mcq.attr('MAXTRIESPERQUESTION',parseInt(mcq.attr('MAXTRIESPERQUESTION'))+1);
				} else {
					mcq.attr('MAXTRIESPERQUESTION',1);
				}				
			}
			var promise = APIRequestService.saveFormResponse(objParams);
			promise.then( function( data ) {
				var validTry = true;
				if( data.success == true ) {
					if (data.returnData[0].iscorrect == 1) {
						mcq.addClass('correct');
						mcq.removeAttr('feedback');
						var messageText=data.returnData[0].responsetext;
						if (messageText != '') {
							mcq.find('.message').html(messageText);
						} else {
							mcq.find('.message').html('Correct.');
						}
					} else {
						mcq.removeClass('correct');
						mcq.addClass('incorrect');
						mcq.attr('feedback','incorrect');
						var feedbackText=data.returnData[0].responsetext;
						if (feedbackText != '') {
							mcq.find('.feedback').html(feedbackText);
						} else {
							mcq.find('.feedback').html('Incorrect.');
						}
						if (self.ENFORCEQREQSTATUSCORRECTMAXTRIES) {
							if (self.MAXTRIESPERQUESTION == 0) {
								mcq.find('.feedback').html(mcq.find('.feedback').html() + ' (You may reattempt to answer this question correctly.)');
								validTry = false;
							} else if (mcq.attr('MAXTRIESPERQUESTION') != 0 && mcq.attr('MAXTRIESPERQUESTION') < self.MAXTRIESPERQUESTION) {
								if ( (self.MAXTRIESPERQUESTION - mcq.attr('MAXTRIESPERQUESTION')) == 1 ) {
									mcq.find('.feedback').html(mcq.find('.feedback').html() + ' (You have one more attempt to answer this question correctly.)');
								} else {
									mcq.find('.feedback').html(mcq.find('.feedback').html() + ' (You have ' + (self.MAXTRIESPERQUESTION - mcq.attr("MAXTRIESPERQUESTION")) + ' remaining attempts to answer this question correctly.)');
								}
								validTry = false;
							}
						}
					}
					if (!self.ENFORCEQREQSTATUSCORRECTMAXTRIES || self.MAXTRIESPERQUESTION == 0 || mcq.attr('MAXTRIESPERQUESTION') < self.MAXTRIESPERQUESTION) {
						mcq.find('.checkBox').removeClass('disable');
					}
					mcq.find('.feedback').displayHide();
					if (callback) {
						callback();
					}					
				} else {
					mcq.addClass('correct');
					if (self.ENFORCEQREQSTATUSCORRECTMAXTRIES && mcq.attr('MAXTRIESPERQUESTION') != 0 && mcq.attr('MAXTRIESPERQUESTION') < self.MAXTRIESPERQUESTION) {
						mcq.find('.checkBox').removeClass('disable');
					}
					if (callback) {
						callback();
					}
				}
				if (callback && $scope.isRequired  && validTry) {
					angular.element('.seminarCompleteProcess #postTest .nextPage').removeClass('disable');
				}
            });
		}
		$scope.gradePageClick = function(e) {
			var _this = e.currentTarget;
			AppManager.addActivityLogEntry("User clicked grade page for an exam question",false);
			if (!angular.element(_this).hasClass('disable')) {
				//feedback shown means the answer is incorrect
				var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));
				self.validateUserAnswer(mcq, function() {
					if (mcq.find('.checkBox.select').length) {
						if (mcq.attr('feedback')) {
							mcq.find('.message').displayHide();
							mcq.find('.feedback').displayShow();
						} else {
							mcq.find('.feedback').html('');
							mcq.find('.feedback').displayHide();
							mcq.find('.checkBox').addClass('disable');
							mcq.find('.message').displayShow();
							mcq.find('.checkBox.select').siblings('.tick').displayShow();
						}
						mcq.attr('feedbackShown',true);  
						angular.element('.seminarCompleteProcess #postTest .gradePage').addClass('disable');	
					}
				});
			}
		}
		/*'Previous Page'/'Next Page' button*/
		$scope.prevNextClick = function (e) {
			var _this = e.currentTarget;
			if (!angular.element(_this).hasClass('disable')) {
				if(angular.element(_this).hasClass('nextPage') && !$scope.showGradeButton){
					var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));	
					if(mcq.attr('answerUpdated')){
						self.validateUserAnswer(mcq);
					}				
				}
				if(angular.element(_this).hasClass('nextPage')){
					$rootScope.sessionPageNumber++;
					var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex));
					if($scope.postTestQnIndex == ($scope.sectionNoArray[$rootScope.sessionCounter])){
						var objeys = Object.keys($scope.sectionObj).length - 1;
						$rootScope.sectionEnd = false;
						if(objeys > $rootScope.sessionCounter){
							$rootScope.sessionCounter++;
							$rootScope.sectionEnd = true;
							$rootScope.sessionStart = false;
							angular.element('.seminarCompleteProcess .elements').displayHideShow('#sectionAfterEnd');
						}
					}
				}else{
					$rootScope.sessionPageNumber--;
					var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-2));
					if($scope.postTestQnIndex == ($scope.sectionNoArray[$rootScope.sessionCounter - 1]) + 1){
						$rootScope.sectionEnd = true;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('#sectionAfterEnd');
						$scope.postTestQnIndex++;
					}
					if($rootScope.sessionCounter == 0 && $scope.postTestQnIndex == 1){
						$rootScope.sectionEnd = false;
						$rootScope.sessionStart = true;
						angular.element('.seminarCompleteProcess .elements').displayHideShow('#sectionAfterEnd');
					}
				}
				if(mcq.find('.checkBox.select').length && self.showImmediateAnswer && !mcq.attr('feedbackShown')){
					angular.element('.seminarCompleteProcess #postTest .gradePage').removeClass('disable');
				}else{
					angular.element('.seminarCompleteProcess #postTest .gradePage').addClass('disable');
				}
				if(angular.element(_this).hasClass('nextPage')){
					AppManager.addActivityLogEntry("User pressed Exam Next Page Button",false);
					$scope.postTestQnIndex++;
				}else{
					AppManager.addActivityLogEntry("User pressed Exam Previous Page Button",false)
					$scope.postTestQnIndex--;
				}
				
				if($scope.postTestQnIndex > self.mcq.length && !self.validateQtnsAnswered()){
					return;
				}
				
				angular.element('.seminarCompleteProcess #postTest .mcq').addRemoveClassI('displayHide', $scope.postTestQnIndex-1);
				var mcq = angular.element('.seminarCompleteProcess #postTest #mcq_' + ($scope.postTestQnIndex-1));

				if (mcq.find('.checkBox.select').length) {
					angular.element('.seminarCompleteProcess #postTest .nextPage').removeClass('disable');
				} else if ($scope.isRequired){
					angular.element('.seminarCompleteProcess #postTest .nextPage').addClass('disable');
				}

				if (angular.element(_this).hasClass('nextPage') && $scope.postTestQnIndex > $scope.mcqLength) {
					var mcqAnsweredLength = angular.element('.seminarCompleteProcess #postTest .checkBox.select').length;
					$scope.corrects = angular.element('.seminarCompleteProcess #postTest .correct').length;
					$rootScope.postTestCurrentPercent = (($scope.corrects/$scope.mcqLength) * 100).toFixed(2);
					($scope.mcqLength === mcqAnsweredLength && $rootScope.postTestCurrentPercent >= $rootScope.postTestPercent) ? ($scope.pass = true) : ($scope.pass = false);
				}
			}
		}

		/* EVALUATION SCREEN BUTTON HANDLER */
		this.navEvaluationClick = function(evt) {
			var btnId = String(angular.element(evt.currentTarget).attr('id')).split('_')[1];
			self.evaluationScreenOrder = Number(btnId)+1;
			
			self.evaluationStepSkip = false;
			if(self.evaluationScreenOrder == 2 && ($scope.evaluationObj.formtitle.length==0 || $scope.evaluationObj.sectionObj[$scope.evaluationSection].sectiontitle.length == 0)){
				self.evaluationScreenOrder = 3;
				self.evaluationStepSkip = true;
			} else {self.evaluationStepSkip = false;}
				
			AppManager.testInProgress = true;
			if(self.evaluationScreenOrder == 3){
				$scope.evaluationView = 1;
			}
			$rootScope.closeConfirmPopUp = 0;
		}

		this.userTimeReportClick = function(evt) {
			if (!angular.element(evt.currentTarget).hasClass('disable')) {

				var hoursVal 	=	angular.element('#right .userTimeReportScreen .textNdInput #timeSpentHours .input-box input').val();
				var minutesVal 	=  	angular.element('#right .userTimeReportScreen .textNdInput #timeSpentMins .input-box input').val()
				
				AppManager.userReportedTimeSpent = (hoursVal * 60) + minutesVal;
				if (!isPostTestPending && !isEvaluationPending) {
					AppManager.finalCheckSeminarforCompletion(AppManager.userReportedTimeSpent);	
				}
			} else {
				$rootScope.alert("Missing Certification", "You must certify that this statement before you may continue");
			}
		}

		/*'Retake Exam' button*/
		$scope.retakeClick = function (e) {
			var _this = e.currentTarget;
			angular.element('.seminarCompleteProcess #postTest .checkBox.select').removeClass('select');
			$scope.mcqShow = true;
			$scope.postTestQnIndex = 1;
			$scope.attemptedQuestionCount = 0;
			$rootScope.sessionPageNumber = 0;
			$rootScope.sessionPageNumber++;
			$rootScope.sessionCounter = 0;
			angular.element('.seminarCompleteProcess #postTest .mcq').addRemoveClassI('displayHide', $scope.postTestQnIndex-1);
			angular.element('.seminarCompleteProcess .elements').displayHideShow('#sectionAfterEnd');
			self.showAllPatches();
			self.retakeExam = true;
			startLoadPoint('posttest');
            if(maxTimeAllowedActualValue){
                PostTestTimerService.time = maxTimeAllowedActualValue*60;
				if(PostTestTimerService.timerStatus == 0){
					PostTestTimerService.start();		
				}		                	
            }
		}
		/*'Return to Program Tab' button*/
		$scope.returnClick = function (e) {
			var _this = e.currentTarget;
			$rootScope.currentActiveTab = angular.element('#right .base .tabs .content .tab.learn');
			$rootScope.show.screen = $rootScope.show.screen.map(function () {return false});
			$rootScope.show.screen[0] = true;
			$rootScope.tabI = 0;
			if ($rootScope.pptShow) {
				angular.element('#left .patch').removeClass('showPatch');
			}
			angular.element('#bottom #right .tab').removeAddClassI('select', $rootScope.tabI);
			$scope.tabClick(e,0,'Program')
		}
		$scope.pass = $scope.mcqShow = $scope.postTestHide = false;
		$scope.corrects = $rootScope.postTestCurrentPercent = 0;
		$scope.postTestQnIndex = 1;
		$scope.attemptedQuestionCount = 0;
		$scope.mcqShow = true;
		
		/*css properties of cmpltController*/
		$scope.css = {
			change: true,
			/*cmplt*/
			cmpltContinuePadding: 1,
			cmpltScreenBgColor: '#FFFFFF',
			cmpltScreenBorderColor: '#053468',
			cmpltNextMinWidth: 60,
			cmpltNextMinHeight: 28,
			cmpltButtonBgColor: '#46607B',
			cmpltButtonColor: '#FFFFFF',
			cmpltNextPadding: 1,
			cmpltNextMarginTop: 2,
			/*postTest*/
			postTestPadding: 1,
			postTestStartMinWidth: 60,
			postTestStartMinHeight: 28,
			postTestButtonColor: '#FFFFFF',
			postTestStartMarginTop: 3,
			postTestMcqMarginTop: 2,
			postTestMcqOptionMarginTop: 2,
			postTestPrevNextMinWidth: 125,
			postTestPrevNextMarginRight: 1,
			cmpltRetakeFeedbackMarginTop: 1,
			cmpltReturnHeadingMarginBottom: 3,
			cmpltReturnTableMarginTop: 2,
			cmpltReturnImgMarginTop: 10,
			cmpltReturnImgMarginRight: 5
		}
		cssData.cmpltController = $scope.css;
		/*watching css properties of cmpltController, and applying these to ng-style values*/
		$scope.$watchCollection('css', function (newVal, oldVal) {
			$scope.cssCmpltContinueScreen = {
				padding: '1%'
			}
			$scope.cssCmpltContinue = {
				minWidth: $scope.css.cmpltNextMinWidth + 'px',
				minHeight: $scope.css.cmpltNextMinHeight + 'px',
				padding: '3px ' + $scope.css.cmpltNextPadding + '%',
				marginTop: $scope.css.cmpltNextMarginTop + '%',
				marginLeft: '1%'
			}
			$scope.cssCmpltStartScreen = {
				padding: $scope.css.cmpltContinuePadding + '%',
				backgroundColor: $scope.css.cmpltScreenBgColor,
				borderColor: $scope.css.cmpltScreenBorderColor
			}
			$scope.cssCmpltStart = {
				minWidth: $scope.css.cmpltNextMinWidth + 'px',
				minHeight: $scope.css.cmpltNextMinHeight + 'px',
				padding: '3px ' + $scope.css.cmpltNextPadding + '%',
				marginTop: $scope.css.cmpltNextMarginTop + '%'
			}
			/*postTest*/
			$scope.cssPostTest = {
				padding: $scope.css.postTestPadding + '%',
				backgroundColor: $scope.css.cmpltScreenBgColor,
				borderColor: $scope.css.cmpltScreenBorderColor
			}
			$scope.cssPostTestStart = {
				minWidth: $scope.css.postTestStartMinWidth + 'px',
				minHeight: $scope.css.postTestStartMinHeight + 'px',
				marginTop: $scope.css.postTestStartMarginTop + '%',
			}
			$scope.cssPostTestMcq = {
				marginTop: $scope.css.postTestMcqMarginTop + '%'
			}
			$scope.cssPostTestMcqQn = {
			}
			$scope.cssPostTestMcqOption = {
				marginTop: $scope.css.postTestMcqOptionMarginTop + '%'
			}
			$scope.cssPostTestCheckBox = {
				borderColor: $scope.css.cmpltButtonBgColor,
				backgroundColor: $scope.css.cmpltButtonBgColor
			}
			$scope.cssPostTestPrevNext = {
				minHeight: $scope.css.postTestStartMinHeight + 'px',
				padding: '3px ' + $scope.css.cmpltNextPadding + '%',
				marginTop: $scope.css.postTestStartMarginTop + '%',
				marginRight: $scope.css.postTestPrevNextMarginRight + '%',
			}
			/*RetakeScreen*/
			$scope.cssCmpltRetakeScreen = {
				padding: $scope.css.cmpltContinuePadding + '%',
				backgroundColor: $scope.css.cmpltScreenBgColor,
				borderColor: $scope.css.cmpltScreenBorderColor
			}
			$scope.cssCmpltRetakeFeedback = {
				marginTop: $scope.css.cmpltRetakeFeedbackMarginTop + '%'
			}
			$scope.cssCmpltRetake = {
				minWidth: $scope.css.cmpltNextMinWidth + 'px',
				minHeight: $scope.css.cmpltNextMinHeight + 'px',
				padding: '3px ' + $scope.css.cmpltNextPadding + '%',
				marginTop: $scope.css.cmpltNextMarginTop + '%'
			}
			/*ReturnScreen*/
			$scope.cssCmpltReturnScreen = {
				padding: $scope.css.cmpltContinuePadding + '%',
				backgroundColor: $scope.css.cmpltScreenBgColor,
				borderColor: $scope.css.cmpltScreenBorderColor
			}
			$scope.cssCmpltReturnHeading = {
				marginBottom: $scope.css.cmpltReturnHeadingMarginBottom + '%'
			}
			$scope.cssCmpltReturn = {
				minWidth: $scope.css.cmpltNextMinWidth + 'px',
				minHeight: $scope.css.cmpltNextMinHeight + 'px',
				padding: '4px ' + $scope.css.cmpltNextPadding + '%',
				marginTop: $scope.css.cmpltNextMarginTop + '%'
			}
			$scope.cssCmpltReturnTable = {
				marginTop: $scope.css.cmpltReturnTableMarginTop + '%'
			}
			$scope.cssCmpltReturnImg = {
				marginTop: $scope.css.cmpltReturnImgMarginTop + '%',
			}
			/*SuccessScreen*/
			$scope.cssCmpltSuccessScreen = {
				padding: '1%',
			}
			/*FailureScreen*/
			$scope.cssCmpltFailureScreen = {
				padding: $scope.css.cmpltContinuePadding + '%',
				backgroundColor: $scope.css.cmpltScreenBgColor,
				borderColor: $scope.css.cmpltScreenBorderColor
			}
			/*Evaluation Screen*/
			$scope.cssEvaluationScreen = {
				padding: $scope.css.cmpltContinuePadding + '%',
			}
			$scope.cssTitleTop = {
				padding: '8px'
			}
		});
	});

})()