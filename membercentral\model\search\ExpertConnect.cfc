<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 50>
	<cfset variables.thisBucketType = "expertConnect">
	<cfset variables.thisBucketCartItemTypeID = 1>
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketSettings" type="xml" required="no" default="<settings/>">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.settingsStruct = prepSettings(arguments.bucketSettings)>

		<cfsavecontent variable="local.header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/header.cfm">
		</cfsavecontent>
		
		<cfreturn local.header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(searchID=arguments.searchID, bucketID=arguments.bucketID)>

		<!--- get states --->
		<cfquery name="local.qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT distinct s.orderpref, s.code, s.name
			from dbo.depodocuments as d
			inner join dbo.states as s on s.code = d.jurisdiction
			order by s.orderpref, s.name		
		</cfquery>
		
		<!--- show common JS --->
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/searchForm.cfm">
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="showSearchNotAccepted" access="private" returntype="struct" output="no" hint="returns not accepted text">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketSettings" type="xml" required="no" default="<settings/>">
		<cfargument name="viewDirectory" required="no" type="string" default="default">
		
		<cfset var local = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/searchNotAccepted.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}','','ALL')>

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_fname = replace(local.searchXML.search["s_fname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_lname = replace(local.searchXML.search["s_lname"].xmlText,chr(34),'','ALL');
		} else {
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID)>
		
		<cfreturn prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=searchXML)>
	</cffunction>

	<cffunction name="prepSearchFromXML" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchXML" required="yes" type="xml">
	
		<cfset var local = structNew()>
		
		<cfscript>
		// get bucket info to get any restrictions
		local.qryBucketInfo = getBucketInfo(arguments.bucketID);
		local.settingsStruct = prepSettings(local.qryBucketInfo.bucketSettings);

		// read/clean from xml
		local.s_fname = preparePhraseString(arguments.searchXML.search["s_fname"].xmlText);
		local.s_lname = preparePhraseString(arguments.searchXML.search["s_lname"].xmlText);

		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"expertfname",local.s_fname);
		structInsert(local.returnStruct,"expertlname",local.s_lname);
		structInsert(local.returnStruct,"expertname","");
		structInsert(local.returnStruct,"keywords","");
		structInsert(local.returnStruct,"strSettings",local.settingsStruct);

		//prepare expertname keywords
		local.keywordsexpertname = "";
		if (Len(local.s_fname) gt 2)
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_fname,chr(7));
		if (Len(local.s_lname) gt 2)
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_lname,chr(7));
		local.keywordsexpertname = Replace(local.keywordsexpertname,chr(7)," and ","ALL");
		local.returnStruct.expertname = local.keywordsexpertname;

		if (local.settingsStruct.search.relatedtestimonyonly) {
			local.relatedexpertkeywords = prepareFullTextExpertKeywords(local.s_fname,local.s_lname);
			local.returnStruct.keywords = local.relatedexpertkeywords;
		}

		// do i have enough criteria to run a search?
		if (not len(local.keywordsexpertname))
			structInsert(local.returnStruct,"searchAccepted",false);
		else
			structInsert(local.returnStruct,"searchAccepted",true);
		</cfscript>

		<cfset local.fulltextsql = "">
		<cfset local.rankExpressionList = "">
		<cfset local.CRLF = chr(13) & chr(10)>
		<cfif local.returnStruct.strSettings.search.deposedexpertonly and local.returnStruct.searchAccepted>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join searchMC.dbo.ts_subscriberDepositions as sd on sd.depoDocumentID = docs.documentid">
		</cfif>
		<cfif len(local.returnStruct.expertname) and not local.returnStruct.strSettings.search.relatedtestimonyonly>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as expertsearch on expertsearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"expertsearch.[rank]")>
		</cfif>
		<cfif local.returnStruct.strSettings.search.relatedtestimonyonly and len(local.returnStruct.expertname)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "left outer join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as excludeexpertsearch on excludeexpertsearch.[key] = docs.documentid where excludeexpertsearch.[key] is null">
		</cfif>

		<cfif len(local.fulltextsql)>
			<cfsavecontent variable="local.fulltextsql">
				select distinct docs.documentid, (<cfoutput>#replacenocase(local.rankExpressionList,","," + ","all")#</cfoutput>) as rank
				from dbo.depoDocuments as docs
				<cfoutput>#local.fulltextsql#</cfoutput>;
			</cfsavecontent>
		</cfif>
	
		<cfset structInsert(local.returnStruct,"fulltextsql",local.fulltextsql)>
		<cfset structInsert(local.returnStruct,"rankExpressionList",local.rankExpressionList)>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepSettings" access="private" returntype="struct" output="no" hint="parses the settingsXML into a standardized struct">
		<cfargument name="bucketSettings" required="yes" type="xml">
		
		<cfset var local = StructNew()>
		
		<cfscript>
			// standardize restrictions
			local.settingsStruct = StructNew();
			local.bucketSettingsXML = XMLParse(arguments.bucketSettings);
			local.settingsStruct.search = structnew();
			local.settingsStruct.search.deposedexpertonly = false;
			local.settingsStruct.search.relatedtestimonyonly = false;

		</cfscript>
		<cfreturn local.settingsStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
				<cfset saveStatsAndBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID, metric='#variables.thisBucketType#.getResultsCount', 
						ms=local.returnStruct.ExecutionTime, itemCount=local.returnStruct.itemCount)>
				<!--- Remove structkeys not expected by caller --->
				<cfset structDelete(local.returnStruct, "ExecutionTime")/>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>
	<cffunction name="getResultsCountForSearchIndex" access="public" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="firstName" required="true" type="string">
		<cfargument name="lastName" required="true" type="string">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.searchXML">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid>#val(arguments.bucketID)#</bid>
				<s_fname>#xmlFormat(trim(replace(arguments.firstName,chr(34),'','ALL')))#</s_fname>
				<s_lname>#xmlFormat(trim(replace(arguments.lastName,chr(34),'','ALL')))#</s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchXML = XMLParse(local.searchXML)>
		
		<cfset local.strSearch = prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=local.searchXML)>
		<cfset local.returnStruct = StructNew()>
		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfelse>
			<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
		</cfif>
		<cfreturn local.returnStruct/>		
	</cffunction>
	<cffunction name="runResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="strSearch" required="yes" type="struct">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.returnStruct = StructNew()>

		<cfset local.memberGroups = variables.cfcuser_TSgroups>
		<cfset local.qJoinedAndPublicGroups = cleanGroups()>
		<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
		<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
		<cfset local.strResults = getSearchResults(strSearch=arguments.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, mode="count")>
		<cfset StructInsert(local.returnStruct,"executionTime",local.strResults.executionTime)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResults.itemcount)>
		<cfset StructInsert(local.returnStruct,"uniqueLawyers",local.strResults.uniqueLawyers)>
		<cfset StructInsert(local.returnStruct,"numStates",local.strResults.numStates)>
		<cfset StructInsert(local.returnStruct,"success",true)>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="downloadExpertMatches" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.strSearch = prepSearch(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset local.memberGroups = variables.cfcuser_TSgroups>
			<cfset local.qJoinedAndPublicGroups = cleanGroups()>
			<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
			<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
			<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(local.strSearch.strSettings.searchoverrideguest,"bankids")>
				<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),local.strSearch.strSettings.searchoverrideguest.bankids)>
				<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,local.strSearch.strSettings.searchoverrideguest.bankids)>
			</cfif>

			<cfset local.returnStruct = getSearchResults(strSearch=local.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, mode="export")>
		<cfelse>
			<cfset local.returnStruct = { "success":false }>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSearchResults" access="private" output="false" returntype="struct">
		<cfargument name="strSearch" type="struct" required="yes">
		<cfargument name="stringJoinedAndPublicGroups" type="string" required="yes">
		<cfargument name="mode" type="string" required="yes">

		<cfset var local = StructNew()>
		<cfset local.strDepoResults = createObject("component","Depositions").getSearchResultsCount(strSearch=arguments.strSearch, stringJoinedAndPublicGroups=arguments.stringJoinedAndPublicGroups, bucketType="expertConnect")>
		<cfset local.strSimilarSearchResults = createObject("component","Searches").getSearchResultsCount(strSearch=arguments.strSearch, bucketType="expertConnect")>
		
		<cfset local.depoResults_depoMemberDataIDs = valueList(local.strDepoResults.qryResults.depoMemberDataID)>
		<cfset local.simSearchResults_depoMemberDataIDs = valueList(local.strSimilarSearchResults.qryResults.depoMemberDataID)>
		<cfset local.matchesLimit = 400>

		<cfif arguments.mode EQ 'export'>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=session.mcstruct.sitecode)>
			<cfset local.reportFileName = "ExpertMatches.csv">
		</cfif>
		
		<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpDepoMembers') IS NOT NULL
				DROP TABLE ##tmpDepoMembers;
			IF OBJECT_ID('tempdb..##tmpExperts') IS NOT NULL
				DROP TABLE ##tmpExperts;
			IF OBJECT_ID('tempdb..##tmpDepoMembersCountByState') IS NOT NULL
				DROP TABLE ##tmpDepoMembersCountByState;
			CREATE TABLE ##tmpDepoMembers (depoMemberDataID int PRIMARY KEY, matchType varchar(15));
			CREATE TABLE ##tmpExperts (depoMemberDataID int PRIMARY KEY, billingState varchar(50));
			CREATE TABLE ##tmpDepoMembersCountByState (stateCode varchar(2), depoMemCount int);

			DECLARE @uniqueLawyers int, @numStates int, @locationDataXML xml, @itemLocDataXML xml, @USStatesXML xml, @lawyersDataXML xml;
			DECLARE @tmpUSStates TABLE (stateCode varchar(4), stateName varchar(40), orderPref int);
			DECLARE @orgID int, @inquiryOptOutListID int, @globalOptOutListID int;

			SELECT @orgID = orgID FROM membercentral.dbo.sites WHERE siteCode = 'TS';

			SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
			FROM dbo.expertConnectInquirySettings;

			SELECT TOP 1 @globalOptOutListID = cl.consentListID
			FROM platformMail.dbo.email_consentLists cl
			INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
				AND clt.orgID = @orgID
				AND clt.consentListTypeName = 'Global Lists'
				AND cl.[status] = 'A'
			INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
				AND modeName = 'GlobalOptOut';
			
			<cfif listLen(local.depoResults_depoMemberDataIDs)>
				INSERT INTO ##tmpDepoMembers (depoMemberDataID, matchType)
				SELECT distinct listitem, 'Depositions'
				FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#local.depoResults_depoMemberDataIDs#" cfsqltype="CF_SQL_LONGVARCHAR">,',');
			</cfif>

			<cfif listLen(local.simSearchResults_depoMemberDataIDs)>
				INSERT INTO ##tmpDepoMembers (depoMemberDataID, matchType)
				SELECT distinct d.listitem, 'Searches'
				FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#local.simSearchResults_depoMemberDataIDs#" cfsqltype="CF_SQL_LONGVARCHAR">,',') AS d
				LEFT OUTER JOIN ##tmpDepoMembers AS tmp ON tmp.depoMemberDataID = d.listitem
				WHERE tmp.depoMemberDataID IS NULL;
			</cfif>

			INSERT INTO @tmpUSStates (stateCode, stateName, orderPref)
			select [code], [Name], orderPref
			from memberCentral.dbo.ams_states
			where countryID = 1;

			INSERT INTO ##tmpExperts (depoMemberDataID, billingState)
			SELECT DISTINCT m.depoMemberDataID, m.billingState
			FROM dbo.depomemberdata AS m
			INNER JOIN ##tmpDepoMembers AS tmp ON tmp.depoMemberDataID = m.depoMemberDataID
			INNER JOIN dbo.depoTransactions AS t ON t.depoMemberDataID = m.depoMemberDataID
			INNER JOIN @tmpUSStates AS s ON s.stateCode = m.billingState
			WHERE NULLIF(m.Email,'') IS NOT NULL
			<cfif session.cfcuser.memberdata.depoMemberDataID gt 0>
				AND m.depoMemberDataID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.depoMemberDataID#">
			</cfif>
			AND m.TLAMemberState <> 'CP'
			AND (m.AdminFlag2 IS NULL OR m.adminflag2 <> 'Y')
			AND m.MailCode = 'A'
			AND m.TSAllowed = 1
			AND t.AccountCode IN ('1001','1002','1003','1004','1005','1006','1101','1102','1103')
			AND t.AmountBilled > 0;

			DELETE tmp
			FROM ##tmpExperts AS tmp
			INNER JOIN dbo.depomemberdata AS d ON d.depoMemberDataID = tmp.depoMemberDataID
			INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
				AND clm.email = d.Email;

			INSERT INTO ##tmpDepoMembersCountByState (stateCode, depoMemCount)
			select s.stateCode, count(distinct tmp.depoMemberDataID)
			from ##tmpExperts as tmp
			inner join @tmpUSStates as s on s.stateCode = tmp.billingState
			group by s.stateCode;

			SELECT @uniqueLawyers = SUM(depoMemCount), @numStates = COUNT(DISTINCT stateCode)
			FROM ##tmpDepoMembersCountByState;

			<cfif arguments.mode EQ 'results'>
				SELECT @itemLocDataXML = ISNULL((
					select statecode, depoMemCount AS itemcount
					from ##tmpDepoMembersCountByState as rowdata
					order by statecode
					FOR XML AUTO, ELEMENTS, ROOT('itemlocation')
				),'<itemlocation/>');

				SELECT @USStatesXML = ISNULL((
					select statecode, statename
					from @tmpUSStates as rowdata
					order by orderPref, statecode
					FOR XML AUTO, ELEMENTS, ROOT('usstates')
				),'<usstates/>');

				SELECT @locationDataXML = ISNULL((
					select @itemLocDataXML, @USStatesXML 
					for xml path ('locdata')
				),'<locdata/>');

				SELECT @lawyersDataXML = ISNULL((
					SELECT TOP #local.matchesLimit# lawyer.depoMemberDataID as "@depomemberdataid", isnull(lawyer.firstName,'') + ' ' + isnull(lawyer.lastName,'') as "@name", isnull(lawyer.BillingFirm,'') as "@firm", isnull(lawyer.BillingState,'') as "@state"
					FROM dbo.depomemberdata AS lawyer
					INNER JOIN ##tmpExperts AS e ON e.depoMemberDataID = lawyer.depoMemberDataID
					ORDER BY lawyer.BillingState, lawyer.lastName, lawyer.firstName
					FOR XML path('lawyer'), root('lawyers'), type
				),'<lawyers/>');

				SELECT @uniqueLawyers as uniqueLawyers, @numStates as numStates, @locationDataXML as locationDataXML, @lawyersDataXML as lawyersDataXML;
			<cfelseif arguments.mode EQ 'export'>
				IF OBJECT_ID('tempdb..##tmpExportData') IS NOT NULL  
					DROP TABLE ##tmpExportData;
				CREATE TABLE ##tmpExportData (Name varchar(250), Email varchar(100), DepomemberdataID int, [TSAdmin Account] varchar(400), MatchType varchar(15),
					[Member Type] varchar(50), [Expiration Date] datetime);

				INSERT INTO ##tmpExportData (Name, Email, DepomemberdataID, [TSAdmin Account], MatchType, [Member Type], [Expiration Date])
				SELECT m.firstName + ' ' + m.lastName, m.email, m.depoMemberDataID, 
					'https://admin.trialsmith.com/MemberEdit.cfm?depoMemberDataID='+cast(m.depoMemberDataID as varchar(10)),
					tmp.matchType, mt.membertype, m.RenewalDate
				FROM dbo.depomemberdata AS m
				INNER JOIN ##tmpExperts AS e ON e.depoMemberDataID = m.depoMemberDataID
				INNER JOIN ##tmpDepoMembers AS tmp ON tmp.depoMemberDataID = m.depoMemberDataID
				INNER JOIN dbo.membertype AS mt ON mt.membertypeID = m.membertype;

				DECLARE @selectsql varchar(max) = 'SELECT *, ROW_NUMBER() OVER(order by [Name]) as mcCSVorder *FROM* ##tmpExportData';

				EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, 
					@csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', 
					@returnColumns=0;
				
				IF OBJECT_ID('tempdb..##tmpExportData') IS NOT NULL
					DROP TABLE ##tmpExportData;
			<cfelse>
				SELECT @uniqueLawyers as uniqueLawyers, @numStates as numStates;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpDepoMembers') IS NOT NULL
				DROP TABLE ##tmpDepoMembers;
			IF OBJECT_ID('tempdb..##tmpExperts') IS NOT NULL
				DROP TABLE ##tmpExperts;
			IF OBJECT_ID('tempdb..##tmpDepoMembersCountByState') IS NOT NULL
				DROP TABLE ##tmpDepoMembersCountByState;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif arguments.mode EQ 'results'>
			<cfset local.returnStruct = { 
				"executionTime":(local.strDepoResults.qryStat.ExecutionTime + local.strSimilarSearchResults.qryStat.ExecutionTime + local.qryStat.ExecutionTime),
				"matchesLimit":local.matchesLimit,
				"itemcount":val(local.qryResults.uniqueLawyers),
				"uniqueLawyers":val(local.qryResults.uniqueLawyers),
				"numStates":val(local.qryResults.numStates),
				"strchart": { 
					"arrlocation":[],
					"bucket":"expertconnect",
					"totalcount":val(local.qryResults.uniqueLawyers),
					"locationobjtitle": "Lawyers Identified",
					"arrusstates":[]
				},
				"lawyersDataXML":XMLParse(local.qryResults.lawyersDataXML).XmlRoot,
				"success":true
			}>
			<cfif len(local.qryResults.locationDataXML) AND arrayLen(XMLSearch(local.qryResults.locationDataXML,'/locdata/child::node()'))>
				<cfset var arrStateCodes = XMLSearch(local.qryResults.locationDataXML,'/locdata/itemlocation/rowdata/statecode')>
				<cfset var arrStateItemCount = XMLSearch(local.qryResults.locationDataXML,'/locdata/itemlocation/rowdata/itemcount')>
				<cfset local.returnStruct['strchart']['arrlocation'] =  arrStateCodes.map(
					function(item,index) { 
						return { "statecode":arguments.item.xmlText, "value":val(arrStateItemCount[arguments.index].xmlText) }; 
					}
				)>
				<cfset var arrUSStateCodes = XMLSearch(local.qryResults.locationDataXML,'/locdata/usstates/rowdata/statecode')>
				<cfset var arrUSStateNames = XMLSearch(local.qryResults.locationDataXML,'/locdata/usstates/rowdata/statename')>
				<cfset local.returnStruct['strchart']['arrusstates'] =  arrUSStateCodes.map(
					function(item,index) { 
						return { "statecode":arguments.item.xmlText, "statename":arrUSStateNames[arguments.index].xmlText }; 
					}
				)>
			</cfif>
		<cfelseif arguments.mode EQ 'export'>
			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

			<cfset local.returnStruct = {  "u":local.stDownloadURL,	"success":true }>
		<cfelse>
			<cfset local.returnStruct = { 
				"executionTime":(local.strDepoResults.qryStat.ExecutionTime + local.strSimilarSearchResults.qryStat.ExecutionTime + local.qryStat.ExecutionTime), 
				"itemcount":val(local.qryResults.uniqueLawyers),
				"uniqueLawyers":val(local.qryResults.uniqueLawyers),
				"numStates":val(local.qryResults.numStates),
				"success":true
			}>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" output="false" returntype="struct" hint="searches and returns not logged in text">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="strSearch" type="struct" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.strSearch = duplicate(arguments.strSearch)>
		<cfset local.memberGroups = variables.cfcuser_TSgroups>
		<cfset local.qJoinedAndPublicGroups = cleanGroups()>
		<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
		<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
		<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(local.strSearch.strSettings.searchoverrideguest,"bankids")>
			<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),local.strSearch.strSettings.searchoverrideguest.bankids)>
			<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,local.strSearch.strSettings.searchoverrideguest.bankids)>
		</cfif>
		<cfset local.strResults = getSearchResults(strSearch=arguments.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, mode="results")>
		<cfset saveStatsAndBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID, metric='#variables.thisBucketType#.getResultsCount', 
					ms=local.strResults.ExecutionTime, itemCount=val(local.strResults.itemCount))>

		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resultmode","summary")>
		<cfif val(local.strResults.itemCount)>
			<cfset StructInsert(local.returnStruct,"strchart",local.strResults.strchart)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"strchart",{})>
		</cfif>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResults.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getUserNoAccessResults" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="strSearch" type="struct" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<cfsavecontent variable="local.inactiveUserInfo">
			<cfif variables.cfcuser_TrialSmithAllowed is not 1>
				<cfoutput>#showTrialsmithNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithPending is 1>
				<cfoutput>#showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithExpired is 1>
				<cfoutput>#showTrialsmithExpired(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithNoPlan is not 0>
				<cfoutput>#showTrialsmithNoPlan(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif arguments.strSearch.strSettings.search.deposedexpertonly AND variables.cfcuser_TSRights LT 3>
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/upgradeUserPlan.cfm">
			</cfif>
		</cfsavecontent>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/userNoAccessResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="filter" required="no" default="">
		<cfargument name="queryOnly" required="no" default="0">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>

		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(StructKeyExists(arguments, "queryOnly") AND NOT IsBoolean(arguments.queryOnly)) OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID,
				bucketID=arguments.bucketID, startrow=arguments.startrow, sortType=arguments.sortType,
				filter=arguments.filter, queryOnly=arguments.queryOnly, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="filter" required="yes" type="string">
		<cfargument name="queryOnly" required="yes" type="boolean">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<!--- checks.
		1. search accepted?
		2. logged in?
		3. RestrictToGroup?
		4. TrialSmithAllowed?
		5. TrialSmithDisabled?
		6. TrialSmithPending?
		7. TrialSmithExpired?
		8. TrialSmithNoPlan?
		9. Pro-Plan or higher for Deposed Experts?
		--->
		<cfif NOT local.strSearch.searchAccepted OR (Len(local.strSearch.expertfname) lt 3 OR Len(local.strSearch.expertlname) lt 3)>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isLoggedIn>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithAllowed is not 1>
			<cfreturn getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithDisabled is 1>
			<cfreturn showTrialsmithDisabled(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfelseif variables.cfcuser_TrialSmithPending is 1 
					OR variables.cfcuser_TrialSmithExpired is 1 
					OR variables.cfcuser_TrialSmithNoPlan is not 0
					OR (local.strSearch.strSettings.search.deposedexpertonly AND variables.cfcuser_TSRights LT 3)>
			<cfreturn getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.memberGroups = variables.cfcuser_TSgroups>
			<cfset local.qJoinedAndPublicGroups = cleanGroups()>
			<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
			<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
			<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(local.strSearch.strSettings.searchoverrideguest,"bankids")>
				<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),local.strSearch.strSettings.searchoverrideguest.bankids)>
				<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,local.strSearch.strSettings.searchoverrideguest.bankids)>
			</cfif>
			
			<cfset local.strResults = getSearchResults(strSearch=local.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, mode="results")>
			<cfset saveStatsAndBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID, metric='#variables.thisBucketType#.getResults', 
						ms=local.strResults.ExecutionTime, itemCount=val(local.strResults.itemCount))>

			<cfif arguments.queryOnly eq 0>
				<cfset local.qryDepoMember = getDepoMemberInfo(depoMemberDataID=session.cfcuser.memberdata.depoMemberDataID)>
				<cfset local.qryLoggedInUserOptOutInfo = getLoggedInUserOptOutInfo()>
				<cfset local.qryStates = application.objCommon.getTSStates()>

				<!--- return content --->
				<cfsavecontent variable="local.stResults">
					<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/expertConnect/results.cfm">
				</cfsavecontent>
			<cfelse>
				<cfset local.stResults = "">
			</cfif>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfif arguments.queryOnly eq 1>
				<cfset StructInsert(local.returnStruct,"resultqry",local.strResults.qryResults)>
			</cfif>
			<cfset StructInsert(local.returnStruct,"resultmode","summary")>
			<cfif val(local.strResults.itemCount)>
				<cfset StructInsert(local.returnStruct,"strchart",local.strResults.strchart)>
			<cfelse>
				<cfset StructInsert(local.returnStruct,"strchart",{})>
			</cfif>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.strResults.itemCount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
		
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
				<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>


	<cffunction name="cleanGroups" access="private" returntype="query" output="no" hint="returns query of TS groups user is authorized to search">
		<cfset var local = StructNew()>
		<cfquery name="local.qryGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select groupid, description
			from dbo.depogroups
		</cfquery>
		<cfreturn local.qryGroups>
	</cffunction>

	<cffunction name="getBucketSettingsHelpText" access="public" output="false" returntype="struct">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.returnStruct.helpText">
			<cfoutput>
				<div class="alert align-items-center pl-2 align-content-center alert-primary show mb-2 alertSection" role="alert">
					There are no supported restrictions on this bucket
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getDepoMemberInfo" access="private" returntype="query" output="no">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		
		<cfset var qryDepoMember = "">

		<cfquery name="qryDepoMember" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT firstName + ' ' + lastName as [name], Email, BillingFirm, BillingAddress, BillingAddress2, BillingAddress3, BillingCity, BillingState, BillingZip
			FROM dbo.depomemberdata
			WHERE depomemberdataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.depoMemberDataID#">
		</cfquery>

		<cfreturn qryDepoMember>
	</cffunction>

	<cffunction name="getLoggedInUserOptOutInfo" access="private" returntype="query" output="no">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfquery name="local.qryMemberOptOutInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			DECLARE @inquiryOptOutListID int, @globalOptOutListID int, @consentListName varchar(100),
				@isOptedOut bit = 0, @orgID int;

			SELECT @inquiryOptOutListID = ISNULL(s.optOutListID,0), @consentListName = l.consentListName
			FROM dbo.expertConnectInquirySettings AS s
			INNER JOIN platformMail.dbo.email_consentLists AS l ON l.consentListID = s.optOutListID
			WHERE l.[status] = 'A';

			SELECT @orgID = orgID FROM membercentral.dbo.sites WHERE siteCode = 'TS';

			SELECT TOP 1 @globalOptOutListID = cl.consentListID
			FROM platformMail.dbo.email_consentLists cl
			INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
				AND clt.orgID = @orgID
				AND clt.consentListTypeName = 'Global Lists'
				AND cl.[status] = 'A'
			INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
				AND modeName = 'GlobalOptOut';

			IF EXISTS(
				SELECT d.depoMemberDataID
				FROM dbo.depomemberdata AS d
				INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
					AND clm.email = d.Email
				WHERE d.depoMemberDataID = <cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
			)
				SET @isOptedOut = 1;

			SELECT @isOptedOut AS isOptedOut, @consentListName AS optOutListName
		</cfquery>

		<cfreturn local.qryMemberOptOutInfo>
	</cffunction>

	<cffunction name="saveEmailInquiry" access="public" returntype="struct" output="no" hint="saves email inquiry data">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="topic" required="yes" type="string">
		<cfargument name="descriptionOfAsk" required="yes" type="string">
		<cfargument name="email" required="yes" type="string">
		<cfargument name="firm" required="yes" type="string">
		<cfargument name="address1" required="yes" type="string">
		<cfargument name="address2" required="yes" type="string">
		<cfargument name="address3" required="yes" type="string">
		<cfargument name="city" required="yes" type="string">
		<cfargument name="stateCode" required="yes" type="string">
		<cfargument name="postalCode" required="yes" type="string">
		<cfargument name="website" required="yes" type="string">
		<cfargument name="updateTSRecord" required="yes" type="boolean">
		<cfargument name="depoMemberDataIDList" required="yes" type="string">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cftry>
			<cfquery name="local.qrySaveEmailInquiry" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @requesterDepoMemberDataID int, @searchID int, @expertFirstName varchar(50), @expertLastName varchar(50),
						@emailAddressSlug varchar(100), @awatingStatusID int, @openStatusID int, @inquiryID int, @conversationID int,
						@requesterRoleID int, @respondentRoleID int, @requesterFirstName varchar(100), @requesterLastName varchar(100),
						@requesterEmail varchar(400), @respondentDepoMemberDataID int, @respondentFirstName varchar(100),
						@respondentLastName varchar(100), @respondentEmail varchar(400), @nowDate datetime;

					SET @requesterDepoMemberDataID = <cfqueryparam value="#session.cfcuser.memberdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;
					SET @searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">;
					SET @requesterEmail = <cfqueryparam value="#arguments.email#" cfsqltype="CF_SQL_VARCHAR">;
					SET @nowDate = getdate();

					SELECT @expertFirstName = s1.value('.','varchar(50)'), @expertLastName = s2.value('.','varchar(50)')
					FROM search.dbo.tblSearchHistory
					OUTER APPLY searchXML.nodes('/search/s_fname') AS x1(s1)
					OUTER APPLY searchXML.nodes('/search/s_lname') AS x2(s2)
					WHERE searchID = @searchID;

					SELECT @awatingStatusID = statusID
					FROM dbo.expertConnectInquiryStatuses
					WHERE statusCode = 'NeedsApproval';

					SELECT @openStatusID = statusID
					FROM dbo.expertConnectInquiryStatuses
					WHERE statusCode = 'Open';

					SELECT @requesterRoleID = roleID
					FROM dbo.expertConnectInquiryRoles
					WHERE roleCode = 'Requestor';

					SELECT @respondentRoleID = roleID
					FROM dbo.expertConnectInquiryRoles
					WHERE roleCode = 'Respondent';

					SELECT @requesterFirstName = FirstName, @requesterLastName = LastName
					FROM dbo.depomemberdata
					WHERE depomemberdataID = @requesterDepoMemberDataID;

					SET @emailAddressSlug = NULLIF(membercentral.dbo.fn_slugify(@expertFirstName + @expertLastName),'');

					BEGIN TRAN;

						INSERT INTO dbo.expertConnectInquiries (depoMemberDataID, email, companyName, companyAddress1, companyAddress2, companyAddress3,
							companyCity, companyState, companyZIP, website, searchID, expertFirstName, expertLastName, topic, emailAddressSlug,
							descriptionOfAsk, statusID)
						VALUES (
							@requesterDepoMemberDataID,
							@requesterEmail,
							<cfqueryparam value="#arguments.firm#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.address1#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.address2#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.address3#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.city#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.stateCode#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.postalCode#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#arguments.website#" cfsqltype="CF_SQL_VARCHAR">,
							@searchID,
							@expertFirstName,
							@expertLastName,
							<cfqueryparam value="#arguments.topic#" cfsqltype="CF_SQL_VARCHAR">,
							@emailAddressSlug,
							<cfqueryparam value="#arguments.descriptionOfAsk#" cfsqltype="CF_SQL_VARCHAR">,
							@awatingStatusID
						);
						SELECT @inquiryID = SCOPE_IDENTITY();

						<cfloop list="#arguments.depoMemberDataIDList#" item="local.thisDepoMemberDataID">
							SET @respondentDepoMemberDataID = <cfqueryparam value="#local.thisDepoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;

							SELECT @respondentFirstName = FirstName, @respondentLastName = LastName, @respondentEmail = Email
							FROM dbo.depomemberdata
							WHERE depomemberdataID = @respondentDepoMemberDataID;

							INSERT INTO dbo.expertConnectInquiryConversations (inquiryID ,statusID)
							VALUES (@inquiryID, @openStatusID);
							SELECT @conversationID = SCOPE_IDENTITY();

							-- entry for the lawyer who created the inquiry
							INSERT INTO dbo.expertConnectInquiryConversationParticipants (conversationID, inquiryID, depoMemberDataID, roleID,
								firstName, lastName, email, addedByDepoMemberDataID, approvedByDepoMemberDataID, dateApproved)
							VALUES (@conversationID, @inquiryID, @requesterDepoMemberDataID, @requesterRoleID, @requesterFirstName,
								@requesterLastName, @requesterEmail, @requesterDepoMemberDataID, @requesterDepoMemberDataID, @nowDate);

							-- entry for the lawyer being contacted
							INSERT INTO dbo.expertConnectInquiryConversationParticipants (conversationID, inquiryID, depoMemberDataID, roleID,
								firstName, lastName, email, addedByDepoMemberDataID, approvedByDepoMemberDataID, dateApproved)
							VALUES ( @conversationID, @inquiryID, @respondentDepoMemberDataID, @respondentRoleID, @respondentFirstName,
								@respondentLastName, @respondentEmail, @requesterDepoMemberDataID, @requesterDepoMemberDataID, @nowDate
							);
						</cfloop>

						<cfif arguments.updateTSRecord>
							UPDATE dbo.depomemberdata
							SET Email = @requesterEmail,
								BillingFirm = <cfqueryparam value="#arguments.firm#" cfsqltype="CF_SQL_VARCHAR">,
								BillingAddress = <cfqueryparam value="#arguments.address1#" cfsqltype="CF_SQL_VARCHAR">,
								BillingAddress2 = <cfqueryparam value="#arguments.address2#" cfsqltype="CF_SQL_VARCHAR">,
								BillingAddress3 = <cfqueryparam value="#arguments.address3#" cfsqltype="CF_SQL_VARCHAR">,
								BillingCity = ISNULL(NULLIF(<cfqueryparam value="#arguments.city#" cfsqltype="CF_SQL_VARCHAR">,''), BillingCity),
								BillingState = ISNULL(NULLIF(<cfqueryparam value="#arguments.stateCode#" cfsqltype="CF_SQL_VARCHAR">,''), BillingState),
								BillingZip = ISNULL(NULLIF(<cfqueryparam value="#arguments.postalCode#" cfsqltype="CF_SQL_VARCHAR">,''), BillingZip)
							WHERE depomemberdataid = @requesterDepoMemberDataID;
						</cfif>
						
					COMMIT TRAN;

					SELECT @requesterFirstName AS requesterFirstName, @requesterLastName AS requesterLastName,
						@expertFirstName AS expertFirstName, @expertLastName AS expertLastName;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.TSSiteInfo = application.objSiteInfo.getSiteInfo('TS')>
			<cfset local.manageInquiriesLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='TrialSmithTools',mca_ta='listDepoConnectInquiries')>

			<cfsavecontent variable="local.emailContent">
				<cfoutput>
					A DepoConnect Inquiry was just created and is awaiting approval.<br/><br/>
					Click <a href="#local.TSSiteInfo.scheme#://#local.TSSiteInfo.mainhostname##local.manageInquiriesLink#">here</a> to Manage Inquiries<br/><br/><br/>
					<b>Expert Name:</b> #local.qrySaveEmailInquiry.expertFirstName# #local.qrySaveEmailInquiry.expertLastName#<br/><br/>
					<b>Topic:</b> #arguments.topic#<br/><br/>
					<b>Description of Ask:</b> #arguments.descriptionOfAsk#<br/><br/>
					<b>Conversations:</b> #ListLen(arguments.depoMemberDataIDList)#<br/><br/>
					<b>Requestor:</b><br/>
					#local.qrySaveEmailInquiry.requesterFirstName# #local.qrySaveEmailInquiry.requesterLastName#<br/>
					#arguments.firm#
				</cfoutput>
			</cfsavecontent>
			<cfset local.emailContent = trim(replace(replace(replace(local.emailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

			<cfset local.toolSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithTools', siteID=local.TSSiteInfo.siteID)>
			<cfset local.emailSubject = "DepoConnect Inquiry Awaiting Approval (Submitted by: #local.qrySaveEmailInquiry.requesterFirstName# #local.qrySaveEmailInquiry.requesterLastName#)">

			<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="TrialSmith", email="<EMAIL>" },
				emailto=[{name:"", email:'<EMAIL>'}],
				emailreplyto="",
				emailsubject=local.emailSubject,
				emailtitle=local.emailSubject,
				emailhtmlcontent=local.emailContent,
				siteID=local.TSSiteInfo.siteID,
				memberID=local.TSSiteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EXPERTINQUIRY"),
				sendingSiteResourceID=local.toolSiteResourceID
			)>

			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfcatch type="any">
			<cfset StructInsert(local.returnStruct,"success",false)>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAttachmentInfo" access="public" returntype="query" output="no">
		<cfargument name="attachmentUUID" type="string" required="yes">
		<cfargument name="messageID" type="numeric" required="no" default="0">
		<cfargument name="conversationID" type="numeric" required="no" default="0">
		
		<cfset var qryAtttachment = "">

		<cfquery name="qryAtttachment" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT fileName, s3Key
			FROM dbo.expertConnectConversationMessageAttachments
			WHERE attachmentUUID = <cfqueryparam value="#arguments.attachmentUUID#" cfsqltype="CF_SQL_VARCHAR">
			<cfif arguments.messageID>
				AND messageID = <cfqueryparam value="#arguments.messageID#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif arguments.conversationID>
				AND conversationID = <cfqueryparam value="#arguments.conversationID#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>;
		</cfquery>

		<cfreturn qryAtttachment>
	</cffunction>

	<cffunction name="saveStatsAndBucketCount" access="private" output="false" returntype="void">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="metric" required="yes" type="string">
		<cfargument name="ms" required="yes" type="numeric">
		<cfargument name="itemCount" required="yes" type="numeric">

		<cfset saveStats(searchID=arguments.searchID, metric=arguments.metric, ms=arguments.ms, itemCount=arguments.itemCount)>
		<cfset saveBucketCount(searchID=arguments.searchID, bucketID=arguments.bucketID, itemCount=arguments.itemCount)>
	</cffunction>
</cfcomponent>