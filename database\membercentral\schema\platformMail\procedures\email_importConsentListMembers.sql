ALTER PROC dbo.email_importConsentListMembers
@orgID int,
@consentListIDList varchar(max), 
@enteredByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportConsentListMemberErrors') IS NOT NULL 
		DROP TABLE #tblImportConsentListMemberErrors;
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols;
	CREATE TABLE #tblImportConsentListMemberErrors (rowid int IDENTITY(1,1), msg varchar(600));
	CREATE TABLE #tblImportCols (columnName varchar(200));
	
	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#mc_consentListMembersImport') IS NULL BEGIN
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to locate the imported data for processing.');
		GOTO on_done;
	END

	-- ensure all required columns exist
	BEGIN TRY
		INSERT INTO #tblImportCols (columnName) 
		VALUES ('rowID'), ('Email'), ('DateEntered');

		IF NOT EXISTS (select 1 from tempdb.sys.columns where object_id = object_id('tempdb..#mc_consentListMembersImport') and [name] = 'Email') BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) 
			VALUES ('The required column "Email" was not provided.');

			GOTO on_done;
		END
		
		IF EXISTS (select 1 from tempdb.sys.columns where object_id = object_id('tempdb..#mc_consentListMembersImport') and [name] not in (select columnName from #tblImportCols)) BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) 
			select 'The invalid column "' + cast([name] as varchar(300)) + '" should be removed.'
			from tempdb.sys.columns 
			where object_id = object_id('tempdb..#mc_consentListMembersImport') 
			and [name] not in (select columnName from #tblImportCols);

			GOTO on_done;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to validate if the file contains all required columns.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- validate columns
	BEGIN TRY
		-- no blank email
		update #mc_consentListMembersImport set Email = '' where Email is null;

		INSERT INTO #tblImportConsentListMemberErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Email.'
		FROM #mc_consentListMembersImport
		WHERE Email = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;
		
		-- email must be valid
		INSERT INTO #tblImportConsentListMemberErrors (msg)
		SELECT distinct 'Email "' + Email + '" is invalid.'
		FROM #mc_consentListMembersImport
		WHERE memberCentral.dbo.fn_RegExReplace(Email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') <> ''
		ORDER BY 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- validate DateEntered
		BEGIN TRY
			ALTER TABLE #mc_consentListMembersImport ALTER COLUMN DateEntered varchar(max) null;
			UPDATE #mc_consentListMembersImport SET DateEntered = null WHERE DateEntered = '';
			ALTER TABLE #mc_consentListMembersImport ALTER COLUMN DateEntered datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('DateEntered column contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- update DateEntered
		UPDATE #mc_consentListMembersImport SET DateEntered = GETDATE() WHERE DateEntered IS NULL;

		IF ISNULL(@consentListIDList,'') = '' BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('No Consent Lists selected.');
			GOTO on_done;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to validate data in required columns.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- import data
	BEGIN TRY
		DECLARE @changeSetUID uniqueidentifier, @nowDate datetime, @addActionID int;
		SET @changeSetUID = NEWID();
		SET @nowDate = GETDATE();
		SELECT @addActionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Add';

		DECLARE @tblemail_consentListMemberHistory TABLE (changeSetUID uniqueidentifier, email varchar(200), consentListID int,
			actionID int, updateDate datetime, enteredByMemberID int);
		
		BEGIN TRAN;
			INSERT INTO dbo.email_consentListMembers (consentListID, email, dateCreated)
				OUTPUT @changeSetUID, inserted.email, inserted.consentListID, @addActionID, @nowDate, @enteredByMemberID
				INTO @tblemail_consentListMemberHistory (changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
			SELECT cl.consentListID, imp.email, MIN(imp.dateEntered)
			FROM #mc_consentListMembersImport as imp
			CROSS APPLY memberCentral.dbo.fn_intListToTable(@consentListIDList,',') as tmp
			INNER JOIN dbo.email_consentLists as cl on cl.consentListID = tmp.listitem
				AND cl.[status] = 'A'
			INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
			LEFT OUTER JOIN dbo.email_consentListMembers as clm on clm.consentListID = cl.consentListID
				AND clm.email = imp.email
			WHERE clt.orgID = @orgID
			AND clm.consentListMemberID IS NULL
			GROUP BY cl.consentListID, imp.email;

			INSERT INTO dbo.email_consentListMemberHistory (changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
			SELECT changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID
			FROM @tblemail_consentListMemberHistory;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to import data.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT memberCentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportConsentListMemberErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportConsentListMemberErrors') IS NOT NULL
		DROP TABLE #tblImportConsentListMemberErrors;
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
