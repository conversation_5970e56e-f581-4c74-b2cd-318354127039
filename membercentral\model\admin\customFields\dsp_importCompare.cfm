<cfoutput>
<div id="divImportCompareCustomField">
	<div class="row my-3">
		<div class="col-md-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Custom Fields Import Comparison
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="mb-2">The following differences were found:</div>
					<cfif arrayLen(arguments.strImportResult.arrNewFields)>
						<table class="table table-sm table-striped">
							<thead>
								<tr>
									<th class="font-weight-bold">Custom Fields to be Added</th>
									<th class="font-weight-bold">UID</th>
								</tr>
							</thead>
							<tbody>
								<cfloop array="#arguments.strImportResult.arrNewFields#" index="local.thisDiff">
									<tr>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.columnName#</td>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.uid#</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>

					<cfif arrayLen(arguments.strImportResult.arrUpdateFields)>
						<table class="table table-sm">
							<thead>
								<tr>
									<th class="font-weight-bold">Custom Fields to be Updated</th>
									<th class="font-weight-bold">UID</th>
								</tr>
							</thead>
							<tbody>
								<cfloop query="local.qryOrgUpdateFields">
									<cfquery name="local.qryThisImportFileField" dbtype="query">
										select [uid], columnID, columnName, columnDesc, dataTypeCode, displayTypeCode, dataType, displayType, allowNewValuesOnImport, allowNull, isReadOnly, allowMultiple, 
											minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, defaultValue,
											linkedDateColumnName, linkedDateCompareDate, linkedDateCompareDateAFName, linkedDateAdvanceDate, linkedDateAdvanceDateAFName
										from [local].qryImportFileUpdateFields
										where uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryOrgUpdateFields.uid#">
									</cfquery>

									<tr>
										<td class="font-size-sm">
											<span class="font-weight-bold">#local.qryOrgUpdateFields.columnName#</span>
											<cfif CompareNoCase(local.qryOrgUpdateFields.dataType,local.qryThisImportFileField.dataType)>
												<div class="ml-2">- <span class="text-info">Data Type</span> will change from <span class="text-danger"><cfif len(local.qryOrgUpdateFields.datatype)>#local.qryOrgUpdateFields.datatype#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif len(qryThisImportFileField.datatype)>#qryThisImportFileField.datatype#<cfelse>[blank]</cfif></span></div>
											</cfif>
											<cfif CompareNoCase(local.qryOrgUpdateFields.displayType,local.qryThisImportFileField.displayType)>
												<div class="ml-2">- <span class="text-info">Display Type</span> will change from <span class="text-danger"><cfif len(local.qryOrgUpdateFields.displaytype)>#local.qryOrgUpdateFields.displaytype#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif len(qryThisImportFileField.displaytype)>#qryThisImportFileField.displaytype#<cfelse>[blank]</cfif></span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.columnName,local.qryThisImportFileField.columnName)>
												<div class="ml-2">- <span class="text-info">Column Name</span> will change from <span class="text-danger">#local.qryOrgUpdateFields.columnname#</span> to <span class="text-success">#local.qryThisImportFileField.columnName#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.defaultValue,local.qryThisImportFileField.defaultValue)>
												<div class="ml-2">- 
												<cfif Len(local.qryOrgUpdateFields.defaultValue) and local.qryOrgUpdateFields.allowNull eq 1>
													<span class="text-info">Default Value</span> is changed from <span class="text-danger">#local.qryOrgUpdateFields.defaultvalue#</span> to <span class="text-success">#local.qryThisImportFileField.defaultValue#</span><br/>&nbsp; (Since Allow Null is set, we cannot import this option.)
												<cfelse>
													<span class="text-info">Default Value</span> will change from <span class="text-danger"><cfif Len(local.qryOrgUpdateFields.defaultValue)>#local.qryOrgUpdateFields.defaultvalue#<cfelse>[blank]</cfif></span> to <span class="text-success">#local.qryThisImportFileField.defaultValue#</span>
												</cfif>	
												</div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.ColumnDesc,local.qryThisImportFileField.ColumnDesc)>
												<div class="ml-2">- <span class="text-info">Column Description</span> will change from <span class="text-danger"><cfif len(local.qryOrgUpdateFields.columndesc)>#local.qryOrgUpdateFields.columndesc#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif len(local.qryThisImportFileField.ColumnDesc)>#local.qryThisImportFileField.ColumnDesc#<cfelse>[blank]</cfif></span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.allowNewValuesOnImport,local.qryThisImportFileField.allowNewValuesOnImport)>
												<div class="ml-2">- <span class="text-info">Allow New Values on Import</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateFields.allownewvaluesonimport)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportFileField.allowNewValuesOnImport)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.allowNull,local.qryThisImportFileField.allowNull)>
												<cfset local.changeDetlMsg = "">
												<cfif local.qryOrgUpdateFields.allowNull eq 0>
													<cfif Len(local.qryOrgUpdateFields.defaultValue) eq 0>
														<cfset local.changeDetlMsg = "Default Value is not set">
													<cfelseif listFindNoCase("CONTENTOBJ,DOCUMENTOBJ",local.qryOrgUpdateFields.dataTypeCode)>
														<cfset local.changeDetlMsg = "Data Type is Content Type or Document">
													</cfif>
												</cfif>
												<div class="ml-2">- 
													<cfif Len(local.changeDetlMsg)>
														<span class="text-info">Allow Null</span> is changed from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateFields.allownull)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportFileField.allowNull)#</span><br/>&nbsp; (Since #local.changeDetlMsg#, we cannot import this option)
													<cfelse>
														<span class="text-info">Allow Null</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateFields.allownull)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportFileField.allowNull)#</span>
													</cfif>
												</div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.isReadOnly,local.qryThisImportFileField.isReadOnly)>
												<div class="ml-2">- <span class="text-info">Is Read Only</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateFields.isreadonly)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportFileField.isReadOnly)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.allowMultiple,local.qryThisImportFileField.allowMultiple)>
												<div class="ml-2">- <span class="text-info">Allow Multiple</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateFields.allowmultiple)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportFileField.allowMultiple)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.minChars,local.qryThisImportFileField.minChars)>
												<div class="ml-2">- <span class="text-info">Minimum characters</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.minchars)#</span> to <span class="text-success">#val(local.qryThisImportFileField.minChars)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.maxChars,local.qryThisImportFileField.maxChars)>
												<div class="ml-2">- <span class="text-info">Maximum characters</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.maxchars)#</span> to <span class="text-success">#val(local.qryThisImportFileField.maxChars)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.minSelected,local.qryThisImportFileField.minSelected)>
												<div class="ml-2">- <span class="text-info">Minimum options</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.minselected)#</span> to <span class="text-success">#val(local.qryThisImportFileField.minSelected)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.maxSelected,local.qryThisImportFileField.maxSelected)>
												<div class="ml-2">- <span class="text-info">Maximum options</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.maxselected)#</span> to <span class="text-success">#val(local.qryThisImportFileField.maxSelected)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.minValueInt,local.qryThisImportFileField.minValueInt)>
												<div class="ml-2">- <span class="text-info">Minimum value</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.minvalueint)#</span> to <span class="text-success">#val(local.qryThisImportFileField.minValueInt)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.maxValueInt,local.qryThisImportFileField.maxValueInt)>
												<div class="ml-2">- <span class="text-info">Maximum value</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.maxvalueint)#</span> to <span class="text-success">#val(local.qryThisImportFileField.maxValueInt)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.minValueDecimal2,local.qryThisImportFileField.minValueDecimal2)>
												<div class="ml-2">- <span class="text-info">Minimum value</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.minvaluedecimal2)#</span> to <span class="text-success">#val(local.qryThisImportFileField.minValueDecimal2)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.maxValueDecimal2,local.qryThisImportFileField.maxValueDecimal2)>
												<div class="ml-2">- <span class="text-info">Maximum value</span> will change from <span class="text-danger">#val(local.qryOrgUpdateFields.maxvaluedecimal2)#</span> to <span class="text-success">#val(local.qryThisImportFileField.maxValueDecimal2)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.minValueDate,local.qryThisImportFileField.minValueDate)>
												<div class="ml-2">- <span class="text-info">Minimum date</span> will change from <span class="text-danger">#local.qryOrgUpdateFields.minvaluedate#</span> to <span class="text-success">#local.qryThisImportFileField.minValueDate#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.maxValueDate,local.qryThisImportFileField.maxValueDate)>
												<div class="ml-2">- <span class="text-info">Maximum date</span> will change from <span class="text-danger">#local.qryOrgUpdateFields.maxvaluedate#</span> to <span class="text-success">#local.qryThisImportFileField.maxValueDate#</span></div>
											</cfif>
											<cfif CompareNoCase(local.qryOrgUpdateFields.linkedDateColumnName,local.qryThisImportFileField.linkedDateColumnName)>
												<div class="ml-2">- <span class="text-info">Linked Date Column</span> will change from <span class="text-danger">#len(local.qryOrgUpdateFields.linkedDateColumnName) ? local.qryOrgUpdateFields.linkedDateColumnName : "[blank]"#</span> to <span class="text-success">#len(local.qryThisImportFileField.linkedDateColumnName) ? local.qryThisImportFileField.linkedDateColumnName : "[blank]"#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.linkedDateCompareDate,local.qryThisImportFileField.linkedDateCompareDate)>
												<div class="ml-2">- <span class="text-info">Linked Date Column Compare Date</span> will change from <span class="text-danger">#len(local.qryOrgUpdateFields.linkedDateCompareDate) ? DateFormat(local.qryOrgUpdateFields.linkedDateCompareDate,'m/d/yyyy') : "[blank]"#</span> to <span class="text-success">#len(local.qryThisImportFileField.linkedDateCompareDate) ? DateFormat(local.qryThisImportFileField.linkedDateCompareDate,'m/d/yyyy') : "[blank]"#</span></div>
											</cfif>
											<cfif CompareNoCase(local.qryOrgUpdateFields.linkedDateCompareDateAFName,local.qryThisImportFileField.linkedDateCompareDateAFName)>
												<div class="ml-2">- <span class="text-info">Linked Date Column Compare Date Advance Formula</span> will change from <span class="text-danger">#len(local.qryOrgUpdateFields.linkedDateCompareDateAFName) ? local.qryOrgUpdateFields.linkedDateCompareDateAFName : "[blank]"#</span> to <span class="text-success">#len(local.qryThisImportFileField.linkedDateCompareDateAFName) ? local.qryThisImportFileField.linkedDateCompareDateAFName : "[blank]"#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateFields.linkedDateAdvanceDate,local.qryThisImportFileField.linkedDateAdvanceDate)>
												<div class="ml-2">- <span class="text-info">Linked Date Column Advance Date</span> will change from <span class="text-danger">#len(local.qryOrgUpdateFields.linkedDateAdvanceDate) ? DateFormat(local.qryOrgUpdateFields.linkedDateAdvanceDate,'m/d/yyyy') : "[blank]"#</span> to <span class="text-success">#len(local.qryThisImportFileField.linkedDateAdvanceDate) ? DateFormat(local.qryThisImportFileField.linkedDateAdvanceDate,'m/d/yyyy') : "[blank]"#</span></div>
											</cfif>
											<cfif CompareNoCase(local.qryOrgUpdateFields.linkedDateAdvanceDateAFName,local.qryThisImportFileField.linkedDateAdvanceDateAFName)>
												<div class="ml-2">- <span class="text-info">Linked Date Column Advance Date Advance Formula</span> will change from <span class="text-danger">#len(local.qryOrgUpdateFields.linkedDateAdvanceDateAFName) ? local.qryOrgUpdateFields.linkedDateAdvanceDateAFName : "[blank]"#</span> to <span class="text-success">#len(local.qryThisImportFileField.linkedDateAdvanceDateAFName) ? local.qryThisImportFileField.linkedDateAdvanceDateAFName : "[blank]"#</span></div>
											</cfif>
										</td>
										<td class="align-top font-size-sm">#local.qryOrgUpdateFields.uid#</td>
									</tr>
									
									<cfif local.qryAddNewFieldValuesForUpdateField.recordCount>
										<cfquery name="local.qryThisNewFieldValues" dbtype="query">
											select dataTypeCode, fieldValue
											from [local].qryAddNewFieldValuesForUpdateField
											where uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.qryOrgUpdateFields.uid#">
										</cfquery>

										<tr>
											<td class="font-size-sm border-top-0" colspan="2">
												<div class="ml-2"><span class="font-weight-bold">- Values To Be Added:</span></div>
											</td>
										</tr>

										<cfloop query="local.qryThisNewFieldValues">
											<tr>
												<td class="font-size-sm border-top-0 py-0" colspan="2">
													<div class="ml-4">
														- <span class="text-info">
															<cfif local.qryThisNewFieldValues.dataTypeCode eq "DATE">#DateFormat(local.qryThisNewFieldValues.fieldvalue, "mm/dd/yyyy")#<cfelse>#local.qryThisNewFieldValues.fieldvalue#</cfif>
														</span>
													</div>
												</td>
											</tr>
										</cfloop>
									</cfif>
									
									<cfif arrayLen(arguments.strImportResult.arrRemoveValues)>
										<cfset var thisUID = local.qryOrgUpdateFields.uid>
										<cfset local.arrThisFieldRemoveValues = arguments.strImportResult.arrRemoveValues.filter(function(thisDiff){
																					return thisDiff.xmlAttributes.uid eq thisUID;
																				})>
										<tr>
											<td class="font-size-sm border-top-0" colspan="2">
												<div class="ml-2"><span class="font-weight-bold">- Values To Be Removed:</span></div>
											</td>
										</tr>

										<cfloop array="#local.arrThisFieldRemoveValues#" index="local.thisDiffRemoveValue">
											<tr>
												<td class="font-size-sm border-top-0 py-0">
													<div class="ml-4">- <span class="text-danger">#local.thisDiffRemoveValue.xmlAttributes.value#</span></div>
												</td>
												<td class="font-size-sm border-top-0 py-0">User count: #local.thisDiffRemoveValue.xmlAttributes.usecount#</td>
											</tr>
										</cfloop>
									</cfif>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>

					<cfif arrayLen(arguments.strImportResult.arrRemoveFields)>
						<table class="table table-sm table-striped">
							<thead>
								<tr>
									<th class="font-weight-bold">Custom Fields to be Removed</th>
									<th class="font-weight-bold">UID</th>
								</tr>
							</thead>
							<tbody>
								<cfloop array="#arguments.strImportResult.arrRemoveFields#" index="local.thisDiff">
									<tr>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.columnName#</td>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.uid#</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>
					
					<br/>
					<form name="frmImportCustomFields" id="frmImportCustomFields">
						<input type="hidden" name="impThreadID" id="impThreadID" value="#arguments.threadID#">
						<button type="button" class="btn btn-sm btn-primary" name="btnImportChanges" id="btnImportChanges" onclick="continueCustomFieldImport();">Continue Import</button> &nbsp; 
						<button type="button" class="btn btn-sm btn-secondary" name="btnReturnToFields" id="btnReturnToFields" onclick="gotoCustomFieldList();">Cancel Import</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="divImportCompareCustomFieldSubmitArea" style="display:none;"></div>

<div id="divImportCustomFieldLoading" style="display:none;">
	<h4>Custom Fields Import</h4>
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="mt-2 font-weight-bold">Please wait while we import the changes.</div>
	</div>
</div>
</cfoutput>