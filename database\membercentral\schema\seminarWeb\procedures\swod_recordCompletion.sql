ALTER PROC dbo.swod_recordCompletion
@enrollmentID int,
@userTimeSpent int,
@realTimeSpent int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @siteSRID int, @seminarID int, @registrantName varchar(300), @dataXML xml;
	DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

	if exists (select enrollmentID from dbo.tblenrollments where enrollmentID = @enrollmentID and dateCompleted is not null)
		RAISERROR('Already enrolled.',16,1);

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @seminarID = s.seminarID
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE e.enrollmentID = @enrollmentID;

	SELECT @siteSRID = siteResourceID from membercentral.dbo.sites where siteID = @siteID;

	BEGIN TRAN;
		-- update master enrollment
		UPDATE dbo.tblenrollments
		set dateCompleted = getdate(), passed = 1
		where enrollmentID = @enrollmentID;

		-- get date for now and when enrolled in course
		-- no matter the creditbase, time cannot be more than time from dateenrolled to now
		DECLARE @dateEnrolled datetime, @timepassed int;
		SELECT @dateEnrolled = dateEnrolled FROM dbo.tblEnrollments where enrollmentID = @enrollmentID;
		SELECT @timepassed = dateDiff(minute,@dateEnrolled,getdate());

		-- temp holder table for times
		DECLARE @tmpTimes TABLE (autoid int, timepassed int);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (1, @timepassed);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (2, @userTimeSpent);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (3, @realTimeSpent);

		-- update final time spent column with realTimeSpent
		UPDATE dbo.tblEnrollmentsAndCredit
		SET finalTimeSpent = (select min(timepassed) from @tmpTimes where autoid in (1,3))
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID IN (
			SELECT eac.enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit AS eac 
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON csa.authorityID = casod.authorityID 
			WHERE eac.enrollmentID = @enrollmentID
		);

		UPDATE tblEnrollmentsSWOD
		SET calcTimeSpent = (select min(timepassed) from @tmpTimes where autoid in (1,3))
		WHERE enrollmentID = @enrollmentID;

		-- update any non-expired credits
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 1
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID NOT IN (
			SELECT eac.enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit AS eac 
			INNER JOIN dbo.tblEnrollments as e on e.enrollmentID = eac.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csaa ON sac.CSALinkID = csaa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csaa.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON casod.authorityID = ca.authorityID
			WHERE e.enrollmentID = @enrollmentID
			AND getdate() > eac.lastDateToComplete
		);

		IF @@ROWCOUNT > 0 BEGIN
			SELECT @dataXML = 
				ISNULL((
					SELECT 'semweb' AS itemtype, @enrollmentID AS itemid for xml path ('data')
				),'<data/>');

			INSERT INTO @tblHookListeners (executionType, objectPath)
			EXEC memberCentral.dbo.hooks_runHook @event='creditAwarded', @siteResourceID=@siteSRID, @dataXML=@dataXML;
		END
	COMMIT TRAN;

	-- process conditions based on seminarweb
	DECLARE @depomemberdataID int;
	SET @depomemberdataID = dbo.fn_getDepoMemberDataIDFromEnrollmentID(@enrollmentID);
	EXEC membercentral.dbo.ams_processSeminarWebConditionsByDepoMemberDataID @depomemberdataID=@depomemberdataID;

	SELECT @registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) + ']' + ISNULL(' (' + m2.membernumber + ')','')
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	LEFT OUTER JOIN membercentral.dbo.ams_members AS m
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
	ON m.memberID = e.MCMemberID
	WHERE e.enrollmentID = @enrollmentID;
	
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWOD-' + CAST(@seminarID AS VARCHAR(10)) + ' has been marked as completed for registrant '+ @registrantName + '.'),'"','\"') + '" } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
