<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getEmailBlasts" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			arguments.event.paramValue('dsp','all');
		</cfscript>

		<cfset local.fCategoryID = arguments.event.getValue('fCategory',0)>
		<cfset local.fSubCategoryID = arguments.event.getValue('fSubCategory',0)>
		<cfset local.fKeyword = arguments.event.getValue('fKeyword','')>
		<cfset local.fMessage = arguments.event.getValue('fMessage','')>
		<cfset local.fCreatedFrom = arguments.event.getValue('fCreatedFrom','')>
		<cfset local.fCreatedTo = arguments.event.getValue('fCreatedTo','')>
		<cfset local.fScheduledFrom = arguments.event.getValue('fScheduledFrom','')>
		<cfset local.fScheduledTo = arguments.event.getValue('fScheduledTo','')>
		<cfset local.fSentFrom = arguments.event.getValue('fSentFrom','')>
		<cfset local.fSentTo = arguments.event.getValue('fSentTo','')>
		<cfset local.fRecurring = arguments.event.getValue('fRecurring',0)>
		<cfset local.fOptOutsBypassed = arguments.event.getValue('fOptOutsBypassed',0)>

		<cfif len(local.fCreatedFrom) gt 0>
			<cfset local.fCreatedFrom = DateFormat(local.fCreatedFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fCreatedTo) gt 0>
			<cfset local.fCreatedTo = DateFormat(local.fCreatedTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fScheduledFrom) gt 0>
			<cfset local.fScheduledFrom = DateFormat(local.fScheduledFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fScheduledTo) gt 0>
			<cfset local.fScheduledTo = DateFormat(local.fScheduledTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fSentFrom) gt 0>
			<cfset local.fSentFrom = DateFormat(local.fSentFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fSentTo) gt 0>
			<cfset local.fSentTo = DateFormat(local.fSentTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fMessage)>
			<cfset local.searchterms = createObject("component","model.search.bucket").prepareSearchString(stringToClean=local.fMessage)>
			<cfif len(local.searchterms)>
				<cfset local.searchterms = local.searchterms & " AND (mcsitecode#arguments.event.getValue('mc_siteinfo.sitecode')#xxx AND mcresourcetypeApplicationCreatedContentxxx)">
			</cfif>
		</cfif>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"eb.blastName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"cl.contentTitle #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"eb.dateCreated #arguments.event.getValue('orderDir')#")>
		<cfif arguments.event.getValue('orderDir') eq "desc">
			<cfset arrayAppend(local.arrCols,"eb.emailDateSent #arguments.event.getValue('orderDir')#")>
		<cfelse>
			<cfset arrayAppend(local.arrCols,"isnull(eb.emailDateSent,@nowDate) #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryBlasts" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpEmailBlasts') IS NOT NULL
					DROP TABLE ##tmpEmailBlasts;
				CREATE TABLE ##tmpEmailBlasts (blastID int PRIMARY KEY, blastName varchar(200), categoryName varchar(200), subCategoryName varchar(200),
					subject varchar(200), afName varchar(200), memberID int, lastname varchar(75), firstname varchar(75), middlename varchar(25),
					membernumber varchar(50), dateCreated datetime, emailDateScheduled datetime, emailDateSent datetime, endRunDate datetime, row int);

				DECLARE @nowDate datetime = getdate(), @totalCount int, @siteID int, @orgID int, @resourceTypeID int = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				<cfif len(local.fMessage)>
					DECLARE @fullTextKeys TABLE (searchKey int PRIMARY KEY, rank int, contentLanguageID int INDEX fullTextKeys_contentLang);
					DECLARE @fulltextkeywords varchar(8000) = '#local.searchterms#';

					INSERT INTO @fullTextKeys(searchKey, rank)
					SELECT sclsearch.[key], rank
					FROM containstable(searchMC.dbo.cms_contentLanguages,searchtext,@fulltextkeywords) as sclsearch;

					UPDATE sclsearch 
					SET contentLanguageID = cl.contentLanguageID
					FROM searchMC.dbo.cms_contentLanguages AS cl
					INNER JOIN @fullTextKeys AS sclsearch 
						ON cl.siteID=@siteID
						and cl.resourceTypeID = @resourceTypeID
						and sclsearch.searchKey = cl.id
				</cfif>

				INSERT INTO ##tmpEmailBlasts (blastID, blastName, categoryName, subCategoryName, subject, afName, memberID, lastname, firstname, middlename,
					membernumber, dateCreated, emailDateScheduled, emailDateSent, endRunDate, row)
				SELECT DISTINCT eb.blastID, eb.blastName, c.categoryName as categoryName, subc.categoryName as subCategoryName, cl.contentTitle as subject,
					af.afName, am.memberID, am.lastname, am.firstname, am.middlename, am.membernumber, eb.dateCreated, eb.emailDateScheduled, eb.emailDateSent, eb.endRunDate,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
				FROM dbo.email_EmailBlasts as eb
				inner join dbo.cms_content as ebc on ebc.siteID = @siteID
					and ebc.contentID = eb.contentID
				inner join dbo.cms_contentLanguages as cl on cl.siteID = @siteID
					and cl.contentID = ebc.contentID 
					and cl.languageID = 1
				inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
					and cv.contentID = cl.contentID
					and cv.contentLanguageID = cl.contentLanguageID 
					and cv.isActive = 1
				<cfif len(local.fMessage)>
					inner join @fullTextKeys as fsearch on fsearch.contentLanguageID = cl.contentLanguageID
				</cfif>
				inner join dbo.ams_members as m on m.orgID in (@orgID,1)
					and m.memberID = eb.memberID
				inner join dbo.ams_members as am on m.orgID = am.orgID
					and am.memberID = m.activeMemberID
				inner join dbo.cms_categories as c on c.categoryID = eb.categoryID
				left outer join dbo.cms_categories as subc on subc.categoryID = eb.subcategoryID
				left outer join dbo.af_advanceFormulas af on af.AFID = eb.afID
				<cfif local.fOptOutsBypassed>
					left outer join email_emailBlastConsentLists ebcl
						on ebcl.blastID = eb.blastID
				</cfif>
				where eb.siteID = @siteID
				<cfif local.fOptOutsBypassed>
					and ebcl.blastID is null
				</cfif>
				<cfif arguments.event.getValue('dsp') neq "all">
					and am.memberid = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
				</cfif>
				<cfif local.fCategoryID gt 0>
					and eb.categoryID = <cfqueryparam value="#local.fCategoryID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif local.fSubCategoryID neq 0>
					and subc.categoryID in (0#local.fSubCategoryID#)
				</cfif>
				<cfif len(local.fCreatedFrom)>
					and eb.dateCreated >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fCreatedFrom#">
				</cfif>
				<cfif len(local.fCreatedTo)>
					and eb.dateCreated <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fCreatedTo#">
				</cfif>
				<cfif len(local.fScheduledFrom)>
					and eb.emailDateScheduled >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fScheduledFrom#">
				</cfif>
				<cfif len(local.fScheduledTo)>
					and eb.emailDateScheduled <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fScheduledTo#">
				</cfif>
				<cfif len(local.fSentFrom)>
					and eb.emailDateSent >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fSentFrom#">
				</cfif>
				<cfif len(local.fSentTo)>
					and eb.emailDateSent <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fSentTo#">
				</cfif>
				<cfif len(local.fKeyword)>
					and eb.blastName like <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="%#local.fKeyword#%">
				</cfif>
				<cfif local.fRecurring eq 1>
					and eb.AFID is not null
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT blastID, blastName, categoryName, subCategoryName, subject, afName, memberID, lastname, firstname, middlename, membernumber, 
					dateCreated, emailDateScheduled, emailDateSent, endRunDate, 
					consentLists = STUFF((SELECT '|' + tclt.consentListTypeName + '/' + tcl.consentListName 
						from email_EmailBlasts teb 
						inner join email_emailBlastConsentLists tebcl 
							on teb.blastID = tebcl.blastID
							and teb.siteID = @siteID
							and teb.blastID = eb.blastID
						inner join platformmail.dbo.email_consentLists tcl 
							on tcl.consentListID = tebcl.consentListID
							and tcl.[status] = 'A'
						inner join platformmail.dbo.email_consentListTypes tclt
							on tclt.consentListTypeID = tcl.consentListTypeID
						order by tclt.consentListTypeName , tcl.consentListName
						FOR XML PATH(''), TYPE).value('.','varchar(max)'),1,1,''),
					row, @totalCount as totalCount
				FROM ##tmpEmailBlasts eb
				WHERE row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
				AND row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpEmailBlasts') IS NOT NULL
					DROP TABLE ##tmpEmailBlasts;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.event.getValue('srid'), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		
		<cfset local.data = []>
		<cfloop query="local.qryBlasts">
			<cfset local.tmpStr = {
				"blastid": local.qryBlasts.blastID,
				"blastname": local.qryBlasts.blastName,
				"subject": local.qryBlasts.subject,
				"categoryname": local.qryBlasts.categoryName,
				"subcategoryname": local.qryBlasts.subCategoryName,
				"consentlists": replace(local.qryBlasts.consentLists,"|",", ","all"),
				"afname": local.qryBlasts.afName,
				"memberid": local.qryBlasts.memberID,
				"creatorname": "#local.qryBlasts.lastName#, #local.qryBlasts.firstname# #local.qryBlasts.middlename# (#local.qryBlasts.membernumber#)",
				"lastsentdatetime": "#len(local.qryBlasts.emailDateSent) gt 0 ? "#dateFormat(local.qryBlasts.emailDateSent,"m/d/yyyy")# #timeFormat(local.qryBlasts.emailDateSent,"h:mm tt")#" : ''#",
				"createddatetime": dateTimeFormat(local.qryBlasts.dateCreated,'m/d/yyyy'),
				"scheduleddatetime": "#len(local.qryBlasts.emailDateScheduled) gt 0 ? "#dateFormat(local.qryBlasts.emailDateScheduled,"m/d/yyyy")# #timeFormat(local.qryBlasts.emailDateScheduled,"h:mm tt")#" : ''#",
				"endrundate": "#len(local.qryBlasts.endRunDate) gt 0 ? "#dateFormat(local.qryBlasts.endRunDate,"m/d/yyyy")# #timeFormat(local.qryBlasts.endRunDate,"h:mm tt")#" : ''#",
				"editrights": val(local.tmpRights.editAnyBlast) or (val(local.tmpRights.editOwnBlast) and local.qryBlasts.memberID eq session.cfcuser.memberdata.memberID),
				"deleterights": val(local.tmpRights.deleteAnyBlast) or (val(local.tmpRights.deleteOwnBlast) and local.qryBlasts.memberID eq session.cfcuser.memberdata.memberID),
				"DT_RowId": "row_#local.qryBlasts.blastID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryBlasts.totalCount),
			"recordsFiltered": val(local.qryBlasts.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSuppressedEmails" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.fCreatedFrom = arguments.event.getValue('fCreatedFrom','')>
		<cfset local.fCreatedTo = arguments.event.getValue('fCreatedTo','')>
		
		<cfset local.strContentArgs = {
			siteID=local.siteID,
			mode = "grid",
			orderby=arguments.event.getValue('orderby'),
			orderDir=arguments.event.getValue('orderDir'),
			posstart=arguments.event.getValue('posStart'),
			count=arguments.event.getValue('count'),
			fCreatedFrom = replace(local.fCreatedFrom,' - ',' '),
			fCreatedTo = replace(local.fCreatedTo,' - ',' '),
			fEmailsList = arguments.event.getValue('fEmailsList',''),
			fSuppressionStatuses = arguments.event.getValue('fSuppressionStatuses',''),
			fTypeOfEmail = arguments.event.getValue('fTypeOfEmail',''),
			fAssociatedMemberID = val(arguments.event.getValue('fAssociatedMemberID',0)),
			fAssociatedGroupID = val(arguments.event.getValue('fAssociatedGroupID',0)),
			fDomain = arguments.event.getValue('fDomain','')
		}>

		<cfset local.qrysuppressionList = CreateObject("component","suppressionListEmails").getSuppressedEmails(argumentcollection=local.strContentArgs)>

		<cfset local.data = []>
		<cfloop query="local.qrysuppressionList">
			<cfset local.arrLinkedMembers = []>
			<cfif listLen(local.qrysuppressionList.linkedMembers,"^---^")>
				<cfset local.linkedMembersArr = listToArray(local.qrysuppressionList.linkedMembers,"^---^",true,true)>
				<cfloop array="#local.linkedMembersArr#" index="local.thisLinkedMemberInfo">
					<cfset local.thisLinkedMemberInfoArr = listToArray(local.thisLinkedMemberInfo,"^***^",true,true)>
					<cfset arrayAppend(local.arrLinkedMembers, {
						"memberid" = local.thisLinkedMemberInfoArr[1],
						"membername" = local.thisLinkedMemberInfoArr[2],
						"membernumber" = local.thisLinkedMemberInfoArr[3]
					})>
				</cfloop>
			</cfif>

			<cfset local.tmpStr = {
				"entryid": local.qrySuppressionList.entryID,
				"email": local.qrySuppressionList.email,
				"linkedmembersarr": local.arrLinkedMembers,
				"statusarr": listToArray(local.qrySuppressionList.status),
				"mailtypearr": listToArray(local.qrySuppressionList.mailtype),
				"reasonarr": listToArray(local.qrySuppressionList.reason,"^---^",true,true),
				"datecreated": "#dateFormat(local.qrySuppressionList.dateCreated,"m/d/yyyy")# #timeFormat(local.qrySuppressionList.dateCreated,"h:mm tt")#",
				"subuserids": local.qrySuppressionList.subuserids,
				"DT_RowId": "row_#local.qrySuppressionList.entryID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySuppressionList.totalCount),
			"recordsFiltered": val(local.qrySuppressionList.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAttachments" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qryAttachments = CreateObject('component','emailBlast').getEmailBlastAttachments(blastID=arguments.event.getValue('blastID'), 
									orderBy=arguments.event.getValue('orderBy'), orderDir=arguments.event.getValue('orderDir'));
		</cfscript>

		<cfset local.data = []>
		<cfif local.qryAttachments.recordCount>
			<cfloop query="local.qryAttachments">
				<cfset local.tmpStr = {
					"documentid": local.qryAttachments.documentID,
					"doctitle": local.qryAttachments.docTitle,
					"filename": local.qryAttachments.fileName,
					"DT_RowId": "row_#local.qryAttachments.autoID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": local.qryAttachments.recordCount,
			"recordsFiltered": local.qryAttachments.recordCount,
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMessages" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.fSentFrom = replace(arguments.event.getTrimValue('fSentFrom',''),' - ',' ')>
		<cfset local.fSentTo = replace(arguments.event.getTrimValue('fSentTo',''),' - ',' ')>
		<cfif len(local.fSentTo) gt 0 and len(local.fSentTo) lte 10>
			<cfset local.fSentTo = DateFormat(local.fSentTo,"m/d/yyyy") & " 23:59:59">
		</cfif>
		<cfif NOT len(local.fSentFrom) AND NOT len(local.fSentTo)>
			<cfset local.fSentFrom = dateFormat(dateAdd("d",-30,now()), "m/d/yyyy")>
			<cfset local.fSentTo = dateFormat(now(), "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateEntered #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"subject #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfset local.qryActivity = CreateObject('component','emailBlast').getMessagesFromFilters(messageTypeID=arguments.event.getTrimValue('messageType',0), 
			categoryID=arguments.event.getValue('fCategory',0), subCategoryIDList=arguments.event.getValue('fSubCategory',0), siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
			fromName=arguments.event.getTrimValue('fromname',''), replyToEmail=arguments.event.getValue('replytoemail',''), subject=arguments.event.getTrimValue('subject',''), 
			fSentFrom=local.fSentFrom, fSentTo=local.fSentTo, emailTypeIDList=arguments.event.getTrimValue('emailType',''), msgCountFrom=arguments.event.getTrimValue('msgCountFrom',''), 
			msgCountTo=arguments.event.getTrimValue('msgCountTo',''), mode="grid", posStart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'), 
			orderBy=local.orderby,fBlastTestMsg=arguments.event.getValue('fBlastTestMsg',''))>

		<cfset local.data = []>
		<cfloop query="local.qryActivity">
			<cfset local.sendOnDate = "">
			<cfset local.openPercent = "0%">
			<cfset local.clickPercent = "0%">
			<cfset local.problemPercent = "0%">
			<cfset local.canCancelMsg = false>
			<cfset local.messageCountAfterOptOuts = local.qryActivity.messageCount - val(local.qryActivity.optout_total)>
			<cfset local.messageCountAfterSuppressions = local.messageCountAfterOptOuts - val(local.qryActivity.suppressed_unique)>
			
			<cfset local.sendOnDate = datetimeFormat(local.qryActivity.sendOnDate,"m/d/yyyy h:nn tt") & " CT">
			<cfif local.messageCountAfterSuppressions gt 0>
				<cfset local.openPercent = "#NumberFormat((val(local.qryActivity.sg_open_unique)/local.messageCountAfterSuppressions)*100, "__")#%">
			</cfif>
			<cfif local.messageCountAfterSuppressions gt 0>
				<cfset local.clickPercent = "#NumberFormat((val(local.qryActivity.sg_click_unique)/local.messageCountAfterSuppressions)*100, "__")#%">
			</cfif>
			<cfif local.messageCountAfterSuppressions gt 0>
				<cfset local.problemPercent = "#NumberFormat(((val(local.qryActivity.problems_unique) + val(local.qryActivity.suppressed_unique))/local.messageCountAfterOptOuts)*100, "__")#%">
			</cfif>
			<cfif dateCompare(local.qryActivity.sendOnDate,now(),'n') GT 0>
				<cfset local.canCancelMsg = true>
			</cfif>
			
			<cfset local.tmpStr = {
				"messageID": local.qryActivity.messageID,
				"sendOnDate": local.sendOnDate,
				"subject": local.qryActivity.subject,
				"replyToEmail": local.qryActivity.replyToEmail,
				"fromName": local.qryActivity.fromName,
				"messageCount": local.messageCountAfterSuppressions,
				"sg_open_total": val(local.qryActivity.sg_open_total),
				"optout_total": val(local.qryActivity.optout_total),
				"sg_open_unique": val(local.qryActivity.sg_open_unique),
				"sg_click_total": val(local.qryActivity.sg_click_total),
				"sg_click_unique": val(local.qryActivity.sg_click_unique),
				"suppressed_unique": val(local.qryActivity.suppressed_unique),
				"problems_unique": val(local.qryActivity.problems_unique),
				"openPercent": local.openPercent,
				"clickPercent": local.clickPercent,
				"problemPercent": local.problemPercent,
				"canCancelMsg": local.canCancelMsg,
				"DT_RowId": "messageRow_#local.qryActivity.messageID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryActivity.totalCount),
			"recordsFiltered": val(local.qryActivity.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getEmailRecipients" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 5)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qryActivity = CreateObject('component','emailBlast').getEmailRecipientsFromFilters(event=arguments.event, gridMode=arguments.event.getValue('gridMode',''));
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryActivity">
			<cfset local.tmpStr = {
				"recipientID": local.qryActivity.recipientID,
				"messageID": local.qryActivity.messageID,
				"memberID": local.qryActivity.memberID,
				"memberName": local.qryActivity.memberName,
				"company": local.qryActivity.company,
				"toEmail": local.qryActivity.toEmail,
				"subject": local.qryActivity.subject,
				"messageType": local.qryActivity.messageType,
				"statusCode": local.qryActivity.statusCode,
				"status": local.qryActivity.status,
				"dateLastUpdated": dateFormat(local.qryActivity.dateLastUpdated,"m/d/yy") & " " & timeFormat(local.qryActivity.dateLastUpdated,"h:mm tt"),
				"canResend": NOT listFindNoCase("I,scheduled", local.qryActivity.statusCode),
				"allowAdminResend": local.qryActivity.allowAdminResend,
				"sg_open": val(local.qryActivity.sg_open),
				"sg_click": val(local.qryActivity.sg_click),
				"DT_RowId": "recipientRow_#local.qryActivity.messageID#|#local.qryActivity.recipientID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryActivity.messageCount),
			"recordsFiltered": val(local.qryActivity.messageCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getBlastMessages" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateEntered #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"subject #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryActivity" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;

			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @blastID int, @siteID int, @totalCount int, @posStart int, @posStartAndCount int;

				set @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				set @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				set @blastID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('blastID',0)#">;

				select @siteID=siteID
				from membercentral.dbo.email_EmailBlasts
				where blastID = @blastID;

				IF OBJECT_ID('tempdb..##tblMessages') IS NOT NULL
					DROP TABLE ##tblMessages;
				IF OBJECT_ID('tempdb..##tblMessagesSubset') IS NOT NULL
					DROP TABLE ##tblMessagesSubset;
				IF OBJECT_ID('tempdb..##tblPivotKeys') IS NOT NULL
					DROP TABLE ##tblPivotKeys;
				IF OBJECT_ID('tempdb..##tblMessageStats') IS NOT NULL
					DROP TABLE ##tblMessageStats;

				CREATE TABLE ##tblMessages (messageID int PRIMARY KEY, MsgCount int, row int);
				CREATE TABLE ##tblMessagesSubset (messageID int PRIMARY KEY, MsgCount int, row int);
				CREATE TABLE ##tblPivotKeys (statuscode varchar(10), suffix varchar(10), pivotkey varchar(20));
				CREATE TABLE ##tblMessageStats (messageID int PRIMARY KEY, 
					[sg_open_total] int,[sg_click_total] int, [sg_drop_total] int,sg_bounce_total int, sg_block_total int, sg_spam_total int, suppressed_total int, optout_total int, 
					sg_open_unique int, sg_click_unique int, sg_drop_unique int, sg_bounce_unique int, sg_block_unique int, sg_spam_unique int, suppressed_unique int,  optout_unique int);

				insert into ##tblPivotKeys (statuscode,suffix,pivotkey)
				select statusesToTrack.listitem as statuscode, suffixes.listitem as suffix, statusesToTrack.listitem + suffixes.listitem as pivotkey
				from membercentral.dbo.fn_varCharListToTable('sg_open,sg_click,sg_drop,sg_bounce,sg_block,sg_spam,suppressed,optout',',') statusesToTrack
				cross join membercentral.dbo.fn_varCharListToTable('_total,_unique',',') as suffixes;

				insert into ##tblMessages (messageID, MsgCount, row)
				select messageID, MsgCount, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
				from (
					select m.messageID, m.dateEntered, m.subject, count(*) as MsgCount
					from platformmail.dbo.email_messages m
					inner join platformmail.dbo.email_messageRecipientHistory mrh
						on m.siteID=@siteID
						and mrh.siteID = @siteID
						and m.messageID = mrh.messageID
						and m.[status]='A'
						and m.referenceType='EmailBlast'
						and m.referenceID = @blastID
					group by m.messageID, m.dateEntered, m.subject

				) as innerTmp;

				SELECT @totalCount = @@ROWCOUNT;

				insert into ##tblMessagesSubset (messageID, MsgCount, row)
				select messageID, MsgCount, row
				from ##tblMessages
				where row > @posStart
				and row <= @posStartAndCount;

				insert into ##tblMessageStats (
					messageID,
					sg_open_total,
					sg_click_total,
					sg_drop_total,
					sg_bounce_total,
					sg_block_total,
					sg_spam_total,
					suppressed_total,
					optout_total,
					sg_open_unique,
					sg_click_unique,
					sg_drop_unique,
					sg_bounce_unique,
					sg_block_unique,
					sg_spam_unique,
					suppressed_unique,
					optout_unique
				)

				select 
					pivottable.messageID, 
					isnull(pivottable.[sg_open_total],0),
					isnull(pivottable.[sg_click_total],0),
					isnull(pivottable.[sg_drop_total],0),
					isnull(pivottable.[sg_bounce_total],0),
					isnull(pivottable.[sg_block_total],0),
					isnull(pivottable.[sg_spam_total],0),
					isnull(pivottable.[suppressed_total],0),
					isnull(pivottable.[optout_total],0),
					isnull(pivottable.[sg_open_unique],0),
					isnull(pivottable.[sg_click_unique],0),
					isnull(pivottable.[sg_drop_unique],0), 
					isnull(pivottable.[sg_bounce_unique],0),
					isnull(pivottable.[sg_block_unique],0),
					isnull(pivottable.[sg_spam_unique],0),
					isnull(pivottable.[suppressed_unique],0),
					isnull(pivottable.[optout_unique],0)
				from (
					select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
					from ##tblMessagesSubset msg
					inner join dbo.email_messageRecipientHistory mrh 
						on mrh.siteID = @siteID
						and mrh.messageID = msg.messageID
					inner join dbo.email_messageRecipientHistoryTracking rt
						on rt.siteID = @siteID 
						and mrh.recipientID = rt.recipientID
					inner join dbo.email_statuses rtst on rtst.statusID = rt.statusID
						and rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_bounce','sg_block','sg_spam')
					inner join ##tblPivotKeys pk on pk.statusCode = rtst.statuscode
					group by mrh.messageID, pk.pivotkey, pk.suffix

					union 

					-- summary for statuses where recipient remains in final status, no entries in email_messageRecipientHistoryTracking
					select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
					from ##tblMessagesSubset msg 
					inner join dbo.email_messageRecipientHistory mrh 
						on mrh.siteID = @siteID
						and mrh.messageID = msg.messageID
					inner join dbo.email_statuses rtst 
						on rtst.statusID = mrh.emailStatusID
						and rtst.statusCode in ('suppressed','optout')
					inner join ##tblPivotKeys pk on pk.statusCode = rtst.statuscode
					group by mrh.messageID, pk.pivotkey, pk.suffix
				) as dataToPivot
				PIVOT (
					sum(pivotvalue) for pivotkey in (
					[sg_open_total],[sg_click_total],[sg_drop_total],[sg_bounce_total],[sg_block_total],[sg_spam_total],[suppressed_total],[optout_total],
					[sg_open_unique],[sg_click_unique],[sg_drop_unique],[sg_bounce_unique],[sg_block_unique],[sg_spam_unique],[suppressed_unique],[optout_unique]) 
				) as pivottable;

				select data.row, data.messageID, data.messageType, data.dateEntered, data.sendOnDate, data.fromName, data.replyToEmail,
					data.subject, data.messageCount, stats.sg_open_total, stats.sg_click_total, stats.sg_open_unique, stats.sg_click_unique,
					stats.[optout_total], stats.[suppressed_total],
					(stats.sg_drop_unique + stats.sg_bounce_unique + stats.sg_block_unique + stats.sg_spam_unique + stats.suppressed_unique) as problems_unique, @totalCount as totalCount
				from (
					select em.messageID, emt.messageType, em.dateEntered, em.sendOnDate, em.fromName, em.replyToEmail, em.subject,
						tmp.MsgCount as messageCount, tmp.row 
					from ##tblMessagesSubset tmp
					inner join dbo.email_messages as em on em.siteID = @siteID and em.status = 'A' and em.messageID = tmp.messageID
					inner join dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID
				) as data
				left outer join ##tblMessageStats stats on stats.messageID = data.messageID
				order by data.row;

				IF OBJECT_ID('tempdb..##tblMessages') IS NOT NULL
					DROP TABLE ##tblMessages;
				IF OBJECT_ID('tempdb..##tblMessagesSubset') IS NOT NULL
					DROP TABLE ##tblMessagesSubset;
				IF OBJECT_ID('tempdb..##tblPivotKeys') IS NOT NULL
					DROP TABLE ##tblPivotKeys;
				IF OBJECT_ID('tempdb..##tblMessageStats') IS NOT NULL
					DROP TABLE ##tblMessageStats;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY

			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryActivity">
			<cfset local.sendOnDate = "">
			<cfset local.openPercent = "0%">
			<cfset local.clickPercent = "0%">
			<cfset local.problemPercent = "0%">
			<cfset local.canCancelMsg = false>
			<cfset local.messageCountAfterOptOuts = local.qryActivity.messageCount - val(local.qryActivity.optout_total)>

			<cfif dateCompare(local.qryActivity.dateEntered,local.qryActivity.sendOnDate,'n')>
				<cfset local.sendOnDate = dateFormat(local.qryActivity.sendOnDate,"m/d/yyyy") & " " & timeFormat(local.qryActivity.sendOnDate,"h:mm tt")>
			</cfif>
			<cfif local.messageCountAfterOptOuts gt 0>
				<cfset local.openPercent = "#NumberFormat((val(local.qryActivity.sg_open_unique)/local.messageCountAfterOptOuts)*100, "__")#%">
			</cfif>
			<cfif local.messageCountAfterOptOuts gt 0>
				<cfset local.clickPercent = "#NumberFormat((val(local.qryActivity.sg_click_unique)/local.messageCountAfterOptOuts)*100, "__")#%">
			</cfif>
			<cfif local.messageCountAfterOptOuts gt 0>
				<cfset local.problemPercent = "#NumberFormat((val(local.qryActivity.problems_unique)/local.messageCountAfterOptOuts)*100, "__")#%">
			</cfif>
			<cfif dateCompare(local.qryActivity.sendOnDate,now(),'n') GT 0>
				<cfset local.canCancelMsg = true>
			</cfif>
			
			<cfset local.tmpStr = {
				"messageID": local.qryActivity.messageID,
				"dateEntered": dateFormat(local.qryActivity.dateEntered,"m/d/yyyy") & " " & timeFormat(local.qryActivity.dateEntered,"h:mm tt"),
				"sendOnDate": local.sendOnDate,
				"subject": local.qryActivity.subject,
				"replyToEmail": local.qryActivity.replyToEmail,
				"fromName": local.qryActivity.fromName,
				"messageCount": local.messageCountAfterOptOuts,
				"optout_total": val(local.qryActivity.optout_total),
				"sg_open_total": val(local.qryActivity.sg_open_total),
				"sg_open_unique": val(local.qryActivity.sg_open_unique),
				"sg_click_total": val(local.qryActivity.sg_click_total),
				"sg_click_unique": val(local.qryActivity.sg_click_unique),
				"problems_unique": val(local.qryActivity.problems_unique),
				"openPercent": local.openPercent,
				"clickPercent": local.clickPercent,
				"problemPercent": local.problemPercent,
				"canCancelMsg": local.canCancelMsg,
				"DT_RowId": "messageRow_#local.qryActivity.messageID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryActivity.totalCount),
			"recordsFiltered": val(local.qryActivity.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getActivityRecipientsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"mActive.lastname + mActive.firstname + isnull(' ' + mActive.middlename,'') + mActive.membernumber")>
		<cfset arrayAppend(local.arrCols,"mrh.toEmail")>
		<cfset arrayAppend(local.arrCols,"s.status")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryActivityRecipients" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##recipients') IS NOT NULL
				DROP TABLE ##recipients;
			CREATE TABLE ##recipients (recipientID int, row int);

			IF OBJECT_ID('tempdb..##recipientStats') IS NOT NULL
				DROP TABLE ##recipientStats;
			CREATE TABLE ##recipientStats (recipientID int PRIMARY KEY, sg_open int, sg_click int, sg_drop int,
				sg_bounce int, sg_block int, sg_spam int);

			DECLARE @siteID int, @totalCount int, @posStart int, @posStartAndCount int, @messageID int;

			SET @messageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('msg',0)#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			
			INSERT INTO ##recipients (recipientID, row)
			SELECT mrh.recipientID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
			FROM dbo.email_messageRecipientHistory AS mrh
			INNER JOIN dbo.email_messages AS m on m.siteID = @siteID
				and m.status = 'A' 
				and m.messageID = mrh.messageID
				AND m.messageID = @messageID
			INNER JOIN dbo.email_statuses AS s on s.statusID = mrh.emailStatusID
			<cfif len(arguments.event.getTrimValue('statusCode',''))>
				AND s.statuscode IN (<cfqueryparam cfsqltype="cf_sql_varchar" list="true" value="#urldecode(arguments.event.getValue('statusCode'))#">)
			</cfif>
			INNER JOIN membercentral.dbo.ams_members mem on mem.memberID = mrh.memberID
			INNER JOIN membercentral.dbo.ams_members mactive on mactive.memberID = mem.activememberID
			WHERE 1 = 1
			<cfif len(arguments.event.getTrimValue('recipientName',''))>
				AND mrh.toName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getValue('recipientName')#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('recipientEmail',''))>
				AND mrh.toEmail LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getValue('recipientEmail')#%">
			</cfif>

			SET @totalCount = @@ROWCOUNT;

			INSERT INTO ##recipientStats (recipientID, sg_open, sg_click, sg_drop, sg_bounce, sg_block, sg_spam)
			SELECT pivottable.recipientID, isnull(pivottable.sg_open,0), isnull(pivottable.sg_click,0), isnull(pivottable.sg_drop,0),
				isnull(pivottable.sg_bounce,0), isnull(pivottable.sg_block,0), isnull(pivottable.sg_spam,0)
				FROM (
				SELECT r.recipientID, rtst.statusCode, count(*) as pivotvalue
				FROM ##recipients r
				INNER JOIN dbo.email_messageRecipientHistoryTracking rt 
					on rt.siteID = @siteID 
					and rt.recipientID = r.recipientID
				INNER JOIN dbo.email_statuses rtst on rtst.statusID = rt.statusID
					AND rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_bounce','sg_block','sg_spam')
				GROUP BY r.recipientID,rtst.statusCode
			) AS dataToPivot
			PIVOT (sum(pivotvalue) for statusCode IN ([sg_open],[sg_click],[sg_drop],[sg_bounce],[sg_block],[sg_spam])) as pivottable;

			SELECT data.recipientID, data.ToName, data.toEmail, data.dateLastUpdated, data.messageType, data.allowAdminResend, data.status, data.statusCode, data.row, data.company,
				data.membernumber, data.memberID, data.membername, sg_open, sg_click, (sg_drop + sg_bounce + sg_block + sg_spam) as problems_unique,
				@totalCount AS totalcount
			FROM (
				SELECT mrh.recipientID, mrh.ToName, mrh.toEmail, mrh.dateLastUpdated, emt.messageType, emt.allowAdminResend, s.status, s.statusCode, r.row, m.company, m.membernumber, 
					m.memberID, mActive.lastname + ', ' + mActive.firstname + isnull(' ' + mActive.middlename,'') + ' (' + mActive.membernumber + ')' as memberName
				FROM ##recipients r
				INNER JOIN dbo.email_messageRecipientHistory as mrh on mrh.recipientID = r.recipientID
				INNER JOIN dbo.email_messages AS em on em.siteID = @siteID and em.status = 'A' and em.messageID = mrh.messageID
				INNER JOIN dbo.email_messageTypes as emt on emt.messageTypeID = em.messageTypeID
				INNER JOIN dbo.email_statuses as s on s.statusID = mrh.emailStatusID
				INNER JOIN membercentral.dbo.ams_members m on m.memberID = mrh.memberID
				INNER JOIN membercentral.dbo.ams_members mactive on mactive.memberID = m.activememberID
			) AS data
			LEFT OUTER JOIN ##recipientStats stats ON stats.recipientID = data.recipientID
			WHERE data.row > @posStart
			AND data.row <= @posStartAndCount
			ORDER BY data.row;

			IF OBJECT_ID('tempdb..##recipients') IS NOT NULL
				DROP TABLE ##recipients;
			
			IF OBJECT_ID('tempdb..##recipientStats') IS NOT NULL
				DROP TABLE ##recipientStats;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryActivityRecipients">
			<cfset local.arrData.append({
				"recipientID": local.qryActivityRecipients.recipientID,
				"memberID": local.qryActivityRecipients.memberID,
				"memberName": htmleditformat(local.qryActivityRecipients.memberName),
				"toEmail": htmleditformat(local.qryActivityRecipients.toEmail),
				"company": local.qryActivityRecipients.company,
				"dateLastUpdated": '#dateFormat(local.qryActivityRecipients.dateLastUpdated,"m/d/yy")# #timeFormat(local.qryActivityRecipients.dateLastUpdated,"h:mm tt")#',
				"status": local.qryActivityRecipients.status,
				"messageType": local.qryActivityRecipients.messageType,
				"sg_open": val(local.qryActivityRecipients.sg_open),
				"sg_click": val(local.qryActivityRecipients.sg_click),
				"canResend": NOT listFindNoCase("I,scheduled", local.qryActivityRecipients.statusCode),
				"allowAdminResend": local.qryActivityRecipients.allowAdminResend,
				"hasBlock": listFindNoCase("sg_drop,sg_bounce,sg_block,sg_spam", local.qryActivityRecipients.statusCode),
				"DT_RowId": "listRow_#local.qryActivityRecipients.recipientID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryActivityRecipients.totalcount),
			"recordsFiltered": val(local.qryActivityRecipients.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCustomFontsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfquery name="local.qryCustomFonts" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			
			SELECT customFontID, siteID, name, fontCssUrl, fontFamily
			FROM dbo.email_customFonts AS cf
			
			WHERE cf.siteID = @siteID
			<cfif len(local.searchValue)>
				AND ( cf.name LIKE @searchValue 
						OR cf.fontCssUrl LIKE @searchValue 
						OR cf.fontFamily LIKE @searchValuee )
			</cfif>
			ORDER BY cf.name;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrCustomFonts = []>
		<cfoutput query="local.qryCustomFonts" >
			<cfset local.tmpStr = {
				"customFontID": local.qryCustomFonts.customFontID,
				"name": local.qryCustomFonts.name,
				"fontCssUrl": local.qryCustomFonts.fontCssUrl,
				"fontFamily": local.qryCustomFonts.fontFamily,
				
				"DT_RowId": "customFont_#local.qryCustomFonts.customFontID#"
			}>
			<cfset local.arrCustomFonts.append(local.tmpStr)>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrCustomFonts),
			"recordsFiltered": arrayLen(local.arrCustomFonts),
			"data": local.arrCustomFonts
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSuppressionAuditLog" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getSuppressionAuditLog(
			siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			keywords=arguments.event.getValue('fDescription',''),
			dateFrom=arguments.event.getValue('fDateFrom',''),
			dateTo=arguments.event.getValue('fDateTo',''),
			limit=50
		).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>

			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>