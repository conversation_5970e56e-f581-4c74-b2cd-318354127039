ALTER PROC dbo.sw_getCreditsforSeminar
@seminarID int,
@siteCode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- get seminar type
	DECLARE @SWType varchar(4);
	SELECT @SWType = CASE
		WHEN sswod.ondemandid is null then 'SWL'
		WHEN sswl.liveid is null then 'SWOD'
		ELSE ''
		END
		FROM dbo.tblSeminars as s
		LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.seminarID
		WHERE s.seminarID = @seminarID;

	IF @SWType = 'SWOD'
		SELECT sac.seminarCreditID, ca.authorityID, ca.code AS authorityCode, ca.authorityName, 
			ca.jurisdiction AS authorityJurisdiction, sac.creditOfferedEndDate, sac.wddxCreditsAvailable, cs.sponsorName, 
			cs.statementAppProvider, cs.statementAppProgram, cs.statementPendProgram, cstat.status, 
			ca.website, ca.wddxCreditTypes, ca.creditIDText, sac.isCreditRequired, 
			sac.isIDRequired, sac.isCreditDefaulted, csa.creditMessage, s.seminarName
		FROM dbo.tblSeminarsAndCredit AS sac 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditAuthoritiesSWOD AS caswod ON caswod.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
		INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = sac.seminarID
		WHERE sac.seminarID = @seminarID
		AND cstat.status IN ('Approved', 'Pending','Self-Submitting')
		AND getdate() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
		ORDER BY case when cs.orgcode = @siteCode then 1 else 0 end desc, authorityCode, cs.sponsorName;

	ELSE
		SELECT sac.seminarCreditID, ca.authorityID, ca.code AS authorityCode, ca.authorityName, 
			ca.jurisdiction AS authorityJurisdiction, sac.wddxCreditsAvailable, cs.sponsorName, 
			cs.statementAppProvider, cs.statementAppProgram, cs.statementPendProgram, cstat.status, 
			ca.website, ca.wddxCreditTypes, ca.creditIDText, sac.isCreditRequired, 
			sac.isIDRequired, sac.isCreditDefaulted, csa.creditMessage, s.seminarName
		FROM dbo.tblSeminarsAndCredit AS sac 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
		INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = sac.seminarID
		WHERE sac.seminarID = @seminarID
		AND cstat.status IN ('Approved', 'Pending','Self-Submitting')
		ORDER BY case when cs.orgcode = @siteCode then 1 else 0 end desc, authorityCode, cs.sponsorName;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
