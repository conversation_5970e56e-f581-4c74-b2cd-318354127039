<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// Load Objects for page -------------------------------------------------------------------- ::
			this.objVirtualGroups = CreateObject("component","model.admin.virtualGroups.virtualGroups");
			// use resourceID of the site for security -------------------------------------------------- ::
			this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
			local.toolTypeSiteResourceID = arguments.event.getValue('mc_admintoolInfo.toolType.siteResourceID');

			// admin instance settings
			this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);

			// set rights into event
			local.tmpRights = buildRightAssignments(siteResourceID=local.toolTypeSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			
			// build quick links ------------------------------------------------------------------------ ::
			this.link.list = buildCurrentLink(arguments.event,"list");
			this.link.addRule = buildCurrentLink(arguments.event,"addRule") & "&mode=direct";
			this.link.editRule = buildCurrentLink(arguments.event,"editRule");
			this.link.processCopyRule = buildCurrentLink(arguments.event,"processCopyRule") & "&mode=direct";
			this.link.cloneRule = buildCurrentLink(arguments.event,"cloneRule");
			this.link.copyRule = buildCurrentLink(arguments.event,"copyRule") & "&mode=stream";
			this.link.saveRule = buildCurrentLink(arguments.event,"saveRule") & "&mode=stream";
			this.link.addConditionToRule = buildCurrentLink(arguments.event,"addConditionToRule") & "&mode=stream";
			this.link.editConditionUID = buildCurrentLink(arguments.event,"editConditionUID") & "&mode=direct";
			this.link.exportConditionMembersPrompt = buildCurrentLink(arguments.event,"exportConditionMembersPrompt") & "&mode=direct";
			this.link.saveCondition = buildCurrentLink(arguments.event,"saveCondition") & "&mode=stream";
			this.link.saveConditionUID = buildCurrentLink(arguments.event,"saveConditionUID") & "&mode=stream";
			this.link.addConditionToConditionSet = buildCurrentLink(arguments.event,"addConditionToConditionSet") & "&mode=stream";
			this.link.exportStructureZIP = buildCurrentLink(arguments.event,"exportStructureZIP") & "&mode=stream";
			this.link.doImportVGC = buildCurrentLink(arguments.event,"doImportVGC") & "&mode=stream";
			this.link.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.dtRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=virtualGroupsJSON&mode=stream";

			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.showImpExTemplate = true>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewVirtualGroupAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>

		<cfset local.conditionsListLink = "#this.link.dtRootLink#&meth=getConditions&dtMode=vgcList">
		<cfset local.rulesListLink = "#this.link.dtRootLink#&meth=getRules">
		<cfset local.conditionID = arguments.event.getValue('conditionID','0')>
		<cfset local.arrConditionAreas = this.objVirtualGroups.getConditionAreasForDropdowns(conditionType="GroupAssignment",mc_siteInfo=arguments.event.getValue('mc_siteInfo'))>

		<cfif arguments.event.getValue('tab','') eq 'ex'>
			<cfif arguments.event.getValue('importFileName','') neq ''>
				<cfset local.showImpExTemplate = false>
				<cfset local.prepResult = prepareVGCImport(event=arguments.event)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_virtualGroups.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addRule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewVirtualGroupAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rule.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editRule" access="public" output="false" returntype="struct" hint="edits a groupRule">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
		<cfset local.ruleVersionID = int(val(arguments.event.getValue('ruleVersionID',0)))>
		<cfset local.qryRule = this.objVirtualGroups.getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'),ruleID=local.ruleID, ruleVersionID=local.ruleVersionID)>

		<!--- 
			Edit Rule Modes:
			editRule: edit rule from group assignment rules
			viewGroupRule: view group rule from group rules management (readOnly)
			addGroupRule: add group rule from group rules management
			editGroupRule: edit group rule from group rules management
			copyGroupRule: copy group rule
			viewGroupRuleVersion: view group rule version from rule versions (readOnly)
			editGroupRuleVersion: edit group rule version
		--->

		<cfset arguments.event.paramValue('editRuleMode','editRule')>
		<cfset local.editRuleMode = arguments.event.getValue('editRuleMode')>
		<cfset local.readOnly = false>
		<cfset local.saveRuleVersionMessage = "Save to Accept Changes">
		<cfset local.groupRuleModes = "viewGroupRule,addGroupRule,editGroupRule,copyGroupRule,viewGroupRuleVersion,editGroupRuleVersion">

		<!--- Manage Group Assignment Rules / Edit Rule --->
		<cfif local.editRuleMode EQ 'editRule'>
			<!--- check perms --->
			<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewVirtualGroupAdmin') is not 1>
				<cflocation url="#this.link.message#&message=1" addtoken="false">
			</cfif>
			
			<cfset appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.qryRule.ruleName) })>
		
		<!--- Group Rule --->
		<cfelseif listFindNoCase(local.groupRuleModes,local.editRuleMode)>
			<cfset local.strGroup = CreateObject("component","model.admin.groups.groups").getGroup(groupID=arguments.event.getValue('grpID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

			<!--- check perms --->
			<cfset local.grpAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GroupAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfset local.tmpGroupAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.grpAdminSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfif NOT (local.tmpGroupAdminRights.ViewGroup EQ 1 AND local.strGroup.qryGroup.isSystemGroup EQ 0)>
				<cflocation url="#this.link.message#&message=1&mode=direct" addtoken="false">
			</cfif>

			<cfset local.returnGroupID = arguments.event.getValue('retGrpID',local.strGroup.qryGroup.groupID)>
			<cfset local.readOnly = listFindNoCase("viewGroupRule,viewGroupRuleVersion",local.editRuleMode) GT 0>
			<cfset local.forceActivateRule = listFindNoCase("addGroupRule,copyGroupRule",local.editRuleMode) GT 0 ? 1 : 0>
			<cfset local.saveRuleVersionMessage = "Activate Rule to Accept Changes">
			
			<cfset local.editRuleLink = "#this.link.editRule#&ruleID=#local.qryRule.ruleID#&grpID=#arguments.event.getValue('grpID')#&retGrpID=#local.returnGroupID#&editRuleMode=editGroupRule&mode=direct">
			<cfset local.testMemberLink = buildCurrentLink(arguments.event,"doesMemberQualifyRule") & "&ruleID=#local.ruleID#&mode=direct">
			<cfset local.listRuleVersionsLink = buildCurrentLink(arguments.event,"listRuleVersions") & "&ruleID=#local.ruleID#&grpID=#arguments.event.getValue('grpID')#&retGrpID=#local.returnGroupID#&mode=direct">
			<cfset local.manageGrpLink = buildLinkToTool(toolType='GroupAdmin',mca_ta='manageGroup') & "&groupID=#local.returnGroupID#&mode=direct">
			<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>

			<cfset local.pendingRuleVersionID = this.objVirtualGroups.getPendingRuleVersionID(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.qryRule.ruleID)>

			<cfif listFindNoCase('viewGroupRule,editGroupRule,viewGroupRuleVersion,editGroupRuleVersion',local.editRuleMode)>
				<cfset var ruleVersionID = local.qryRule.ruleVersionID>
				
				<!--- Display the View Rule screen with the Rule in Progress Rule inline tab selected --->
				<cfif local.editRuleMode EQ 'viewGroupRuleVersion' AND ruleVersionID EQ local.pendingRuleVersionID>
					<cfset arguments.event.setValue("grpRuleTab","viewGrpRuleInProgress")>
				</cfif>

				<cfif local.editRuleMode EQ 'viewGroupRule' 
					OR (local.pendingRuleVersionID
						AND local.editRuleMode EQ 'viewGroupRuleVersion' 
						AND ruleVersionID EQ local.qryRule.activeVersionID
					)>
					<cfset ruleVersionID = local.pendingRuleVersionID>
				</cfif>
				
				<cfset local.ruleVersionIDList = listRemoveDuplicates("#local.qryRule.activeVersionID#,#ruleVersionID#")>
				<cfset local.qryRuleVersions = this.objVirtualGroups.getRuleVersions(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.ruleID, 
						ruleVersionIDList=local.ruleVersionIDList, mode='ruleversions', includeVersionsWithNoConditions=1)>
				<cfset local.qryActiveRuleVersion = local.qryRuleVersions.filter(function(thisRow) { return arguments.thisRow.isActiveVersion EQ 1; })>
				<cfset local.qryRuleVersion = local.qryRuleVersions.filter(function(thisRow) { return arguments.thisRow.ruleVersionID EQ ruleVersionID; })>

				<!--- override mode --->
				<cfif local.editRuleMode EQ 'viewGroupRuleVersion' AND ruleVersionID EQ local.pendingRuleVersionID>
					<cfset local.editRuleMode = "viewGroupRule">
				</cfif>
			</cfif>

			<cfif local.editRuleMode EQ 'viewGroupRule' AND local.pendingRuleVersionID>
				<cfset local.editRuleLink = "#this.link.editRule#&ruleID=#local.qryRule.ruleID#&ruleVersionID=#local.pendingRuleVersionID#&grpID=#arguments.event.getValue('grpID')#&retGrpID=#local.returnGroupID#&editRuleMode=editGroupRule&mode=direct">
			<cfelseif listFindNoCase('viewGroupRuleVersion,editGroupRuleVersion',local.editRuleMode)>
				<cfset local.editRuleLink = "#this.link.editRule#&ruleID=#local.qryRule.ruleID#&grpID=#arguments.event.getValue('grpID')#&retGrpID=#local.returnGroupID#&editRuleMode=editGroupRuleVersion&mode=direct">
			</cfif>

		<!--- Invalid Mode --->
		<cfelse>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editRule.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRuleVersionVerbose" access="public" output="false" returntype="string">
		<cfargument name="qryRuleVersion" type="query" required="true">

		<cfset var ruleVersionInfoVerbose = "">
		<cfif len(arguments.qryRuleVersion.startDate)>
			<cfsavecontent variable="ruleVersionInfoVerbose">
				<cfoutput>Version #arguments.qryRuleVersion.versionNum#: #arguments.qryRuleVersion.isActiveVersion ? 'Active' : 'Paused'# #DateTimeFormat(arguments.qryRuleVersion.startDate,'m/d/yyyy h:nn tt')# to #len(arguments.qryRuleVersion.endDate) ? DateTimeFormat(arguments.qryRuleVersion.startDate,'m/d/yyyy h:nn tt') : 'Present'#; Created by #arguments.qryRuleVersion.createdBy#</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn trim(ruleVersionInfoVerbose)>
	</cffunction>

	<cffunction name="cloneRule" access="public" output="false" returntype="void" hint="clone groupRule">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.ruleID = this.objVirtualGroups.copyGroupRule(orgID=arguments.event.getValue('mc_siteInfo.orgID'), 
			copyRuleID=arguments.event.getValue('ruleID',0), 
			ruleName="Copy of #URLDecode(arguments.event.getValue('ruleName',''))#", 
			incGroups=1)>

		<cflocation url="#this.link.editrule#&ruleID=#local.ruleID#" addtoken="no">
	</cffunction>

	<cffunction name="processCopyRule" access="public" output="false" returntype="struct" hint="process copy groupRule">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
	
		<cfset local.qryRule = this.objVirtualGroups.getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'),ruleID=arguments.event.getValue('ruleID'))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_copyRule.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copyRule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.ruleID = this.objVirtualGroups.copyGroupRule(orgID=arguments.event.getValue('mc_siteInfo.orgID'), 
			copyRuleID=arguments.event.getValue('copyRuleID',0), ruleName=arguments.event.getValue('ruleName',''),
			incGroups=arguments.event.getValue('incGroups',0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif arguments.event.getValue('ret','') eq "newrule">
					top.location.href = '#this.link.editRule#&ruleID=#local.ruleID#';
				<cfelse>
					top.MCModalUtils.hideModal();
					top.reloadRules(true);
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doesMemberQualifyRule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
		<cfset local.qryRule = this.objVirtualGroups.getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.ruleID)>
		<cfset local.qryRuleVersions = this.objVirtualGroups.getRuleVersions(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.ruleID, 
										ruleVersionIDList=local.qryRule.activeVersionID, mode='ruleversions')>
		<cfset local.qryActiveRuleVersion = local.qryRuleVersions.filter(function(thisRow) { return arguments.thisRow.isActiveVersion EQ 1; })>

		<cfset local.conditionsXML = "/?pg=admin&mca_ajaxlib=dhtmlGrid&com=virtualGroupsXML&meth=conditionsXML&ruleID=#local.ruleID#&mcrb_gridnum=1&rohi=1&testMember=1&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_testMember.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listRuleVersions" access="public" output="false" returntype="struct" hint="List Rule Versions">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
		<cfset local.groupID = int(val(arguments.event.getValue('grpID',0)))>
		<cfset local.returnGroupID = int(val(arguments.event.getValue('retGrpID',local.groupID)))>
		<cfset local.qryRule = this.objVirtualGroups.getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=local.ruleID)>

		<cfset local.ruleVersionsListLink = "#this.link.dtRootLink#&meth=getRuleVersions&ruleID=#local.ruleID#">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_ruleVersions.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="toggleRuleStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="isActive" type="boolean" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>
			
			<cfif arguments.isActive>
				<cfset local.msg = activateRule(ruleID=arguments.ruleID)>
				<cfif local.msg is 2>
					<cfset local.data.success = false>
					<cfset local.data.errmsg = "This rule could not be activated. Check to ensure there are no empty condition sets or undefined conditions and try again.">
				<cfelse>
					<cfset local.data.success = true>
				</cfif>
			<cfelse>
				<cfquery name="local.qryInactivateRule" datasource="#application.dsn.memberCentral.dsn#">
					UPDATE dbo.ams_virtualGroupRules
					SET isActive = 0
					WHERE ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
				</cfquery>

				<cfset local.data.success = true>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="countRuleConditions" access="public" output="false" returntype="numeric">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="false" default="0">

		<cfset var qryConditions = "">

		<cfquery name="qryConditions" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @ruleVersionID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">,0);
			
			SELECT c.uid as conditionID
			FROM dbo.ams_virtualGroupRules as r
			INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.ruleID = r.ruleID
				AND rv.ruleVersionID = ISNULL(@ruleVersionID,r.activeVersionID)
			INNER JOIN dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = r.ruleID
				AND rcs.ruleVersionID = rv.ruleVersionID
			INNER JOIN dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
			INNER JOIN dbo.ams_virtualGroupConditions as c on c.conditionID = rc.conditionID
			WHERE r.ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryConditions.recordcount>
	</cffunction>

	<cffunction name="activateRule" access="public" output="false" returntype="numeric">
		<cfargument name="ruleID" type="numeric" required="yes">
		<cfargument name="forceCache" type="boolean" required="no" default="0"> <!--- used by reports/blast to force condition cache rebuild since they would be skipped otherwise --->
		
		<cfset var local = structNew()>
		<cfset local.msg = 0>

		<cftry>
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_activateVirtualGroupRule">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.forceCache#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset local.msg = 2>
		</cfcatch>
		</cftry>

		<cfreturn local.msg>
	</cffunction>

	<cffunction name="saveRuleSettings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="ruleName" type="string" required="true">
		<cfargument name="ruleUID" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySaveRuleSettings" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
						@ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
						@ruleVersionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">;
					
					UPDATE dbo.ams_virtualGroupRules
					SET ruleName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ruleName#">
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
							, [uid] = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.ruleUID#">
						</cfif>
					WHERE ruleID = @ruleID;

					<cfif arguments.ruleVersionID>
						EXEC dbo.ams_activateVirtualGroupRuleVersion @orgID=@orgID, @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;
					</cfif>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveRule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.ruleID = createVirtualGroupRule(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
								ruleName=arguments.event.getValue('ruleName'), appendDateTimeStamp=false).ruleID>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.reloadRules(true);
				top.editRule(#local.ruleID#);
			</script>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="createVirtualGroupRule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleName" type="string" required="true">
		<cfargument name="appendDateTimeStamp" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":false, "ruleid":0 }>

		<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
			<cfreturn local.strReturn>
		</cfif>

		<cfset local.ruleXML = '<rule><conditionset op="AND" act="include" id="#ucase(createUUID())#" /></rule>'>

		<cftry>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCreateVirtualGroupRule">
				SET NOCOUNT ON;
	
				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">,
					@ruleName varchar(400) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ruleName#">, 
					@ruleXML xml = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.ruleXML#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@ruleID int;
	
				<cfif arguments.appendDateTimeStamp>
					SET @ruleName = @ruleName + format(getdate(),'yyyyMMddHHmmssffff');
				</cfif>
	
				EXEC dbo.ams_createVirtualGroupRule @orgID=@orgID, @ruleTypeID=1, @ruleName=@ruleName, 
					@ruleXML=@ruleXML, @ruleSQL='', @recordedByMemberID=@recordedByMemberID, @ruleID=@ruleID OUTPUT
				
				SELECT @ruleID AS ruleID;
			</cfquery>

			<cfset local.strReturn = { "success":true, "ruleid":local.qryCreateVirtualGroupRule.ruleID }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="acceptRuleVersion" access="public" output="false" returntype="struct">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">

		<cfset var qryAcceptRuleVersion = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryAcceptRuleVersion">
			EXEC dbo.ams_activateVirtualGroupRuleVersion
				@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">,
				@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
				@ruleVersionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">;
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="cancelRuleVersion" access="public" output="false" returntype="struct">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">

		<cfset var qryDeleteRuleVersion = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDeleteRuleVersion">
			EXEC dbo.ams_deleteVirtualGroupRuleVersion
				@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">,
				@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
				@ruleVersionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">;
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="takeOverRuleVersion" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">

		<cfset var qryTakeOverRuleVersion = "">

		<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
			<cfreturn { "success":false }>
		</cfif>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryTakeOverRuleVersion">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
				@ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
				@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
				@ruleVersionID int, @useRuleVersionID int, @takeOverRuleVersion bit = 0, @dateSkipped datetime;

			-- valid ruleVersionID
			SELECT @ruleVersionID = ruleVersionID
			FROM dbo.ams_virtualGroupRuleVersions
			WHERE ruleID = @ruleID
			AND ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">
			AND orgID = @orgID;

			-- takeover
			IF @ruleVersionID IS NOT NULL 
				AND EXISTS (SELECT 1 FROM dbo.ams_virtualGroupRuleVersions
							WHERE ruleVersionID = @ruleVersionID
							AND orgID = @orgID
							AND dateActivated IS NULL
							AND dateSkipped IS NULL
							AND createdByMemberID <> @recordedByMemberID)
				SET @takeOverRuleVersion = 1;

			IF @takeOverRuleVersion = 1 BEGIN
				EXEC dbo.ams_copyVirtualGroupRuleVersion @orgID=@orgID, @ruleID=@ruleID, @ruleVersionID=@ruleVersionID, @recordedByMemberID=@recordedByMemberID, @newRuleVersionID=@useRuleVersionID OUTPUT;

				SELECT @dateSkipped = dateCreated
				FROM dbo.ams_virtualGroupRuleVersions
				WHERE ruleVersionID = @useRuleVersionID;

				UPDATE dbo.ams_virtualGroupRuleVersions
				SET dateSkipped = @dateSkipped
				WHERE ruleVersionID = @ruleVersionID;

			END ELSE
				SET @useRuleVersionID = @ruleVersionID;

			-- return ruleVersionID
			SELECT @useRuleVersionID AS ruleVersionID;
		</cfquery>

		<cfreturn { "success":true, "ruleversionid":val(qryTakeOverRuleVersion.ruleVersionID) }>
	</cffunction>

	<cffunction name="isActiveRule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">

		<cfset var qryRule = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryRule">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT isActive
			FROM dbo.ams_virtualGroupRules
			WHERE ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
			AND orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "isactive":qryRule.isActive }>
	</cffunction>
	
	<cffunction name="addConditionToRule" access="public" output="false" returntype="struct" hint="add condition to rule">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryRule = this.objVirtualGroups.getRules(orgID=arguments.event.getValue('mc_siteInfo.orgID'), ruleID=arguments.event.getValue('ruleID'), ruleVersionID=arguments.event.getValue('ruleVersionID',0))>
		
		<cfif local.qryRule.ruleTypeID is 1>
			<cfset local.CSName = "Condition">
			<cfset local.CSTitle = "Add Condition to Rule">
		<cfelseif local.qryRule.ruleTypeID is 2>
			<cfset local.CSName = "Filter">
			<cfset local.CSTitle = "Add Report Filter">
		<cfelseif local.qryRule.ruleTypeID is 3>
			<cfset local.CSName = "Filter">
			<cfset local.CSTitle = "Add Email Blast Filter">
		</cfif>

		<cfset local.areaCode = arguments.event.getValue('areaCode','m')>
		<cfset local.conditionsListLink = "#this.link.dtRootLink#&meth=getConditions&areaCode=#local.areaCode#&gridword=#local.CSName#&gridtype=#local.qryRule.ruleTypeID#&condsetid=#arguments.event.getValue('conditionSetID')#&ruleid=#arguments.event.getValue('ruleID')#&ruleVersionID=#arguments.event.getValue('ruleVersionID',0)#&dtMode=vgcSelect">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_addCondition.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editConditionUID" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getConditionUID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT c.conditionID, c.uid, c.verbose
			FROM dbo.ams_virtualGroupConditions AS c
			WHERE c.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="cf_sql_integer">
			and c.conditionID = <cfqueryparam value="#arguments.event.getValue('conditionID')#" cfsqltype="cf_sql_integer">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editConditionUID.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCondition" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset structAppend(local, this.objVirtualGroups.getConditionDetails(orgID=arguments.event.getValue('mc_siteInfo.orgID'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), 
			conditionTypeID=arguments.event.getValue('conditionTypeID'), conditionID=arguments.event.getValue('conditionID',0), ruleID=arguments.event.getValue('ruleID',0),
			ruleVersionID=val(arguments.event.getValue('ruleVersionID',0))))>
		
		<cfset local.gridNum = iif(val(arguments.event.getValue('mcrb_gridnum',1)) gt 1,de(val(arguments.event.getValue('mcrb_gridnum',1))),de(''))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_condition.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveConditionUID" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.err = 0>

		<cfset local.conditionID = arguments.event.getValue('conditionID',0)>
		<cfset local.conditionUID = arguments.event.getTrimValue('condUID','')>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCurrentCondUID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT uid
			FROM dbo.ams_virtualGroupConditions
			WHERE conditionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.conditionID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- condUID length --->
		<cfif NOT len(local.conditionUID)>
			<cfset local.err = 1>

		<!--- no change to condUID --->
		<cfelseif not compare(local.qryCurrentCondUID.uid,local.conditionUID)>
			<cfset local.err = 2>

		<cfelse>
			<!--- condUID cant already exist on another condition --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCheckUID">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT conditionID
				FROM dbo.ams_virtualGroupConditions
				WHERE uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.conditionUID#">
				and conditionID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#local.conditionID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryCheckUID.recordcount>
				<cfset local.err = 3>
			<cfelse>

				<!--- update condUID --->
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateCond">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
							@conditionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.conditionID#">;
						DECLARE @minRuleVersionID int, @ruleXML xml, @ruleID int;
						DECLARE @tmpRules TABLE (ruleID int, ruleXML xml, ruleVersionID int);

						INSERT INTO @tmpRules (ruleID, ruleXML, ruleVersionID)
						SELECT r.ruleID, rv.ruleXML, rv.ruleVersionID
						FROM dbo.ams_virtualGroupRules AS r
						INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
							AND rv.ruleID = r.ruleID
						INNER JOIN dbo.ams_virtualGroupRuleConditionSets AS rcs ON rcs.ruleID = r.ruleID
							AND rcs.ruleVersionID = rv.ruleVersionID
						INNER JOIN dbo.ams_virtualGroupRuleConditions AS rc ON rc.conditionSetID = rcs.conditionSetID
						WHERE r.orgID = @orgID
						AND rc.conditionID = @conditionID;

						BEGIN TRAN;
							UPDATE dbo.ams_virtualGroupConditions
							SET uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.conditionUID#">
							WHERE conditionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.conditionID#">;

							WHILE EXISTS (select ruleVersionID from dbo.ams_virtualGroupRuleVersions WHERE ruleXML.exist('//condition[@id="#UCASE(local.qryCurrentCondUID.uid)#"]')=1) BEGIN
								UPDATE dbo.ams_virtualGroupRuleVersions
								SET ruleXML.modify('
									replace value of (//condition[@id="#UCASE(local.qryCurrentCondUID.uid)#"]/@id)[1]
									with "#UCASE(local.conditionUID)#"
								');
							END;

							SELECT @minRuleVersionID = MIN(ruleVersionID) FROM @tmpRules;
							WHILE @minRuleVersionID IS NOT NULL BEGIN
								SELECT @ruleXML = NULL, @ruleID = NULL;

								SELECT @ruleXML = ruleXML, @ruleID = ruleID
								FROM @tmpRules
								WHERE ruleVersionID = @minRuleVersionID;

								EXEC dbo.ams_populateVirtualGroupRuleConditionSets @ruleID=@ruleID, @ruleXML=@ruleXML, @ruleVersionID=@minRuleVersionID;

								SELECT @minRuleVersionID = MIN(ruleVersionID) FROM @tmpRules WHERE ruleVersionID > @minRuleVersionID;
							END
						COMMIT TRAN;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

			</cfif>
		</cfif>

		<cfif listFind("1,3",local.err)>
			<cflocation url="#this.link.editConditionUID#&conditionID=#local.conditionID#&err=#local.err#" addtoken="false">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.MCModalUtils.hideModal();
				 </script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCondition" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cftry>
			<cfset local.conditionID = arguments.event.getValue('conditionID',0)>
			<cfset local.conditionTypeID = arguments.event.getValue('conditionTypeID',1)>
			<cfset local.fieldCode = arguments.event.getValue('fieldCode','')>
			<cfset local.fieldCodeDataType = arguments.event.getValue('fieldCodeDataType','')>
			<cfset local.fieldCodeDisplayType = arguments.event.getValue('fieldCodeDisplayType','')>
			<cfset local.ruleID = int(val(arguments.event.getValue('ruleID',0)))>
			<cfset local.ruleVersionID = int(val(arguments.event.getValue('ruleVersionID',0)))>
			<cfset local.conditionSetID = arguments.event.getValue('conditionSetID','')>
			<cfset local.expression = arguments.event.getValue('expression','')>
			<cfset local.exprDatePart = arguments.event.getValue('datepart','')>
			<cfset local.dateOperator = arguments.event.getValue('dateOperator','')>
			<cfset local.dateOperatorAnniv = arguments.event.getValue('dateOperatorAnniv','')>
			<cfset local.conditionValue = arguments.event.getValue('conditionValue','')>
			<cfset local.pastOrFutureValue = arguments.event.getValue('pastOrFutureValue','')>
			<cfset local.canHaveMultipleValues = arguments.event.getValue('canHaveMultipleValues',0)>
			<cfset local.asNew = arguments.event.getValue('asNew',0)>
			<cfset local.areaCode = arguments.event.getValue('s1_1','')>
			<cfset local.ruleBuilderMode = arguments.event.getValue('ruleBuilderMode','')>
			<cfset local.gridNum = iif(val(arguments.event.getValue('mcrb_gridnum',1)) gt 1,de(val(arguments.event.getValue('mcrb_gridnum',1))),de(''))>

			<cfif local.ruleID GT 0>
				<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=local.ruleID, ruleVersionID=local.ruleVersionID)>
				<cfset arguments.event.setValue('ruleVersionID',local.ruleVersionID)>
			</cfif>

			<cfset local.valueXML = createValueXML(event=arguments.event)>

			<!--- for a new condition --->
			<cfif local.conditionID is 0>
				<cftry>
					<cfset local.createConditionReturnCode = 0>
					<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createVirtualGroupCondition" returncode="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.conditionTypeID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fieldCode#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.expression#">
						<cfif local.fieldcodeDataType eq "DATE" AND (local.expression eq "datepart" or local.expression eq "datediff")>
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.exprDatePart#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperator#">
						<cfelseif local.fieldcodeDataType eq "DATE" AND local.expression eq "isanniversary">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperatorAnniv#">
						<cfelse>
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
						</cfif>
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.valueXML#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.conditionID">
					</cfstoredproc>

					<cfset local.createConditionReturnCode = cfstoredproc.statusCode>
					<cfset arguments.event.setValue('conditionID',local.conditionID)>
					
					<cfif val(local.ruleID) gt 0 AND (local.createConditionReturnCode IS 0 OR NOT existsInConditionSet(arguments.event))>
						<cfset addConditionToConditionSet(arguments.event)>
					<cfelseif local.createConditionReturnCode IS NOT 0>
						<cfthrow>
					</cfif>
				
					<cfsavecontent variable="local.data">
						<cfoutput>
						<script language="javascript">
							if (typeof top.close#local.gridNum#ConditionBox == 'function') {
								top.close#local.gridNum#ConditionBox(#local.ruleVersionID#);
							} else if (typeof top.$('##btnReturnToRule#local.ruleID#').length) {
								top.MCModalUtils.setTitle('Edit Rule');
								self.location.href = top.$('##btnReturnToRule#local.ruleID#').attr('data-rulelink') + '&ruleVersionID=#local.ruleVersionID#';
							} else {
								top.MCModalUtils.hideModal();
							}
						</script>
						</cfoutput>
					</cfsavecontent>
				<cfcatch type="Any">
					<cfif arguments.event.getValue('conditionTypeID') is 1 OR arguments.event.getValue('conditionTypeID') is 3>
						<cfset local.conditionWord = "Condition"> 
					<cfelse>
						<cfset local.conditionWord = "Filter">
					</cfif>

					<cfif local.createConditionReturnCode is -2>
						<cfif val(local.ruleID) gt 0>
							<cfset local.errorMsg = "This #LCASE(local.conditionWord)# already exists in your #local.conditionWord# Set.">
						<cfelse>
							<cfset local.errorMsg = "We could not add your #LCASE(local.conditionWord)# because another #LCASE(local.conditionWord)# with that setup already exists.">
						</cfif>
					<cfelse>
						<cfset local.errorMsg = "There was an error saving your changes.">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfif>

					<cfsavecontent variable="local.data">
						<cfoutput>
						<script language="javascript">
							self.location.href = top.mca_getEditVGCLink(0,#local.conditionTypeID#,#local.ruleID#,#local.ruleVersionID#,'#local.conditionSetID#','#local.gridNum#','#local.areaCode#','#encodeForJavascript(local.ruleBuilderMode)#','#encodeForJavascript(local.errorMsg)#');
						</script>
						</cfoutput>
					</cfsavecontent>
				</cfcatch>
				</cftry>
			
			<!--- for editing a condition --->
			<cfelse>

				<!--- if this is a save as new, then we need to save a new condition and use the new one instead of the old one in the rule --->
				<!--- be sure to bypass queue to wait until we have changed the rule --->
				<cfif local.asNew is 1 and local.ruleID gt 0>
					<cfset local.oldConditionID = local.conditionID>
					<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createVirtualGroupCondition">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.conditionTypeID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fieldCode#">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.expression#">
						<cfif local.fieldcodeDataType eq "DATE" AND (local.expression eq "datepart" or local.expression eq "datediff")>
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.exprDatePart#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperator#">
						<cfelseif local.fieldcodeDataType eq "DATE" AND local.expression eq "isanniversary">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperatorAnniv#">
						<cfelse>
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
						</cfif>
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.valueXML#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.conditionID">
					</cfstoredproc>

					<!--- only need to change rule if a new condition was created. --->
					<cfif local.oldConditionID is not local.conditionID>
						<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetCondUID">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							SELECT uid
							FROM dbo.ams_virtualGroupConditions
							WHERE conditionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.conditionID#">;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetOldCondUID">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							SELECT uid
							FROM dbo.ams_virtualGroupConditions
							WHERE conditionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.oldConditionID#">;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateRules">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								DECLARE @orgID int, @ruleID int, @ruleVersionID int;
								SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
								SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleID#">;
								SET @ruleVersionID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">,0);

								SELECT @ruleVersionID = ISNULL(@ruleVersionID,activeVersionID)
								FROM dbo.ams_virtualGroupRules
								WHERE ruleID = @ruleID
								AND orgID = @orgID;

								WHILE EXISTS (select ruleVersionID from dbo.ams_virtualGroupRuleVersions WHERE ruleXML.exist('//condition[@id="#UCASE(local.qryGetOldCondUID.uid)#"]')=1 AND ruleVersionID=@ruleVersionID) BEGIN
									UPDATE dbo.ams_virtualGroupRuleVersions
									SET ruleXML.modify('
											replace value of (//condition[@id="#UCASE(local.qryGetOldCondUID.uid)#"]/@id)[1]
											with "#UCASE(local.qryGetCondUID.uid)#"
										'),
										dateUpdated = GETDATE()
									WHERE ruleVersionID = @ruleVersionID;
								END

								-- update rule SQL
								EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;

								-- reprocess condition
								IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
									DROP TABLE ##tblMCQRun;
								CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

								INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
								VALUES (@orgID, null, #local.conditionID#);

								EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

								IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
									DROP TABLE ##tblMCQRun;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>
					</cfif>
					<cfsavecontent variable="local.data">
						<cfoutput>
						<script language="javascript">
							if (typeof top.close#local.gridNum#ConditionBox == 'function') {
								top.close#local.gridNum#ConditionBox(#local.ruleVersionID#);
							} else if (typeof top.$('##btnReturnToRule#local.ruleID#').length) {
								top.MCModalUtils.setTitle('Edit Rule');
								self.location.href = top.$('##btnReturnToRule#local.ruleID#').attr('data-rulelink') + '&ruleVersionID=#local.ruleVersionID#';
							} else {
								top.MCModalUtils.hideModal();
							}
						</script>
						</cfoutput>
					</cfsavecontent>
				<cfelse>
					<cftry>
						<cfset local.updateConditionReturnCode = 0>
						<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_updateVirtualGroupCondition" returncode="true">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.conditionID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.expression#">
							<cfif local.fieldcodeDataType eq "DATE" AND (local.expression eq "datepart" or local.expression eq "datediff")>
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.exprDatePart#">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperator#">
							<cfelseif local.fieldcodeDataType eq "DATE" AND local.expression eq "isanniversary">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.dateOperatorAnniv#">
							<cfelse>
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
							</cfif>
							<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.valueXML#">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						</cfstoredproc>
						<cfset local.updateConditionReturnCode = cfstoredproc.statusCode>
						
						<cfif local.updateConditionReturnCode is not 0>
							<cfthrow>
						</cfif>

						<!--- need to edit any rule that contains this condition --->
						<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.affectedRuleVersions">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
								@conditionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.conditionID#">;
							
							SELECT DISTINCT r.ruleID, rv.ruleVersionID
							FROM dbo.ams_virtualGroupRules AS r
							INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
								AND rv.ruleID = r.ruleID
							INNER JOIN dbo.ams_virtualGroupRuleConditionSets AS rcs ON rcs.ruleID = r.ruleID
								AND rcs.ruleVersionID = rv.ruleVersionID
							INNER JOIN dbo.ams_virtualGroupRuleConditions AS rc ON rc.conditionSetID = rcs.conditionSetID
							WHERE r.orgID = @orgID
							AND rc.conditionID = @conditionID;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						</cfquery>
						<cfloop query="local.affectedRuleVersions">
							<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_updateVirtualGroupRuleSQL">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.affectedRuleVersions.ruleID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.affectedRuleVersions.ruleVersionID#">
							</cfstoredproc>
						</cfloop>
		
						<cfsavecontent variable="local.data">
							<cfoutput>
							<script language="javascript">
								if (typeof top.close#local.gridNum#ConditionBox == 'function') {
									top.close#local.gridNum#ConditionBox(#local.ruleVersionID#);
								} else if (typeof top.$('##btnReturnToRule#local.ruleID#').length) {
									top.MCModalUtils.setTitle('Edit Rule');
									self.location.href = top.$('##btnReturnToRule#local.ruleID#').attr('data-rulelink') + '&ruleVersionID=#local.ruleVersionID#';
								} else {
									top.MCModalUtils.hideModal();
								}
							 </script>
							</cfoutput>
						</cfsavecontent>
					<cfcatch type="Any">
						<cfsavecontent variable="local.data">
							<cfoutput>
							<script language="javascript">
								<cfif local.updateConditionReturnCode is -2>
									self.location.href = top.mca_getEditVGCLink(#local.conditionID#,#local.conditionTypeID#,#local.ruleID#,#local.ruleVersionID#,'#local.conditionSetID#','#local.gridNum#','#local.areaCode#','#encodeForJavascript(local.ruleBuilderMode)#','#encodeForJavascript("We could not save your changes because another condition with that setup already exists.")#');
								<cfelse>
									self.location.href = top.mca_getEditVGCLink(#local.conditionID#,#local.conditionTypeID#,#local.ruleID#,#local.ruleVersionID#,'#local.conditionSetID#','#local.gridNum#','#local.areaCode#','#encodeForJavascript(local.ruleBuilderMode)#','#encodeForJavascript("There was an error saving your changes.")#');
								</cfif>
							 </script>
							</cfoutput>
						</cfsavecontent>
					</cfcatch>
					</cftry>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="existsInConditionSet" access="public" output="false" returntype="boolean">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.result = false>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getConditions">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('ruleID')#">,
				@ruleVersionID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('ruleVersionID',0)#">,0),
				@conditionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('conditionID')#">,
				@conditionSetUID varchar(36) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#UCASE(arguments.event.getValue('conditionSetID'))#">;

			SELECT rv.ruleVersionID
			FROM dbo.ams_virtualGroupRules AS r
			INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
				AND rv.ruleID = r.ruleID
				AND rv.ruleVersionID = ISNULL(@ruleVersionID,r.activeVersionID)
			INNER JOIN dbo.ams_virtualGroupRuleConditionSets AS rcs ON rcs.ruleID = r.ruleID
				AND rcs.ruleVersionID = rv.ruleVersionID
				AND rcs.[uid] = @conditionSetUID
			INNER JOIN dbo.ams_virtualGroupRuleConditions AS rc ON rc.conditionSetID = rcs.conditionSetID
			WHERE r.orgID = @orgID
			AND r.ruleID = @ruleID
			AND rc.conditionID = @conditionID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.getConditions.recordCount gt 0>
			<cfset local.result = true>
		</cfif>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="saveConditionSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="conditionSetUID" type="string" required="true">
		<cfargument name="operator" type="string" required="true">
		<cfargument name="action" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUdateVirtualGroupRuleSet">
				EXEC dbo.ams_updateVirtualGroupRuleSet
					@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">,
					@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
					@ruleVersionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">,
					@conditionSetUID=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.conditionSetUID#">,
					@operator=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.operator#">,
					@action=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.action#">;
			</cfquery>

			<cfset local.returnStruct = { "success":true, "ruleversionid":local.ruleVersionID }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct = { "success":false, "ruleversionid":0 }>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addConditionToConditionSet" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.ruleID = int(val(arguments.event.getValue('ruleID')))>
		<cfset local.ruleVersionID = int(val(arguments.event.getValue('ruleVersionID',0)))>
		<cfset local.gridNum = iif(val(arguments.event.getValue('mcrb_gridnum',1)) gt 1,de(val(arguments.event.getValue('mcrb_gridnum',1))),de(''))>
		<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=local.ruleID, ruleVersionID=local.ruleVersionID)>
		<cfset local.ruleBuilderMode = arguments.event.getTrimValue('ruleBuilderMode','traditional')>

		<cfif local.ruleBuilderMode EQ 'simple'>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryAddCondition">
				EXEC dbo.ams_insertSimpleVirtualGroupRuleSetCondition 
					@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
					@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleID#">,
					@ruleVersionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">,
					@conditionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('conditionID')#">,
					@conditionSetUID=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('conditionSetID')#">;
			</cfquery>
		<cfelse>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryAddCondition">
				EXEC dbo.ams_insertVirtualGroupRuleSetCondition 
					@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
					@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleID#">,
					@ruleVersionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">,
					@conditionID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('conditionID')#">,
					@conditionSetUID=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('conditionSetID')#">;
			</cfquery>
		</cfif>
			
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (typeof top.reload#local.gridNum#RuleGrid == 'function') {
					top.MCModalUtils.hideModal();
					top.reload#local.gridNum#RuleGrid(#local.ruleVersionID#);
				} else if (typeof top.$('##btnReturnToRule#local.ruleID#').length) {
					top.MCModalUtils.setTitle('Edit Rule');
					self.location.href = top.$('##btnReturnToRule#local.ruleID#').attr('data-rulelink') + '&ruleVersionID=#local.ruleVersionID#';
				} else {
					top.MCModalUtils.hideModal();
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="deleteRuleGroup" access="public" output="false" returntype="struct" hint="deletes a rule group">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleGroupIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.groupSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GroupAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.groupSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif NOT local.tmpRights.EditGroup>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_deleteVirtualGroupRuleGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.ruleGroupIDList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="addRuleGroup" access="public" output="false" returntype="struct" hint="adds a rule group">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createVirtualGroupRuleGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="addConditionSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="conditionSetID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getOrigRuleXML">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int,	@ruleID int, @ruleVersionID int;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
			SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
			SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;

			SELECT rv.ruleXML
			FROM dbo.ams_virtualGroupRules AS r
			INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
				AND rv.ruleID = r.ruleID
				AND rv.ruleVersionID = @ruleVersionID
				AND rv.dateActivated IS NULL
			WHERE r.ruleID = @ruleID
			AND r.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.rulesXML = xmlParse(local.getOrigRuleXML.ruleXML)>
			
		<!--- isolate the conditionset being modified --->
		<cfset local.CSnode = XMLSearch(local.rulesXML,"//conditionset[@id='#arguments.conditionSetID#']")>

		<!--- Insert New Condition Set w/ ArrayPrepend --->
		<cfset local.newConditionSet = XmlElemNew(local.rulesXML,"","conditionset")>
		<cfset local.newConditionSet.xmlAttributes['op']="AND">
		<cfset local.newConditionSet.xmlAttributes['act']="include">
		<cfset local.newConditionSet.xmlAttributes['id']=ucase(createUUID())>
		<cfset ArrayPrepend(local.CSnode[1].xmlChildren,local.newConditionSet)>

		<!--- Check to see if conditionSet has conditions --->
		<!--- if there are children conditions, copy them into the new conditionset and then delete them --->
		<cfset local.conditionNodes = XMLSearch(local.CSnode[1],"//conditionset[@id='#arguments.conditionSetID#']/condition")>
		<cfif arrayLen(local.conditionNodes)>
			<cfloop index="local.i" array="#local.CSnode[1].xmlChildren#">
				<cfif local.i.xmlName eq "condition">
					<cfset ArrayAppend(local.CSnode[1].xmlChildren[1].xmlchildren,local.i)>
				</cfif>
			</cfloop>
		</cfif>
		<cfset local.conditionNodes = XMLSearch(local.CSnode[1],"//conditionset[@id='#arguments.conditionSetID#']/condition")>
		<cfif arrayLen(local.conditionNodes)>
			<cfloop index="local.i" from="#arrayLen(local.CSnode[1].xmlChildren)#" to="1" step="-1">
				<cfif local.CSnode[1].xmlChildren[local.i].xmlName eq "condition">
					<cfset arrayDeleteAt(local.CSnode[1].xmlChildren,local.i)>
				</cfif>
			</cfloop>
		</cfif>

		<!--- remove the <xml> tag, specifically the encoding. It breaks under Railo. --->
		<cfset local.rulesXML = replaceNoCase(toString(local.rulesXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.addCondition">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int,	@ruleID int, @ruleVersionID int, @dateActivated datetime;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
				SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
				SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;

				SELECT @dateActivated = rv.dateActivated
				FROM dbo.ams_virtualGroupRules AS r
				INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
					AND rv.ruleID = r.ruleID
					AND rv.ruleVersionID = @ruleVersionID
				WHERE r.ruleID = @ruleID
				AND r.orgID = @orgID;

				IF @dateActivated IS NULL BEGIN
					BEGIN TRAN;
						UPDATE dbo.ams_virtualGroupRuleVersions
						SET	ruleXML = <cfqueryparam value="#local.rulesXML#" cfsqltype="CF_SQL_LONGVARCHAR">,
							dateUpdated = GETDATE()
						WHERE ruleVersionID = @ruleVersionID;

						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;
					COMMIT TRAN;
				END

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "ruleversionid":local.ruleVersionID }>
	</cffunction>
	
	<cffunction name="removeConditionFromSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="conditionSetID" type="string" required="true">
		<cfargument name="conditionID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetCondUID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT uid
			FROM dbo.ams_virtualGroupConditions
			WHERE conditionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.conditionID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryRemoveConditionFromSet">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int,	@ruleID int, @ruleVersionID int, @dateActivated datetime;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
				SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
				SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;

				SELECT @dateActivated = rv.dateActivated
				FROM dbo.ams_virtualGroupRules AS r
				INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
					AND rv.ruleID = r.ruleID
					AND rv.ruleVersionID = @ruleVersionID
				WHERE r.ruleID = @ruleID
				AND r.orgID = @orgID;

				IF @dateActivated IS NULL BEGIN
					BEGIN TRAN;
						UPDATE dbo.ams_virtualGroupRuleVersions
						SET ruleXML.modify('delete //condition[@id="#ucase(local.qryGetCondUID.uid)#" and ../@id="#arguments.conditionSetID#"]'),
							dateUpdated = GETDATE()
						WHERE ruleVersionID = @ruleVersionID;

						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;
					COMMIT TRAN;
				END

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "ruleversionid":local.ruleVersionID }>
	</cffunction>
	
	<cffunction name="deleteRule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="string" required="true">
		<cfargument name="ruleIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_deleteVirtualGroupRule">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.ruleIDList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="deleteCondition" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="conditionIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_deleteVirtualGroupCondition">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.conditionIDList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="exportConditionMembersPrompt" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Groups Download Members Standard',
			allowBlankOption=false, inlinePreviewSectionID="frmExportMemWrap")>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getCondition">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT c.conditionID, c.verbose
			FROM dbo.ams_virtualGroupConditions AS c
			WHERE c.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="cf_sql_integer">
			and c.conditionID = <cfqueryparam value="#arguments.event.getValue('conditionID')#" cfsqltype="cf_sql_integer">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;			
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportConditionMembers.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportConditionMembers" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="conditionID" type="numeric" required="true">
		<cfargument name="fieldSetID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getCondition">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT c.conditionID, c.verbose
				FROM dbo.ams_virtualGroupConditions AS c
				WHERE c.orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="cf_sql_integer">
				and c.conditionID = <cfqueryparam value="#arguments.conditionID#" cfsqltype="cf_sql_integer">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strReturn.strFolder = application.objDocDownload.createHoldingFolder()>
			<cfset local.strReturn.reportFileName = "#left(ReReplace(local.getCondition.verbose,'[^A-Za-z0-9]','','ALL'),50)#.csv">

			<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				-- drop the temp table
				IF OBJECT_ID('tempdb..##GReport') IS NOT NULL 
					DROP TABLE ##GReport;
				IF OBJECT_ID('tempdb..##tmpGroupMembers') IS NOT NULL 
					DROP TABLE ##tmpGroupMembers;
				CREATE TABLE ##GReport (memberID int PRIMARY KEY);
				CREATE TABLE ##tmpGroupMembers (MFSAutoID int IDENTITY(1,1) not null);

				declare @conditionID int, @orgID int, @fieldSetID int, @outputFieldsXML xml;
				set @conditionID = <cfqueryparam value="#arguments.conditionID#" cfsqltype="CF_SQL_INTEGER">;
				set @orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
				set @fieldSetID = <cfqueryparam value="#arguments.fieldSetID#" cfsqltype="CF_SQL_INTEGER">;

				-- get members. cache_members_conditions already restricts to activememberid so no need to do it here again
				INSERT INTO ##GReport (memberID)
				SELECT distinct m.memberid
				from dbo.cache_members_conditions as cmc
				inner join dbo.ams_members as m on m.memberid = cmc.memberid
				where m.orgID = @orgID
				and cmc.orgID = @orgID
				and cmc.conditionID = @conditionID
				and m.status <> 'D';

				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##GReport', @membersResultTableName='##tmpGroupMembers',
					@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

				ALTER TABLE ##tmpGroupMembers DROP COLUMN memberID;

				DECLARE @selectsql varchar(max) = '
					SELECT *, 1 as mcCSVorder 
					*FROM* ##tmpGroupMembers';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strReturn.strFolder.folderPathUNC#/#local.strReturn.reportFileName#",'\')#', @returnColumns=0;

				-- drop the temp table
				IF OBJECT_ID('tempdb..##GReport') IS NOT NULL 
					DROP TABLE ##GReport;
				IF OBJECT_ID('tempdb..##tmpGroupMembers') IS NOT NULL 
					DROP TABLE ##tmpGroupMembers;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strReturn.strFolder.folderPath#/#local.strReturn.reportFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strReturn.strFolder.folderPath#/#local.strReturn.reportFileName#", displayName=local.strReturn.reportFileName, deleteSourceFile=1)>

			<cfset local.strReturn['url'] = '/tsdd/#local.stDownloadURL#'>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="removeConditionSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="conditionSetID" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.removeConditionSet">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int,	@ruleID int, @ruleVersionID int, @dateActivated datetime;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
				SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
				SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;

				SELECT @dateActivated = rv.dateActivated
				FROM dbo.ams_virtualGroupRules AS r
				INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
					AND rv.ruleID = r.ruleID
					AND rv.ruleVersionID = @ruleVersionID
				WHERE r.ruleID = @ruleID
				AND r.orgID = @orgID;

				IF @dateActivated IS NULL BEGIN
					BEGIN TRAN;
						UPDATE dbo.ams_virtualGroupRuleVersions
						SET ruleXML.modify('delete //conditionset[@id="#arguments.conditionSetID#"]'),
							dateUpdated = GETDATE()
						WHERE ruleVersionID = @ruleVersionID;

						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;
					COMMIT TRAN;
				END
			
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "ruleversionid":local.ruleVersionID }>
	</cffunction>
	
	<cffunction name="doConditionSetMove" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="conditionSetID" type="string" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.ruleVersionID = createObject("component","model.admin.common.modules.ruleBuilder.ruleBuilder").checkUncommittedRuleVersion(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.getRuleXML">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
			DECLARE @orgID int,	@ruleID int, @ruleVersionID int;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
			SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
			SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;
		
			SELECT rv.ruleXML
			FROM dbo.ams_virtualGroupRules AS r
			INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
				AND rv.ruleID = r.ruleID
				AND rv.ruleVersionID = @ruleVersionID
				AND rv.dateActivated IS NULL
			WHERE r.ruleID = @ruleID
			AND r.orgID = @orgID;
		
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.rulesXML = xmlParse(local.getRuleXML.ruleXML)>
		<cfset local.ParentNode = XMLSearch(local.rulesXML,"//conditionset[@id='#arguments.conditionSetID#']/..")>
		<cfset local.pos = XMLSearch(local.rulesXML,"count(//conditionset[@id='#arguments.conditionSetID#']/preceding-sibling::*)+1")>
		<cfset arraySwap(local.ParentNode[1].xmlchildren, local.pos, (arguments.dir EQ "Up" ? local.pos-1 : local.pos+1))>
		
		<!--- remove the <xml> tag, specifically the encoding. It breaks under Lucee. --->
		<cfset local.rulesXML = replaceNoCase(toString(local.rulesXML),'<?xml version="1.0" encoding="UTF-8"?>','')>
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.conditionSetMoveUp">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int,	@ruleID int, @ruleVersionID int, @dateActivated datetime;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">;
				SET @ruleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">;
				SET @ruleVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ruleVersionID#">;

				SELECT @dateActivated = rv.dateActivated
				FROM dbo.ams_virtualGroupRules AS r
				INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
					AND rv.ruleID = r.ruleID
					AND rv.ruleVersionID = @ruleVersionID
				WHERE r.ruleID = @ruleID
				AND r.orgID = @orgID;

				IF @dateActivated IS NULL BEGIN
					BEGIN TRAN;
						UPDATE dbo.ams_virtualGroupRuleVersions
						SET ruleXML = <cfqueryparam value="#local.rulesXML#" cfsqltype="CF_SQL_LONGVARCHAR">,
							dateUpdated = GETDATE()
						WHERE ruleVersionID = @ruleVersionID;

						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;
					COMMIT TRAN;
				END

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
			
		<cfreturn { "success":true, "ruleversionid":local.ruleVersionID }>
	</cffunction>
		
	<!--- ajax --->
	<cffunction name="convertQueryToArrayForDropdowns" access="public" returnType="array">
		<cfargument name="qryData" type="query" required="true">

		<cfset var local = structNew()>

		<!--- query to array of structs --->
		<cfset local.aTmp = arrayNew(1)>
		<cfloop query="arguments.qryData">
			<cfset local.stTmp = structNew()>
			<cfloop list="#lcase(arguments.qryData.columnlist)#" index="local.col">
				<cfset local.stTmp[local.col] = arguments.qryData[local.col][arguments.qryData.currentRow]>
			</cfloop>
			<cfset arrayAppend(local.aTmp,local.stTmp)>
		</cfloop>

		<cfreturn local.aTmp>
	</cffunction>

	<cffunction name="getExpressions" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="dit" type="string" required="true">
		<cfargument name="dat" type="string" required="true">
		<cfargument name="fc" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif arguments.fc eq 'm_prefix'>
			<cfquery name="local.qryPrefixSetting" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select usePrefixList
				from dbo.organizations
				where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfif left(arguments.fc,4) eq "sub_">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'subscribed'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "l_entry">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'onlist'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif left(arguments.fc,3) eq "rt_">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'linked'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif left(arguments.fc,3) eq "ev_">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression in ('registered','attended','awarded')
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif left(arguments.fc,4) eq "grp_">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'included'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "acct_inv">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'hasinvoice'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "acct_cc">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'hasexpiringcard'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>	
		<cfelseif listFindNoCase("acct_allocsum,acct_allocsumRecog,acct_balance",arguments.fc)>
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, coalesce(edt.ovexpressionVerbose,e.expressionVerbose) as expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				INNER JOIN dbo.ams_virtualGroupExpressionsDataTypes as edt ON e.expressionID = edt.expressionID 
				INNER JOIN dbo.ams_memberDataColumnDataTypes as cdt ON edt.dataTypeID = cdt.dataTypeID 
				WHERE cdt.dataTypeCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.dat#">
					union all
				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression in ('between')
				ORDER BY 3;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif listFindNoCase("m_membertypeid,m_status",arguments.fc)>
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression in ('eq','neq')
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "m_recordtypeid">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, coalesce(edt.ovexpressionVerbose,e.expressionVerbose) as expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				INNER JOIN dbo.ams_virtualGroupExpressionsDataTypes as edt ON e.expressionID = edt.expressionID 
				INNER JOIN dbo.ams_memberDataColumnDataTypes as cdt ON edt.dataTypeID = cdt.dataTypeID 
				WHERE cdt.dataTypeCode = 'BIT'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif (arguments.fc eq "m_prefix" and local.qryPrefixSetting.usePrefixList is 1)
			or (left(arguments.fc,4) eq "mad_")
			or (left(arguments.fc,5) eq "madt_")
			or (left(arguments.fc,3) eq "ma_" and getToken(arguments.fc,3,'_') eq "stateprov")
			or (left(arguments.fc,4) eq "mat_" and getToken(arguments.fc,3,'_') eq "stateprov")
			or (left(arguments.fc,3) eq "ma_" and getToken(arguments.fc,3,'_') eq "country")
			or (left(arguments.fc,4) eq "mat_" and getToken(arguments.fc,3,'_') eq "country")
			or (left(arguments.fc,4) eq "mpl_" and getToken(arguments.fc,3,'_') eq "status")
			or (left(arguments.fc,3) eq "md_" and listFindNoCase("RADIO,SELECT,CHECKBOX",arguments.dit))>
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, coalesce(edt.ovexpressionVerbose,e.expressionVerbose) as expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				INNER JOIN dbo.ams_virtualGroupExpressionsDataTypes as edt ON e.expressionID = edt.expressionID 
				INNER JOIN dbo.ams_memberDataColumnDataTypes as cdt ON edt.dataTypeID = cdt.dataTypeID 
				WHERE cdt.dataTypeCode = 'BIT'
				<cfif arguments.dat eq "DATE">
						union
					SELECT e.expression, e.expressionVerbose, e.expressionOrder
					FROM dbo.ams_virtualGroupExpressions as e 
					WHERE e.expression = 'isanniversary'
				</cfif>
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif listFindNoCase("mh_entry,rel_entry,mn_entry,task_entry",arguments.fc)>
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'exists';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "m_hasmemberphoto">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'hasphoto';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "cp_entry">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'contributed';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "cp_valuesum">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, coalesce(edt.ovexpressionVerbose,e.expressionVerbose) as expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				INNER JOIN dbo.ams_virtualGroupExpressionsDataTypes as edt ON e.expressionID = edt.expressionID 
				INNER JOIN dbo.ams_memberDataColumnDataTypes as cdt ON edt.dataTypeID = cdt.dataTypeID 
				WHERE cdt.dataTypeCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.dat#">
				AND e.expression not in ('exists','not_exists')
					union all
				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression in ('between')
				ORDER BY 3;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif left(arguments.fc,3) eq "sw_">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'swregistered';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "sup_entry">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'insuppressionlist'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "cl_entry">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'inconsentlist'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.fc eq "ref_entry">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, e.expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				WHERE e.expression = 'hasreferral'
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif (left(arguments.fc,3) eq "ma_" or left(arguments.fc,3) eq "me_") and getToken(arguments.fc,3,'_') eq "tagged">
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT expression, expressionVerbose, expressionOrder
				FROM dbo.ams_virtualGroupExpressions
				WHERE expression = 'istaggedwith';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelse>
			<cfquery name="local.qryExpressions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT e.expression, coalesce(edt.ovexpressionVerbose,e.expressionVerbose) as expressionVerbose, e.expressionOrder
				FROM dbo.ams_virtualGroupExpressions as e 
				INNER JOIN dbo.ams_virtualGroupExpressionsDataTypes as edt ON e.expressionID = edt.expressionID 
				INNER JOIN dbo.ams_memberDataColumnDataTypes as cdt ON edt.dataTypeID = cdt.dataTypeID 
				WHERE cdt.dataTypeCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.dat#">
				ORDER BY e.expressionOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryExpressions = convertQueryToArrayForDropdowns(qryData=local.qryExpressions)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAdvanceFormula" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="fororg" type="boolean" required="true">

		<cfset var local = structNew()>

		<cfif arguments.fororg EQ 1>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllAFs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT AFID, afName, siteName
				FROM dbo.af_advanceFormulas af
				inner join dbo.sites s on s.siteID = af.siteID
				WHERE s.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
				ORDER BY siteName, afName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelse>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllAFs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT AFID, afName
				FROM dbo.af_advanceFormulas
				WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
				ORDER BY afName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>	

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryAF = convertQueryToArrayForDropdowns(qryData=local.qryAllAFs)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_member" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="ctID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<!--- m_datelastupdated is not allowed for type 1 until code is updated to process conditions when that field changes --->
		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fieldCode, fieldLabel, displayTypeCode, dataTypeCode
			from dbo.fn_ams_getConditionFields_member(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">)
			<cfif arguments.ctid is 1>
				where fieldCode <> 'm_datelastupdated'
			</cfif>
			order by sorting;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getFields_accttypes" access="public" returnType="struct">
		<cfargument name="accr" type="boolean" required="true">

		<cfset var local = structNew()>
		
		<cfset local.aTmp = [
			{ 'fc':'acct_allocsum', 'desc':'Allocation Summary of Cash' }
		]>
		
		<cfif arguments.accr>
			<cfset arrayAppend(local.aTmp,{ 'fc':'acct_allocsumrecog', 'desc':'Allocation Summary of Recognized Revenue' })> 
		</cfif>

		<cfset arrayAppend(local.aTmp,{ 'fc':'acct_balance', 'desc':'Credit Balances' })> 
		<cfset arrayAppend(local.aTmp,{ 'fc':'acct_cc', 'desc':'Credit Card Expiration Dates' })> 
		<cfset arrayAppend(local.aTmp,{ 'fc':'acct_inv', 'desc':'Invoices Due' })> 

		<cfset local.returnStruct = { "success":true, qryFields:local.aTmp }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_acctpayprof" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select mp.profileID, mp.profileName, s.siteName
			from dbo.mp_profiles as mp
			inner join dbo.sites as s on s.siteID = mp.siteID
			inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			where s.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and mp.status = 'A'
			and g.isActive = 1
			and g.gatewayType not in ('PayLater')
			order by s.siteName ,mp.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_acctinvprof" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select profileID, profileName
			from dbo.tr_invoiceProfiles
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and status = 'A'
			order by profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_acctinvMP" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select p.profileID, p.profileName, s.siteName
			from dbo.mp_profiles as p
			inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			inner join dbo.sites as s on s.siteID = p.siteID
			where s.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and p.status = 'A'
			and g.gatewayType in ('AuthorizeCCCIM','SageCCCIM','BankDraft','AffiniPayCC','MCPayEcheck')
			and g.isActive = 1
			order by s.siteName, p.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_acctccprof" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select p.profileID, p.profileName ,s.siteName
			from dbo.mp_profiles as p
			inner join dbo.sites as s on s.siteID = p.siteID
			inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			where s.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and p.status = 'A'
			and g.gatewayType = 'AuthorizeCCCIM'
			and g.isActive = 1
			order by s.siteName ,p.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_acctcctypes" access="public" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cardtypeid,cardtype 
			from mp_cardTypes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_addrtypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select 'ma' as prefix, addressTypeID, addressType, addressTypeOrder
			from dbo.ams_memberAddressTypes
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
				union all
			select 'mat' as prefix, addressTagTypeID, 'Designated ' + addressTagType, 100+addressTagTypeOrder 
			from dbo.ams_memberAddressTagTypes 
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			order by 4;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_addrfields" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="atid" type="numeric" required="true">
		<cfargument name="atgid" type="numeric" required="true">
		<cfargument name="ctID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fieldCode, fieldLabel, displayTypeCode, dataTypeCode
			from dbo.fn_ams_getConditionFields_addrfields(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
				<cfif arguments.atid><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.atid#"><cfelse>null</cfif>,
				<cfif arguments.atgid><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.atgid#"><cfelse>null</cfif>
			)
			<cfif arguments.ctid is 1 AND arguments.atid>
				where fieldCode <> 'ma_#arguments.atid#_datelastupdated'
			<cfelseif arguments.ctid is 1 AND arguments.atgid>
				where fieldCode <> 'mat_#arguments.atgid#_datelastupdated'
			</cfif>
			order by sorting;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_custfields" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select 'md_' + cast(mdc.columnID as varchar(10)) as fieldCode, mdc.columnName as fieldLabel, dt.displayTypeCode, ddt.dataTypeCode
			from dbo.ams_memberdatacolumns as mdc
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			order by mdc.columnName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_groups" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT 'grp_' + cast(groupID as varchar(10)) as fieldCode, left(groupPathExpanded,420) as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode 
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
			ORDER BY groupPathSortOrder

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getFields_emailtypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fieldCode, fieldLabel, displayTypeCode, dataTypeCode
			from dbo.fn_ams_getConditionFields_emailtypes(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">)
			order by sorting;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_consentlistmodes" access="public" returnType="struct">
		
		<cfset var local = structNew()>

		<cfset local.qryConsentListModes = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentListModes(modeNameList="Opt-In,Opt-Out")>

		<cfset local.arrTmp = arrayNew(1)>
		<cfloop query="local.qryConsentListModes">
			<cfset local.stTmp = structNew()>
			<cfloop list="#lcase(local.qryConsentListModes.columnlist)#" index="local.col">
				<cfset local.stTmp[local.col] = local.qryConsentListModes[local.col][local.qryConsentListModes.currentRow]>
			</cfloop>
			<cfset arrayAppend(local.arrTmp,local.stTmp)> 
		</cfloop>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryConsentListModes = local.arrTmp>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_consentlisttypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.platformmail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select consentListTypeID as typeid, consentListTypeName as typename
			from dbo.email_consentListTypes
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			order by orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_consentlists" access="public" returnType="struct">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="modeID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryConsentLists" datasource="#application.dsn.platformmail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @modeID int, @modeName varchar(25), @GlobalOptOutModeID int;
			SET @modeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.modeID#">
			SELECT @modeName = modeName FROM dbo.email_consentListModes WHERE consentListModeID = @modeID;
			SELECT @GlobalOptOutModeID = consentListModeID FROM dbo.email_consentListModes WHERE modeName = 'GlobalOptOut';
			
			SELECT cl.consentListID, cl.consentListName
			FROM dbo.email_consentLists cl
			WHERE cl.consentListTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
			AND cl.[status] = 'A'
			AND (
				(@modeName = 'Opt-Out' AND cl.consentListModeID IN (@modeID, @GlobalOptOutModeID)) OR
				(@modeName = 'Opt-In' AND cl.consentListModeID = @modeID)
			);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrTmp = arrayNew(1)>
		<cfloop query="local.qryConsentLists">
			<cfset local.stTmp = structNew()>
			<cfset local.stTmp["consentlistid"] = local.qryConsentLists.consentListID>
			<cfset local.stTmp["consentlistname"] = local.qryConsentLists.consentListName>
			<cfset arrayAppend(local.arrTmp,local.stTmp)>
		</cfloop>

		<cfset local.returnStruct = {
			"success":true,
			"qryConsentLists":local.arrTmp
		}>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_lictypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select PLTypeID, PLName
			from dbo.ams_memberProfessionalLicenseTypes
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			order by orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>
		<cfset local.returnStruct.strProfLicFieldLabels = QueryRowData(application.objOrgInfo.getOrgProfLicenseLabels(orgID=arguments.mcproxy_orgID),1)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getFields_licstatuses" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select PLStatusID, StatusName
			from dbo.ams_memberProfessionalLicenseStatuses
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			order by orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_rectypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qryFields = application.objOrgInfo.getOrgRecordTypes(arguments.mcproxy_orgID)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_recroles" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="r" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @recordTypeID int, @isPerson bit;
			select @recordTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.r#">;
			select @isPerson = isPerson from dbo.ams_recordTypes where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#"> and recordTypeId = @recordTypeID;

			IF @isPerson = 1
				select rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
				from dbo.ams_recordTypes as rt
				inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.childRecordTypeID = rt.recordTypeID
				inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
				inner join dbo.ams_recordTypes as linkingRT on rtrt.masterRecordTypeID = linkingRT.recordTypeID
				where rt.recordTypeID = @recordTypeID
				and rtrt.isActive = 1
				order by 2, 3;
			else
				select rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
				from dbo.ams_recordTypes as rt
				inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.masterRecordTypeID = rt.recordTypeID
				inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
				inner join dbo.ams_recordTypes as linkingRT on rtrt.childRecordTypeID = linkingRT.recordTypeID
				where rt.recordTypeID = @recordTypeID
				and rtrt.isActive = 1
				order by 2, 3;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_subtypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select st.typeID, st.typeName, s.siteName
			from dbo.sub_types st
			inner join dbo.sites s on s.siteID = st.siteID
			where s.orgid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and [status] <> 'D'
			order by s.siteName ,st.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_subsubs" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="t" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select subs.subscriptionID, subs.subscriptionName
			from dbo.sub_subscriptions as subs
			inner join dbo.sub_types as st on st.typeID = subs.typeID
			inner join dbo.sites s on s.siteID = st.siteID
			where s.orgid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and st.typeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.t#">
			and subs.status <> 'D'
			and st.status <> 'D'
			order by subs.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_subrates" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="s" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpCurrRates') IS NOT NULL 
				DROP TABLE ##tmpCurrRates;
			CREATE TABLE ##tmpCurrRates (rateID int PRIMARY KEY, rateName varchar(500), isRenewalRate bit);

			INSERT INTO ##tmpCurrRates (rateID, rateName, isRenewalRate)
			select r.rateID, rs.scheduleName + ' \ ' + r.rateName + case when rs.status = 'A' and r.status = 'A' then '' else ' (inactive)' end as rateName, r.isRenewalRate
			from dbo.sub_rates as r
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID
			inner join dbo.sub_subscriptions as sub on sub.scheduleID = r.scheduleID
			inner join dbo.sites s on s.siteID = rs.siteID
			where s.orgid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and sub.subscriptionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.s#">
			and r.[status] <> 'D';

			select rateID, rateName, isRenewalRate
			from ##tmpCurrRates
				union all
			select distinct r.rateID, rs.scheduleName + ' \ ' + r.rateName + ' (inactive)' as rateName, r.isRenewalRate
			from dbo.sub_subscribers as ss
			inner join dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
			inner join dbo.sub_types as subtype on subtype.typeID = sub.typeID
			inner join dbo.sub_rateFrequencies as rf on rf.RFID = ss.RFID and rf.status <> 'D'
			inner join dbo.sub_rates as r on r.rateID = rf.rateID and r.[status] <> 'D'
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID
			inner join dbo.sites s on s.siteID = rs.siteID
			where s.orgid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			and sub.subscriptionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.s#">
			and NOT EXISTS (select rateID from ##tmpCurrRates where rateID = r.rateID)
			order by rateName;

			IF OBJECT_ID('tempdb..##tmpCurrRates') IS NOT NULL 
				DROP TABLE ##tmpCurrRates;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getFields_substatus" access="public" returnType="struct">
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select statusID, statusName
			from dbo.sub_statuses
			ORDER BY statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_subpaystatus" access="public" returnType="struct">
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select statusID, statusName
			from dbo.sub_paymentStatuses
			ORDER BY statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_subfrequency" access="public" returnType="struct">
		<cfargument name="t" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = (select siteID from sub_Types WHERE typeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.t#">);
			select sf.frequencyID, sf.frequencyName
			from dbo.sub_frequencies sf 
			where sf.siteID = @siteID
			and status <> 'D'
			order by  sf.frequencyName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_webtypes" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fieldCode, fieldLabel, displayTypeCode, dataTypeCode
			from dbo.fn_ams_getConditionFields_websitetypes(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">)
			order by sorting;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_prefix" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qryFields = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_states" access="public" returnType="struct">
		
		<cfset var local = structNew()>

		<cfset local.qryFields = application.objCommon.getStates()>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_countries" access="public" returnType="struct">
		
		<cfset var local = structNew()>

		<cfset local.qryFields = application.objCommon.getCountries()>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_custfieldvalues" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="columnID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qrydataTypeCode" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ddt.dataTypeCode
			from dbo.ams_memberDataColumnDataTypes as ddt
			inner join dbo.ams_memberDataColumns as c on c.dataTypeID = ddt.dataTypeID
			where c.orgid = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
			AND c.columnID = <cfqueryparam value="#arguments.columnID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cv.valueID, 
				<cfswitch expression="#local.qrydataTypeCode.dataTypeCode#">
				<cfcase value="STRING">cv.columnValueString</cfcase>
				<cfcase value="DECIMAL2">cv.columnValueDecimal2</cfcase>
				<cfcase value="INTEGER">cv.columnValueInteger</cfcase>
				<cfcase value="DATE">convert(varchar(10),cv.columnValueDate,101)</cfcase>
				<cfcase value="BIT">cv.columnValueBit</cfcase>
				<cfdefaultcase>''</cfdefaultcase>
				</cfswitch> as [value]
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on c.displayTypeID = dt.displayTypeID and dt.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
			inner join dbo.ams_memberDataColumnDataTypes as ddt on c.dataTypeID = ddt.dataTypeID and ddt.dataTypeCode in ('STRING','DECIMAL2','INTEGER','DATE','BIT')
			inner join dbo.ams_memberDataColumnValues as cv on c.columnID = cv.columnID 
			where c.orgid = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
			AND c.columnID = <cfqueryparam value="#arguments.columnID#" cfsqltype="CF_SQL_INTEGER">
			order by 2;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_districtValues" access="public" returnType="struct">
		<cfargument name="dt" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select valueID, vendorValue as [value], valid_from, valid_to
			from dbo.ams_memberDistrictValues
			where districtTypeID = <cfqueryparam value="#arguments.dt#" cfsqltype="CF_SQL_INTEGER">
			order by vendorValue;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_resourceTypeCategories" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryCategory" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">, 
				@ResourceType varchar(50) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.resourceType#">,
				@ResourceTypeID int;
			
			DECLARE @tmpSiteResources TABLE (siteID int, siteName varchar(60), siteResourceID int);
			DECLARE @tmpCategoryTreee TABLE (siteID int, siteName varchar(60), categoryTreeID int);

			SELECT @ResourceTypeID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = @ResourceType;

			INSERT INTO @tmpSiteResources (siteID, siteName, siteResourceID)
			select s.siteID, s.siteName, sr.siteResourceID 
			from dbo.sites as s
			INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
				and sr.resourceTypeID = @ResourceTypeID
				and sr.siteResourceStatusID = 1
			WHERE s.orgID = @orgID;

			INSERT INTO @tmpCategoryTreee (siteID, siteName, categoryTreeID)
			select tmp.siteID, tmp.siteName, isNull(ct.categoryTreeID, 0)
			from @tmpSiteResources as tmp
			inner join dbo.cms_categoryTrees as ct on ct.controllingSiteResourceID = tmp.siteResourceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID
				and sr.siteID = tmp.siteID
				and sr.siteResourceStatusID = 1;

			select tmp.siteID, tmp.siteName, categoryID, categoryName, categoryDesc 
			from dbo.cms_categories c
			inner join @tmpCategoryTreee as tmp on c.categoryTreeID = tmp.categoryTreeID
			where c.isActive = 1 
			and c.parentCategoryID is NULL
			order by siteName, categoryName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryCategory)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_resourceTypeSubCategories" access="public" returnType="struct">
		<cfargument name="categoryID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objCategories = createObject("component","model.system.platform.category")>
		<cfset local.qryFields = local.objCategories.getSubCategories(categoryID=arguments.categoryID)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_event_calendars" access="public" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qryCalendars = CreateObject("component","model.events.calendar").getCalendars(siteID=arguments.mcproxy_siteID)>

		<!--- query to array of structs --->
		<cfset local.aTmp = arrayNew(1)>
		<cfloop query="local.qryCalendars">
			<cfset local.stTmp = structNew()> 
			<cfset local.stTmp["calendarid"] = local.qryCalendars.calendarid> 
			<cfset local.stTmp["calendarname"] = local.qryCalendars.calendarname> 
			<cfset arrayAppend(local.aTmp,local.stTmp)> 
		</cfloop>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryCalendars = local.aTmp>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_event_categories" access="public" returnType="struct">
		<cfargument name="calendarID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryCategories = CreateObject("component","model.admin.events.event").getCategoriesForCalendar(calID=arguments.calendarID, includeAll=false, returnJSON=false)>

		<!--- query to array of structs --->
		<cfset local.aTmp = arrayNew(1)>
		<cfloop query="local.qryCategories">
			<cfset local.stTmp = structNew()> 
			<cfset local.stTmp["categoryid"] = local.qryCategories.categoryID> 
			<cfset local.stTmp["category"] = local.qryCategories.category> 
			<cfset arrayAppend(local.aTmp,local.stTmp)> 
		</cfloop>

		<cfset local.returnStruct = {
			"success":true,
			"qrycategories":local.aTmp
		}>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_event_roles" access="public" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.controllingSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.qryEventRoles = CreateObject("component","model.admin.events.event").getEventRoles(siteResourceID=local.controllingSiteResourceID)>

		<!--- query to array of structs --->
		<cfset local.aTmp = arrayNew(1)>
		<cfloop query="local.qryEventRoles">
			<cfset local.stTmp = structNew()> 
			<cfset local.stTmp["categoryid"] = local.qryEventRoles.categoryID> 
			<cfset local.stTmp["rolename"] = local.qryEventRoles.categoryName> 
			<cfset arrayAppend(local.aTmp,local.stTmp)> 
		</cfloop>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryEventRoles = local.aTmp>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_swCreditAuthorities" access="public" returnType="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @sponsorID int;

			select @sponsorID = sponsorID 
			FROM dbo.tblCreditSponsors 
			WHERE orgCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.mcproxy_siteCode#">;

			SELECT csa.CSALinkID, ca.jurisdiction + ' - ' + ca.authorityName as optiontext
			FROM dbo.tblCreditSponsorsAndAuthorities as csa
			INNER JOIN dbo.tblCreditAuthorities as ca on ca.authorityID = csa.authorityID
			WHERE csa.sponsorID = @sponsorID
			order by ca.jurisdiction, ca.authorityName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.arrOpts = convertQueryToArrayForDropdowns(qryData=local.qryAuthorities)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_refStatuses" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryStatuses" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT s.siteID, s.siteName, st.clientReferralStatusID AS valueid, st.statusName AS [value]
			FROM dbo.sites as s
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = s.siteID 
			INNER JOIN dbo.ref_referrals as r on r.applicationInstanceID = ai.applicationInstanceID
			INNER JOIN dbo.ref_clientReferralStatus as st on st.referralID = r.referralID
			WHERE s.orgID = @orgID
			ORDER BY s.siteName, st.statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryStatuses)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_refPanels" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryPanels" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @tmpReferral TABLE (siteID int, siteName varchar(60), referralID int);

			INSERT INTO @tmpReferral (siteID, siteName, referralID)
			SELECT s.siteID, s.siteName, r.referralID
			FROM dbo.sites as s
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = s.siteID 
			INNER JOIN dbo.ref_referrals as r on r.applicationInstanceID = ai.applicationInstanceID
			WHERE s.orgID = @orgID;

			WITH Panels AS (
				SELECT referralID, panelID, name, panelParentID,
					CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					CAST(name as varchar(max)) as thePathExpanded
				FROM (
					SELECT tmp.referralID, rp.panelID, rp.name, rp.panelParentID, ROW_NUMBER() OVER (ORDER BY tmp.siteName, rp.name) AS theRow
					FROM dbo.ref_panels as rp
					INNER JOIN @tmpReferral as tmp ON rp.referralID = tmp.referralID
					WHERE rp.panelParentID IS NULL
				) AS x
					UNION ALL
				SELECT referralID, panelID, name, panelParentID,
					thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					thePathExpanded + ' \ ' + name as thePathExpanded
				FROM (
					SELECT tmp.referralID, p.panelID, p.name, p.panelParentID, pan.thePath, pan.thePathExpanded, ROW_NUMBER() OVER (ORDER BY tmp.siteName, p.name) AS theRow
					FROM dbo.ref_panels AS p
					INNER JOIN Panels AS pan ON p.panelParentID = pan.panelID
					INNER JOIN @tmpReferral as tmp ON p.referralID = tmp.referralID
				) AS y
			)
			SELECT tmp.siteID, tmp.siteName, p.panelID as valueid, p.thePathExpanded as [value]
			FROM Panels as p
			INNER JOIN @tmpReferral as tmp on tmp.referralID = p.referralID
			ORDER BY tmp.siteName, p.thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryPanels)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_refCustomFields" access="public" returnType="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageID int;
			DECLARE @orgID INT = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @tmpSRIDTable TABLE (siteID int, siteName varchar(60), controllingSiteResourceID int);
			SELECT @usageID = dbo.fn_cf_getUsageID('ClientReferrals','ClientReferrals', NULL);

			INSERT INTO @tmpSRIDTable (siteID, siteName, controllingSiteResourceID)
			SELECT s.siteID, s.siteName, ai.siteResourceID
			FROM dbo.sites as s
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = s.siteID 
			INNER JOIN dbo.ref_referrals as r on r.applicationInstanceID = ai.applicationInstanceID
			WHERE s.orgID = @orgID;

			WITH FieldGroupings AS (
				SELECT cf.fieldGroupingID, cf.fieldGroupingOrder
				FROM dbo.cf_fieldsGrouping cf
				INNER JOIN @tmpSRIDTable as tmp ON cf.fieldControllingSiteResourceID = tmp.controllingSiteResourceID
				WHERE cf.fieldUsageID = @usageID
				AND ISNULL(cf.fieldDetailID,0) = 0
					UNION ALL
				SELECT 0, 0
			)
			SELECT tmp.siteID, tmp.siteName, f.fieldID as valueid, f.fieldText as [value]
			FROM FieldGroupings AS fg
			LEFT OUTER JOIN dbo.cf_fields AS f 
				INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
				INNER JOIN @tmpSRIDTable as tmp ON f.controllingSiteResourceID = tmp.controllingSiteResourceID
					AND f.isActive = 1
				ON fg.fieldGroupingID = isnull(f.fieldGroupingID,0)
			WHERE f.usageID = @usageID
			AND ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
			AND ft.supportAmt = 0
			AND EXISTS (SELECT 1 FROM dbo.cf_fieldValues WHERE fieldID = f.fieldID)
			ORDER BY tmp.siteName, fg.fieldGroupingOrder, f.fieldOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_refFeeDiscrepancyStatuses" access="public" returnType="struct">
		<cfset var local = structNew()>

		<cfset local.qryStatuses = CreateObject("component","model.admin.referrals.referrals").getFeeDiscrepancyStatuses()>

		<cfquery name="local.qryFields" dbtype="query">
			SELECT feeDiscrepancyStatusID AS valueid, statusName AS [value]
			FROM [local].qryStatuses
			ORDER BY sortOrder
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResourceFieldValues" access="public" output="false" returntype="struct">
		<cfargument name="fieldID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfset local.qryFieldValues = CreateObject("component","model.admin.common.modules.customFields.customFields").getFieldOptions(fieldID=arguments.fieldID)>

		<cfquery name="local.returnStruct.qryFieldValues" dbtype="query" returntype="array">
			SELECT valueID, fieldValue
			FROM [local].qryFieldValues
			ORDER BY valueOrder
		</cfquery>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSendGridSubUsers" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="true">

		<cfset var arrSubUsers = []>

		<cfquery name="arrSubUsers" datasource="#application.dsn.platformMail.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT u.subuserID AS subuserid, u.firstname, u.lastname
			FROM dbo.sendgrid_subusers AS u
			INNER JOIN dbo.sendgrid_subuserStatuses AS s ON s.subuserStatusID = u.statusID
			WHERE u.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			AND s.status = 'Active'
			ORDER BY u.firstname, u.lastname;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "arrsubusers":arrSubUsers }>
	</cffunction>

	<cffunction name="getSendGridSuppressionListTypes" access="public" output="false" returntype="struct">
		<cfset var arrSuppressionListTypes = []>

		<cfquery name="arrSuppressionListTypes" datasource="#application.dsn.platformMail.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT typeID as typeid, typeCode as typecode, typeName as typename, typeDescription as typedescription
			FROM dbo.sendgrid_suppressionListTypes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "arrsuppressionlisttypes":arrSuppressionListTypes }>
	</cffunction>

	<cffunction name="getOrgAddressTags" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var arrAddressTags = []>

		<cfquery name="arrAddressTags" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT addressTagTypeID as addresstagtypeid, addressTagType as addresstagtype
			FROM dbo.ams_memberAddressTagTypes
			WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "arraddresstags":arrAddressTags }>
	</cffunction>

	<cffunction name="getOrgEmailTags" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var arrEmailTags = []>

		<cfquery name="arrEmailTags" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT emailTagTypeID as emailtagtypeid, emailTagType as emailtagtype
			FROM dbo.ams_memberEmailTagTypes
			WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "arremailtags":arrEmailTags }>
	</cffunction>

	<cffunction name="getFields_cp_programs" access="public" output="false" returntype="struct" hint="returns list of programs">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.qryPrograms = CreateObject('component','model.admin.contributions.contributions').getAllSitePrograms(siteID=arguments.mcproxy_siteID)>
		
		<!--- query to array of structs --->
		<cfset local.aTmp = arrayNew(1)>
		<cfloop query="local.qryPrograms">
			<cfset local.stTmp = structNew()>
			<cfset local.stTmp['programid'] = local.qryPrograms.programid>
			<cfset local.stTmp['programname'] = local.qryPrograms.applicationInstanceName & ' - ' & local.qryPrograms.programName>
			<cfif local.qryPrograms.siteResourceStatusDesc eq 'Inactive'>
				<cfset local.stTmp['programname'] = local.stTmp['programname'] & " (Inactive)">
			</cfif>
			<cfset arrayAppend(local.aTmp,local.stTmp)>
		</cfloop>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryPrograms = local.aTmp>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_rates" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['arrrates'] = arrayNew(1)>

		<cfset local.qryRates = CreateObject('component','model.admin.contributions.contributions').getProgramRates(siteID=arguments.mcproxy_siteID,programID=arguments.programID)>

		<cfloop query="local.qryRates">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['rateid'] = local.qryRates.rateID>
			<cfset local.tmp['ratename'] = local.qryRates.rateName>
			<cfset arrayAppend(local.returnStruct['arrrates'], local.tmp)>
		</cfloop>

		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_statuses" access="public" output="false" returntype="struct">
		<cfset var local = structNew()>

		<cfset local.qryCPStatus = CreateObject('component','model.admin.contributions.contributions').getCPStatuses()>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryCPStatus = convertQueryToArrayForDropdowns(qryData=local.qryCPStatus)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_frequencies" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryFrequency" datasource="#application.dsn.membercentral.dsn#">
			 select distinct cf.frequencyID, cf.frequency
			 from dbo.cp_frequencies as cf
			 <cfif arguments.programid gt 0>
			 	inner join dbo.cp_programFrequencies as cpf on cpf.frequencyID = cf.frequencyID
					and cpf.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			</cfif>
			where cf.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			order by cf.frequency
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFrequency = convertQueryToArrayForDropdowns(qryData=local.qryFrequency)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_distributions" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.qryDistributions = CreateObject('component','model.admin.contributions.contributions').getProgramDistributions(programID=arguments.programID)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryDistributions = convertQueryToArrayForDropdowns(qryData=local.qryDistributions)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_monetaryProgramCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.qryFields = CreateObject('component','model.admin.contributions.contributions').getMonetaryProgramCustomFields(programID=arguments.programID)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFields_cp_nonMonetaryProgramCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.qryFields = CreateObject('component','model.admin.contributions.contributions').getNonMonetaryProgramCustomFields(siteID=arguments.mcproxy_siteID, programID=arguments.programID)>
		
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.qryFields = convertQueryToArrayForDropdowns(qryData=local.qryFields)>

		<cfreturn local.returnStruct>
	</cffunction>

	<!--- export/import --->
	<cffunction name="exportStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "GroupAssignmentRules.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_exportVirtualGroupRulesStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />
		
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		
		<cftry>
			<cfset local.GroupAssignmentRulesImportStruct = application.mcCacheManager.sessionGetValue(keyname='GroupAssignmentRulesImportStruct', defaultValue={})>
			<cfif isStruct(local.GroupAssignmentRulesImportStruct) and structKeyExists(local.GroupAssignmentRulesImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.GroupAssignmentRulesImportStruct[arguments.reportuid].folderPath & "/GroupAssignmentRulesReport.html">
				<cfset local.returnStruct.reportOutput = "">

				<cfif fileExists("#local.reportFileName#")>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepareVGCImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>

		<cfsetting requesttimeout="500">

		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/GroupAssignmentRules.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/GroupAssignmentRules.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>

		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/GroupAssignmentRules.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name = 'sync_vgc_conditions.bcp' 
					or name = 'sync_vgc_rules.bcp' 
					or name = 'sync_vgc_allrulegroups.bcp' 
					or name = 'sync_vgc_allfields.bcp'
					or name = 'sync_vgc_allvalueids.bcp' 
					or name = 'sync_vgc_supporting.bcp'
				</cfquery>
				<cfif local.qryFiles.recordcount neq 6>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain 6.">
				<cfelseif local.qryFilesCheck.theCount neq 6>
					<cfthrow message="One or more required files in the backup file is missing.">
				</cfif>

				<cfzip file="#local.strImportFile.strFolder.folderPath#/GroupAssignmentRules.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/GroupAssignmentRules.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>

		<!--- prepare import --->
		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Group Assignment Rules Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			<cfset local.GroupAssignmentRulesImportStruct = application.mcCacheManager.sessionGetValue(keyname='GroupAssignmentRulesImportStruct', defaultValue={})>
			<cfset local.GroupAssignmentRulesImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue(keyname='GroupAssignmentRulesImportStruct', value=local.GroupAssignmentRulesImportStruct)>

			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>

			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isVGCImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.ams_prepareVirtualGroupRulesImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showVGCImportCompareResults(orgID=arguments.paramStruct.orgID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/GroupAssignmentRulesReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showVGCImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewConditions = XMLSearch(arguments.strResult.importResultXML,"/import/newconditions/condition")>
			<cfset local.strImportResult.arrUpdateConditions = XMLSearch(arguments.strResult.importResultXML,"/import/updateconditions/condition")>
			<cfset local.strImportResult.arrIgnoreConditions = XMLSearch(arguments.strResult.importResultXML,"/import/ignoreconditions/condition")>
			<cfset local.strImportResult.arrRemoveConditions = XMLSearch(arguments.strResult.importResultXML,"/import/removeconditions/condition")>
			<cfset local.strImportResult.arrNewRules = XMLSearch(arguments.strResult.importResultXML,"/import/newrules/rule")>
			<cfset local.strImportResult.arrUpdateRules = XMLSearch(arguments.strResult.importResultXML,"/import/updaterules/rule")>
			<cfset local.strImportResult.arrIgnoreRules = XMLSearch(arguments.strResult.importResultXML,"/import/ignorerules/rule")>
			<cfset local.strImportResult.arrRemoveRules = XMLSearch(arguments.strResult.importResultXML,"/import/removerules/rule")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfset local.importReport = generateVGCImportResultsReport(orgID=arguments.orgID, threadID=arguments.threadID, strImportResult=local.strImportResult)>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.errorMsg = XMLSearch(arguments.strResult.importResultXML,'string(/import/errors/error/@msg)')>
			<cfset local.errorCode = XMLSearch(arguments.strResult.importResultXML,'string(/import/errors/error/@errorcode)')>
			<cfset local.errorReport = generateVGCImportErrorReport(orgID=arguments.orgID, errorcode=local.errorCode, errorMsg=local.errorMsg)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Group Assignment Rules Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelVGCImport(orgID=arguments.orgID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">Group Assignment Rules Import No Action Needed</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">Group Assignment Rules Import Issue Report</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateVGCImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateRules)>
			<cfquery name="local.qryImportFileUpdateRules" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct ruleName, ruleXML, isActive, uid
				from dbo.sync_vgc_rules
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and uid in (
					'#arguments.strImportResult.arrUpdateRules[1].xmlAttributes.uid#'
					<cfloop array="#arguments.strImportResult.arrUpdateRules#" index="local.thisRule">
						,'#local.thisRule.xmlAttributes.uid#'
					</cfloop>);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateRules" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select r.ruleID, r.ruleName, rv.ruleXML, r.isActive, r.uid
				from dbo.ams_virtualGroupRules as r
				inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
					and rv.ruleVersionID = r.activeVersionID
				where r.orgID = @orgID
				and r.ruleTypeID = 1
				and r.uid in (#listQualify(valueList(local.qryImportFileUpdateRules.uid), "'")#)
				order by r.ruleName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateRuleGroups" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select rg.groupID, rg.useGroupID, rg.thePathExpanded, r.uid as ruleUID, rg.uid as groupUID
				from dbo.sync_vgc_allrulegroups as rg
				inner join dbo.sync_vgc_rules as r on r.orgID = @orgID and r.groupID = rg.groupID
					and r.uid in (#listQualify(valueList(local.qryImportFileUpdateRules.uid), "'")#)
				where rg.orgID = @orgID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateRuleGroups" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select rg.ruleID, rg.groupID, g.groupPathExpanded, g.uid as groupUID
				from dbo.ams_virtualGroupRuleGroups as rg
				inner join dbo.ams_virtualGroupRules as r on r.ruleID = rg.ruleID
				inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = rg.groupID
				where r.orgID = @orgID
				and r.ruleID in (#valueList(local.qryOrgUpdateRules.ruleID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfif arrayLen(arguments.strImportResult.arrUpdateConditions)>
			<cfquery name="local.qryImportFileUpdateConditions" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select distinct c.conditionID, c.uid, c.datatypeid, c.displaytypeid, c.expressionid, c.dateexpressionid,
					c.[datepart], c.verbose, f.useFieldCode
				from dbo.sync_vgc_conditions as c
				inner join dbo.sync_vgc_allfields as f on f.orgID = @orgID and f.fieldCode = c.fieldCode
				where c.orgID = @orgID
				and uid in (
					'#arguments.strImportResult.arrUpdateConditions[1].xmlAttributes.uid#'
					<cfloop array="#arguments.strImportResult.arrUpdateConditions#" index="local.thisCond">
						,'#local.thisCond.xmlAttributes.uid#'
					</cfloop>);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateConditions" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select conditionID, uid, datatypeid, displaytypeid, expressionid, isnull(dateexpressionid,0) as dateexpressionid,
					[datepart], verbose, fieldCode
				from dbo.ams_virtualGroupConditions
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and conditionTypeID = 1
				and uid in (#listQualify(valueList(local.qryImportFileUpdateConditions.uid), "'")#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryConditionAFIDsChanged" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select vgc.uid, k.conditionKey, isnull(cv.afid,0) as afid
				from dbo.ams_virtualGroupConditions as vgc
				inner join dbo.ams_virtualGroupConditionValues as cv on vgc.conditionTypeID = 1 and cv.conditionID = vgc.conditionID
				inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
				where vgc.orgID = @orgID
				and vgc.conditionID in (#valueList(local.qryOrgUpdateConditions.conditionID)#)
					except 
				select c.uid, sK.itemType as conditionKey, c.afid
				from dataTransfer.dbo.sync_vgc_conditions as c 
				inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID 
					and sK.cat = 'ck' 
					and sK.itemID = c.conditionkeyID
				where c.orgID = @orgID 
				and c.conditionID in (#valueList(local.qryImportFileUpdateConditions.conditionID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateVGCImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="errorCode" type="string" required="yes">
		<cfargument name="errorMsg" type="string" required="yes">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="USEFIELDCODENULL">
				<cfquery name="local.qryInvalidFields" datasource="#application.dsn.datatransfer.dsn#">
					select fieldCode, fieldLabel
					from dbo.sync_vgc_allfields 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and useFieldcode is null
				</cfquery>
				<cfquery name="local.qryConditionsUsingFields" datasource="#application.dsn.datatransfer.dsn#">
					select distinct fieldCode, verbose
					from dbo.sync_vgc_conditions
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and fieldCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#valueList(local.qryInvalidFields.fieldCode)#">)
				</cfquery>
			</cfcase>
			<cfcase value="USEVALUENULL">
				<cfquery name="local.qryConditionsHavingInvalidValues" datasource="#application.dsn.datatransfer.dsn#">
					select distinct v.columnValue, c.verbose
					from dbo.sync_vgc_allvalueids as v
					inner join dbo.sync_vgc_conditions as c on c.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
						and c.conditionID = v.conditionID
					where v.useValue is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDAFNULL">
				<cfquery name="local.qryInvalidAF" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'af'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDMENULL">
				<cfquery name="local.qryInvalidEmailTypes" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'me'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDMETNULL">
				<cfquery name="local.qryInvalidEmailTagTypes" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'met'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDACCTMPNULL">
				<cfquery name="local.qryInvalidACCTMP" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'acctmp'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDACCTIPNULL">
				<cfquery name="local.qryInvalidACCTIP" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'acctip'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDACCTGLNULL">
				<cfquery name="local.qryInvalidACCTGL" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'acctgl'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDACCTCCTYPENULL">
				<cfquery name="local.qryInvalidACCTCCTYPE" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'acctcctype'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDRTNULL">
				<cfquery name="local.qryInvalidRecordTypes" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'rt'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDRRTNULL">
				<cfquery name="local.qryInvalidRecordRelTypes" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'rrt'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDMHNULL">
				<cfquery name="local.qryInvalidMHCats" datasource="#application.dsn.datatransfer.dsn#">
					select itemType2
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'mh'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDRTRTNULL">
				<cfquery name="local.qryInvalidRecordRelRoles" datasource="#application.dsn.datatransfer.dsn#">
					set nocount on;

					declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select sRT.itemType as childRT, sRT2.itemType as masterRT, sRRT.itemType as relType
					from dbo.sync_vgc_supporting as s
					inner join dbo.sync_vgc_supporting as sRT on sRT.orgID = @orgID and sRT.cat = 'rt' and sRT.itemID = s.itemID4
					inner join dbo.sync_vgc_supporting as sRT2 on sRT2.orgID = @orgID and sRT2.cat = 'rt' and sRT2.itemID = s.itemID3
					inner join dbo.sync_vgc_supporting as sRRT on sRRT.orgID = @orgID and sRRT.cat = 'rrt' and sRRT.itemID = s.itemID2
					where s.useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEVALUECPCFVALNULL">
				<cfquery name="local.qryInvalidCustomFieldValues" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select c.conditionvalue
					from dataTransfer.dbo.sync_vgc_conditions as c
					inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
						and sK.cat = 'ck'
						and c.conditionkeyID = sK.itemID
						and sK.itemType = 'programNonMonetaryCustomFieldValue'
					where c.orgID = @orgID
					and nullif(c.conditionvalue,'') is not null
					and c.useValue is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDSGSUBUSERNULL">
				<cfquery name="local.qryInvalidSendGridSubUsers" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'sgsubuser'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDCLMODEIDNULL">
				<cfquery name="local.qryInvalidConsentListMode" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'clmodeid'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDCLTYPEIDNULL">
				<cfquery name="local.qryInvalidConsentListTypes" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'cltypeid'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDCLIDNULL">
				<cfquery name="local.qryInvalidConsentLists" datasource="#application.dsn.datatransfer.dsn#">
					select itemType
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'clid'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDREFSTATUSNULL">
				<cfquery name="local.qryInvalidRefStatuses" datasource="#application.dsn.datatransfer.dsn#">
					select itemType2
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'refstatus'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDREFPANELNULL">
				<cfquery name="local.qryInvalidPanels" datasource="#application.dsn.datatransfer.dsn#">
					select itemType2
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'refpanel'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEIDREFFDSTATNULL">
				<cfquery name="local.qryInvalidFeeDiscrepancyStatuses" datasource="#application.dsn.datatransfer.dsn#">
					select itemType2
					from dbo.sync_vgc_supporting 
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and cat = 'reffdstat'
					and useID is null
				</cfquery>
			</cfcase>
			<cfcase value="USEVALUEREFCFVALNULL">
				<cfquery name="local.qryInvalidCustomFieldValues" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select c.conditionvalue
					from dataTransfer.dbo.sync_vgc_conditions as c
					inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
						and sK.cat = 'ck'
						and c.conditionkeyID = sK.itemID
						and sK.itemType = 'clientReferralsCustomFieldValue'
					where c.orgID = @orgID
					and nullif(c.conditionvalue,'') is not null
					and c.useValue is null
				</cfquery>
			</cfcase>
			<cfcase value="INVALIDRULEGRP">
				<cfquery name="local.qryInvalidRuleGroups" datasource="#application.dsn.memberCentral.dsn#">
					set nocount on;

					declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select rg.groupID, rg.thePathExpanded, rg.groupCode
					from dataTransfer.dbo.sync_vgc_allrulegroups as rg
					left outer join dbo.ams_groups as g on g.orgID = @orgID 
						and g.uid = rg.uid 
						and g.isProtected = 0 
						and g.isSystemGroup = 0
					where rg.orgID = @orgID
					and g.groupID is null
				</cfquery>
				<cfquery name="local.qryRulesUsingInvalidGroups" datasource="#application.dsn.datatransfer.dsn#">
					select distinct ruleName, groupID
					from dbo.sync_vgc_rules
					where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					and groupID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryInvalidRuleGroups.groupID)#">)
				</cfquery>
			</cfcase>
			<cfcase value="UNMATCHEDUID">
				<cfquery name="local.qryInvalidConditionValues" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					DECLARE @tmpConditionKeys TABLE (conditionKey varchar(50), conditionKeyDesc varchar(100), shortDesc varchar(30));

					INSERT INTO @tmpConditionKeys (conditionKey, conditionKeyDesc, shortDesc)
					VALUES ('subSubType', 'Subscription Type', 'types'), 
							('subSubscription', 'Subscription', 'subscriptions'),
							('subRate', 'Subscription Rate', 'rates'),
							('subFrequency', 'Subscription Frequency', 'frequencies'),
							('programList', 'Contribution Program', 'programs'),
							('programRate', 'Contribution Program Rate', 'rates'),
							('programDistribution', 'Contribution Program Distribution', 'distributions'),
							('programMonetaryCustomField', 'Contribution Program Monetary Custom Field', 'fields'),
							('programNonMonetaryCustomField', 'Contribution Program Non-Monetary Custom Field', 'fields'),
							('clientReferralsCustomFieldID', 'Client Referrals Custom Field', 'fields');

					SELECT DISTINCT tmp.conditionKeyDesc + ' UID: "' + c.valueUID + '" does not match any existing ' + tmp.shortDesc + '.' as issueDesc
					FROM dataTransfer.dbo.sync_vgc_conditions as c
					INNER JOIN dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID 
					INNER JOIN @tmpConditionKeys as tmp on tmp.conditionKey = sK.itemType
					WHERE c.orgID = @orgID
					AND NULLIF (c.valueUID,'') IS NOT NULL
					AND c.useValue is null;
				</cfquery>
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cancelVGCImport" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_vgc_conditions WHERE orgID = @orgID;
					DELETE FROM dbo.sync_vgc_rules WHERE orgID = @orgID;
					DELETE FROM dbo.sync_vgc_allrulegroups WHERE orgID = @orgID;
					DELETE FROM dbo.sync_vgc_allfields WHERE orgID = @orgID;
					DELETE FROM dbo.sync_vgc_allvalueids WHERE orgID = @orgID;
					DELETE FROM dbo.sync_vgc_supporting WHERE orgID = @orgID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="doImportVGC" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="500">

		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.GroupAssignmentRulesImportStruct = application.mcCacheManager.sessionGetValue(keyname='GroupAssignmentRulesImportStruct', defaultValue={})>
		<cfif NOT structKeyExists(local.GroupAssignmentRulesImportStruct,local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Group Assignment Rules. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="ams_importVirtualGroupRules" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Group Assignment Rules file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>
			<cfset StructDelete(local.GroupAssignmentRulesImportStruct, local.threadID)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='GroupAssignmentRulesImportStruct', value=local.GroupAssignmentRulesImportStruct)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="createValueXML" access="private" output="false" returntype="xml">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.fieldCode = arguments.event.getValue('fieldCode','')>
		<cfset local.fieldCodeDataType = arguments.event.getValue('fieldCodeDataType','')>
		<cfset local.expression = arguments.event.getValue('expression','')>
		<cfset local.dateOperatorAnniv = arguments.event.getValue('dateOperatorAnniv','')>
		<cfset local.conditionValue = arguments.event.getValue('conditionValue','')>
		<cfset local.pastOrFutureValue = arguments.event.getValue('pastOrFutureValue','')>
		<cfset local.canHaveMultipleValues = arguments.event.getValue('canHaveMultipleValues',0)>
		<cfset local.asNew = arguments.event.getValue('asNew',0)>
		<cfset local.rollDate = arguments.event.getValue('rollDate',0)>
		<cfset local.useRollDates = false>

		<cfsavecontent variable="local.valueXML">
			<cfoutput>
			<values>
				<cfif listFindNoCase("acct_allocSum,acct_allocSumRecog",local.fieldCode)>
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_batchDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_batchDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="linkAllocType" value="#arguments.event.getValue('s1_3','cash')#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4',''), key='revenueGL')#
					<value key="batchDateLower" value="#xmlformat(arguments.event.getValue('s1_5a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_batchDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_batchDateLower_afid','0'))#"</cfif> />
					<value key="batchDateUpper" value="#xmlformat(arguments.event.getValue('s1_5b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_batchDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_batchDateUpper_afid','0'))#"</cfif> />
					<cfif local.expression eq "between">
						<value key="valueLower" value="#xmlformat(arguments.event.getValue('conditionValueLower',''))#" />
						<value key="valueUpper" value="#xmlformat(arguments.event.getValue('conditionValueUpper',''))#" />
					<cfelseif NOT ListFindNoCase("exists,not_exists,included",local.expression)>
						<value key="value" value="#xmlformat(local.conditionValue)#" />
					</cfif>
				<cfelseif local.fieldCode eq "acct_balance">
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='acctBalMP')#
					<cfif local.expression eq "between">
						<value key="valueLower" value="#xmlformat(arguments.event.getValue('conditionValueLower',''))#" />
						<value key="valueUpper" value="#xmlformat(arguments.event.getValue('conditionValueUpper',''))#" />
					<cfelseif NOT ListFindNoCase("exists,not_exists,included",local.expression)>
						<value key="value" value="#xmlformat(local.conditionValue)#" />
					</cfif>
				<cfelseif local.fieldCode eq "acct_cc">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_acctCCExpirationLower_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCExpirationUpper_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCInvoiceDueLower_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCInvoiceDueUpper_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCLastUpdatedLower_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCLastUpdatedUpper_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCLastFailedLower_afid','0')) OR 
							val(arguments.event.getValue('roll_acctCCLastFailedUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='acctCCProf')#
					<value key="acctCCExpirationLower" value="#xmlformat(arguments.event.getValue('s1_4a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCExpirationLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCExpirationLower_afid','0'))#"</cfif> />
					<value key="acctCCExpirationUpper" value="#xmlformat(arguments.event.getValue('s1_4b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCExpirationUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCExpirationUpper_afid','0'))#"</cfif> />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5a',''), key='acctCCCardType')#
					<value key="acctCCLimitToInvDue" value="#xmlformat(arguments.event.getValue('s1_5b',''))#" />
					<cfif val(arguments.event.getValue('s1_5b','0'))>
						<value key="acctCCInvoiceDueLower" value="#xmlformat(arguments.event.getValue('s1_5c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCInvoiceDueLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCInvoiceDueLower_afid','0'))#"</cfif> />
						<value key="acctCCInvoiceDueUpper" value="#xmlformat(arguments.event.getValue('s1_5d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCInvoiceDueUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCInvoiceDueUpper_afid','0'))#"</cfif> />
					</cfif>
					<value key="acctCCLimitToSubForPAO" value="#xmlformat(arguments.event.getValue('s1_5e',''))#" />
					<value key="acctCCLimitToCPForAPQ" value="#xmlformat(arguments.event.getValue('s1_5f',''))#" />
					<value key="acctCCLastUpdatedLower" value="#xmlformat(arguments.event.getValue('s1_5g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCLastUpdatedLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCLastUpdatedLower_afid','0'))#"</cfif> />
					<value key="acctCCLastUpdatedUpper" value="#xmlformat(arguments.event.getValue('s1_5h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCLastUpdatedUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCLastUpdatedUpper_afid','0'))#"</cfif> />
					<value key="acctCCLastFailedLower" value="#xmlformat(arguments.event.getValue('s1_5i',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCLastFailedLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCLastFailedLower_afid','0'))#"</cfif> />
					<value key="acctCCLastFailedUpper" value="#xmlformat(arguments.event.getValue('s1_5j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctCCLastFailedUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctCCLastFailedUpper_afid','0'))#"</cfif> />
				<cfelseif local.fieldCode eq "acct_inv">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_acctInvCOFUpdatedLower_afid','0')) OR 
							val(arguments.event.getValue('roll_acctInvCOFUpdatedUpper_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvCOFLastFailLower_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvCOFLastFailUpper_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvCOFFailSinceLower_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvCOFFailSinceUpper_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvDueDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvDueDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvBilledDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_acctInvBilledDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='acctInvProf')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4',''), key='acctInvStatus')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5a',''), key='acctInvCOFMP')#
					<value key="acctInvCOFStatus" value="#xmlformat(arguments.event.getValue('s1_5b',''))#" />
					<value key="acctInvCOFUpdatedLower" value="#xmlformat(arguments.event.getValue('s1_5c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFUpdatedLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFUpdatedLower_afid','0'))#"</cfif> />
					<value key="acctInvCOFUpdatedUpper" value="#xmlformat(arguments.event.getValue('s1_5d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFUpdatedUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFUpdatedUpper_afid','0'))#"</cfif> />
					<value key="acctInvCOFLastFailLower" value="#xmlformat(arguments.event.getValue('s1_5e',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFLastFailLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFLastFailLower_afid','0'))#"</cfif> />
					<value key="acctInvCOFLastFailUpper" value="#xmlformat(arguments.event.getValue('s1_5f',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFLastFailUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFLastFailUpper_afid','0'))#"</cfif> />
					<value key="acctInvCOFFailSinceLower" value="#xmlformat(arguments.event.getValue('s1_5g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFFailSinceLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFFailSinceLower_afid','0'))#"</cfif> />
					<value key="acctInvCOFFailSinceUpper" value="#xmlformat(arguments.event.getValue('s1_5h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvCOFFailSinceUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvCOFFailSinceUpper_afid','0'))#"</cfif> />
					<value key="acctInvDueDateLower" value="#xmlformat(arguments.event.getValue('s1_5i',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvDueDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvDueDateLower_afid','0'))#"</cfif> />
					<value key="acctInvDueDateUpper" value="#xmlformat(arguments.event.getValue('s1_5j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvDueDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvDueDateUpper_afid','0'))#"</cfif> />
					<value key="acctInvBilledDateLower" value="#xmlformat(arguments.event.getValue('s1_5k',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvBilledDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvBilledDateLower_afid','0'))#"</cfif> />
					<value key="acctInvBilledDateUpper" value="#xmlformat(arguments.event.getValue('s1_5l',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_acctInvBilledDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_acctInvBilledDateUpper_afid','0'))#"</cfif> />
				<cfelseif local.fieldCode eq "cl_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_clDateAddedLower_afid','0')) OR 
							val(arguments.event.getValue('roll_clDateAddedUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<cfif listLen(arguments.event.getValue('s1_2a',''))>
						<cfloop list="#arguments.event.getValue('s1_2a')#" index="local.listItem">
							<cfif left(local.listItem,4) EQ 'met_'>
								<value key="emailTagTypeID" value="#xmlformat(listGetAt(local.listItem,2,'_'))#" />
							<cfelseif left(local.listItem,3) EQ 'me_'>
								<value key="emailTypeID" value="#xmlformat(listGetAt(local.listItem,2,'_'))#" />
							</cfif>
						</cfloop>
					</cfif>
					<value key="consentListModeID" value="#xmlformat(arguments.event.getValue('s1_2b',''))#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2c',''), key='consentListTypeID')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2d',''), key='consentListID')#
					<value key="clDateAddedLower" value="#xmlformat(arguments.event.getValue('s1_2e',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_clDateAddedLower_afid','0'))> afid="#val(arguments.event.getValue('roll_clDateAddedLower_afid','0'))#"</cfif> />
					<value key="clDateAddedUpper" value="#xmlformat(arguments.event.getValue('s1_2f',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_clDateAddedUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_clDateAddedUpper_afid','0'))#"</cfif> />
				<cfelseif local.fieldCode eq "ev_entry">
					<cfif arguments.event.getValue('s1_2i','') eq "event">
						<value key="evCalendarID" value="" />
						<value key="evCategoryID" value="" />
						<value key="evEventType" value="" />
						<value key="evEventTitle" value="" />
						<value key="evReportCode" value="" />
						<value key="evStartLower" value="" />
						<value key="evStartUpper" value="" />
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_2j',''), key='evEventID')#
					<cfelse>
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND (
								val(arguments.event.getValue('roll_evStartLower_afid','0')) OR 
								val(arguments.event.getValue('roll_evStartUpper_afid','0'))
							)>
							<cfset local.useRollDates = true>
						</cfif>
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_2a',''), key='evCalendarID')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_2b',''), key='evCategoryID')#
						<value key="evEventType" value="#xmlformat(arguments.event.getValue('s1_2d',''))#" />
						<value key="evEventTitle" value="#xmlformat(arguments.event.getValue('s1_2e',''))#" />
						<value key="evReportCode" value="#xmlformat(arguments.event.getValue('s1_2f',''))#" />
						<value key="evStartLower" value="#xmlformat(arguments.event.getValue('s1_2g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_evStartLower_afid','0'))> afid="#val(arguments.event.getValue('roll_evStartLower_afid','0'))#"</cfif> />
						<value key="evStartUpper" value="#xmlformat(arguments.event.getValue('s1_2h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_evStartUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_evStartUpper_afid','0'))#"</cfif> />
						<value key="evEventID" value="" />
					</cfif>
					<value key="evFilterOption" value="#xmlformat(arguments.event.getValue('s1_2i',''))#" />
					<value key="evSiteID" value="#xmlformat(arguments.event.getValue('s1_2k',0))#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2c',''), key='evRoleID')#
				<cfelseif local.fieldCodeDataType eq "DATE" and local.expression eq "isanniversary">
					<cfif local.dateOperatorAnniv eq "between">
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND (
								val(arguments.event.getValue('roll_valueLower_afid','0')) OR 
								val(arguments.event.getValue('roll_valueUpper_afid','0'))
							)>
							<cfset local.useRollDates = true>
						</cfif>
						<value key="valueLower" value="#xmlformat(arguments.event.getValue('conditionValueLower',''))#" <cfif local.useRollDates and val(arguments.event.getValue('roll_valueLower_afid','0'))> afid="#val(arguments.event.getValue('roll_valueLower_afid','0'))#"</cfif> />
						<value key="valueUpper" value="#xmlformat(arguments.event.getValue('conditionValueUpper',''))#" <cfif local.useRollDates and val(arguments.event.getValue('roll_valueUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_valueUpper_afid','0'))#"</cfif> />
					<cfelse>
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND val(arguments.event.getValue('roll_value_afid','0'))>
							<cfset local.useRollDates = true>
						</cfif>
						<value key="value" value="#xmlformat(local.conditionValue)#" <cfif local.useRollDates and val(arguments.event.getValue('roll_value_afid','0'))> afid="#val(arguments.event.getValue('roll_value_afid','0'))#"</cfif> />
					</cfif>
				<cfelseif local.fieldCode eq "rt_role">
					<value key="recordType" value="#arguments.event.getValue('s1_2',0)#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='role')#
				<cfelseif local.fieldCode eq "sub_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_subStartDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_subStartDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_subEndDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_subEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_subGraceDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_subGraceDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="subSubType" value="#xmlformat(arguments.event.getValue('s1_2',''))#" />

					<cfif listLen(arguments.event.getValue('s1_3','')) gt 0>
						<cfset local.strSubs = getFields_subsubs(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgID'), t=arguments.event.getValue('s1_2',''))>
						<cfif listLen(arguments.event.getValue('s1_3','')) gte arrayLen(local.strSubs.qryFields)>
							<value key="subSubscription" value="" />
						<cfelse>
							<value key="subSubscription" value="#xmlformat(ListSort(arguments.event.getValue('s1_3',''),'numeric','asc'))#" />
						</cfif>
					<cfelse>
						<value key="subSubscription" value="#xmlformat(arguments.event.getValue('s1_3',''))#" />
					</cfif>

					<cfif listLen(arguments.event.getValue('s1_4','')) gt 0>
						<cfset local.strRates = getFields_subrates(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgid'), s=arguments.event.getValue('s1_3',''))>
						<cfif listLen(arguments.event.getValue('s1_4','')) gte arrayLen(local.strRates.qryFields)>
							<value key="subRate" value="" />
						<cfelse>
							<value key="subRate" value="#xmlformat(ListSort(arguments.event.getValue('s1_4',''),'numeric','asc'))#" />
						</cfif>
					<cfelse>
						<value key="subRate" value="#xmlformat(arguments.event.getValue('s1_4',''))#" />
					</cfif>

					<cfif listLen(arguments.event.getValue('s1_5','')) gt 0>
						<cfset local.strStatus = getFields_substatus()>
						<cfif listLen(arguments.event.getValue('s1_5','')) gte arrayLen(local.strStatus.qryFields)>
							<value key="subStatus" value="" />
						<cfelse>
							<value key="subStatus" value="#xmlformat(ListSort(arguments.event.getValue('s1_5',''),'numeric','asc'))#" />
						</cfif>
					<cfelse>
						<value key="subStatus" value="#xmlformat(arguments.event.getValue('s1_5',''))#" />
					</cfif>

					<cfif listLen(arguments.event.getValue('s1_6','')) gt 0>
						<cfset local.strPayStatus = getFields_subpaystatus()>
						<cfif listLen(arguments.event.getValue('s1_6','')) gte arrayLen(local.strPayStatus.qryFields)>
							<value key="subPaymentStatus" value="" />
						<cfelse>
							<value key="subPaymentStatus" value="#xmlformat(ListSort(arguments.event.getValue('s1_6',''),'numeric','asc'))#" />
						</cfif>
					<cfelse>
						<value key="subPaymentStatus" value="#xmlformat(arguments.event.getValue('s1_6',''))#" />
					</cfif>

					<cfif listLen(arguments.event.getValue('s1_7','')) gt 0>
						<cfset local.strFreq = getFields_subfrequency(t=arguments.event.getValue('s1_2',''))>
						<cfif listLen(arguments.event.getValue('s1_7','')) gte arrayLen(local.strFreq.qryFields)>
							<value key="subFrequency" value="" />
						<cfelse>
							<value key="subFrequency" value="#xmlformat(ListSort(arguments.event.getValue('s1_7',''),'numeric','asc'))#" />
						</cfif>
					<cfelse>
						<value key="subFrequency" value="#xmlformat(arguments.event.getValue('s1_7',''))#" />
					</cfif>

					<value key="subHasCard" value="#xmlformat(arguments.event.getValue('s1_8',''))#" />

					<value key="subStartDateLower" value="#xmlformat(arguments.event.getValue('s1_9a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subStartDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_subStartDateLower_afid','0'))#"</cfif> />
					<value key="subStartDateUpper" value="#xmlformat(arguments.event.getValue('s1_9b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subStartDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_subStartDateUpper_afid','0'))#"</cfif> />
					<value key="subEndDateLower" value="#xmlformat(arguments.event.getValue('s1_9c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_subEndDateLower_afid','0'))#"</cfif> />
					<value key="subEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_9d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_subEndDateUpper_afid','0'))#"</cfif> />
					<value key="subGraceDateLower" value="#xmlformat(arguments.event.getValue('s1_9e',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subGraceDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_subGraceDateLower_afid','0'))#"</cfif> />
					<value key="subGraceDateUpper" value="#xmlformat(arguments.event.getValue('s1_9f',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_subGraceDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_subGraceDateUpper_afid','0'))#"</cfif> />
				<cfelseif local.fieldCode eq "mh_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_historyDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_historyDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_historyEndDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_historyEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_historyEnteredDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_historyEnteredDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="historyCategory" value="#arguments.event.getValue('s1_2',0)#" />
					<cfloop list="#arguments.event.getValue('s1_3','')#" index="local.listItem">
						<value key="historySubCategory" value="#xmlformat(local.listItem)#" />
					</cfloop>
					<value key="historyDateLower" value="#xmlformat(arguments.event.getValue('s1_4a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_historyDateLower_afid','0'))#"</cfif> />
					<value key="historyDateUpper" value="#xmlformat(arguments.event.getValue('s1_4b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_historyDateUpper_afid','0'))#"</cfif> />
					<value key="historyEndDateLower" value="#xmlformat(arguments.event.getValue('s1_4j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_historyEndDateLower_afid','0'))#"</cfif> />
					<value key="historyEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_4k',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_historyEndDateUpper_afid','0'))#"</cfif> />
					<value key="historyEnteredDateLower" value="#xmlformat(arguments.event.getValue('s1_4c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyEnteredDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_historyEnteredDateLower_afid','0'))#"</cfif> />
					<value key="historyEnteredDateUpper" value="#xmlformat(arguments.event.getValue('s1_4d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_historyEnteredDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_historyEnteredDateUpper_afid','0'))#"</cfif> />
					<value key="historyQuantityLower" value="#xmlformat(arguments.event.getValue('s1_4e',''))#" />
					<value key="historyQuantityUpper" value="#xmlformat(arguments.event.getValue('s1_4f',''))#" />
					<value key="historyAmountLower" value="#xmlformat(arguments.event.getValue('s1_4g',''))#" />
					<value key="historyAmountUpper" value="#xmlformat(arguments.event.getValue('s1_4h',''))#" />
					<value key="historyDescriptionContains" value="#xmlformat(arguments.event.getValue('s1_4i',''))#" />
				<cfelseif local.fieldCode eq "l_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_listJoinDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_listJoinDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_listExpireDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_listExpireDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2',''), key='listList')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='listMemberType')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4',''), key='listSubType')#
					<value key="listLockAddress" value="#xmlformat(arguments.event.getValue('s1_5','-1'))#" />
					<value key="listKeepActive" value="#xmlformat(arguments.event.getValue('s1_6','-1'))#" />
					<value key="listJoinDateLower" value="#xmlformat(arguments.event.getValue('s1_7a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_listJoinDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_listJoinDateLower_afid','0'))#"</cfif> />
					<value key="listJoinDateUpper" value="#xmlformat(arguments.event.getValue('s1_7b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_listJoinDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_listJoinDateUpper_afid','0'))#"</cfif> />
					<value key="listExpireDateLower" value="#xmlformat(arguments.event.getValue('s1_7c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_listExpireDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_listExpireDateLower_afid','0'))#"</cfif> />
					<value key="listExpireDateUpper" value="#xmlformat(arguments.event.getValue('s1_7d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_listExpireDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_listExpireDateUpper_afid','0'))#"</cfif> />
				<cfelseif local.fieldCode eq "cp_valuesum">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_programInstallDateLower_afid','0')) OR val(arguments.event.getValue('roll_programInstallDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_contribStartDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribStartDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_contribEndDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='programList')#
					<value key="programInstallDateLower" value="#xmlformat(arguments.event.getValue('s1_4a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_programInstallDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_programInstallDateLower_afid','0'))#"</cfif> />
					<value key="programInstallDateUpper" value="#xmlformat(arguments.event.getValue('s1_4b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_programInstallDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_programInstallDateUpper_afid','0'))#"</cfif> />
					<value key="programListOption" value="#xmlformat(arguments.event.getValue('s1_5b',''))#" />
					<cfif listLen(arguments.event.getValue('s1_3','')) eq 1>
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_5a',''), key='campaignList')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_5t',''), key='programRate')#
						<cfif arguments.event.getValue('s1_5b','') eq 'ds'>
							#generateKeyValuePairForList(list=arguments.event.getValue('s1_5c',''), key='programDistribution')#
						<cfelseif arguments.event.getValue('s1_5b','') eq 'cf'>
							#generateKeyValuePairForList(list=arguments.event.getValue('s1_5d',''), key='programMonetaryCustomField')#
						</cfif>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5r',''), key='programNonMonetaryCustomField')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5s',''), key='programNonMonetaryCustomFieldValue')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5e',''), key='programStatus')#
					<value key="programDuration" value="#xmlformat(arguments.event.getValue('s1_5f',''))#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5g',''), key='programFrequency')#
					<value key="contribMode" value="#xmlformat(arguments.event.getValue('s1_5h',''))#" />
					<value key="contribHasCard" value="#xmlformat(arguments.event.getValue('s1_5i',''))#" />
					<value key="contribStartDateLower" value="#xmlformat(arguments.event.getValue('s1_5l',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribStartDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribStartDateLower_afid','0'))#"</cfif> />
					<value key="contribStartDateUpper" value="#xmlformat(arguments.event.getValue('s1_5m',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribStartDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribStartDateUpper_afid','0'))#"</cfif> />
					<value key="contribEndDateLower" value="#xmlformat(arguments.event.getValue('s1_5n',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEndDateLower_afid','0'))#"</cfif> />
					<value key="contribEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_5o',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEndDateUpper_afid','0'))#"</cfif> />
					<value key="contribEnteredDateLower" value="#xmlformat(arguments.event.getValue('s1_5p',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0'))#"</cfif> />
					<value key="contribEnteredDateUpper" value="#xmlformat(arguments.event.getValue('s1_5q',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))#"</cfif> />
					<cfif local.expression eq "between">
						<value key="valueLower" value="#xmlformat(arguments.event.getValue('conditionValueLower',''))#" />
						<value key="valueUpper" value="#xmlformat(arguments.event.getValue('conditionValueUpper',''))#" />
					<cfelse>
						<value key="value" value="#xmlformat(local.conditionValue)#" />
					</cfif>
				<cfelseif NOT ListFindNoCase("exists,not_exists,registered,attended,awarded,included,contributed,swregistered,insuppressionlist,inconsentlist,hasreferral",local.expression)>
					<cfif local.expression eq "datediff" and local.pastOrFutureValue NEQ "past">
						<value key="value" value="-#xmlformat(local.conditionValue)#" />
					<cfelseif local.canHaveMultipleValues is 1 and local.fieldCodeDataType neq "BIT" and local.expression neq "datediff">
						<cfloop list="#local.conditionValue#" index="local.listItem">
							<value key="value" value="#xmlformat(local.listItem)#" />
						</cfloop>
					<cfelse>
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND val(arguments.event.getValue('roll_value_afid','0'))>
							<cfset local.useRollDates = true>
						</cfif>
						<value key="value" value="#xmlformat(local.conditionValue)#"<cfif local.useRollDates and val(arguments.event.getValue('roll_value_afid','0'))> afid="#val(arguments.event.getValue('roll_value_afid','0'))#"</cfif> />
					</cfif>
				<cfelseif local.fieldCode eq "rel_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_relationshipDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_relationshipDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_relationshipEndDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_relationshipEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_relationshipEnteredDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_relationshipEnteredDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="relationshipCategory" value="#arguments.event.getValue('s1_2',0)#" />
					<cfloop list="#arguments.event.getValue('s1_3','')#" index="local.listItem">
						<value key="relationshipSubCategory" value="#xmlformat(local.listItem)#" />
					</cfloop>
					<value key="relationshipDateLower" value="#xmlformat(arguments.event.getValue('s1_4a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipDateLower_afid','0'))#"</cfif> />
					<value key="relationshipDateUpper" value="#xmlformat(arguments.event.getValue('s1_4b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipDateUpper_afid','0'))#"</cfif> />
					<value key="relationshipEndDateLower" value="#xmlformat(arguments.event.getValue('s1_4j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipEndDateLower_afid','0'))#"</cfif> />
					<value key="relationshipEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_4k',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipEndDateUpper_afid','0'))#"</cfif> />
					<value key="relationshipEnteredDateLower" value="#xmlformat(arguments.event.getValue('s1_4c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipEnteredDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipEnteredDateLower_afid','0'))#"</cfif> />
					<value key="relationshipEnteredDateUpper" value="#xmlformat(arguments.event.getValue('s1_4d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_relationshipEnteredDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_relationshipEnteredDateUpper_afid','0'))#"</cfif> />
					<value key="relationshipDescriptionContains" value="#xmlformat(arguments.event.getValue('s1_4i',''))#" />
				<cfelseif local.fieldCode eq "mn_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_noteDateLower_afid','0')) OR val(arguments.event.getValue('roll_noteDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_noteEndDateLower_afid','0')) OR val(arguments.event.getValue('roll_noteEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_noteEnteredDateLower_afid','0')) OR val(arguments.event.getValue('roll_noteEnteredDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="noteCategory" value="#arguments.event.getValue('s1_2',0)#" />
					<cfloop list="#arguments.event.getValue('s1_3','')#" index="local.listItem">
						<value key="noteSubCategory" value="#xmlformat(local.listItem)#" />
					</cfloop>
					<value key="noteDateLower" value="#xmlformat(arguments.event.getValue('s1_4a',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_noteDateLower_afid','0'))#"</cfif> />
					<value key="noteDateUpper" value="#xmlformat(arguments.event.getValue('s1_4b',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_noteDateUpper_afid','0'))#"</cfif> />
					<value key="noteEndDateLower" value="#xmlformat(arguments.event.getValue('s1_4j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_noteEndDateLower_afid','0'))#"</cfif> />
					<value key="noteEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_4k',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_noteEndDateUpper_afid','0'))#"</cfif> />
					<value key="noteEnteredDateLower" value="#xmlformat(arguments.event.getValue('s1_4c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteEnteredDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_noteEnteredDateLower_afid','0'))#"</cfif> />
					<value key="noteEnteredDateUpper" value="#xmlformat(arguments.event.getValue('s1_4d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_noteEnteredDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_noteEnteredDateUpper_afid','0'))#"</cfif> />
					<value key="noteDescriptionContains" value="#xmlformat(arguments.event.getValue('s1_4i',''))#" />
				<cfelseif local.fieldCode eq "cp_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_contribStartDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribStartDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_contribEndDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribEndDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0')) OR val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateValueNodes_cp_entry(event=arguments.event, useRollDates=local.useRollDates)#
				<cfelseif local.fieldCode eq "task_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_taskCreatedDateLower_afid','0')) OR val(arguments.event.getValue('roll_taskCreatedDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_taskDueDateLower_afid','0')) OR val(arguments.event.getValue('roll_taskDueDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_taskReminderDateLower_afid','0')) OR val(arguments.event.getValue('roll_taskReminderDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_taskLastModifiedDateLower_afid','0')) OR val(arguments.event.getValue('roll_taskLastModifiedDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateValueNodes_task_entry(event=arguments.event, useRollDates=local.useRollDates)#
				<cfelseif local.fieldCode eq "sup_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_suppListDateAddedLower_afid','0')) OR 
							val(arguments.event.getValue('roll_suppListDateAddedUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2a',''), key='sendGridSubUser')#
					<cfif listLen(arguments.event.getValue('s1_2b',''))>
						<cfloop list="#arguments.event.getValue('s1_2b')#" index="local.listItem">
							<cfif left(local.listItem,4) EQ 'met_'>
								<value key="emailTagTypeID" value="#xmlformat(listGetAt(local.listItem,2,'_'))#" />
							<cfelseif left(local.listItem,3) EQ 'me_'>
								<value key="emailTypeID" value="#xmlformat(listGetAt(local.listItem,2,'_'))#" />
							</cfif>
						</cfloop>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2c',''), key='sendGridSuppListType')#
					<value key="suppListDateAddedLower" value="#xmlformat(arguments.event.getValue('s1_2d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_suppListDateAddedLower_afid','0'))> afid="#val(arguments.event.getValue('roll_suppListDateAddedLower_afid','0'))#"</cfif> />
					<value key="suppListDateAddedUpper" value="#xmlformat(arguments.event.getValue('s1_2e',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_suppListDateAddedUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_suppListDateAddedUpper_afid','0'))#"</cfif> />
					<value key="suppListSiteID" value="#xmlformat(arguments.event.getValue('s1_2f',0))#" />
				<cfelseif local.fieldCode eq "sw_entry">
					<cfif arguments.event.getValue('s1_3e','') eq "program">
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND (
								val(arguments.event.getValue('roll_enrollmentDateLower_afid','0')) OR 
								val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0')) OR
								val(arguments.event.getValue('roll_completedDateLower_afid','0')) OR 
								val(arguments.event.getValue('roll_completedDateUpper_afid','0'))
							)>
							<cfset local.useRollDates = true>
						</cfif>
						<value key="swType" value="#xmlformat(arguments.event.getValue('s1_2',''))#" />
						<value key="swProgramName" value="" />
						<value key="swPubType" value="" />
						<value key="swStartDateLower" value="" />
						<value key="swStartDateUpper" value="" />
						<value key="swFilterOption" value="#xmlformat(arguments.event.getValue('s1_3e',''))#" />
						<value key="enrollmentDateLower" value="#xmlformat(arguments.event.getValue('s1_3g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_enrollmentDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_enrollmentDateLower_afid','0'))#"</cfif> />
						<value key="enrollmentDateUpper" value="#xmlformat(arguments.event.getValue('s1_3h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0'))#"</cfif> />
						<value key="completedDateLower" value="#xmlformat(arguments.event.getValue('s1_3i',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_completedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_completedDateLower_afid','0'))#"</cfif> />
						<value key="completedDateUpper" value="#xmlformat(arguments.event.getValue('s1_3j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_completedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_completedDateUpper_afid','0'))#"</cfif> />
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_3k',''), key='swCreditAwardLinkID')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_3l',''), key='swCreditDeniedLinkID')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_3f',''), key='swSeminarID')#
						<value key="swOrgCode" value="#arguments.event.getValue('mc_siteinfo.orgCode')#" />
					<cfelse>
						<cfif local.rollDate is 1 
							AND len(arguments.event.getValue('roll_adv','')) 
							AND val(arguments.event.getValue('roll_adv_afid','0'))
							AND (
								val(arguments.event.getValue('roll_swStartDateLower_afid','0')) OR 
								val(arguments.event.getValue('roll_swStartDateUpper_afid','0')) OR
								val(arguments.event.getValue('roll_enrollmentDateLower_afid','0')) OR 
								val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0')) OR
								val(arguments.event.getValue('roll_completedDateLower_afid','0')) OR 
								val(arguments.event.getValue('roll_completedDateUpper_afid','0'))
							)>
							<cfset local.useRollDates = true>
						</cfif>
						<value key="swType" value="#xmlformat(arguments.event.getValue('s1_2',''))#" />
						<value key="swProgramName" value="#xmlformat(arguments.event.getValue('s1_3a',''))#" />
						<value key="swPubType" value="#xmlformat(arguments.event.getValue('s1_3b',''))#" />
						<cfif arguments.event.getValue('s1_2','') eq 'swl'>
							<value key="swStartDateLower" value="#xmlformat(arguments.event.getValue('s1_3c',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_swStartDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_swStartDateLower_afid','0'))#"</cfif> />
							<value key="swStartDateUpper" value="#xmlformat(arguments.event.getValue('s1_3d',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_swStartDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_swStartDateUpper_afid','0'))#"</cfif> />
						<cfelse>
							<value key="swStartDateLower" value="" />
							<value key="swStartDateUpper" value="" />
						</cfif>
						<value key="swFilterOption" value="#xmlformat(arguments.event.getValue('s1_3e',''))#" />
						<value key="enrollmentDateLower" value="#xmlformat(arguments.event.getValue('s1_3g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_enrollmentDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_enrollmentDateLower_afid','0'))#"</cfif> />
						<value key="enrollmentDateUpper" value="#xmlformat(arguments.event.getValue('s1_3h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_enrollmentDateUpper_afid','0'))#"</cfif> />
						<value key="completedDateLower" value="#xmlformat(arguments.event.getValue('s1_3i',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_completedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_completedDateLower_afid','0'))#"</cfif> />
						<value key="completedDateUpper" value="#xmlformat(arguments.event.getValue('s1_3j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_completedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_completedDateUpper_afid','0'))#"</cfif> />
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_3k',''), key='swCreditAwardLinkID')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_3l',''), key='swCreditDeniedLinkID')#
						<value key="swSeminarID" value="" />
						<value key="swOrgCode" value="#arguments.event.getValue('mc_siteinfo.orgCode')#" />
					</cfif>
				<cfelseif local.fieldCode eq "ref_entry">
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND (
							val(arguments.event.getValue('roll_callDateLower_afid','0')) OR 
							val(arguments.event.getValue('roll_callDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_referralDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_referralDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_retainedDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_retainedDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_closedDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_closedDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_lastUpdatedDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_lastUpdatedDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_feeDiscrepancyDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_feeDiscrepancyDateUpper_afid','0')) OR
							val(arguments.event.getValue('roll_followUpDateLower_afid','0')) OR
							val(arguments.event.getValue('roll_followUpDateUpper_afid','0'))
						)>
						<cfset local.useRollDates = true>
					</cfif>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2a',''), key='clientReferralStatusID')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2b',''), key='panelID')#
					<value key="clientReferralsCustomFieldID" value="#xmlformat(arguments.event.getValue('s1_2c',''))#" />
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2c_val',''), key='clientReferralsCustomFieldValue')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2d',''), key='followUpStatus')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_2e',''), key='feeDiscrepancyStatusID')#
					<value key="callDateLower" value="#xmlformat(arguments.event.getValue('s1_2f',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_callDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_callDateLower_afid','0'))#"</cfif> />
					<value key="callDateUpper" value="#xmlformat(arguments.event.getValue('s1_2g',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_callDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_callDateUpper_afid','0'))#"</cfif> />
					<value key="referralDateLower" value="#xmlformat(arguments.event.getValue('s1_2h',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_referralDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_referralDateLower_afid','0'))#"</cfif> />
					<value key="referralDateUpper" value="#xmlformat(arguments.event.getValue('s1_2i',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_referralDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_referralDateUpper_afid','0'))#"</cfif> />
					<value key="retainedDateLower" value="#xmlformat(arguments.event.getValue('s1_2j',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_retainedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_retainedDateLower_afid','0'))#"</cfif> />
					<value key="retainedDateUpper" value="#xmlformat(arguments.event.getValue('s1_2k',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_retainedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_retainedDateUpper_afid','0'))#"</cfif> />
					<value key="closedDateLower" value="#xmlformat(arguments.event.getValue('s1_2l',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_closedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_closedDateLower_afid','0'))#"</cfif> />
					<value key="closedDateUpper" value="#xmlformat(arguments.event.getValue('s1_2m',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_closedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_closedDateUpper_afid','0'))#"</cfif> />
					<value key="lastUpdatedDateLower" value="#xmlformat(arguments.event.getValue('s1_2n',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_lastUpdatedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_lastUpdatedDateLower_afid','0'))#"</cfif> />
					<value key="lastUpdatedDateUpper" value="#xmlformat(arguments.event.getValue('s1_2o',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_lastUpdatedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_lastUpdatedDateUpper_afid','0'))#"</cfif> />
					<value key="feeDiscrepancyDateLower" value="#xmlformat(arguments.event.getValue('s1_2p',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_feeDiscrepancyDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_feeDiscrepancyDateLower_afid','0'))#"</cfif> />
					<value key="feeDiscrepancyDateUpper" value="#xmlformat(arguments.event.getValue('s1_2q',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_feeDiscrepancyDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_feeDiscrepancyDateUpper_afid','0'))#"</cfif> />
					<value key="followUpDateLower" value="#xmlformat(arguments.event.getValue('s1_2r',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_followUpDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_followUpDateLower_afid','0'))#"</cfif> />
					<value key="followUpDateUpper" value="#xmlformat(arguments.event.getValue('s1_2s',''))#"<cfif local.useRollDates and val(arguments.event.getValue('roll_followUpDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_followUpDateUpper_afid','0'))#"</cfif> />
					<value key="referralSiteID" value="#xmlformat(arguments.event.getValue('mc_siteinfo.siteid'))#" />
				</cfif>
				<cfif left(local.fieldCode,4) EQ 'mad_' or left(local.fieldCode,5) EQ 'madt_'>
					<cfif local.rollDate is 1 
						AND len(arguments.event.getValue('roll_adv','')) 
						AND val(arguments.event.getValue('roll_adv_afid','0'))
						AND val(arguments.event.getValue('roll_validAsOfDate_afid','0'))>
						<cfset local.useRollDates = true>
					</cfif>
					<value key="validAsOfDate" value="#xmlformat(arguments.event.getValue('s1_4',''))#" <cfif local.useRollDates and val(arguments.event.getValue('roll_validAsOfDate_afid','0'))> afid="#val(arguments.event.getValue('roll_validAsOfDate_afid','0'))#"</cfif> />
				</cfif>
				<cfif local.useRollDates>
					<value key="afRunDate" value="#xmlformat(arguments.event.getValue('roll_adv',''))#"<cfif val(arguments.event.getValue('roll_adv_afid','0'))> afid="#val(arguments.event.getValue('roll_adv_afid','0'))#"</cfif> />
				</cfif>
			</values>
			</cfoutput>
		</cfsavecontent>

		<cfreturn application.objCommon.minText(local.valueXML)>
	</cffunction>

	<cffunction name="generateValueNodes_cp_entry" access="private" returntype="string" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="useRollDates" type="boolean" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.valueNodeString">
			<cfoutput>
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='programList')#
				<value key="programListOption" value="#xmlformat(arguments.event.getValue('s1_4b',''))#" />
				<cfif listLen(arguments.event.getValue('s1_3','')) eq 1>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4a',''), key='campaignList')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4u',''), key='programRate')#
					<cfif arguments.event.getValue('s1_4b','') eq 'ds'>
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_4c',''), key='programDistribution')#
					<cfelseif arguments.event.getValue('s1_4b','') eq 'cf'>
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_4d',''), key='programMonetaryCustomField')#
					</cfif>
				</cfif>
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_4s',''), key='programNonMonetaryCustomField')#
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_4t',''), key='programNonMonetaryCustomFieldValue')#
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_4e',''), key='programStatus')#
				<value key="programDuration" value="#xmlformat(arguments.event.getValue('s1_4f',''))#" />
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_4g',''), key='programFrequency')#
				<value key="contribMode" value="#xmlformat(arguments.event.getValue('s1_4h',''))#" />
				<value key="contribHasCard" value="#xmlformat(arguments.event.getValue('s1_4v',''))#" />
				<value key="firstInstallAmtLower" value="#xmlformat(arguments.event.getValue('s1_4i',''))#" />
				<value key="firstInstallAmtUpper" value="#xmlformat(arguments.event.getValue('s1_4j',''))#" />
				<value key="recurringInstallAmtLower" value="#xmlformat(arguments.event.getValue('s1_4k',''))#" />
				<value key="recurringInstallAmtUpper" value="#xmlformat(arguments.event.getValue('s1_4l',''))#" />
				<value key="contribStartDateLower" value="#xmlformat(arguments.event.getValue('s1_4m',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribStartDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribStartDateLower_afid','0'))#"</cfif> />
				<value key="contribStartDateUpper" value="#xmlformat(arguments.event.getValue('s1_4n',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribStartDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribStartDateUpper_afid','0'))#"</cfif> />
				<value key="contribEndDateLower" value="#xmlformat(arguments.event.getValue('s1_4o',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribEndDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEndDateLower_afid','0'))#"</cfif> />
				<value key="contribEndDateUpper" value="#xmlformat(arguments.event.getValue('s1_4p',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribEndDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEndDateUpper_afid','0'))#"</cfif> />
				<value key="contribEnteredDateLower" value="#xmlformat(arguments.event.getValue('s1_4q',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEnteredDateLower_afid','0'))#"</cfif> />
				<value key="contribEnteredDateUpper" value="#xmlformat(arguments.event.getValue('s1_4r',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_contribEnteredDateUpper_afid','0'))#"</cfif> />
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.valueNodeString>
	</cffunction>

	<cffunction name="generateValueNodes_task_entry" access="private" returntype="string" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="useRollDates" type="boolean" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.valueNodeString">
			<cfoutput>
				<value key="taskLinkType" value="#xmlformat(arguments.event.getValue('s1_2',''))#" />
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_3',''), key='workspaceList')#
				<cfif listLen(arguments.event.getValue('s1_3','')) eq 1>
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5a',''), key='taskTagList')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5b',''), key='taskTagFieldList')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_5b_val',''), key='taskTagFieldValue')#
					#generateKeyValuePairForList(list=arguments.event.getValue('s1_4',''), key='projectList')#
					<cfif listLen(arguments.event.getValue('s1_4','')) eq 1>
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_5c',''), key='taskProjectField')#
						#generateKeyValuePairForList(list=arguments.event.getValue('s1_5d',''), key='taskProjectFieldValue')#
					</cfif>
				</cfif>
				#generateKeyValuePairForList(list=arguments.event.getValue('s1_5e',''), key='taskStatus')#
				<value key="taskCreatedDateLower" value="#xmlformat(arguments.event.getValue('s1_5f',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskCreatedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_taskCreatedDateLower_afid','0'))#"</cfif> />
				<value key="taskCreatedDateUpper" value="#xmlformat(arguments.event.getValue('s1_5g',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskCreatedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_taskCreatedDateUpper_afid','0'))#"</cfif> />
				<value key="taskDueDateLower" value="#xmlformat(arguments.event.getValue('s1_5h',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskDueDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_taskDueDateLower_afid','0'))#"</cfif> />
				<value key="taskDueDateUpper" value="#xmlformat(arguments.event.getValue('s1_5i',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskDueDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_taskDueDateUpper_afid','0'))#"</cfif> />
				<value key="taskReminderDateLower" value="#xmlformat(arguments.event.getValue('s1_5j',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskReminderDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_taskReminderDateLower_afid','0'))#"</cfif> />
				<value key="taskReminderDateUpper" value="#xmlformat(arguments.event.getValue('s1_5k',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskReminderDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_taskReminderDateUpper_afid','0'))#"</cfif> />
				<value key="taskLastModifiedDateLower" value="#xmlformat(arguments.event.getValue('s1_5l',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskLastModifiedDateLower_afid','0'))> afid="#val(arguments.event.getValue('roll_taskLastModifiedDateLower_afid','0'))#"</cfif> />
				<value key="taskLastModifiedDateUpper" value="#xmlformat(arguments.event.getValue('s1_5m',''))#"<cfif arguments.useRollDates and val(arguments.event.getValue('roll_taskLastModifiedDateUpper_afid','0'))> afid="#val(arguments.event.getValue('roll_taskLastModifiedDateUpper_afid','0'))#"</cfif> />
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.valueNodeString>
	</cffunction>

	<cffunction name="generateKeyValuePairForList" access="private" returntype="string" output="false">
		<cfargument name="list" type="string" required="true">
		<cfargument name="key" type="string" required="true">

		<cfset var local = structNew()>

		<cfif len(arguments.list)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfloop list="#arguments.list#" index="local.listItem">
					<value key="#arguments.key#" value="#xmlformat(local.listItem)#" />
				</cfloop>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.data = ''>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberQualifiedRuleConditions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="ruleID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfif NOT hasViewVirtualGroupAdminRights(siteID=arguments.mcproxy_siteID)>
			<cfreturn { "success":false }>
		</cfif>
		
		<cfset local.returnStruct = { 
			"success":true, 
			"memberid":application.objMember.getMemberIDByMemberNumber(memberNumber=arguments.membernumber, orgID=arguments.mcproxy_orgID),
			"qcndids":"",
			"qcndsetids":""
		}>

		<cfif local.returnStruct.memberID GT 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberQualifiedRuleConditions">
				SET NOCOUNT ON;

				DECLARE @qualifiedConditionIDList varchar(800), @qualifiedConditionSetIDList varchar(800);

				EXEC dbo.ams_getMemberQualifiedRuleConditions
					@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
					@ruleID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
					@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.returnStruct.memberID#">,
					@qualifiedConditionIDList=@qualifiedConditionIDList OUTPUT,
					@qualifiedConditionSetIDList=@qualifiedConditionSetIDList OUTPUT;

				SELECT @qualifiedConditionIDList AS qualifiedConditionIDList, @qualifiedConditionSetIDList AS qualifiedConditionSetIDList;
			</cfquery>

			<cfif local.qryMemberQualifiedRuleConditions.recordCount>
				<cfset local.returnStruct.qcndids = local.qryMemberQualifiedRuleConditions.qualifiedConditionIDList>
				<cfset local.returnStruct.qcndsetids = local.qryMemberQualifiedRuleConditions.qualifiedConditionSetIDList>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="hasViewVirtualGroupAdminRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='VirtualGroup', siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn local.tmpRights.viewVirtualGroupAdmin is 1>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
			
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>That group set was not found.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>