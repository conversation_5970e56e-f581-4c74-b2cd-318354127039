<cfcomponent output="false">

	<cffunction name="getConsentListType" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		
		<cfset var qryConsentListType = "">

		<cfquery name="qryConsentListType" datasource="#application.dsn.platformmail.dsn#">
			select consentListTypeID, consentListTypeName
			from dbo.email_consentListTypes
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			and consentListTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">
		</cfquery>

		<cfreturn qryConsentListType>
	</cffunction>

	<cffunction name="saveConsentListType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="consentListTypeName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif arguments.consentListTypeID gt 0>
			<cfset updateConsentListType(orgID=arguments.mcproxy_orgID, consentListTypeID=arguments.consentListTypeID, consentListTypeName=arguments.consentListTypeName)>
		<cfelse>
			<cfset local.returnStruct.consentListTypeID = insertConsentListType(orgID=arguments.mcproxy_orgID, consentListTypeName=arguments.consentListTypeName)>
		</cfif>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertConsentListType" access="private" output="false" returntype="numeric">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="consentListTypeName" type="string" required="true">

		<cfset var qryInsertConsentListType = "">

		<cfquery name="qryInsertConsentListType" datasource="#application.dsn.platformmail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @consentListTypeID int, @orgID int, @consentListTypeName varchar(100);
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @consentListTypeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListTypeName#">;

				EXEC dbo.email_addConsentListType @orgID=@orgID, @consentListTypeName=@consentListTypeName, @isSystemType=0, @consentListTypeID=@consentListTypeID OUTPUT;
				
				SELECT @consentListTypeID as consentListTypeID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn qryInsertConsentListType.consentListTypeID>
	</cffunction>

	<cffunction name="updateConsentListType" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="consentListTypeName" type="string" required="true">

		<cfset var qryUpdateConsentListType = "">

		<cfquery name="qryUpdateConsentListType" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID int, @consentListTypeID int, @consentListTypeName varchar(100), @oldConsentListTypeName varchar(100);

				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @consentListTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">;
				SET @consentListTypeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListTypeName#">;

				SELECT @oldConsentListTypeName = consentListTypeName
				FROM platformMail.dbo.email_consentListTypes
				WHERE consentListTypeID = @consentListTypeID
				AND orgID = @orgID;

				UPDATE platformMail.dbo.email_consentListTypes
				SET consentListTypeName = @consentListTypeName
				WHERE consentListTypeID = @consentListTypeID
				AND orgID = @orgID;

				IF @consentListTypeName <> @oldConsentListTypeName BEGIN
					-- update conditions verbose
					UPDATE dbo.ams_virtualGroupConditions
					SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
					WHERE orgID = @orgID
					AND fieldCode = 'cl_entry'
					AND conditionID IN (
						SELECT c.conditionID
						FROM dbo.ams_virtualGroupConditions AS c
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
							AND cv.conditionValue = cast(@consentListTypeID as varchar(10))
						INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID
							AND k.conditionKey = 'consentListTypeID'
						WHERE c.orgID = @orgID
					);
				END
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteConsentListType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cftry>
			<cfquery name="local.qryDeleteConsentListType" datasource="#application.dsn.platformmail.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @consentListTypeID int, @orgID int;
					SET @consentListTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

					IF EXISTS (
						SELECT 1
						FROM dbo.email_consentListTypes
						WHERE consentListTypeID = @consentListTypeID
						AND isSystemType = 1
					)
						RAISERROR('system type', 16, 1);

					IF EXISTS (select 1 from dbo.email_consentLists where consentListTypeID = @consentListTypeID)
						RAISERROR('Consent List Type has lists', 16, 1);

					BEGIN TRAN;
						DELETE FROM dbo.email_consentListTypes
						WHERE consentListTypeID = @consentListTypeID;

						EXEC dbo.email_reorderConsentListTypes @orgID=@orgID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getConsentList" access="public" output="false" returntype="query">
		<cfargument name="consentListID" type="numeric" required="true">
		
		<cfset var qryConsentList = "">

		<cfquery name="qryConsentList" datasource="#application.dsn.platformmail.dsn#">
			select l.consentListID, l.consentListTypeID, l.consentListName, l.consentListDesc, l.siteResourceID, m.consentListModeID, m.modeName as consentListModeName, l.orgIdentityID
			from dbo.email_consentLists l
			inner join dbo.email_consentListModes m on m.consentListModeID = l.consentListModeID
			where l.consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListID#">
			and l.[status] = 'A'
		</cfquery>

		<cfreturn qryConsentList>
	</cffunction>

	<cffunction name="getOrgConsentListTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		
		<cfset var qryOrgConsentListTypes = "">

		<cfquery name="qryOrgConsentListTypes" datasource="#application.dsn.platformmail.dsn#">
			select consentListTypeID, consentListTypeName
			from dbo.email_consentListTypes
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			and isSystemType = 0
			order by orderNum
		</cfquery>

		<cfreturn qryOrgConsentListTypes>
	</cffunction>

	<cffunction name="getConsentListModes" access="public" output="false" returntype="query">
		<cfargument name="modeNameList" type="string" required="false" default="">

		<cfset var qryConsentListModes = "">

		<cfquery name="qryConsentListModes" datasource="#application.dsn.platformmail.dsn#">
			select clm.consentListModeID, clm.modeName, clm.modeDescription
			from dbo.email_consentListModes clm
			<cfif len(arguments.modeNameList)>
				inner join membercentral.dbo.fn_varCharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.modeNameList#">,',') as li on li.listItem = clm.modeName
			</cfif>;
		</cfquery>

		<cfreturn qryConsentListModes>
	</cffunction>

	<cffunction name="saveConsentList" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="consentListID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="consentListName" type="string" required="true">
		<cfargument name="consentListDesc" type="string" required="true">
		<cfargument name="consentListModeID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif arguments.consentListID gt 0>
			<cfset updateConsentList(siteID=arguments.mcproxy_siteID, consentListID=arguments.consentListID, consentListTypeID=arguments.consentListTypeID, consentListName=arguments.consentListName,
						consentListDesc=arguments.consentListDesc, orgIdentityID=arguments.orgIdentityID)>
		<cfelse>
			<cfset local.returnStruct.consentListID = insertConsentList(siteID=arguments.mcproxy_siteID, consentListTypeID=arguments.consentListTypeID, consentListName=arguments.consentListName,
						consentListDesc=arguments.consentListDesc, consentListModeID=arguments.consentListModeID, orgIdentityID=arguments.orgIdentityID)>
		</cfif>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertConsentList" access="private" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="consentListName" type="string" required="true">
		<cfargument name="consentListDesc" type="string" required="true">
		<cfargument name="consentListModeID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">

		<cfset var consentListID = "">

		<cfstoredproc procedure="email_addConsentList" datasource="#application.dsn.platformmail.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListDesc#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListModeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="consentListID">
		</cfstoredproc>

		<cfreturn consentListID>
	</cffunction>

	<cffunction name="updateConsentList" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="consentListID" type="numeric" required="true">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="consentListName" type="string" required="true">
		<cfargument name="consentListDesc" type="string" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">

		<cfset var qryUpdateConsentList = "">

		<cfquery name="qryUpdateConsentList" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID int, @consentListID int, @consentListTypeID int, @consentListName varchar(100),
					@oldConsentListTypeID int, @oldConsentListName varchar(100);

				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);
				SET @consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListID#">;
				SET @consentListTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">;
				SET @consentListName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListName#">;

				SELECT @oldConsentListTypeID = consentListTypeID, @oldConsentListName = consentListName
				FROM platformMail.dbo.email_consentLists
				WHERE consentListID = @consentListID
				AND [status] = 'A';

				UPDATE platformMail.dbo.email_consentLists
				SET consentListTypeID = @consentListTypeID,
					consentListName = @consentListName,
					consentListDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListDesc#">,
					orgIdentityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
				WHERE consentListID = @consentListID;

				IF @consentListName <> @oldConsentListName BEGIN
					-- update conditions verbose
					UPDATE dbo.ams_virtualGroupConditions
					SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
					WHERE orgID = @orgID
					AND fieldCode = 'cl_entry'
					AND conditionID IN (
						SELECT c.conditionID
						FROM dbo.ams_virtualGroupConditions AS c
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
							AND cv.conditionValue = cast(@consentListID as varchar(10))
						INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID
							AND k.conditionKey = 'consentListID'
						WHERE c.orgID = @orgID
					);
				END

				IF @consentListTypeID <> @oldConsentListTypeID BEGIN
					-- process conditions
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select distinct @orgID, m.activeMemberID, vgc.conditionID
					from dbo.ams_virtualGroupConditions as vgc
					inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.emailTypeID = met.emailTypeID
					inner join platformMail.dbo.email_consentListMembers as clm on clm.email = me.email
					inner join platformMail.dbo.email_consentLists as cl on cl.consentListID = clm.consentListID
						and cl.[status] = 'A'
					inner join platformMail.dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
						and clt.orgID = @orgID
						and clt.consentListTypeID in (@consentListTypeID,@oldConsentListTypeID)
					inner join dbo.ams_members as m on m.orgID = @orgID
						and m.memberID = me.memberID
					where vgc.orgID = @orgID
					and vgc.fieldCode = 'cl_entry';

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
				END
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteConsentList" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="email_deleteConsentList" datasource="#application.dsn.platformmail.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doMoveListType" access="public" output="false" returntype="struct">
		<cfargument name="consentListTypeID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.platformmail.dsn#" procedure="email_moveConsentListType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doMoveList" access="public" output="false" returntype="struct">
		<cfargument name="consentListID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformmail.dsn#" procedure="email_moveConsentList">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDefaultConsentList" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="emailConsentListID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="qryUpdateDefaultConsentList" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.sites
						SET 
							defaultConsentListID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.emailConsentListID#">
						WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">;

			</cfquery>
			
			<cfset local.data.success = true>
			<cfset application.objSiteInfo.triggerClusterWideReload()>	
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getConsentListMembersFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="operationMode" type="string" required="true">
		<cfargument name="listMode" type="string" required="true">

		<cfset var local = structNew()>

		<cfif listFindNoCase('allConsentLists,memberConsentLists',arguments.operationMode)>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"email")>
			<cfset arrayAppend(local.arrCols,"consentListName")>
			<cfset arrayAppend(local.arrCols,"dateCreated")>
			<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderDir')#">
		</cfif>

		<cfif arguments.listMode EQ 'Opt-Out'>
			<cfset arguments.listMode = "Opt-Out,GlobalOptOut">
		</cfif>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryConsentListMembers" result="local.qryConsentListMembersResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpConsentListSearch') IS NOT NULL 
					DROP TABLE ##tmpConsentListSearch;
				IF OBJECT_ID('tempdb..##tmpConsentLists') IS NOT NULL
					DROP TABLE ##tmpConsentLists;
				CREATE TABLE ##tmpConsentListSearch (consentListMemberID int PRIMARY KEY);
				CREATE TABLE ##tmpConsentLists (consentListMemberID int, memberIDList varchar(max), memberDisplayNameList varchar(max), listName varchar(100), 
					emailAddress varchar(200), dateCreated datetime, row int);

				DECLARE @fCreatedFrom DATE, @fCreatedTo datetime, @fAssignedMemberID int, @fAssignedGroupID int, @totalCount int, @orgID int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				<cfif arguments.event.getTrimValue('fCreatedFrom','') NEQ ''>
					SET @fCreatedFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fCreatedFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedTo','') NEQ ''>
					SET @fCreatedTo = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fCreatedTo')#">;
				</cfif>
				<cfif arguments.event.getValue('fAssignedMemberID',0) gt 0>
					SET @fAssignedMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fAssignedMemberID')#">;
				</cfif>
				<cfif arguments.event.getValue('fAssignedGroupID',0) gt 0>
					SET @fAssignedGroupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fAssignedGroupID')#">;
				</cfif>
				
				INSERT INTO ##tmpConsentListSearch (consentListMemberID)
				SELECT DISTINCT lm.consentListMemberID
				FROM dbo.email_consentListMembers AS lm
				INNER JOIN dbo.email_consentLists AS l ON l.consentListID = lm.consentListID
					AND l.[status] = 'A'
				INNER JOIN dbo.email_consentListModes AS clm ON clm.consentListModeID = l.consentListModeID
				INNER JOIN dbo.email_consentListTypes AS clt ON clt.consentListTypeID = l.consentListTypeID
				LEFT OUTER JOIN memberCentral.dbo.ams_memberEmails AS me 
					INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = me.memberID
					<cfif val(arguments.event.getValue('fAssignedGroupID',0))>
						INNER JOIN memberCentral.dbo.cache_members_groups AS mgAssigned ON mgAssigned.orgID = @orgID AND mgAssigned.memberID = m.memberID
					</cfif>
					ON me.orgID = @orgID
					AND me.email = lm.email
				WHERE clt.orgID = @orgID
				AND clm.modeName in (<cfqueryparam value="#arguments.listMode#" list="true" cfsqltype="CF_SQL_VARCHAR">)
				<cfif arguments.event.getValue('fConsentListId',0) gt 0>
					AND l.consentListID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConsentListID')#" list="true">)
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedFrom','') NEQ ''>
					AND lm.dateCreated >= @fCreatedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedTo','') NEQ ''>
					AND lm.dateCreated <= dateadd(day,1,@fCreatedTo)
				</cfif>
				<cfif arguments.event.getValue('fAssignedMemberID',0) gt 0>
					AND m.memberID = @fAssignedMemberID
				</cfif>
				<cfif val(arguments.event.getValue('fAssignedGroupID',0))>
					AND mgAssigned.groupID = @fAssignedGroupID
				</cfif>
				<cfif arguments.event.getValue('fEmailAddressMode','') EQ 'EmailTied'>
					AND m.memberID IS NOT NULL
				</cfif>
				<cfif arguments.event.getValue('fEmailAddressMode','') EQ 'EmailNotTied'>
					AND m.memberID IS NULL
				</cfif>;

				<cfif listFindNoCase('allConsentLists,memberConsentLists',arguments.operationMode)>
					SELECT @totalCount = COUNT(consentListMemberID) FROM ##tmpConsentListSearch;

					<cfif arguments.operationMode is 'allConsentLists'>
						DECLARE @posStart INT, @posStartPlusCount INT;
						SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
						SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
					</cfif>

					<!--- the casting to varchar max is here because string_agg will break with "STRING_AGG aggregation result exceeded the limit of 8000 bytes" otherwise --->
					INSERT INTO ##tmpConsentLists (consentListMemberID, memberIDList, memberDisplayNameList, listName, emailAddress, dateCreated, row)
					SELECT consentListMemberID, STRING_AGG(cast(memberID as varchar(max)),','), 
						STRING_AGG(cast(membername as varchar(max)),'|'),
						consentListName, email, dateCreated, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
					FROM (	
					SELECT DISTINCT lm.consentListMemberID,  mActive.memberID, 
						 mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as membername, 
						l.consentListName, lm.email, lm.dateCreated
					FROM dbo.email_consentListMembers AS lm
					INNER JOIN ##tmpConsentListSearch AS tmp ON tmp.consentListMemberID = lm.consentListMemberID
					INNER JOIN dbo.email_consentLists AS l ON l.consentListID = lm.consentListID
						AND l.[status] = 'A'
					INNER JOIN dbo.email_consentListModes AS clm ON clm.consentListModeID = l.consentListModeID
					LEFT OUTER JOIN memberCentral.dbo.ams_memberEmails AS me 
						INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = me.memberID
						INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
						ON me.orgID = @orgID
						AND me.email = lm.email) as tmp
					GROUP BY consentListMemberID,consentListName, email, dateCreated;

					SELECT consentListMemberID, memberIDList, memberDisplayNameList, listName, emailAddress, dateCreated, @totalCount AS totalCount
					FROM ##tmpConsentLists
					<cfif arguments.operationMode is 'allConsentLists'>
						WHERE row > @posStart
						AND row <= @posStartPlusCount
					</cfif>
					ORDER BY row;
				<cfelseif listFindNoCase("exportEntries,massDelete",arguments.operationMode)>
					SELECT consentListMemberID
					FROM ##tmpConsentListSearch;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpConsentLists') IS NOT NULL
					DROP TABLE ##tmpConsentLists;
				IF OBJECT_ID('tempdb..##tmpConsentListSearch') IS NOT NULL 
					DROP TABLE ##tmpConsentListSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryConsentListMembers>
	</cffunction>

	<cffunction name="getConsentLists" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="excludeGlobalLists" type="boolean" required="false" default="0">

		<cfset var qryConsentLists = "">

		<cfif arguments.mode EQ 'Opt-Out' and arguments.excludeGlobalLists eq 0>
			<cfset arguments.mode = "Opt-Out,GlobalOptOut">
		</cfif>

		<cfquery name="qryConsentLists" datasource="#application.dsn.platformMail.dsn#">
			SELECT l.consentListID, l.consentListName, clt.consentListTypeName, case WHEN clt.consentListTypeName = 'Global Lists' then 1 else 2 end as groupPriority
			, (select count(consentListMemberID) from dbo.email_consentListMembers where consentListID = l.consentListID) as memberCount, m.modeName
			FROM dbo.email_consentLists as l
			INNER JOIN dbo.email_consentListModes as m on m.consentListModeID = l.consentListModeID
			INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = l.consentListTypeID
			WHERE m.modeName IN (<cfqueryparam value="#arguments.mode#" list="true" cfsqltype="CF_SQL_VARCHAR">)
			AND clt.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			AND l.[status] = 'A'
			ORDER BY groupPriority, clt.consentListTypeName, l.consentListName;
		</cfquery>

		<cfreturn qryConsentLists>
	</cffunction>

	<cffunction name="saveConsentListMember" access="public" output="true" returntype="struct" hint="Insert new consent list members">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListIDList" type="string" required="true">
		<cfargument name="consentListEmailAddress" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfset local.returnStruct = {
			"success": true,
			"entrycount": ListLen(arguments.consentListIDList),
			"successcount": 0
		}>

		<cfif NOT len(arguments.consentListEmailAddress) OR NOT isValid("regex",arguments.consentListEmailAddress,application.regEx.email)>
			<cfset local.returnStruct.success = false>
			<cfreturn local.returnStruct>
		</cfif>
		
		<cfquery name="local.qryInsertListMember" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY			
				
				DECLARE @orgID int, @consentListID int, @email varchar(200), @changeSetUID uniqueidentifier, @consentListMemberID int, @enteredByMemberID int, @actionID int;
				DECLARE @listMember TABLE(consentListMemberID int);
				
				SET @changeSetUID = NEWID();
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
				SET @email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListEmailAddress#">;

				SELECT @actionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Add';

				BEGIN TRAN;
				<cfloop list="#arguments.consentListIDList#" index="local.thisListID">
					SET @consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisListID#">;					

					IF NOT EXISTS (SELECT consentListMemberID FROM dbo.email_consentListMembers WHERE consentListID = @consentListID AND email = @email) BEGIN
						INSERT INTO dbo.email_consentListMembers(consentListID, email, dateCreated)
						VALUES (@consentListID, @email, getDate());

						SELECT @consentListMemberID = SCOPE_IDENTITY();

						INSERT INTO @listMember VALUES (@consentListMemberID);
						
						INSERT INTO dbo.email_consentListMemberHistory(changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
						VALUES (@changeSetUID, @email, @consentListID, @actionID, getDate(), @enteredByMemberID);
					END
				</cfloop>
				COMMIT TRAN;

				-- process conditions
				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;
				CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
				select distinct @orgID, m.activeMemberID, vgc.conditionID
				from membercentral.dbo.ams_virtualGroupConditions as vgc
				inner join membercentral.dbo.ams_memberEmailTypes as met on met.orgID = @orgID
				inner join membercentral.dbo.ams_memberEmails as me on me.orgID = @orgID
					and me.emailTypeID = met.emailTypeID
					and me.email = @email
				inner join membercentral.dbo.ams_members as m on m.orgID = @orgID
					and m.memberID = me.memberID
				where vgc.orgID = @orgID
				and vgc.fieldCode = 'cl_entry';

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;

				SELECT COUNT(consentListMemberID) AS insertedCount FROM @listMember;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif local.qryInsertListMember.recordCount GT 0>
			<cfset local.returnStruct.successcount = local.qryInsertListMember.insertedCount>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteConsentListMember" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="consentListMemberIDs" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cftry>
			<cfquery name="local.qryDeleteConsentListMember" datasource="#application.dsn.platformMail.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpConsentMemberIDs') IS NOT NULL
						DROP TABLE ##tmpConsentMemberIDs;
					CREATE TABLE ##tmpConsentMemberIDs (consentListMemberID int);
				
					DECLARE @orgID int,  @enteredByMemberID INT, @actionID INT, @changeSetUID UNIQUEIDENTIFIER;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
					SELECT @actionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Removed';
					SET @changeSetUID = newID();

					INSERT INTO ##tmpConsentMemberIDs (consentListMemberID)
					select listItem
					from membercentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.consentListMemberIDs#">,',');
					
					BEGIN TRAN;
						INSERT INTO dbo.email_consentListMemberHistory(changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
						SELECT @changeSetUID, m.email, m.consentListID, @actionID, getDate(), @enteredByMemberID
						FROM dbo.email_consentListMembers as m
						INNER JOIN ##tmpConsentMemberIDs as tmp on tmp.consentListMemberID = m.consentListMemberID;
						
						DELETE m
						FROM dbo.email_consentListMembers as m
						INNER JOIN ##tmpConsentMemberIDs as tmp on tmp.consentListMemberID = m.consentListMemberID;
					COMMIT TRAN;
					
					-- process conditions
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select distinct @orgID, m.activeMemberID, vgc.conditionID
					from membercentral.dbo.ams_virtualGroupConditions as vgc
					inner join membercentral.dbo.ams_memberEmailTypes as met on met.orgID = @orgID
					inner join membercentral.dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.emailTypeID = met.emailTypeID
					inner join dbo.email_consentListMemberHistory as clmh on clmh.email = me.email
						and clmh.changeSetUID = @changeSetUID
					inner join membercentral.dbo.ams_members as m on m.orgID = @orgID
						and m.memberID = me.memberID
					where vgc.orgID = @orgID
					and vgc.fieldCode = 'cl_entry';

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
					
					IF OBJECT_ID('tempdb..##tmpConsentMemberIDs') IS NOT NULL
						DROP TABLE ##tmpConsentMemberIDs;

				END TRY	
				BEGIN CATCH	
					IF @@trancount > 0 ROLLBACK TRANSACTION;	
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;	
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getListMemberDetails" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="consentListMemberID" type="numeric" required="true">

		<cfset var qryConsentListMemberDetail = "">

		<cfquery name="qryConsentListMemberDetail" datasource="#application.dsn.platformMail.dsn#">
			SELECT DISTINCT cl.consentListName, mActive.memberID, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' AS memberDisplayName
			FROM dbo.email_consentListMembers lm
			INNER JOIN dbo.email_consentLists cl ON cl.consentListID = lm.consentListID
				AND cl.[status] = 'A'
			LEFT OUTER JOIN memberCentral.dbo.ams_memberEmails AS me
				INNER JOIN memberCentral.dbo.ams_members AS m ON m.memberID = me.memberID
				INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				ON me.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
				AND me.email = lm.email
			WHERE lm.consentListMemberID = <cfqueryparam value="#arguments.consentListMemberID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY memberDisplayName;
		</cfquery>

		<cfreturn qryConsentListMemberDetail>
	</cffunction>

	<cffunction name="getConsentListMemberHistory" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="operationMode" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"updateDate")>
		<cfset arrayAppend(local.arrCols,"action")>
		<cfset arrayAppend(local.arrCols,"consentListName")>
		<cfset arrayAppend(local.arrCols,"email")>
		<cfset arrayAppend(local.arrCols,"enteredByMemberName")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderDir')#">

		<cfset local.listMode = arguments.event.getValue('listMode','Opt-Out')>
		<cfif arguments.operationMode EQ 'memberConsentListHistory' AND local.listMode EQ 'Opt-Out'>
			<cfset local.listMode = "Opt-Out,GlobalOptOut">
		</cfif>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryConsentListMemberHistory" result="local.qryConsentListMemberHistoryResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpListMemberHistory') IS NOT NULL
					DROP TABLE ##tmpListMemberHistory;
				CREATE TABLE ##tmpListMemberHistory (historyID INT, consentListName varchar(200), updateDate DATETIME, action VARCHAR(20), email VARCHAR(200), enteredByMemberID INT, 
					enteredByMemberOrgID INT, enteredByMemberName VARCHAR(200), enteredByMemberCompany VARCHAR(200), row INT);

				DECLARE @orgID INT, @totalCount INT;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

				declare @emailAddressesToFind TABLE (email varchar(200) PRIMARY KEY)

				declare @consentListsToSearch TABLE (consentListID int PRIMARY KEY, consentListName varchar(100))

				<cfif arguments.operationMode EQ 'memberConsentListHistory'>
					insert into @emailAddressesToFind (email)
					select distinct email
					from membercentral.dbo.ams_memberEmails
					where orgID = @orgID
					and memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fMemberID',0)#">;

					insert into @consentListsToSearch (consentListID, consentListName)
					select cl.consentListID, cl.consentListName
					from dbo.email_consentListModes AS clm 
					INNER JOIN dbo.email_consentLists cl
						ON cl.consentListModeID = clm.consentListModeID
						AND cl.[status] = 'A'
					INNER JOIN dbo.email_consentListTypes AS clt 
						ON clt.consentListTypeID = cl.consentListTypeID
						and clt.orgID = @orgID

				<cfelse>
					insert into @emailAddressesToFind (email)
					select distinct email
					from dbo.email_consentListMembers lm 
					where consentListMemberID = <cfqueryparam value="#arguments.event.getValue('consentListMemberID',0)#" cfsqltype="CF_SQL_INTEGER">;

					insert into @consentListsToSearch (consentListID, consentListName)
					select cl.consentListID, cl.consentListName
					from dbo.email_consentListMembers lm 
					INNER JOIN dbo.email_consentLists cl
						ON cl.consentListID = lm.consentListID
						AND cl.[status] = 'A'
						and lm.consentListMemberID = <cfqueryparam value="#arguments.event.getValue('consentListMemberID',0)#" cfsqltype="CF_SQL_INTEGER">;

				</cfif>

				
				INSERT INTO ##tmpListMemberHistory (historyID, consentListName, updateDate, action, email, enteredByMemberID, enteredByMemberOrgID, enteredByMemberName, enteredByMemberCompany, row)
				SELECT historyID, consentListName, updateDate, action, email, memberID, enteredByMemberOrgID, enteredByMemberName, company, ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
				FROM (
					SELECT DISTINCT h.historyID, lts.consentListName, h.updateDate, ha.action, h.email, mActive.memberID, mActive.orgID AS enteredByMemberOrgID,
						mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' AS enteredByMemberName, 
						mActive.company
					FROM @consentListsToSearch lts
					cross join @emailAddressesToFind em
					inner join dbo.email_consentListMemberHistory h
						on h.email = em.email
						and h.consentListID = lts.consentListID
					INNER JOIN dbo.email_consentListMemberHistoryActions ha ON ha.actionID = h.actionID
					INNER JOIN memberCentral.dbo.ams_members AS m2 ON m2.memberID = h.enteredByMemberID
					INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.memberID = m2.activeMemberID
				) tmpOuter;

				SELECT @totalCount = @@ROWCOUNT;

				DECLARE @posStart INT, @posStartPlusCount INT;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				SELECT historyID, consentListName, updateDate, action, email, enteredByMemberID, enteredByMemberOrgID, enteredByMemberName, enteredByMemberCompany, @totalCount AS totalCount
				FROM ##tmpListMemberHistory
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpListMemberHistory') IS NOT NULL
					DROP TABLE ##tmpListMemberHistory;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryConsentListMemberHistory>
	</cffunction>

	<cffunction name="importConsentListMembers" access="package" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","model.admin.common.modules.import.import")>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>

		<!--- upload file --->
		<cfset local.uploadResult = local.objImport.uploadFile(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), formFieldName='uploadFileName')>
		<cfif local.uploadResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.uploadResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif local.uploadResult.ext eq "xls">
			<cfset local.parseResult = local.objImport.parseXLS(strFilePath=local.uploadResult.uploadedFile)>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfset local.csvFile = replaceNoCase(local.uploadResult.uploadedFile, ".xls", ".csv")>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>

			<!--- if only one sheet --->
			<cfif arrayLen(local.parseResult.arrSheets) is 1>
				<cfset local.lstDateColumns = 'DateEntered'>
				<cfset local.parseResult = local.objImport.parseXLSSheet(strFilePath=local.uploadResult.uploadedFile,strFilePathCSV=local.csvFile,sheetIndex=0,lstDateColumns=local.lstDateColumns)>
				<cfset local.returnStruct.success = local.parseResult.success>
				<cfif NOT local.returnStruct.success>
					<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
					<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
					<cfreturn local.returnStruct>
				</cfif>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 7>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>
			<!--- Files was successfully read in --->
			<cfset local.uploadResult.uploadedFile = local.csvFile>
		</cfif>

		<!--- parse CSVs --->
		<cfset local.parseResult = local.objImport.parseCSV(stFilePath=local.uploadResult.uploadedFile, stFilePathTmp="#local.uploadResult.uploadedFilePath#/#local.uploadResult.uploadFilenameWithoutExt#Parsed.csv")>
		<cfif local.parseResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 2>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		<cfelse>
			<cfset local.uploadResult.columnNames = ToString(local.parseResult.strTableColumnNames)>
		</cfif>

		<!--- import files --->
		<cfset local.importToSQLResult = importConsentListMembersToSQL(strImportFile=local.uploadResult, columnNames=local.uploadResult.columnNames, 
			orgID=arguments.event.getValue('mc_siteinfo.orgID'), consentListIDList=arguments.event.getValue('clid',''))>
		<cfif local.importToSQLResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfif isDefined("local.importToSQLResult.importResultXML")>
				<cfset local.returnStruct.errorCode = 5>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.importResultXML)>
			<cfelse>
				<cfset local.returnStruct.errorCode = 4>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.errMsg)>
			</cfif>
			<cfreturn local.returnStruct>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="importConsentListMembersToSQL" access="private" output="false" returntype="struct">
		<cfargument name="strImportFile" type="struct" required="yes">
		<cfargument name="columnNames" type="string" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="consentListIDList" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { isErr=0, errMsg='', errCount=0 } >

		<cftry>
			<cfquery name="local.qryImport" datasource="#application.dsn.platformmail.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @orgID int, @importResult xml, @enteredByMemberID int, @consentListIDList varchar(max);
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					SET @consentListIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.consentListIDList#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_consentListMembersImport') IS NOT NULL 
							DROP TABLE ##mc_consentListMembersImport;
						CREATE TABLE ##mc_consentListMembersImport (
							<cfloop list="#arguments.columnnames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(arguments.columnnames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##mc_consentListMembersImport 
							FROM '#arguments.strImportFile.uploadedFilePathUNC#\#arguments.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- preparing table for import
					BEGIN TRY
						ALTER TABLE ##mc_consentListMembersImport ALTER COLUMN rowID INT NOT NULL;
						<cfif not listFindNoCase(arguments.columnnames,"DateEntered",chr(7))>
							ALTER TABLE ##mc_consentListMembersImport ADD DateEntered DATETIME NULL;
						</cfif>
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to prepare table for import." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.email_importConsentListMembers @orgID=@orgID, @consentListIDList=@consentListIDList, @enteredByMemberID=@enteredByMemberID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					
					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##mc_consentListMembersImport') IS NOT NULL 
						DROP TABLE ##mc_consentListMembersImport;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.rs.errCount = local.qryImport.errCount>
			<cfif local.rs.errCount gt 0>
				<cfset local.rs.isErr = 1>
			</cfif>

			<cfcatch type="Any">
				<cfset local.rs.isErr = 1>
				<cfset local.rs.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>
		<cfreturn local.rs>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="refreshGridFn" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Import Results</h4>

			<cfif NOT arguments.strResult.success>
				<div class="alert alert-danger">
					<div class="font-weight-bold mb-3">The import was stopped and requires your attention.</div>
					<cfif arguments.strResult.errorCode is 5>
						<cfset local.arrErrors = XMLSearch(arguments.strResult.errorInfo[arguments.strResult.errorCode],"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 200>
							<b>Only the first 200 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 200>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
					</cfif>
					<div class="mt-2">
						<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
						<button type="button" name="btnCancel" class="btn btn-sm btn-secondary" onclick="top.MCModalUtils.hideModal();">Cancel</button>
					</div>
				</div>
			<cfelse>
				<div class="alert alert-success">
					<p class="font-weight-bold">Import Has Been Completed</p>
					<div>The import of the uploaded file has been completed.</div>
					<div class="mt-2">
						<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
						<button type="button" name="btnCancel" class="btn btn-sm btn-secondary" onclick="top.MCModalUtils.hideModal();">Cancel</button>
					</div>
				</div>
				<script type="text/javascript">
					top.#arguments.refreshGridFn#();
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getConsentListMemberCount" access="public" output="false" returntype="numeric">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="listMode" type="string" required="true">

		<cfset var qryConsentListMembers = "">

		<cfif arguments.listMode EQ 'Opt-Out'>
			<cfset arguments.listMode = "Opt-Out,GlobalOptOut">
		</cfif>

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="qryConsentListMembers">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			SELECT COUNT(DISTINCT lm.consentListMemberID) AS consentListMemberCount
			FROM dbo.email_consentListMembers AS lm
			INNER JOIN dbo.email_consentLists AS l ON l.consentListID = lm.consentListID
				AND l.[status] = 'A'
			INNER JOIN dbo.email_consentListModes AS clm ON clm.consentListModeID = l.consentListModeID
			INNER JOIN dbo.email_consentListTypes AS clt ON clt.consentListTypeID = l.consentListTypeID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me ON me.orgID = @orgID
				AND me.email = lm.email
			INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID
				AND m.memberID = me.memberID
				AND m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			WHERE clt.orgID = @orgID
			AND clm.modeName in (<cfqueryparam value="#arguments.listMode#" list="true" cfsqltype="CF_SQL_VARCHAR">);
				
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryConsentListMembers.consentListMemberCount)>
	</cffunction>

	<cffunction name="getAvailableMemberConsentLists" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="listMode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="emailAddress" type="string" required="true">

		<cfset var local = structNew()>

		<cfif arguments.listMode EQ 'Opt-Out'>
			<cfset arguments.listMode = "Opt-Out,GlobalOptOut">
		</cfif>

		<cfquery name="local.qryConsentLists" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
			DECLARE @tmpIncConsentLists TABLE (consentListID int);

			INSERT INTO @tmpIncConsentLists (consentListID)
			SELECT DISTINCT lm.consentListID
			FROM dbo.email_consentListMembers AS lm
			INNER JOIN dbo.email_consentLists AS l ON l.consentListID = lm.consentListID
				AND l.[status] = 'A'
			INNER JOIN dbo.email_consentListModes AS clm ON clm.consentListModeID = l.consentListModeID
			INNER JOIN dbo.email_consentListTypes AS clt ON clt.consentListTypeID = l.consentListTypeID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me ON me.orgID = @orgID
				AND me.email = lm.email
			INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID 
				AND m.memberID = me.memberID
				AND m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			WHERE clt.orgID = @orgID
			AND me.email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailAddress#">
			AND clm.modeName in (<cfqueryparam value="#arguments.listMode#" list="true" cfsqltype="CF_SQL_VARCHAR">);

			SELECT l.consentListID, l.consentListName, clt.consentListTypeName, case WHEN clt.consentListTypeName = 'Global Lists' then 1 else 2 end as groupPriority
			FROM dbo.email_consentLists as l
			INNER JOIN dbo.email_consentListModes as m on m.consentListModeID = l.consentListModeID
			INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = l.consentListTypeID
			LEFT OUTER JOIN @tmpIncConsentLists AS tmp ON tmp.consentListID = l.consentListID
			WHERE m.modeName IN (<cfqueryparam value="#arguments.listMode#" list="true" cfsqltype="CF_SQL_VARCHAR">)
			AND clt.orgID = @orgID
			AND l.[status] = 'A'
			AND tmp.consentListID IS NULL
			ORDER BY groupPriority, clt.consentListTypeName,l.consentListName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = { "success":true, "arroptinconsentlists":[], "arroptoutconsentlists":[] }>

		<cfif arguments.listMode EQ 'Opt-In'>
			<cfoutput query="local.qryConsentLists" group="consentListTypeName">
				<cfset local.thisType = { 
					"consentListTypeName": local.qryConsentLists.consentListTypeName,
					"arrconsentlists":[] 
					}>
				<cfoutput>
					<cfset local.thisType.arrconsentlists.append({
						"consentlistid": local.qryConsentLists.consentListID,
						"consentlistname": local.qryConsentLists.consentListName,
						"consentlisttypename": local.qryConsentLists.consentListTypeName
					})>
				</cfoutput>
				<cfset local.returnStruct.arroptinconsentlists.append(local.thisType)>
			</cfoutput>
		<cfelse>
			<cfoutput query="local.qryConsentLists" group="consentListTypeName">
				<cfset local.thisType = { 
					"consentListTypeName": local.qryConsentLists.consentListTypeName,
					"arrconsentlists":[] 
					}>
				<cfoutput>
					<cfset local.thisType.arrconsentlists.append({
						"consentlistid": local.qryConsentLists.consentListID,
						"consentlistname": local.qryConsentLists.consentListName,
						"consentlisttypename": local.qryConsentLists.consentListTypeName
					})>
				</cfoutput>
				<cfset local.returnStruct.arroptoutconsentlists.append(local.thisType)>
			</cfoutput>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>