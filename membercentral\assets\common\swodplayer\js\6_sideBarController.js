(function(){


    angular.module('module_1').controller('navController', function ($scope, $rootScope, $http, $window, $timeout, cssData, components, SeminarDataService,APIRequestService, AppManager) {
    var self = this;
    $rootScope. alreadyInitialized = 0;
      $scope.init = function(initFrom) {
            $rootScope.examData = [];
            $rootScope.sideBarClick = 0;
            $rootScope. alreadyInitialized = 1;
            $rootScope. enrollmentCreditObjLength = 0;
            $rootScope. dateCompleted = '';
            $rootScope.showBottomMessage = 0;
            setTimeout(function(){
                var promise = APIRequestService.getSeminarforPlayer(1);
                promise.then(function (response) {
                    self.materialTitle = response.returnData.seminarName;
                    self.materialPairs = SeminarDataService.materialPairs;	
                    AppManager.seminarDetailsObjNew = response.returnData;
                    AppManager.seminarCompletionCheckObjNew = response.returnData.completionCheckObj.returnData;	
                    $rootScope.dateCompleted = response.returnData.dateCompleted;
                    if(seminarType != 'swl'){
                        AppManager.allEvaluationCompleted = (AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allEvaluationCompleted == 1);
                        AppManager.allPreTestCompleted = (AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allPreTestCompleted == 1);
                        AppManager.allPostTestCompleted = (AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allPostTestCompleted == 1);
                    }else{
                        AppManager.allEvaluationCompleted = (AppManager.seminarDetailsObjNew.allEvaluationCompleted == 1);
                        AppManager.allPreTestCompleted = (AppManager.seminarDetailsObjNew.allPreTestCompleted == 1);
                        AppManager.allPostTestCompleted = (AppManager.seminarDetailsObjNew.allPostTestCompleted == 1);
                        if(AppManager.seminarDetailsObjNew.currentLoadPoint == "postTest"){
                            AppManager.allPreTestCompleted = true;
                        }
                        if(AppManager.seminarDetailsObjNew.currentLoadPoint == "evaluation"){
                            AppManager.allPreTestCompleted = true;
                            AppManager.allPostTestCompleted = true;
                        }
                        if(AppManager.seminarDetailsObjNew.currentLoadPoint == ""){
                            AppManager.allPreTestCompleted = true;
                            
                            if(AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allPreTestCompleted == 1){
                                AppManager.seminarDetailsObjNew.currentLoadPoint = 'preTest';
                            }
                            if(AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allPostTestCompleted == 1){
                                AppManager.seminarDetailsObjNew.currentLoadPoint = 'postTest';
                            }
                            if(AppManager.seminarDetailsObjNew.completionCheckObj.returnData.allEvaluationCompleted == 1){
                                AppManager.seminarDetailsObjNew.currentLoadPoint = 'evaluation';
                            }
                        }
                        
                    } 
                    AppManager.isSeminarFinalCompleted = AppManager.seminarDetailsObjNew.isCompleted;
                    AppManager.isCompleted = AppManager.seminarDetailsObjNew.completionCheckObj.returnData.isCompleted;
                    if(AppManager.seminarDetailsObjNew.enrollmentCreditObj.length){
                        $rootScope.enrollmentCreditObjLength = AppManager.seminarDetailsObjNew.enrollmentCreditObj.length;
                    }
                    
                    if(seminarType != 'swl' && SeminarDataService.materialPairs.length > 0){
                        $scope.materialTitles = SeminarDataService.materialPairs[0].materialTitles;
                    }else{
                        $scope.materialTitles = [];
                    }                
                    var itK = 0;
                    for(var topic of  SeminarDataService.materialPairs){ 
                        if(topic.type == 'ppt'){
                            AppManager.currentPaperIndex =  itK;
                            $rootScope.hasPaper = 1;
                            break;
                        }
                        itK++;					
                    }    
                    if(seminarType != 'swl'  ){                        
                        self.setUpCompleteTree(initFrom);
                    }       
                });
            } ,500);
		}
        self.setUpCompleteTree= function(initFrom){  
            _obj = $('.materials');
            _obj.next('#scrollArrow').hide('slow');  
            var mateiralLen = SeminarDataService.materialPairs.length;
                var total = 0;
                var materialsCompletedPercentLength = 0;
                $rootScope.showCompleteStart = '';
                for (var i=0; i<mateiralLen; i++) {
                    var materialType = SeminarDataService.materialPairs[i].type;
                    // show viewed percent for video and audio only
                    if(materialType.indexOf('video') > -1 || materialType.indexOf('audio') > -1) {
                        //show mateiral percent
                        var streamAccessDetails = SeminarDataService.materialPairs[i].accessDetails.join("");
                        if (streamAccessDetails.lastIndexOf('1') > -1) {
                            var noOfOccurencesOfOne = 0;
                            var materialAccessDetails = SeminarDataService.materialPairs[i].accessDetails;
                            for (var k=0; k<materialAccessDetails.length; k++) {
                                if (materialAccessDetails[k] == 1) {
                                    noOfOccurencesOfOne++;
                                }
                            }
                            var curPercent =  (noOfOccurencesOfOne / streamAccessDetails.length) *100;
                            $rootScope.materialsCompletedPercent[i] = Math.round(curPercent);
                            materialsCompletedPercentLength = materialsCompletedPercentLength + 1;
                            total += Math.round(curPercent);
                        }else{
                            materialsCompletedPercentLength = materialsCompletedPercentLength + 1;
                        }
                    }
                }

                if(materialsCompletedPercentLength > 0){
                    $rootScope.materialsCompletedPercentAvg = total/materialsCompletedPercentLength;
                }else{
                    $rootScope.materialsCompletedPercentAvg = 0;
                }
                
                $rootScope.learningRequirementsCompleted = '';
                if($rootScope.materialsCompletedPercentAvg <  AppManager.seminarCompletionCheckObj.mediaRequiredPct){
                    $rootScope.learningRequirementsCompleted = 'learningRequirementsIncomplete';
                }
                if( AppManager.mediaListRendered  == 0){                    
                  self.sortMaterialAccTotitles(self.materialPairs);
                }
                setTimeout(function(){
                    if(initFrom != 2){
                        $rootScope.firstLoad = true;
                        angular.element(angular.element('.videoPpt')[0]).trigger('click');
                    }
                },1500);
                
                $rootScope.certificateLink = '';
                $rootScope.seminarCompletedFlag = 0;
                if (AppManager.isSeminarFinalCompleted == 0 && (!AppManager.semninarHasPostTest) && (!AppManager.semninarHasEvaluation)) {
                    $rootScope.showCompleteStart = 1;
                    $rootScope.showBottomMessage = 1;
                }else if (AppManager.isSeminarFinalCompleted == 1 && (!AppManager.semninarHasPostTest) && (!AppManager.semninarHasEvaluation)) {
                    $rootScope.showCompleteStart = 2;
                    if(AppManager.seminarDetailsObj.offerCertificate == 1) {
                        $rootScope.certificateLink = AppManager.seminarDetailsObj.certificateURL;
                    }
                    $rootScope.seminarCompletedFlag = 1;

                }else if (AppManager.isSeminarFinalCompleted == 1 ) {
                    if(AppManager.seminarDetailsObj.offerCertificate == 1){
                        $rootScope.certificateLink = AppManager.seminarDetailsObj.certificateURL;
                    }
                    $rootScope.seminarCompletedFlag = 1;
                } else if ((AppManager.isCompleted == 1 && AppManager.isSeminarFinalCompleted == 1 ) && (AppManager.allPostTestCompleted) && (AppManager.allEvaluationCompleted)) {
                    $rootScope.certificateLink = AppManager.seminarDetailsObj.certificateURL;
                } else if ((AppManager.isCompleted == 1) && (AppManager.allPostTestCompleted) && (!AppManager.allEvaluationCompleted)) {
                } else if ((AppManager.isCompleted == 1) && (!AppManager.allPostTestCompleted)) {
                    if (!self.postTestFailed || AppManager.allPostTestCompleted == false) {
                    } else {
                        if(AppManager.seminarDetailsObj.offerCertificate == 1) {
                            $rootScope.certificateLink = AppManager.seminarDetailsObj.certificateURL;
                        }
                        $rootScope.seminarCompletedFlag = 1;
                    }
                }

                if(AppManager.semninarHasPostTest == 1){
                    APIRequestService.loadFormsByLoadPoint( 'posttestall' ).then( function( data ) {
                        if( data.userLoggedIn == 1 ) {
                            for(var k=0; k< data.returnData.examObj.length; k++){

                                if(data.returnData.strFormsMessage.length > 0){
                                    if(data.returnData.examObj.length > 0){
                                        if(data.returnData.examObj[0].PASSINGPCT == -1){
                                            data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                                        }else{
                                            if(data.returnData.strFormsMessage[0].ATTEMPTSREMAINING == 0 && data.returnData.strFormsMessage[0].PASSFAIL == 'Failed'){
                                                data.returnData.examObj[k].isCompletedExam = 'fail';
                                            }else{
                                                data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                                            } 
                                        }

                                    }else if(data.returnData.strFormsMessage[0].ATTEMPTSREMAINING == 0 && data.returnData.strFormsMessage[0].PASSFAIL == 'Failed'){
                                        data.returnData.examObj[k].isCompletedExam = 'fail';
                                    }else{
                                        data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                                    }
                                    
                                } else if(AppManager.allPostTestCompleted){
                                    data.returnData.examObj[k].isCompletedExam = 'complete';
                                }else{
                                    data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                                }
                                
                                data.returnData.examObj[k].examType = 'posttest';
                                if(data.returnData.examObj.length == 1){
                                    $rootScope.hasOnePostTest = 1;
                                }else{  
                                   $rootScope.hasOnePostTest = 0;                          
                                }
                                $rootScope.examData.push(data.returnData.examObj[k]);
                            }
                            self.evaluationDataSet();
                        } else {
                            AppManager.disconnectedFromServer();
                        }
                    });
                }else{
                    self.evaluationDataSet();
                }
                if( AppManager.completeProcessing == 1){
                    $rootScope.startCompleteProcess({},0);
                    AppManager.completeProcessing = 0;
                }
        }
        self.evaluationDataSet = function(){            
                
            if(AppManager.semninarHasEvaluation == 1){
                APIRequestService.loadFormsByLoadPoint( 'evaluationall' ).then( function( data ) {
                    if( data.userLoggedIn == 1 ) {
                        AppManager.evaluationAllObj = data.returnData.examObj;
                        for(var k=0; k< data.returnData.examObj.length; k++){
                            if(AppManager.allEvaluationCompleted || data.returnData.examObj[k].FORMATTENDED == 1){
                               data.returnData.examObj[k].isCompletedExam = 'evComplete';
                           }else{
                               data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                           }
                           
                           data.returnData.examObj[k].examType = 'evaluation';
                           $rootScope.examData.push(data.returnData.examObj[k]);
                           if(data.returnData.examObj.length == 1){
                             $rootScope.hasOneEvaluation = 1;
                           }else{  
                            $rootScope.hasOneEvaluation = 0;                          
                           }
                           angular.element('.playerWell #leftBox').removeClass('leftBoxWrap');
                           angular.element('#leftLoading').hide();
                           angular.element('#viewModeBtn').removeClass('viewModeBtnHide');
                       }
                    } else {
                        AppManager.disconnectedFromServer();
                    }
                });                
            }else{
                angular.element('.playerWell #leftBox').removeClass('leftBoxWrap');
                angular.element('#viewModeBtn').removeClass('viewModeBtnHide');
                angular.element('#leftLoading').hide();
            }

        }
        $rootScope.evaluationDataReset = function(){            
            APIRequestService.loadFormsByLoadPoint( 'evaluationall' ).then( function( data ) {
                if( data.userLoggedIn == 1 ) {
                    AppManager.evaluationAllObj = data.returnData.examObj;
                    for(var k=0; k< data.returnData.examObj.length; k++){
                        if(AppManager.allEvaluationCompleted || data.returnData.examObj[k].FORMATTENDED == 1){
                           data.returnData.examObj[k].isCompletedExam = 'evComplete';
                       }else{
                           data.returnData.examObj[k].isCompletedExam = 'Incomplete';
                       }
                       
                       data.returnData.examObj[k].examType = 'evaluation';
                       $rootScope.examData.push(data.returnData.examObj[k]);
                       if(data.returnData.examObj.length == 1){
                            $rootScope.hasOneEvaluation = 1;
                       }else{  
                            $rootScope.hasOneEvaluation = 0;                          
                       }
                   }
                } else {
                    AppManager.disconnectedFromServer();
                }
            });  
        }
		$rootScope.tabClickComplete = function (e, i,tabName) {
			$scope.tabClick(e,i,tabName)
		}
		$rootScope.processCompleteSteps = function (e) {
			if($rootScope.showCompleteStart == 1 &&  AppManager.completeProcessing == 0 &&  $rootScope.learningRequirementsCompleted == ''){
                if(seminarType != 'swl'){                     
                    angular.element('.startCompleteProcess .lable').html('Processing...');
                    AppManager.completeProcessing = 1;
					AppManager.finalCheckSeminarforCompletion(AppManager.userReportedTimeSpent);
				}   
            }
		}
        self.processCompletion = function (e) {
			if($rootScope.showCompleteStart == 1){
                AppManager.checkCompletionForSeminar();
            }
		}
        $scope.updateMediaPercent= function(i){  
            var streamAccessDetails = SeminarDataService.materialPairs[i].accessDetails.join("");
            if(streamAccessDetails.lastIndexOf('1') > -1){  
                noOfOccurencesOfOne = 0;
                materialAccessDetails = SeminarDataService.materialPairs[i].accessDetails;
                for(var k=0; k<materialAccessDetails.length; k++){
                    if(materialAccessDetails[k] == 1) noOfOccurencesOfOne++;
                }
                curPercent =  (noOfOccurencesOfOne / streamAccessDetails.length) *100;
                $rootScope.materialsCompletedPercent[i] = Math.floor(curPercent);
                SeminarDataService.materialPairs[i].percentCompleted = Math.floor(curPercent);
                SeminarDataService.materialPairs[i].completeClass = '';
                if(SeminarDataService.materialPairs[i].percentCompleted == 100){
                    SeminarDataService.materialPairs[i].completeClass = 'success';
                }else if(SeminarDataService.materialPairs[i].percentCompleted == 0){
                    SeminarDataService.materialPairs[i].completeClass = 'zeroPercent';
                }else{
                    SeminarDataService.materialPairs[i].completeClass = 'inProgress';
                }
            }
        }
		self.findArrayIndex = function (array, predicateFunction) {
			var length = array == null ? 0 : array.length;
			if (!length) {
				return -1;
			}
			var index = -1;
			for (var i = 0; i < array.length; ++i) {
				if(predicateFunction(array[i])) {
					index = i;
					break;
				}
			}
			return index;
		}
        self.sortMaterialAccTotitles = function(materialPairs){
            for(var j=0; j<$scope.materialTitles.length; j++){
                for(var i=0; i<materialPairs.length; i++){
                    if(materialPairs[i].titleid == $scope.materialTitles[j].titleid){
                        $scope.materialTitles[j].pairs[$scope.materialTitles[j].pairs ? $scope.materialTitles[j].pairs.length:0]= self.materialPairs[i];
                        $scope.materialTitles[j].indexes[$scope.materialTitles[j].indexes ? $scope.materialTitles[j].indexes.length:0]= i;
                    }
                }                
            }
            AppManager.mediaListRendered = 1;
        }
        //init();
        /*chapters show/hide in 'Learning Materials'*/
        $scope.chaptersClick = function (e, i) {
            var _this = e.currentTarget;
            angular.element(_this).parent().toggleClass('decreaseHeight chaptersOpen');
            var id = angular.element(_this).attr('id').charAt(angular.element(_this).attr('id').length-1);
            angular.element(_this).find('#arrowDown_'+id).toggleClass('arrowRight');
            angular.element('.nav #topics_'+id).toggleClass('height_zero');
        }
            /*Hiding video and showing only controls for audio*/
        $scope.showOnlyMediaControls = function () {
            cssData.controller_1.leftVideoHeight = 7;
            cssData.videoController.videoHeight = 0;
            cssData.controller_1.leftWidth = 37;
            cssData.videoController.videoSliderUiMarginTop = 2;
            cssData.videoController.volumeLableBottom = 1;
            cssData.videoController.videoControlsHeight = 100;
            cssData.videoController.videoControllsBgColor = 'black';
            cssData.noteController.change = !cssData.noteController.change;
		}
        $scope.noteTopAdjust = function(){
            angular.element('#note-panel,.leftTabContent').removeClass('notesHide');
            $('#note-panel').fadeIn().animate({top:0}, 800, function(){});
        }
		$scope.scrollToContent = function () {
			$('html, body').animate({
				scrollTop: $("#rightBase").offset().top
			}, 800);
		}
            /*chapter selection from 'Learning Materials'*/
        $rootScope.topicClick = function (e, i, revert, scope) {
			$rootScope.showMedia = false;
            if(e.target != undefined){
                e.stopPropagation();
            }
            
            var _this = e.currentTarget;
            var index = angular.element(_this).attr('index');
			$rootScope.videoPlayIndex = index;			
            _this = angular.element('.videoPpt[index=' + index + ']');
            var select = angular.element('.nav .bottom .videoPpt.select');
            $rootScope.showingControlls = false;
            $rootScope.showingControllsD = false;
            if(select.parent().index() !== angular.element(_this).parent().index() || angular.element(_this).hasClass('pptTopic') || (select.length && revert)) 
            {
                angular.element('.nav .videoPpt').removeAddClassEle('select', _this);
                angular.element('#right .screen.learn .ppts').displayHide();
                if(AppManager.seminarLayout != "largeVideo")
                {
					var paperObj = $.grep(SeminarDataService.materialPairs, function (e) { return e.fileid == SeminarDataService.materialPairs[i].syncPaperId; });
					if (paperObj.length) {
						var sync = self.findArrayIndex(SeminarDataService.materialPairs, function(fo) { 
							return fo.fileid == paperObj[0].fileid;
						});
					}
                    var noSync = false;
                    if(sync == undefined){
						
                        for(var j = $rootScope.videoPlayIndex ; j < SeminarDataService.materialPairs.length; j++){
                            if(SeminarDataService.materialPairs[j]['type'] == "ppt"){
                                var sync = j;
                                break;
                            }
                        }
                        noSync = true;
                    }
                    var ppt = angular.element('#right .screen.learn #ppts_' + sync);
					if (ppt.length) {
                        ppt.displayShow();
						AppManager.currentPaperIndex = sync;
                        var path = SeminarDataService.materialPairs[sync].path;
                        var img = ppt.find('.bottom .img');
                        if ($rootScope.currentPage[sync]) {
                            var imgpath = path + SeminarDataService.materialPairs[sync].fileURL[0];
                            img.css({backgroundImage: 'url("'+imgpath +'")'});
                            if(noSync){
                                var imgDummy = angular.element('#right .screen.learn #referenceImg');
                                imgDummy.attr('src', imgpath);
            
                                $timeout(function() {
                                    angular.element('#learn-controller').scope().imageLoadCallBack();
                                }, 2000);
                            }
                        }
                        $rootScope.seminar = false;
                        $rootScope.pptShow = true;
                    }else{
                        $rootScope.seminar = true;
                        $rootScope.pptShow = false;
                    }
                }
                if (angular.element(_this).hasClass('pptTopic')) {
                    angular.element('#right .screen.learn #ppts_' + i).displayShow();
                }

                if (angular.element(_this).attr('type').indexOf('video') !== -1) {

                    if(AppManager.seminarLayout == "largeVideo" || AppManager.seminarLayout == "normalVideo"){
                        //console.log('buffer shown!!');
                        angular.element('.video .buffer').displayShow();                     
                    } 
                    AppManager.addActivityLogEntry("User switched video stream to: " + SeminarDataService.materialPairs[i].fileName, false);
                    /*this block is for video/large_video*/
                   // $rootScope.showingControlls = false;
                    angular.element('.video').attr({type: angular.element(_this).attr('type'), i: i});
                    /*resetting video to the left-panel from the right-panel screen*/
                    if (!angular.element('#right .video').length) {
                        angular.element('#video').removeClass('large_video');
                        angular.element('.video').insertBefore(angular.element('#afterVideo'));
                    }
                    /*if video src attribute value is not equal to path which we are going to initiate*/
                    if (!angular.element('video').length || angular.element('video').attr('src').indexOf([SeminarDataService.materialPairs[i]['media'][1]] + '.mp4') === -1) {
                        angular.element('.video .play').addClass('pause');
                        var tempObj = SeminarDataService.materialPairs[i];
                            var promise = APIRequestService.getURLForMediaPlayer(tempObj.fileid, tempObj.fileURL,'video');
                             promise.then(function (data) {
                                var mediaURL = data.MEDIAURL;
                                AppManager.addDebugLogEntry("Received new video URL from server for " + SeminarDataService.materialPairs[i].fileName + ": " + mediaURL, false);
                               // console.log("getURLForMediaPlayer : "  + JSON.stringify(data))
                                components.mediaInit([mediaURL], i,angular.element(_this).attr('type'));

                            }); 
                        
                        // for offline check
                        /*var mediaURL = tempObj.media + ".mp4" 
                        components.mediaInit([mediaURL], i,angular.element(_this).attr('type'));*/

                    }
                    cssData.videoController.videoControllsBgColor = 'rgba(0, 0, 0, 0.6)';
                    cssData.noteController.change = !cssData.noteController.change;
                    angular.element('.video').removeClass('height_zero');
                    $('.flex-video').css('margin-bottom', '0%');
                    var material = SeminarDataService.materialPairs[i];										
/*                    if(!material.isMediaSync){
                        $rootScope.seminar = false;
                        $rootScope.pptShow = true;
                    }*/ 
                    $scope.noteTopAdjust();
                } else if (angular.element(_this).attr('type') === 'audio') {
                    AppManager.addActivityLogEntry("User switched audio stream to: " + SeminarDataService.materialPairs[i].fileName, false);
                    /*this block is audio*/
                    angular.element('.video').attr({type: angular.element(_this).attr('type')});
                    /*resetting video to the left-panel from the right-panel screen*/
                    if (!angular.element('#right .video').length) {
                        angular.element('#video').removeClass('large_video');
                        angular.element('.video').insertBefore(angular.element('#afterVideo'));
                    }
                    //if ((!components.media || !components.media.audio || !components.media.audio.length) || (components.media && components.media.audio && !components.media.audio.length && angular.element(components.media.audio[0]).attr('src').indexOf([SeminarDataService.materialPairs[i]['media'][1]] + '.mp3') === -1)) {
                    if (!angular.element('audio').length || angular.element('audio').attr('src').indexOf([SeminarDataService.materialPairs[i]['media'][1]] + '.mp3') === -1) {
                            var tempObj = SeminarDataService.materialPairs[i]; 
                            var promise = APIRequestService.getURLForMediaPlayer(tempObj.fileid, tempObj.fileURL,'audio');
                            promise.then(function (data) {
                            var mediaURL = data.MEDIAURL;
                                AppManager.addDebugLogEntry("Received new audio URL from server for " + SeminarDataService.materialPairs[i].fileName + ": " + mediaURL, false);
                                components.mediaInit([mediaURL], i,angular.element(_this).attr('type'));
                            });  

                        /* for offline check */
                    /*var mediaURL = tempObj.media + ".mp3" 
                    components.mediaInit([mediaURL], i,angular.element(_this).attr('type'));*/

                    }
                    $scope.showOnlyMediaControls();
                    /*reset left and right panel widths to initial*/
                    $rootScope.zoomCount = 1;

                    angular.element('#right').removeClass('large-6 large-7 large-8').addClass("large-8");
                    angular.element('.video').removeClass('height_zero');
                    
                    $scope.noteTopAdjust();
                }
                if (angular.element(_this).attr('type') === 'large_video') {
                    AppManager.addActivityLogEntry("User switched video stream to: " + SeminarDataService.materialPairs[i].fileName, false);
                    $rootScope.pptShow = false;
                    /*this block is for only large_video*/
                    angular.element('.video').attr({type: angular.element(_this).attr('type')});
                    /*appending video from the left-panel to the right-panel screen*/
                    angular.element('#video').addClass('large_video');
                    angular.element('.video').removeClass('videoRemove');
                    $scope.showOnlyMediaControls();

                    cssData.videoController.videoControllsBgColor = 'rgba(0, 0, 0, 0.6)';
                    cssData.noteController.change = !cssData.noteController.change;
                    /*reset left and right panel widths to initial*/
                    $rootScope.zoomCount = 1;

                    angular.element('#right').removeClass('large-6 large-7 large-8').addClass("large-8");
					angular.element('.video').removeClass('height_zero');
                    if(angular.element('#activity .seminarCompleted.elementsSem').hasClass('displayHide') && angular.element('#activity .seminarCompleteProcess.elementsSem').hasClass('displayHide')){                        
                        $scope.scrollToContent();
                    }
                } else if (angular.element(_this).attr('type') === 'ppt') {
                    AppManager.addActivityLogEntry("User switched paper to: " + SeminarDataService.materialPairs[i].fileName, false);
                    /*this block is for ppt which is in sync with video/audio*/
                        $rootScope.seminar = false;
                        $rootScope.pptShow = true;
                        $rootScope.pageNavClick(false,i)

                        var imgpath = path + SeminarDataService.materialPairs[i].fileURL[0];
                        if(noSync){
                            var imgDummy = angular.element('#right .screen.learn #referenceImg');
                            imgDummy.attr('src', imgpath);
                            $timeout(function() {
                                angular.element('#learn-controller').scope().imageLoadCallBack();
                            }, 2000);
                        }

                    if (!angular.element('#right .video').length) {
                        angular.element('.video').insertBefore(angular.element('#afterVideo'));
                        angular.element('#video').removeClass('large_video');
                        angular.element('.video').addClass('videoRemove height_zero');
                        cssData.controller_1.leftVideoHeight = 0;
                        cssData.noteController.change = !cssData.noteController.change;
						try {components.media.stop(0)} catch (e) {}
                    }
					
					if(angular.element('#activity .seminarCompleted.elementsSem').hasClass('displayHide') && angular.element('#activity .seminarCompleteProcess.elementsSem').hasClass('displayHide')){                        
                        $scope.scrollToContent();
                    }
                }
				setTimeout(function(){
					AppManager.currentVideoIndex = angular.element('.video #video > div').attr('index');
					if(AppManager.currentVideoIndex != undefined){
                        if(SeminarDataService.materialPairs[AppManager.currentVideoIndex] != undefined){
                            $rootScope.arrSyncPointsToDisp = SeminarDataService.materialPairs[AppManager.currentVideoIndex].syncArray;
                            $rootScope.syncPointsPaper = SeminarDataService.materialPairs[AppManager.currentPaperIndex ].fileTitle;
                        }
						
					}else{						
                        if(AppManager.arrVideoSync[0].fileArray[0] != undefined){
                            $rootScope.arrSyncPointsToDisp = AppManager.arrVideoSync[0].fileArray[0].syncArray;
                        }
						if(AppManager.currentPaperIndex == undefined){
							curPpdIdx = 0;
						}else{
							curPpdIdx = AppManager.currentPaperIndex;
						}
						$rootScope.syncPointsPaper = SeminarDataService.materialPairs[curPpdIdx].fileTitle;
						
					}
					angular.element('.syncPointsPaper').html($rootScope.syncPointsPaper);
				},1500);
            } else if (revert) {
                angular.element('#video').removeClass('large_video');
                angular.element('#video').append("<video src=''></video>")
                angular.element('.video').insertBefore(angular.element('#afterVideo'));
                angular.element('.video').removeClass('height_zero');
                $('.flex-video').css('margin-bottom', '0%');
                angular.element('.video .play').addClass('pause');
                angular.element('.video .top .slider').off('slidecreate slidechange slide slidestart slidestop');
                angular.element('.video .top .slider').slider('value', 0);
                $timeout(function () {angular.element('.video .time .lable').html('00:00:00')}, 0)
            }
            /*trigger window resize to align sliders and play video buttons vertically*/
            angular.element(window).trigger('resize');
            $rootScope.zoomCount = 1;
            if ($rootScope.zoomCount == 1) {
                $('.controls .videoZoomInOutParent .videoZoomOut').css({opacity: '1', cursor: 'pointer'});
                $('.controls .videoZoomInOutParent .videoZoomIn').css({opacity: '0.5', cursor: 'default'});
            }
            angular.element(window).trigger('resize');
            $('.controls .top .sliderUi').css('width', $('.controls').width() - $('.videoZoomInOutParent').width() - 24 + 'px');
            angular.element('#activity').removeClass('zoomMode-1 zoomMode-2 zoomMode-3');
            angular.element('#activity').addClass('zoomMode-'+$rootScope.zoomCount);
            $rootScope.mediaType = angular.element(_this).attr('type');
            
            angular.element('#lm-popup').addClass('displayHide');

            if(angular.element('#closeSideBar').is(':visible'))
                angular.element('#closeSideBar').trigger('click');

            if(scope != undefined)
                $scope.tabClick(e,0,scope);

            if( angular.element(window).width() < 1024){
                $timeout(function() {
                    angular.element('#leftBox').removeClass('reachUp');
                }, 1500);
                
            }
            
        }
		$rootScope.getSyncPointsPaper = function (fileId) {
			return SeminarDataService.materialPairs[AppManager.currentPaperIndex ].fileTitle;
		}
		$rootScope.startCompleteProcess = function (e,i) {
			if(AppManager.completByPass == 0){
                hideShowPopUpElements('.creditExpired.elementsSem');
            }else{
                if($rootScope.showCompleteStart == '' && $rootScope.learningRequirementsCompleted != 'learningRequirementsIncomplete'){
                    hideShowPopUpElements('.seminarCompleteProcess.elementsSem');
					angular.element('#activity .seminarCompleteProcess.elementsSem').removeClass('displayHide').removeClass('hide');
                    angular.element("#examWrapper").show();
                    setExamPopup();
                    var scope = angular.element('.seminarCompleteProcess').scope();
                    scope.init();
                } else if($rootScope.showCompleteStart == 2){
                    hideShowPopUpElements('.seminarCompleteProcess.elementsSem');
					angular.element('#activity .seminarCompleteProcess.elementsSem').removeClass('displayHide').removeClass('hide');
                    angular.element("#examWrapper").show();
                    setExamPopup();
                    var scope = angular.element('.seminarCompleteProcess').scope();
                    scope.init();
                }
            }
		}
        /*$scope.openLMPopup = function(e){
            angular.element('#lm-popup').removeClass('displayHide');
            if (components.media) {
                components.media.pause(0);
                angular.element('.video .play').addClass('pause');
            }
        }
        $scope.closeLMPopup = function(e){
            angular.element('#lm-popup').addClass('displayHide');
            if (components.media) {
                components.media.resume(0);
                angular.element('.video .play').removeClass('pause');
            }
        }*/

        $rootScope.topic = 0;
        /*css properties of navController*/
        $scope.css = {
            change: true,
            /*Nav*/
            navTopHeight: 5,
            navTopBgColor: '#B7C4D4',
            navTopColor: '#24374B',
            navTopFontSize: 16,
            navBottomBgColor: '#FFFFFF',
            navBottomFontSize: 13,
            navChaptersPaddingTop: 1,
            navChaptersPaddingLeft: 0,
            navChaptersPaddingRight: 3,
            navTopicsPaddingLeft: 3,
            videoTopicPaddingTop: 1
        }
        cssData.navController = $scope.css;
        /*watching css properties of navController, and applying these to ng-style values*/
        $scope.$watchCollection('css', function (newVal, oldVal) {
            var navTopHeight = ((100 / cssData.controller_1.leftNavHeight) * $scope.css.navTopHeight);
            $scope.cssNavTop = {
                height: navTopHeight + '%',
                fontSize: $scope.css.navTopFontSize
            }
            $scope.cssNavBottom = {
                backgroundColor: $scope.css.navBottomBgColor,
                fontSize: $scope.css.navBottomFontSize + 'px'
            }
            $scope.cssNavChapters = {
                paddingTop: $scope.css.navChaptersPaddingTop + '%',
                paddingLeft: $scope.css.navChaptersPaddingLeft + '%',
                paddingRight: $scope.css.navChaptersPaddingLeft + '%',

            }
            $scope.cssNavTopicContent = {
                paddingLeft: $scope.css.navTopicsPaddingLeft + '%',
            }
            $scope.cssVideoTopic = {
            }
            $scope.cssPptTopic = {
            }
        });

        $scope.secondsToHms = function(d) {
            d = Number(d);
            var h = Math.floor(d / 3600);
            var m = Math.floor(d % 3600 / 60);
            var s = Math.floor(d % 3600 % 60);
        
            var hDisplay = h > 0 ? h + (h == 1 ? "hr " : "hrs ") : "";
            var mDisplay = m > 0 ? m + (m == 1 ? "m " : "m ") : "";
            //var sDisplay = s > 0 ? s + (s == 1 ? " second" : " seconds") : "";
            //return hDisplay + mDisplay + sDisplay; 
            return hDisplay + mDisplay ; 
        }

       
        /*trigger window resize to align sliders and play video buttons vertically*/
        angular.element(window).trigger('resize');

    });
})()