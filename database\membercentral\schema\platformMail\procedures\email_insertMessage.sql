ALTER PROC dbo.email_insertMessage
@messageTypeID int,
@siteID int,
@orgIdentityID int = NULL,
@sendingSiteResourceID int,
@isTestMessage bit = 0,
@sendOnDate datetime,
@recordedByMemberID int,
@fromName varchar(200),
@fromEmail varchar(200),
@replyToEmail varchar(200),
@senderEmail varchar(200),
@subject varchar(400),
@contentVersionID int,
@messageWrapper varchar(max),
@referenceType varchar(20), 
@referenceID int,
@consentListIDs varchar(max) = NULL,
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	set @messageID = null;

	-- validate fromEmail for DMARC compliance and determine if sender value is needed
	DECLARE @messageContent varchar(max),@cleanedContent varchar(max), @contentLanguageID int, @orgID int, @orgSystemMemberID int, 
		@allowedDomainsRegex varchar(1050), @emailRegex varchar(100), @senderValue varchar(200)='', @defaultSendingHostname varchar(100),
		@mailStreamID int, @environmentName varchar(12), @environmentID int;
	SET @emailRegex = '[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}';

	IF @orgIdentityID IS NULL
		SELECT @orgIdentityID = defaultOrgIdentityID FROM memberCentral.dbo.sites WHERE siteID = @siteID;

	IF membercentral.dbo.fn_RegexMatch (@fromName,@emailRegex) = 1 BEGIN
		SELECT @fromName = siteName, @orgID = orgID FROM memberCentral.dbo.sites WHERE siteID = @siteID;
	END

	SELECT @environmentName = tier 
	FROM membercentral.dbo.fn_getServerSettings();

	SELECT @environmentID = environmentID 
	FROM membercentral.dbo.platform_environments 
	WHERE environmentName = @environmentName;

	SELECT @mailStreamID = mailStreamID
	FROM dbo.email_messageTypes
	WHERE messageTypeID = @messageTypeID;

	SELECT @allowedDomainsRegex = '[.@](' + replace(STRING_AGG(suds.sendingHostname,'|'),'.','\.') + ')$', 
		@defaultSendingHostname = (SELECT sendingHostname FROM dbo.sendgrid_subuserDomains WHERE subuserDomainID = su.activeSubuserDomainID)
	FROM dbo.sendgrid_subusers su
	INNER JOIN dbo.sendgrid_subuserDomains suds
		ON suds.subuserID = su.subuserID
		AND su.siteID = @siteID 
	INNER JOIN dbo.sendgrid_subuserMailstreams sums
		ON sums.subuserID = su.subuserID 
	INNER JOIN dbo.email_mailstreams ms
		ON sums.mailstreamID = ms.mailStreamID
		AND ms.mailStreamID = @mailstreamID
	GROUP BY su.activeSubuserDomainID;

	-- from default subuser for the environment/mailstream combination
	IF @allowedDomainsRegex IS NULL
		SELECT @allowedDomainsRegex = '[.@](' + replace(STRING_AGG(suds.sendingHostname,'|'),'.','\.') + ')$', 
			@defaultSendingHostname = (SELECT sendingHostname FROM dbo.sendgrid_subuserDomains WHERE subuserDomainID = su.activeSubuserDomainID)
		FROM dbo.sendgrid_defaultSubusers dsu
		INNER JOIN dbo.sendgrid_subusers su
			ON su.subuserID = dsu.subuserID
			AND dsu.mailStreamID = @mailStreamID
			AND dsu.environmentID = @environmentID
		INNER JOIN dbo.sendgrid_subuserDomains suds
			ON suds.subuserID = su.subuserID
		GROUP BY su.activeSubuserDomainID;

	IF membercentral.dbo.fn_RegexMatch (@fromEmail,@allowedDomainsRegex) = 0 BEGIN
		SET @fromEmail = 'noreply@'+@defaultSendingHostname;

		IF len(@senderEmail) > 0  AND membercentral.dbo.fn_RegexMatch (@senderEmail,@allowedDomainsRegex) = 1 
			SET @senderValue = @senderEmail;
	END

	
	set @messageWrapper=rtrim(ltrim(@messageWrapper))
	-- clear wrapper if it's simply @@rawcontent@@
	if @messageWrapper='@@rawcontent@@' 
		set @messageWrapper=''

	-- cleanup HTML, cleanup wrapper if it's defined ... otherwise cleanup contentVersion
	if len(@messageWrapper) > 0 BEGIN

		exec dbo.email_cleanupHTML
			@html = @messageWrapper,
			@doctype = DEFAULT, 
			@headtext = DEFAULT,
			@cleanHTML =  @cleanedContent OUTPUT
		set @messageWrapper = @cleanedContent;

	END ELSE BEGIN
		select @messageContent = cv.rawContent, @contentLanguageID=cv.contentLanguageID
		from membercentral.dbo.cms_contentVersions cv
		where cv.contentVersionID = @contentVersionID

		exec dbo.email_cleanupHTML
			@html = @messageContent,
			@doctype = DEFAULT, 
			@headtext = DEFAULT,
			@cleanHTML =  @cleanedContent OUTPUT

		-- if changes were made to the content, create a new inactive contentversion and use it for the email.
		if @messageContent <> @cleanedContent BEGIN
			set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID)

			exec membercentral.dbo.cms_createContentVersion
				@contentLanguageID = @contentLanguageID,
				@rawContent = @cleanedContent,
				@isActive = 0,
				@memberID = @orgSystemMemberID,
				@contentVersionID = @contentVersionID OUTPUT

		END
	END

	insert into dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, recordedByMemberID, 
		fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, messageWrapper, [uid], 
		sendOnDate, referenceType, referenceID, orgIdentityID, isTestMessage)
	values (@messageTypeID, @siteID, @sendingSiteResourceID, getdate(), @recordedByMemberID, @fromName, @fromEmail, 
		@replyToEmail, @senderValue, @subject, @contentVersionID, @messageWrapper, NEWID(), @sendOnDate, 
		@referenceType, @referenceID, @orgIdentityID, @isTestMessage);

	SELECT @messageID = SCOPE_IDENTITY();

	IF LEN(@consentListIDs) > 0 
		BEGIN 
			IF OBJECT_ID('tempdb..#consentListIDs') IS NOT NULL 
				DROP TABLE #consentListIDs;
			CREATE TABLE #consentListIDs (autoid INT IDENTITY(1,1) NOT NULL ,consentListID int);
		
			INSERT INTO #consentListIDs (consentListID)
			SELECT ft.listitem
			FROM  membercentral.dbo.fn_intListToTable(@consentListIDs,',') as ft
			INNER JOIN email_consentLists ec ON ec.consentListID = ft.listitem
				AND ec.[status] = 'A'
			INNER JOIN membercentral.dbo.cms_siteresources sr ON sr.siteResourceID = ec.siteResourceID
			AND sr.siteID = @siteID

			IF EXISTS (select top 1 * FROM #consentListIDs)
				BEGIN
					INSERT INTO dbo.email_messageConsentLists (siteID, messageID, consentListID, isPrimary)
					SELECT @siteID, @messageID, consentListID,CASE WHEN autoid = 1 THEN 1 ELSE 0 END
					FROM #consentListIDs
				END	

			IF OBJECT_ID('tempdb..#consentListIDs') IS NOT NULL 
			DROP TABLE #consentListIDs;

		END
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
