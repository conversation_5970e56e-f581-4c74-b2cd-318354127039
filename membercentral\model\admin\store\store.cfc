<cfcomponent output="false">
	
	<cffunction name="getDefaultLanguageID" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySiteInfo">
			SELECT defaultLanguageID
			FROM dbo.sites
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		<cfreturn local.qrySiteInfo.defaultLanguageID />
	</cffunction>

	<cffunction name="getStoreInfo" access="public" output="false" returntype="query" hint="Get the store information">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStore">
			set nocount on;

			declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;

			SELECT s.storeid, s.siteid, s.applicationInstanceID, ai.siteResourceID, s.mainContentID, s.emailRecipient, 
				s.ShowProductID, s.MaxRecords, s.showCartThumbnails, s.rootSectionID, s.displayCredit,
				storecontent.rawContent as mainContent, s.OfferAffirmations, s.OfferStreams, s.defaultSortOrder,
				s.GLAccountID, '' as GLAccountPath, s.ShippingGLAccountID, '' as ShippingGLAccountPath, sites.orgID,
        		s.emailReplyTo, s.orderReceiptFooterContentID, orderReceiptFooterContent.rawContent as orderReceiptFooterContent,
				s.orgIdentityID
			from dbo.store as s
			inner join dbo.sites on sites.siteID = s.siteID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = s.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			cross apply dbo.fn_getContent(s.mainContentID,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#">) as storecontent
			cross apply dbo.fn_getContent(s.orderReceiptFooterContentID,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#">) as orderReceiptFooterContent
			where s.siteID = @siteID;
		</cfquery>

		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryStore.GLAccountID), orgID=val(local.qryStore.orgID))>
		<cfset QuerySetCell(local.qryStore,'GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>
		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryStore.shippingGLAccountID), orgID=val(local.qryStore.orgID))>
		<cfset QuerySetCell(local.qryStore,'shippingGLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>

		<cfreturn local.qryStore>
	</cffunction>

	<cffunction name="getOrderReceiptFooterContent" access="public" output="false" returntype="string">
		<cfargument name="storeID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetOrderReceiptFooterContent" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT  REPLACE(REPLACE(REPLACE(orderReceiptFooterContent.rawContent, CHAR(13) + CHAR(10), '<br>'), CHAR(13), '<br>'), CHAR(10), '<br>') as  orderReceiptFooterContent
			
			from dbo.store as s
			
			cross apply dbo.fn_getContent(s.orderReceiptFooterContentID,1) as orderReceiptFooterContent
			where s.storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryGetOrderReceiptFooterContent.orderReceiptFooterContent>
	</cffunction>

	<cffunction name="getStoreInfoBySiteID" access="public" output="false" returntype="query" hint="Get the store information">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
	
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStore">
			set nocount on;

			declare @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT s.storeid, s.siteid, s.applicationInstanceID, ai.siteResourceID, s.mainContentID, s.emailRecipient, 
				s.ShowProductID, s.MaxRecords, s.showCartThumbnails, s.rootSectionID, s.displayCredit, s.OfferAffirmations, s.OfferStreams, 
				s.defaultSortOrder,	s.GLAccountID, '' as GLAccountPath, s.ShippingGLAccountID, '' as ShippingGLAccountPath, sites.orgID
			from dbo.store as s
			inner join dbo.sites on sites.siteID = s.siteID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = s.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			where s.siteID = @siteID;
		</cfquery>
		
		<cfreturn local.qryStore>
	</cffunction>
	
	<cffunction name="updateStoreSettings" access="public" output="false" returntype="void" hint="Update">
		<cfargument name="Event" type="any">
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.store
			SET
				EmailRecipient = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('EmailRecipient')#">,
				emailReplyTo = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('emailReplyTo')#">,
				showProductID = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showProductID')#">,
				MaxRecords = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('MaxRecords')#">,
				showCartThumbnails = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showCartThumbnails')#">,
				GLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('GLAccountID')#">,
				ShippingGLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('ShippingGLAccountID')#">,
				OfferAffirmations = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('OfferAffirmations')#">,
				OfferStreams = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('OfferStreams')#">,
				displayCredit = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('DisplayCredit')#">,
				defaultSortOrder = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('defaultSortOrder')#">,
				orgIdentityID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('orgIdentityID'))#">
			WHERE
				siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			AND 
				storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID')#">
		</cfquery>
	</cffunction>

	<cffunction name="updateContent" access="public" output="false" returntype="void">
		<cfargument name="contentID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isHTML" type="boolean" required="true">
		<cfargument name="contentTitle" type="string" required="true">
		<cfargument name="contentDesc" type="string" required="true">
		<cfargument name="rawContent" type="string" required="true">

		<cfif arguments.contentTitle EQ 'StoreMailFooter'>
			<cfset local.sanitizeOptions = {
				"allowedTags": [],
				"allowedAttributes": {}
			}>
			<cfset local.sanitizeResponse = application.objCommon.sanitizeHTML(dirtyHTML=trim(arguments.rawContent), sanitizeOptions=local.sanitizeOptions)>
			
			<cfif local.sanitizeResponse.success>
				<cfset local.cleanInputString = trim(REREPLACE(REREPLACE(local.sanitizeResponse.content,"\t","","ALL"),"(\r\n)+","","ALL"))>
			<cfelse>
				<cfset local.cleanInputString = arguments.rawContent>
			</cfif>

		</cfif>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.cms_updateContent">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isHTML#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentDesc#">
			<cfif arguments.contentTitle EQ 'StoreMailFooter'>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.cleanInputString#">
			<cfelse>	
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rawContent#">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getCategories" access="public" output="false" returntype="query" hint="Get the store information">
		<cfargument name="Event" type="any">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryCategories">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @storeID int = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">;

			SELECT categoryID, categoryName
			from dbo.store_Categories 
			where storeID = @storeID
			order by CategoryName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="getStoreCategories" access="public" output="false" returntype="query">
		<cfargument name="storeID" type="numeric" required="true">
		
		<cfset var qryStoreCategories = "">

		<cfquery name="qryStoreCategories" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @storeID int = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT categoryID, categoryName, parentcategoryID, thePath, thePathExpanded
			from dbo.fn_getRecursiveStoreCategories(@storeID, NULL, NULL)
			order by thepath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStoreCategories>
	</cffunction>

	<cffunction name="checkCategoryName" access="public" output="false" returntype="boolean" hint="unique category name check">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="categoryName" type="string" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="parentCategoryID" type="string" required="false">

		<cfset var qryStoreCategory = "">

		<cfquery name="qryStoreCategory" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT sc.categoryID
			FROM dbo.store_Categories AS sc
			INNER JOIN dbo.store AS s ON s.storeID = sc.storeID
				AND s.applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.storeApplicationInstanceID#">
			WHERE sc.categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.categoryName#">
			<cfif val(arguments.parentCategoryID)>
				AND sc.parentCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.parentCategoryID#">
			</cfif>
			<cfif arguments.categoryID GT 0>
				AND sc.categoryID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryStoreCategory.recordCount GT 0>
	</cffunction>

	<cffunction name="saveCategory" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="categoryName" type="string" required="true">
		<cfargument name="parentCategoryID" type="string" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
		<cfargument name="visibility" type="string" required="false">
		
		<cfset var local = structNew()>
		<cfset local.data.success = true>
		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.categoryNameExists = checkCategoryName(storeApplicationInstanceID=arguments.storeApplicationInstanceID,
				categoryName=arguments.categoryName, categoryID=arguments.categoryID, parentCategoryID=arguments.parentCategoryID)>

			<cfif local.categoryNameExists>
				<cfset local.data = { "success":false, "errmsg":"Category Name already in use." }>
				<cfreturn local.data>
			</cfif>

			<cfif arguments.categoryID EQ 0>
				<cfset local.categoryID = insertCategory(storeApplicationInstanceID=arguments.storeApplicationInstanceID,
					categoryName=arguments.categoryName, parentCategoryID=val(arguments.parentCategoryID))>
			<cfelse>
				<cfset local.data = updateCategory(categoryID=arguments.categoryID, storeApplicationInstanceID=arguments.storeApplicationInstanceID, categoryName=arguments.categoryName, visibility=arguments.visibility)>
			</cfif>
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateCategory" access="private" output="false" returntype="struct" hint="Update store category">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="categoryName" type="string" required="true">
		<cfargument name="visibility" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data.success = true>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetStoreID">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT storeID
				FROM dbo.store
				WHERE applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeApplicationInstanceID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryParentVisibility">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">;

				SELECT 
					(
						SELECT visibility 
						FROM dbo.store_Categories 
						WHERE storeID = @storeID
						AND categoryID = main.parentCategoryID
					) AS visibility,
					(
						SELECT COUNT(*) 
						FROM dbo.store_Categories 
						WHERE storeID = @storeID
						AND parentCategoryID = main.CategoryID
					) AS childCount
				FROM dbo.store_Categories AS main
				WHERE main.storeID = @storeID
				AND main.categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif (arguments.visibility NEQ 'A' AND local.qryParentVisibility.visibility EQ 'A') OR 
				(arguments.visibility EQ 'N' AND local.qryParentVisibility.visibility EQ 'H' )>
				<cfset local.data = { "success":false, "errmsg":"Cannot change visibility to a less restrictive visibility value when parent has more restrictive visibility." }>
				<cfreturn local.data>
			</cfif>
			
			<cfif local.qryParentVisibility.recordCount AND LEN(local.qryParentVisibility.visibility) EQ 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateCategory">
					UPDATE dbo.store_Categories
					SET visibility = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.visibility#">,
						categoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.categoryName#">
					WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">
					AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
				</cfquery>

				<cfif local.qryParentVisibility.childCount>
					<cfset updateChildCategories(categoryID=arguments.categoryID, storeID=local.qryGetStoreID.storeID, visibility=arguments.visibility)>
				</cfif>

				<cfset clearStoreCategoryCache(storeID=local.qryGetStoreID.storeID)>
				<cfreturn local.data>
			</cfif>

			<!--- Get current category visibility --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategory">
				SELECT visibility 
				FROM dbo.store_Categories 
				WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">
				AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
			</cfquery>

			<!--- Check if the new visibility is more or less restrictive --->
			<cfset local.currentVisibility = local.qryCategory.visibility>
			
			<cfif local.currentVisibility GT arguments.visibility>
				<!--- More restrictive update (N → H → A) --->
				<!--- Update this category visibility --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateCategory">
					UPDATE dbo.store_Categories
					SET visibility = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.visibility#">,
						categoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.categoryName#">
					WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">
					AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
				</cfquery>
				<!--- Update child categories if needed --->
				<cfset updateChildCategories(categoryID=arguments.categoryID, storeID=local.qryGetStoreID.storeID, visibility=arguments.visibility)>
			<cfelseif local.currentVisibility LT arguments.visibility>
				<!--- Less restrictive update (A → H → N) --->
				<!--- Only update this category, not its children --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateCategory">
					UPDATE dbo.store_Categories
					SET visibility = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.visibility#">,
						categoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.categoryName#">
					WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">
					AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
				</cfquery>
			<cfelse>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
					UPDATE dbo.store_Categories
					SET categoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.categoryName#">
					WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryGetStoreID.storeID#">
					AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
				</cfquery>
			</cfif>

			<cfset clearStoreCategoryCache(storeID=local.qryGetStoreID.storeID)>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateChildCategories" access="private" output="false" returntype="void" hint="Recursively update visibility of child categories based on parent category's visibility">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="visibility" type="string" required="true">
	
		<cfset var local = structNew()>
	
		<!--- Get all child categories of the current category for the given store --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryChildren">
			SELECT categoryID, visibility
			FROM dbo.store_Categories
			WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			AND parentCategoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
		</cfquery>
		<!--- Loop through child categories and update their visibility --->
		<cfloop query="local.qryChildren">
			<cfif local.qryChildren.visibility GT arguments.visibility>
				<!--- Update child category visibility to the parent's new visibility --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateChild">
					UPDATE dbo.store_Categories
					SET visibility = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.visibility#">
					WHERE storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
					AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryChildren.categoryID#">
				</cfquery>
				
				<!--- Recursively update the child's children --->
				<cfset updateChildCategories(categoryID=local.qryChildren.categoryID, storeID=arguments.storeID, visibility=arguments.visibility)>
			<cfelseif local.qryChildren.visibility EQ 'A'>
				<!--- Do not update child category if it is already A --->
			</cfif>
		</cfloop>
	</cffunction>
		
	<cffunction name="insertCategory" access="private" output="false" returntype="numeric">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="categoryName" type="string" required="true">
		<cfargument name="parentCategoryID" type="string" required="true">

		<cfscript>
			var local = structNew();
			local.data.success = true;
			local.insert.categoryID = "";
		</cfscript>
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insert">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @sitecode varchar(10), @storeID int, @parentVisibility char(1);

					SELECT @storeID = storeID
					from dbo.store
					where applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeApplicationInstanceID#">;

					-- Fetch the parent category visibility if parentCategoryID is not null
					-- otherwise Default visibility to N if no parentCategoryID is provided
					IF (<cfif arguments.parentCategoryID eq 0>0<cfelse>1</cfif> = 1)
						SELECT @parentVisibility = visibility
						FROM dbo.store_Categories
						WHERE storeID = @storeID
						AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentCategoryID#">;
					ELSE
						SET @parentVisibility = 'N';

					INSERT INTO dbo.store_Categories (storeID, categoryName, parentCategoryID, visibility)
					values (@storeID, '#arguments.categoryName#', <cfif arguments.parentCategoryID eq 0>null<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentCategoryID#"></cfif>, @parentVisibility);

					SELECT SCOPE_IDENTITY() AS categoryID, @storeID as storeID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset clearStoreCategoryCache(storeID=local.insert.storeID)>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.insert.categoryID>
	</cffunction>

	<cffunction name="deleteCategory" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="said" type="numeric" required="true">
		<cfargument name="sid" type="numeric" required="true">
		<cfargument name="cid" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllCats">
				SELECT c.categoryID, (SELECT count(ItemId) from dbo.store_ProductCategoryLinks p where p.categoryID = c.categoryID) as numberLinkedProducts
				FROM fn_getRecursiveStoreCategories(<cfqueryparam value="#arguments.sid#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam value="#arguments.cid#" cfsqltype="CF_SQL_INTEGER">, null) as c
				order by c.thePath Desc
			</cfquery>

			<cfloop query="local.qryAllCats">
				<cfif local.qryAllCats.numberLinkedProducts eq 0>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.delCat">
						SET NOCOUNT ON;

						DECLARE @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sid#">;

						DELETE dbo.store_Categories
						WHERE storeID = @storeID
						AND categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryAllCats.categoryID#">;
					</cfquery>
				<cfelse>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfset clearStoreCategoryCache(storeID=arguments.sid)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCategoryByID" access="public" output="false" returntype="query" hint="Get the category information">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryCategories">
			SELECT categoryID, categoryName, parentCategoryID, visibility
			from dbo.store_Categories 
			where storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">
			and categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="getShippingMethods" access="public" output="false" returntype="query" hint="Get the store information">
		<cfargument name="Event" type="any">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryCategories">
			SELECT shippingID, shippingName, Visible
			from dbo.store_ShippingMethods 
			where storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">
			order by ShippingName
		</cfquery>
		
		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="checkShippingName" access="public" output="false" returntype="boolean" hint="unique shipping method name check">
		<cfargument name="shippingID" type="numeric" required="false">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="shippingName" type="string" required="true">
	
		<cfscript>
			var local = structNew();
			local.check = TRUE;
		</cfscript>

		<cfquery name="local.data" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @storeID int;

			SELECT @storeID = storeID 
			FROM dbo.store 
			WHERE applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeApplicationInstanceID#">;	

			SELECT shippingID
			FROM dbo.store_ShippingMethods
			WHERE shippingName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.shippingName#">
			AND shippingID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.shippingID#">
			AND storeID = @storeID;
		</cfquery>
		
		<cfreturn local.data.recordCount>
	</cffunction>

	<cffunction name="saveshipping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="shippingID" type="numeric" required="false">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="shippingName" type="string" required="true">
		<cfargument name="shippingVisible" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.categoryNameExists = checkShippingName(storeApplicationInstanceID=arguments.storeApplicationInstanceID,
				shippingName=arguments.shippingName, shippingID=arguments.shippingID)>

			<cfif local.categoryNameExists>
				<cfset local.data = { "success":false, "errmsg":"Shipping method Name already in use." }>
				<cfreturn local.data>
			</cfif>

			<cfset updateShipping(shippingID=arguments.shippingID, storeApplicationInstanceID=arguments.storeApplicationInstanceID, shippingName=arguments.shippingName, shippingVisible=arguments.shippingVisible)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateShipping" access="public" output="false" returntype="void" hint="Update store shipping method">
		<cfargument name="shippingID" type="numeric" required="false">
		<cfargument name="storeApplicationInstanceID" type="numeric" required="true">
		<cfargument name="shippingName" type="string" required="true">
		<cfargument name="shippingVisible" type="numeric" required="true">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			UPDATE dbo.store_ShippingMethods
			SET shippingName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.shippingName#">,
				Visible = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.shippingVisible#">
			WHERE shippingID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.shippingID#">
			AND storeID = (SELECT storeID
							FROM dbo.store
							WHERE applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeApplicationInstanceID#">)
		</cfquery>
	</cffunction>
		
	<cffunction name="insertShipping" access="public" output="false" returntype="numeric" hint="Insert store shipping method">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="shippingName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfstoredproc procedure="store_createShippingMethod" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.shippingName#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.shippingID">
		</cfstoredproc>

		<cfreturn local.shippingID>
	</cffunction>

	<cffunction name="deleteShipping" access="public" output="false" returntype="struct" hint="Delete store shipping method">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="said" type="numeric" required="true">
		<cfargument name="sid" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="store_deleteShippingMethod" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.said#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sid#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getShippingByID" access="public" output="false" returntype="query" hint="Get the shipping method information">
		<cfargument name="Event" type="any">
		<cfargument name="shippingID" type="numeric">

		<cfset var qryShippingMethods = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryShippingMethods">
			SELECT shippingID, shippingName, Visible
			from dbo.store_ShippingMethods 
			where storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">
			and shippingID = <cfqueryparam value="#arguments.shippingID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryShippingMethods>
	</cffunction>
	
	<cffunction name="getShippingRates" access="public" returntype="query">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.storeShippingRates" datasource="#application.dsn.memberCentral.dsn#">
			SELECT spf.itemID,
				spf.formatID,
				sr.rateID,
				sr.RateName,
				ssm.shippingID, 
				ssm.shippingName, 
				isNULL(sps.ProductShippingID, 0) as ProductShippingID,
				isNULL(sps.PerShipment, CONVERT(decimal(18,2),0.00)) as PerShipment, 
				isNULL(sps.perItem, CONVERT(decimal(18,2),0.00)) as PerItem
			from dbo.store_rates sr 		
			inner join dbo.store_ProductFormats spf on sr.formatID = spf.formatID
			inner join dbo.store_Products sp on sp.storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#"> 
				and sp.ItemID = spf.itemID
			inner join dbo.store_ShippingMethods ssm on ssm.storeID = sp.storeID
			inner join dbo.store_ProductShipping sps on sps.rateID = sr.rateID 
				and sps.shippingID = ssm.shippingID		
				and sr.rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">
		</cfquery>

		<cfreturn local.storeShippingRates>
	</cffunction>

	<cffunction name="getProductByID" access="public" output="false" returntype="query" hint="Get the price code information">
		<cfargument name="Event" type="any">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryProductID = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryProductID">
			SELECT p.ItemID, p.storeID, p.productID, p.productContentID, p.showQuantity, p.showAvailable,
				p.status, pc.contentTitle, pc.rawContent, p.productDate, 
				p.GLAccountID as productGLAccountID, rgl.thePathExpanded as productGLAccountPath,
				p.ShippingGLAccountID as productShippingGLAccountID, rgl2.thePathExpanded as productShippingGLAccountPath,
				p.summaryContentID, psc.contentTitle as summaryContentTitle, psc.rawContent as summaryRawContent,
				categoryIDList = stuff((
					SELECT ',' + cast(c.categoryID as varchar(10))
					FROM dbo.fn_getRecursiveStoreCategories(p.storeID, null, null) as c
					INNER JOIN dbo.store_ProductCategoryLinks as cl on cl.categoryID = c.categoryID
					WHERE cl.itemID = p.ItemID
					FOR XML PATH ('')
				),1,1,'')
			FROM dbo.store_Products as p
			left outer JOIN dbo.fn_getRecursiveGLAccounts(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">) as rgl on rgl.GLAccountID = p.GLAccountID
			left outer JOIN dbo.fn_getRecursiveGLAccounts(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">) as rgl2 on rgl2.GLAccountID = p.ShippingGLAccountID
			cross apply dbo.fn_getContent(p.productContentID, 1) as pc
			cross apply dbo.fn_getContent(p.summaryContentID, 1) as psc
			where p.storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">
			and p.ItemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryProductID>
	</cffunction>
	
	<cffunction name="updateProduct" access="public" output="false" returntype="void" hint="Update store product information">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @storeID int, @itemID int, @categoryIDList varchar(max);

				SELECT @storeID = storeID
				FROM dbo.store
				WHERE applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeApplicationInstanceID')#">;

				SET @itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('itemID')#">;
				SET @categoryIDList = '#arguments.event.getTrimValue('categoryID','')#';

				BEGIN TRAN;

					UPDATE dbo.store_Products
					SET status = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('status')#">,
						productID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('productID')#">,
						<cfif len(arguments.event.getValue('productDate'))>
							productDate = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('productDate')#">,
						<cfelse>
							productDate = NULL,
						</cfif>
						<cfif len(arguments.event.getValue('productGLAccountID')) AND arguments.event.getValue('productGLAccountID') NEQ "0">
							GLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('productGLAccountID')#">,
						<cfelse>
							GLAccountID = NULL,
						</cfif>
						<cfif len(arguments.event.getValue('productShippingGLAccountID')) AND arguments.event.getValue('productShippingGLAccountID') NEQ "0">
							ShippingGLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('productShippingGLAccountID')#">,
						<cfelse>
							ShippingGLAccountID = NULL,
						</cfif>
						showQuantity = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('showQuantity')#">,
						showAvailable = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('showAvailable')#">
					WHERE ItemID = @itemID
					AND storeID = @storeID;

					-- clear any existing category links
					DELETE FROM dbo.store_ProductCategoryLinks WHERE itemID = @itemID;

					<cfif len(arguments.event.getTrimValue('categoryID',''))>
						INSERT INTO dbo.store_ProductCategoryLinks(itemID, categoryID)
						SELECT @itemID, listitem
						FROM dbo.fn_intListToTable(@categoryIDList, ',');
					</cfif>

				COMMIT TRAN;

				SELECT @storeID AS storeID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset clearStoreCategoryCache(storeID=local.update.storeID)>
	</cffunction>
	
	<cffunction name="saveCopyProduct" access="public" output="false" returntype="void">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="copyFromitemID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_copyProduct">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyFromitemID#">
		</cfstoredproc>

		<cfset clearStoreCategoryCache(storeID=arguments.storeID)>
	</cffunction>

	<cffunction name="deleteProduct" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="said" type="numeric" required="true">
		<cfargument name="pid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryDeleteProduct" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @storeID int, @siteID int, @srID int, @rightsXML xml;
					SET @siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;

					SELECT @storeID = s.storeID, @srID = ai.siteResourceID
					FROM dbo.store AS s
					INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = s.applicationInstanceID
					WHERE s.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.said#"> 
					AND s.siteID = @siteID;

					IF @storeID IS NULL
						RAISERROR('invalid product',16,1);

					SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@srID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">, @siteID);
					IF @rightsXML.exist('/rights/right[@functionName="DeleteProducts"][@allowed="1"]') = 1
						UPDATE dbo.store_Products
						SET status = 'D'
						WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pid#">
						AND storeID = @storeID;
					ELSE
						RAISERROR('invalid request',16,1);

					SELECT @storeID as storeID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset clearStoreCategoryCache(storeID=local.qryDeleteProduct.storeID)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteFormatStream" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryDeleteStream" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @usageID int;
					SET @usageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.usageID#">;

					BEGIN TRAN;	
						DELETE FROM dbo.store_ProductFormatsStreamUsages
						WHERE usageID = @usageID;

						DELETE FROM dbo.stream_profileUsageFields
						WHERE usageID = @usageID;

						DELETE FROM dbo.stream_profileUsages
						WHERE usageID = @usageID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteFormatDocument" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">
		<cfargument name="formatID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.mcproxy_siteID, documentID=arguments.documentID)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDelete">
				DELETE FROM dbo.store_ProductFormatsDocuments
				WHERE formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">
				AND itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">
				AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="checkProductID" access="public" output="false" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="productID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.chkProductID">
			SELECT itemID
			from store_products
			where productID = <cfqueryparam value="#arguments.productID#" cfsqltype="CF_SQL_VARCHAR">
			and storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">
			and itemID <> <cfqueryparam value="#arguments.itemID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.isavailable = local.chkProductID.recordcount eq 0>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertProductFormat" access="public" output="false" returntype="numeric" hint="Insert product format">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfstoredproc procedure="store_createProductFormat" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('itemID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('formatName')#">
			<cfif val(arguments.event.getTrimValue('GLAccountID',0)) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('GLAccountID'))#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif val(arguments.event.getTrimValue('ShippingGLAccountID',0)) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('ShippingGLAccountID'))#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('status')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('offerAffirmations',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isAffirmation')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('quantity')#">
			<cfif int(val(arguments.event.getTrimValue('inventory',0))) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getTrimValue('inventory')))#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.formatID">
		</cfstoredproc>
		
		<cfstoredproc procedure="store_reorderFormats" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.formatID#" />
		</cfstoredproc>

		<cfset local.storeID = getStoreIDFromItemID(itemID=arguments.event.getTrimValue('itemID'))/>
		<cfset clearStoreCategoryCache(storeID=local.storeID)>

		<cfreturn local.formatID>
	</cffunction>
	
	<cffunction name="updateFormat" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.store_productformats
			SET GLAccountID = nullif(<cfqueryparam value="#arguments.event.getValue('GLAccountID')#" cfsqltype="cf_sql_integer">,0),
				ShippingGLAccountID = nullif(<cfqueryparam value="#arguments.event.getValue('ShippingGLAccountID')#" cfsqltype="cf_sql_integer">,0),
				[Name] = <cfqueryparam value="#arguments.event.getValue('formatName')#" cfsqltype="cf_sql_varchar">,
				[status] = <cfqueryparam value="#arguments.event.getValue('status')#" cfsqltype="cf_sql_char">,
				offerAffirmations = <cfqueryparam value="#arguments.event.getValue('offerAffirmations')#" cfsqltype="cf_sql_bit">,
				quantity = <cfqueryparam value="#arguments.event.getValue('quantity')#" cfsqltype="cf_sql_integer" />,
				inventory = nullif(<cfqueryparam value="#int(val(arguments.event.getValue('inventory')))#" cfsqltype="cf_sql_integer">,0)
			WHERE not exists (
				SELECT name 
				from dbo.store_productformats 
				where itemID = <cfqueryparam value="#arguments.event.getValue('itemID')#" cfsqltype="cf_sql_integer">
				and name = <cfqueryparam value="#arguments.event.getValue('formatName')#" cfsqltype="cf_sql_varchar">
				and formatID <> <cfqueryparam value="#arguments.event.getValue('formatID')#" cfsqltype="cf_sql_integer">
				and status <> 'D'
			)
			and formatID = <cfqueryparam value="#arguments.event.getValue('formatID')#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfset local.storeID = getStoreIDFromItemID(itemID=arguments.event.getTrimValue('itemID'))/>
		<cfset clearStoreCategoryCache(storeID=local.storeID)>
	</cffunction>
	
	<cffunction name="deleteFormat" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="formatID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteFormat">
				DECLARE @storeID int;

				SELECT @storeID = storeID
				from dbo.store_Products
				where storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer" />
				and ItemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="cf_sql_integer" />;

				IF @storeID IS NULL
					RAISERROR('invalid product',16,1);

				UPDATE dbo.store_productformats
				SET [status] = 'D'
				WHERE formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">;
			</cfquery>

			<cfset clearStoreCategoryCache(storeID=arguments.storeID)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
		
	<cffunction name="getActiveMerchantProfiles" access="public" output="false" returntype="query" hint="Get the current merchant profiles">
		<cfargument name="storeID" type="numeric" required="true">

		<cfset var qryActiveMerchangeProfiles = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryActiveMerchangeProfiles">
			SELECT mp.profileID
			from dbo.store_merchantProfiles sp
			inner join dbo.mp_profiles mp on sp.merchantProfileID = mp.profileID
			inner join dbo.mp_gateways g on g.gatewayID = mp.gatewayID
			where sp.storeid = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">
			and mp.status = 'A'
			and g.isActive = 1
		</cfquery>
		
		<cfreturn qryActiveMerchangeProfiles>
	</cffunction>

	<cffunction name="updateMerchantProfiles" access="public" output="false" returntype="void">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="profileIDList" type="string" required="true">

		<cfset var qryUpdateProfiles = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateProfiles">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @storeID int;
				set @storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">;

				BEGIN TRAN;
					DELETE from dbo.store_merchantProfiles
					WHERE storeID = @storeID
					AND storeProfileID NOT IN (<cfqueryparam value="#arguments.profileIDList#" cfsqltype="CF_SQL_INTEGER" list="true">);

					INSERT INTO dbo.store_merchantProfiles (storeID, merchantProfileID)
					SELECT @storeID as storeID, profileID
					from dbo.mp_profiles
					where profileID IN (<cfqueryparam value="#arguments.profileIDList#" cfsqltype="CF_SQL_INTEGER" list="true">)
					and [status] in ('A','I')
						except
					SELECT @storeID as storeID, merchantProfileID
					from dbo.store_merchantProfiles
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	
	<cffunction name="getOrder" access="public" output="false" returntype="query">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderID" type="numeric" required="true">
		
		<cfset var qryOrder = "">
			
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryOrder">
			SELECT so.OrderID, so.storeID, so.OrderNumber, so.DateOfOrder, so.totalProduct, so.totalTax, 
				so.totalDiscount, so.totalDiscountExcTax, so.merchantProfileID, mp.gatewayID, so.xmlShippingInfo, 
				mActive.memberID, mActive.firstName, mActive.lastName, so.OrderCompleted, so.shippingKey, 
				so.orderStatusID, so.orderNotes, me.email
			FROM dbo.store_orders as so
			INNER JOIN dbo.ams_members as m on m.memberID = so.memberID
			INNER JOIN dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			LEFT OUTER JOIN dbo.mp_profiles as mp on mp.profileID = so.merchantProfileID
			INNER JOIN dbo.ams_memberEmails as me on me.orgId = m.orgID and me.memberID = mActive.memberID
			INNER JOIN dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
			WHERE so.storeid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			AND so.orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>

		<cfreturn qryOrder>
	</cffunction>
	
	<cffunction name="getOrderDetails" access="public" output="false" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_getOrderDetails">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.orderID#">
			<cfprocresult name="local.strReturn.qryOrderDetails" resultset="1">
			<cfprocresult name="local.strReturn.qryOrderPayments" resultset="2">
			<cfprocresult name="local.strReturn.qryOrderTotals" resultset="3">
			<cfprocresult name="local.strReturn.qryOrderProductSaleDetails" resultset="4">
			<cfprocresult name="local.strReturn.qryOrderShippingSaleDetails" resultset="5">
			<cfprocresult name="local.strReturn.qryOrderStreamsDetails" resultset="6">
			<cfprocresult name="local.strReturn.qryOrderCouponDiscounts" resultset="7">
		</cfstoredproc>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="getStoreDocuments" access="public" returntype="query">
		<cfargument name="formatID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="false" default="0">
		
		<cfset var qryDocuments = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDocuments">
			SELECT sd.uid, sd.formatID, sd.itemID, sd.documentID, sd.accessExpireInDays, 
				d.siteID, d.dateCreated, dl.documentLanguageID, dl.docTitle, dl.docDesc, 
				dv.documentVersionID, dv.fileName, dv.fileExt, dv.dateModified,
				l.languageCode
			FROM dbo.store_ProductFormatsDocuments as sd
			INNER JOIN dbo.cms_documents as d ON sd.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			INNER JOIN dbo.cms_languages as l ON l.languageID = dl.languageID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			WHERE sd.formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			<cfif arguments.usageID gt 0>
				AND sd.uid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.usageID#">
			</cfif>
			ORDER BY dl.docTitle, dv.dateModified
		</cfquery>

		<cfreturn qryDocuments>
	</cffunction>
	
	<cffunction name="getStoreStreams" access="public" returntype="query">
		<cfargument name="formatID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="false" default="0">
		
		<cfset var storeStreams = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="storeStreams">
			SELECT su.formatID, su.usageID, p.profileID, su.accessExpireInDays, p.profileName, pu.streamName
			FROM dbo.store_ProductFormatsStreamUsages as su
			INNER JOIN dbo.stream_profileUsages as pu on pu.usageID = su.usageID
			INNER JOIN dbo.stream_profiles as p on p.profileID = pu.profileID
			WHERE su.formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			<cfif arguments.usageID gt 0>
				AND su.usageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.usageID#">
			</cfif>
			ORDER BY p.profileName, su.usageID
		</cfquery>

		<cfreturn storeStreams>
	</cffunction>
	
	<cffunction name="updateOrderStatus" access="public" output="false" returntype="void">
		<cfargument name="orderID" type="numeric" required="yes">
		<cfargument name="orderStatusID" type="string" required="yes">
		<cfargument name="memberID" type="string" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrentStatus">
			SELECT statusLogID, currentStatusID
			from dbo.store_OrderStatusLog osl
			inner join (
				SELECT orderID, max(datelastupdated) as max_date
				from dbo.store_OrderStatusLog 
				where orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
			group by orderID ) tmp on tmp.max_date = osl.dateLastUpdated and osl.orderid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			insert into dbo.store_OrderStatusLog (orderID, memberID, previousStatusID, currentStatusID)
			values(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,
				<cfif len(local.qryCurrentStatus.currentStatusID)>
					<cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryCurrentStatus.currentStatusID#">,
				<cfelse>
					null,
				</cfif>
				<cfif len(arguments.orderStatusID)>
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderStatusID#">
				<cfelse>
					null
				</cfif>
			)
			<cfif len(arguments.orderStatusID)>
				update dbo.store_Orders
				SET orderStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderStatusID#">
				WHERE orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
			<cfelse>
				update dbo.store_Orders
				SET orderStatusID = null
				WHERE orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
			</cfif>
		</cfquery>
	</cffunction>
	
	<cffunction name="updateOrderNotes" access="public" output="false" returntype="void">
		<cfargument name="orderID" type="numeric" required="yes">
		<cfargument name="orderNotes" type="string" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			update dbo.store_orders
			set orderNotes = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.orderNotes#">
			where orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>
	</cffunction>

	<cffunction name="updateOrderShipping" access="public" output="false" returntype="void">
		<cfargument name="orderID" type="numeric" required="yes">
		<cfargument name="attn" type="string" required="yes">
		<cfargument name="address1" type="string" required="yes">
		<cfargument name="city" type="string" required="yes">
		<cfargument name="state" type="string" required="yes">
		<cfargument name="zip" type="string" required="yes">
		<cfargument name="firm" type="string" required="no" value="">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.xmlShippingInfo">
			<cfoutput>
			<shipping>
				<fldship_attn>#xmlformat(arguments.attn)#</fldship_attn>
				<fldship_firm>#xmlformat(arguments.firm)#</fldship_firm>
				<fldship_address1>#xmlformat(arguments.address1)#</fldship_address1>
				<fldship_city>#xmlformat(arguments.city)#</fldship_city>
				<fldship_state>#xmlformat(arguments.state)#</fldship_state>
				<fldship_zip>#xmlformat(arguments.zip)#</fldship_zip>
			</shipping>
			</cfoutput>
		</cfsavecontent>
		<cfset local.xmlShippingInfo = XMLParse(local.xmlShippingInfo)>
		<cfset local.xmlShippingInfo = replaceNoCase(toString(local.xmlShippingInfo),'<?xml version="1.0" encoding="UTF-8"?>','')>
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			update dbo.store_orders
			set xmlShippingInfo = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.xmlShippingInfo#">
			where orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>
	</cffunction>
		
	<!--- RATES --->
	
	<cffunction name="getRateByItemID" access="public" output="false" returntype="query">
		<cfargument name="itemID" type="numeric" required="true" />

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="Qualify")>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_getRatesForAdminByItemID">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.itemID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryRates">
		</cfstoredproc>

		<cfreturn local.qryRates>
	</cffunction>
	
	<cffunction name="getRateByFormatID" access="public" output="false" returntype="query">
		<cfargument name="formatID" type="numeric" required="true" />

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="Qualify")>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_getRatesForAdminByFormatID">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryRates">
		</cfstoredproc>

		<cfreturn local.qryRates>
	</cffunction>
	
	<cffunction name="getRateByRateID" access="public" output="false" returntype="query">
		<cfargument name="rateID" type="numeric" required="true" />
		
		<cfset var qryRate = "" />
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_getRateByRateIDForAdmin">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateID#" />
			<cfprocresult name="qryRate" />
		</cfstoredproc>
		
		<cfreturn qryRate />
	</cffunction>
	
	<cffunction name="insertRate" access="public" output="false" returntype="struct">
		<cfargument name="formatID" type="numeric" required="true" />
		<cfargument name="GLAccountID" type="numeric" required="true" />
		<cfargument name="ShippingGLAccountID" type="numeric" required="true" />
		<cfargument name="rateName" type="string" required="true" />
		<cfargument name="reportCode" type="string" required="true" />
		<cfargument name="rate" type="string" required="true" />
		<cfargument name="startDate" type="date" required="true" />
		<cfargument name="endDate" type="string" required="true" default="NULL" />
		
		<cfset var local = structNew() />
		<cfset local.storeID = getStoreIDFromFormatID(formatID=arguments.formatID)/>
		
		<cfstoredproc procedure="store_createRate" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.storeID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			<cfif arguments.GLAccountID gt 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.GLAccountID#" />
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="true" />
			</cfif>
			<cfif arguments.ShippingGLAccountID gt 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.ShippingGLAccountID#" />
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="true" />
			</cfif>
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.rateName#" />
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.reportCode#" />
			<cfprocparam type="In" cfsqltype="cf_sql_double" value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#" />
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.startDate#" />
			<cfif len(arguments.endDate)>
				<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.endDate#" />
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_timestamp" null="true" />
			</cfif>
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.data.rateID" />
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.data.siteResourceID" />
		</cfstoredproc>
		
		<cfstoredproc procedure="store_reorderRates" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.data.rateID#" />
		</cfstoredproc>

		<cfset clearStoreCategoryCache(storeID=local.storeID)>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="updateRate" access="public" output="false" returntype="void">
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="GLAccountID" type="numeric" required="true" />
		<cfargument name="ShippingGLAccountID" type="numeric" required="true" />
		<cfargument name="rateName" type="string" required="true" />
		<cfargument name="reportCode" type="string" required="true" />
		<cfargument name="rate" type="string" required="true" />
		<cfargument name="startDate" type="date" required="true" />
		<cfargument name="endDate" type="string" required="true" />

		<cfset var qryUpdate = "" />

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			UPDATE 
				dbo.store_rates
			SET 
				GLAccountID = nullif(<cfqueryparam value="#arguments.GLAccountID#" cfsqltype="cf_sql_integer">,0),
				ShippingGLAccountID = nullif(<cfqueryparam value="#arguments.ShippingGLAccountID#" cfsqltype="cf_sql_integer">,0),
				rateName = <cfqueryparam value="#arguments.rateName#" cfsqltype="cf_sql_varchar" />,
				rate = <cfqueryparam value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#" cfsqltype="cf_sql_double" />,
				startDate = <cfqueryparam value="#arguments.startDate#" cfsqltype="cf_sql_timestamp" />,
				<cfif len(arguments.endDate)>
					endDate	= <cfqueryparam value="#arguments.endDate#" cfsqltype="cf_sql_timestamp"/>,
				<cfelse>
					endDate	= NULL,
				</cfif>
				reportCode = <cfqueryparam value="#arguments.reportCode#" cfsqltype="cf_sql_varchar" />
			WHERE 
				rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />
		</cfquery>
	</cffunction>

	<cffunction name="deleteRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true" />
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="storeSRID" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryDeleteRate" datasource="#application.dsn.membercentral.dsn#" >
				UPDATE sr
				SET sr.siteResourceStatusID = 3
				FROM dbo.cms_siteResources as sr
				INNER JOIN dbo.store_rates as r on r.siteResourceID = sr.siteResourceID
				WHERE r.rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer">;
			</cfquery>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_reorderRates">
				<cfprocparam type="In" value="#arguments.rateID#" cfsqltype="cf_sql_integer">
			</cfstoredproc>

			<cfset local.storeID = getStoreIDFromRateID(rateID=arguments.rateID)/>
			<cfset clearStoreCategoryCache(storeID=local.storeID)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data />
	</cffunction>

	<cffunction name="deleteMemberGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true" />
		<cfargument name="rateid" type="numeric" required="true" />
		<cfargument name="groupid" type="numeric" required="true" />
		<cfargument name="include" type="numeric" required="false" default="1">
		<cfargument name="storeSRID" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySiteResourceRights" datasource="#application.dsn.membercentral.dsn#">
				DECLARE @rateSRID int;

				SELECT @rateSRID = siteResourceID
				from dbo.store_rates
				where rateID = <cfqueryparam value="#arguments.rateid#" cfsqltype="cf_sql_integer" />;

				SELECT srr.resourceRightsID, sr.siteResourceID, sr.siteID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID
					and srr.siteID = sr.siteID
				where srr.resourceID = @rateSRID
				and srr.groupID = <cfqueryparam value="#arguments.groupID#" cfsqltype="cf_sql_integer" />
				and srr.include = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.include#">;
			</cfquery>

			<cfloop query="local.qrySiteResourceRights">
				<cfstoredproc procedure="cms_deleteSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam value="#local.qrySiteResourceRights.siteid#" cfsqltype="cf_sql_integer" />
					<cfprocparam value="#local.qrySiteResourceRights.siteResourceID#" cfsqltype="cf_sql_integer" />
					<cfprocparam value="#local.qrySiteResourceRights.resourceRightsID#" cfsqltype="cf_sql_integer" />
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>
	
	<cffunction name="getRateOverrides" access="public" returntype="query">
		<cfargument name="rateID" type="numeric" required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.rateOverrides" datasource="#application.dsn.memberCentral.dsn#">
			SELECT
				r.rateID,
				r.formatID,
				r.rateName,
				r.rate,
				r.startDate,
				r.endDate,
				r.rateOrder,
				r.siteResourceID,
				r.GLAccountID,
				r.reportCode,
				ro.rateOverrideID,
				ro.rateID,
				ro.rateOverrideName,
				ro.rateOverride,
				ro.startDate as overrideStartDate,
				ro.endDate as overrideEndDate,
				getDate()
			from 
				store_rates r
				inner join store_ProductRatesOverride ro on 
					ro.rateid = r.rateID
			where r.rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />
		</cfquery>
		<cfreturn local.rateOverrides />
	</cffunction>
	
	<cffunction name="getRateOverrideByID" access="public" output="false" returntype="query">
		<cfargument name="rateOverrideID" type="numeric" required="true" />

		<cfset var qryRateOverride = "" />

		<cfquery name="qryRateOverride" datasource="#application.dsn.membercentral.dsn#">
			SELECT
				rateOverrideID,
				rateID,
				rateOverrideName,
				rateOverride,
				startDate,
				endDate			
			FROM 
				dbo.store_productRatesOverride
			WHERE 
				rateOverrideID = <cfqueryparam value="#arguments.rateOverrideID#" cfsqltype="cf_sql_integer" />
		</cfquery>

		<cfreturn qryRateOverride />
	</cffunction>
	
	<cffunction name="insertRateOverride" access="public" output="false" returntype="numeric">
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="rateOverrideName" type="string" required="true" />
		<cfargument name="rateOverride" type="string" required="true" >
		<cfargument name="startDate" type="date" required="true" />
		<cfargument name="endDate" type="date" required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				INSERT INTO dbo.store_productRatesOverride (rateID, rateOverrideName, rateOverride, startDate, endDate)
				VALUES(
					<cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />,
					<cfqueryparam value="#arguments.rateOverrideName#" cfsqltype="cf_sql_varchar" />,
					<cfqueryparam value="#NumberFormat(replace(arguments.rateOverride,',','','ALL'),"0.00")#" cfsqltype="cf_sql_double" />,
					<cfqueryparam value="#arguments.startDate#" cfsqltype="cf_sql_timestamp" />,
					<cfqueryparam value="#arguments.endDate#" cfsqltype="cf_sql_timestamp" />
				);

				SELECT SCOPE_IDENTITY() AS rateOverrideID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH		
		</cfquery>
		
		<cfreturn local.data.rateOverrideID />
	</cffunction>
	
	<cffunction name="updateRateOverride" access="public" output="false" returntype="void">
		<cfargument name="rateOverrideID" type="numeric" required="true" />
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="rateOverrideName" type="string" required="true" />
		<cfargument name="rateOverride" type="string" required="true" >
		<cfargument name="startDate" type="date" required="true" />
		<cfargument name="endDate" type="date" required="true" />

		<cfset var qryUpdate = "" />

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			UPDATE 
				dbo.store_productRatesOverride
			SET 
				rateOverrideName = <cfqueryparam value="#arguments.rateOverrideName#" cfsqltype="cf_sql_varchar" />,
				rateOverride = <cfqueryparam value="#NumberFormat(replace(arguments.rateOverride,',','','ALL'),"0.00")#" cfsqltype="cf_sql_double" />,
				startDate = <cfqueryparam value="#arguments.startDate#" cfsqltype="cf_sql_timestamp" />,
				endDate	= <cfqueryparam value="#arguments.endDate#" cfsqltype="cf_sql_timestamp" />
			WHERE 
				rateOverrideID = <cfqueryparam value="#arguments.rateOverrideID#" cfsqltype="cf_sql_integer" />
				AND rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />
		</cfquery>
	</cffunction>

	<cffunction name="deleteRateOverride" access="public" output="false" returntype="struct" hint="Delete rate override">
		<cfargument name="mcproxy_siteID" type="numeric" required="true" />
		<cfargument name="rateOverrideID" type="numeric" required="true" />
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="storeSRID" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDelete">
				DELETE FROM dbo.store_productRatesOverride
				WHERE rateOverrideID = <cfqueryparam value="#arguments.rateOverrideID#" cfsqltype="cf_sql_integer" />
				AND rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="cf_sql_integer" />
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>	
	</cffunction>

	<cffunction name="doFormatMove" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="formatID" type="numeric" required="true" />
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_moveFormat">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.formatID#"/>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>
	
	<cffunction name="getAffirmationByID" access="public" output="false" returntype="query" hint="Get the Affirmation information">
		<cfargument name="Event" type="any" />

		<cfset var qryAffirmation = "" />

		<cfquery name="qryAffirmation" datasource="#application.dsn.memberCentral.dsn#">
			SELECT 
				ca.affirmationid,
				ca.affirmationCode,
				ca.dateIssued,
				ca.issuedByMemberID,
				ca.orderID,
				ca.productFormatID,
				ca.offeringID,
				ca.status,
				ca.assignToMemberID,
				spf.itemid,
				spf.name as formatName,
				cl.contentTitle as productName,
				m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as purchaserName,
				m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') as purchaserFullName,
				m.activememberid,
				co.completeByDate,
				case
					when co.completeByDate >= getDate() then 1
					else 1
				end as completeByDateInd,
				CAST( '0' AS bit) as validFormatInd,
				isNull(m2.company, '') as purchaserCompany
			from dbo.store s
			inner join dbo.store_orders so on so.storeid = s.storeid
				and s.storeid = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="cf_sql_integer" />
			inner join dbo.crd_affirmations ca on ca.orderid = so.orderid
				and ca.assignToMemberID is null
				and ca.affirmationid = <cfqueryparam value="#arguments.event.getValue('affirmationid')#" cfsqltype="cf_sql_integer" />
			inner join dbo.store_productFormats spf on spf.formatID = ca.productFormatID
			inner join dbo.store_products sp on sp.itemid = spf.itemid 
			inner join dbo.cms_contentLanguages cl on cl.contentid = sp.productContentID
			inner join dbo.crd_offerings co on co.offeringid = ca.offeringid
			inner join dbo.ams_members m on m.memberid = ca.issuedByMemberID
			inner join dbo.ams_members m2 on m2.memberid = m.activememberid
		</cfquery>
		
		<cfreturn qryAffirmation />
	</cffunction>
	
	<cffunction name="linkAffirmationToMember" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />

		<cfset local.arrCredits = ArrayNew(1)>
		<cfloop list="#arguments.event.getValue('offeringTypeIDList',0)#" index="local.thisOfferingTypeID">
			<cfif val(arguments.event.getValue('txt_ov_creditvalue_#local.thisOfferingTypeID#',0)) gt 0>
				<cfset local.thisCreditStr = StructNew()>
				<cfset local.thisCreditStr["offeringTypeID"] = int(local.thisOfferingTypeID)>
				<cfset local.thisCreditStr["creditValue"] = val(arguments.event.getValue('txt_ov_creditvalue_#local.thisOfferingTypeID#'))>
				<cfset arrayAppend(local.arrCredits, local.thisCreditStr)>
			</cfif>
		</cfloop>
		
		<cfset local.qryAffirmation = getAffirmationByID(arguments.event)>
		<cfif local.qryAffirmation.recordcount>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @siteID int, @affirmationID int, @assignToMemberID int, @assignToFormatID int, @dateClaimed datetime,
						@requestID int, @creditValue decimal(6,2), @offeringTypeID int, @offeringID int, @ASTID int,
						@lastDateToComplete smalldatetime;
					set @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteid')#" cfsqltype="cf_sql_integer">;
					set @affirmationID = <cfqueryparam value="#arguments.event.getValue('affirmationid')#" cfsqltype="cf_sql_integer">;
					set @assignToMemberID = <cfqueryparam value="#arguments.event.getValue('assignToMemberID')#" cfsqltype="cf_sql_integer">;
					set @assignToFormatID = <cfqueryparam value="#arguments.event.getValue('assignToFormatID')#" cfsqltype="cf_sql_integer">;
					set @dateClaimed = <cfqueryparam value="#arguments.event.getValue('dateClaimed')# #timeformat(now(),'h:mm tt')#" cfsqltype="cf_sql_timestamp">;
				
					BEGIN TRAN;
						update dbo.crd_affirmations
						set assignToMemberID = @assignToMemberID,
							assignToFormatID = @assignToFormatID,
							dateClaimed = @dateClaimed
						where affirmationid = @affirmationID;
						
						SELECT @offeringTypeID = offeringTypeID,
							@offeringID = offeringID,
							@ASTID = ASTID,
							@creditValue = creditValue 
						from dbo.crd_offeringTypes 
						where offeringid = <cfqueryparam value="#local.qryAffirmation.offeringID#" cfsqltype="cf_sql_integer">;
				
						SELECT @lastDateToComplete = completeByDate
						from dbo.crd_offerings
						where offeringid = <cfqueryparam value="#local.qryAffirmation.offeringID#" cfsqltype="cf_sql_integer">;

						<cfloop array="#local.arrCredits#" item="local.thisCredit">
							set @requestID = null;
							set @offeringTypeID = <cfqueryparam value="#local.thisCredit.offeringTypeID#" cfsqltype="cf_sql_integer" />;
							SELECT @creditValue = <cfqueryparam value="#local.thisCredit.creditValue#" cfsqltype="cf_sql_decimal" scale="2">;

							EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Store', @itemID=@affirmationID, @offeringTypeID=@offeringTypeID,
								@idnumber='', @lastDateToComplete=@lastDateToComplete, @creditAwarded=1, @creditValueAwarded=@creditValue,
								@addedViaAward=1, @requestID=@requestID OUTPUT;
						</cfloop>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
	</cffunction>

	<cffunction name="unclaimAffirmation" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="affirmationID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @affirmationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.affirmationID#">;
				
					BEGIN TRAN;
						DELETE from dbo.crd_requests 
						where affirmationID = @affirmationID;
				
						UPDATE dbo.crd_affirmations
						set assignToMemberID=null, assignToFormatID=null, dateClaimed=null
						where affirmationID = @affirmationID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteAffirmation" access="public" output="false" returntype="Struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="affirmationID" type="numeric" required="true" />
		<cfargument name="storeSRID" type="numeric" required="true" />

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryDelete" datasource="#application.dsn.membercentral.dsn#">
				update dbo.crd_affirmations
				set status = 'D'
				where affirmationid = <cfqueryparam value="#arguments.affirmationID#" cfsqltype="cf_sql_integer" />
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAffirmationCount" access="public" output="false" returntype="Numeric">
		<cfargument name="orderID" type="numeric" required="true" />
		<cfargument name="formatID" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cfquery name="local.qryAffirmationCount" datasource="#application.dsn.membercentral.dsn#">
			SELECT count(*) as affirmationCount
			from dbo.crd_affirmations as aff
			inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = aff.affirmationTypeID and cat.affirmationType = 'paper'
			where aff.orderID = <cfqueryparam value="#arguments.orderID#" cfsqltype="cf_sql_integer" />
			and aff.productFormatID = <cfqueryparam value="#arguments.formatID#" cfsqltype="cf_sql_integer" />
			and aff.status = 'A'
		</cfquery>
		
		<cfreturn local.qryAffirmationCount.affirmationCount>
	</cffunction>
	
	<cffunction name="removeAffirmation" access="public" output="false" returntype="void" >
		<cfargument name="removeAffirmationList" type="string" required="true" />

		<cfset var local = structNew() />
		
		<cfloop list="#arguments.removeAffirmationList#" index="local.thisItem">
			<cftry>
				<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
					update aff
					set aff.status = 'D'
					from dbo.crd_affirmations as aff
					inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = aff.affirmationTypeID and cat.affirmationType = 'paper'
					where aff.orderID = <cfqueryparam value="#listGetAt(local.thisItem,1,'|')#" cfsqltype="cf_sql_integer">
					and aff.productFormatID = <cfqueryparam value="#listGetAt(local.thisItem,2,'|')#" cfsqltype="cf_sql_integer">
				</cfquery>
				
				<cfcatch type="any"></cfcatch>
			</cftry>
		</cfloop>
	</cffunction>
	
	<cffunction name="getMyPurchasedProducts" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true" />
			
		<cfset var local = structNew()>

		<cfquery name="local.qryPurchased" datasource="#application.dsn.membercentral.dsn#" >
			SELECT od.productItemID
			from dbo.store_orders as o
			inner join dbo.store_orderDetails as od on od.orderID = o.orderID
			INNER JOIN dbo.ams_members as m on m.memberid = o.memberID 
			INNER JOIN dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				and mActive.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			where o.orderCompleted = 1
		</cfquery>
		
		<cfreturn local.qryPurchased />
	</cffunction>
	<cffunction name="getStoreCategoryCachedFileName" access="public" output="false" returntype="string" hint="Get the filepath to the cached wddx file of the store category listing">
		<cfargument name="storeID" type="numeric" required="true">

		<cfreturn application.paths.RAIDTempNoWeb.path & "clean_daily/store_ID_#arguments.storeID#_categoryCache_#dateformat(now(),"yyyymmdd")#.wddx">
	</cffunction>
	<cffunction name="getStoreCategoryRootPathsCachedFileName" access="public" output="false" returntype="string" hint="Get the filepath to the cached wddx file of the store category listing">
		<cfargument name="storeID" type="numeric" required="true">

		<cfreturn application.paths.RAIDTempNoWeb.path & "clean_daily/store_ID_#arguments.storeID#_categoryRootPathCache_#dateformat(now(),"yyyymmdd")#.wddx">
	</cffunction>

	<cffunction name="clearStoreCategoryCache" access="public" output="false" returntype="void">
		<cfargument name="storeID" type="numeric" required="true">

		<cfset var local = structNew()/>
		<cfset local.cachedFileName = getStoreCategoryCachedFileName(storeID=arguments.storeID)/>
		<cfif fileExists(local.cachedFileName)>
			<cffile action="delete" file="#local.cachedFileName#"/>
		</cfif>

		<cfset local.cachedFileName = getStoreCategoryRootPathsCachedFileName(storeID=arguments.storeID)/>
		<cfif fileExists(local.cachedFileName)>
			<cffile action="delete" file="#local.cachedFileName#"/>
		</cfif>


	</cffunction>
	<cffunction name="getStoreCategoryListing" access="public" output="false" returntype="array">
		<cfargument name="storeID" type="numeric" required="true">

		<cfset var local = structNew()/>

		<cfset local.cachedFileName = getStoreCategoryCachedFileName(storeID =arguments.storeID)/>
		<cfset local.cacheMiss = false>

		<cftry>
			<cffile action="read" file="#local.cachedFileName#" variable="local.cachedFileData" />
			<cfwddx action = "wddx2cfml" input = "#local.cachedFileData#" output = "local.arrAllCategories" />
			<cfcatch type="Any">
				<cfset local.cacheMiss = true>
				<!--- Send alert if failed for any reason other than file not existing --->
				<cfif fileExists(local.cachedFileName)>
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="Cached File Exists, but error reading. Will attempt to run query instead.") />
				</cfif>
			</cfcatch>
		</cftry>

		<cfif local.cacheMiss>
			<cfquery name="local.qryAllCategories" datasource="#application.dsn.memberCentral.dsn#">
				exec dbo.store_getCategoryListing @storeID=<cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfset local.arrAllCategories = arrayNew(1)>
			<cfloop query="local.qryAllCategories">
				<cfset local.thisCategory = {
					categoryID = local.qryAllCategories.categoryID, 
					categoryName = local.qryAllCategories.categoryName, 
					parentCategoryID = local.qryAllCategories.parentCategoryID, 
					thePath = local.qryAllCategories.thePath, 
					thepathexpanded = local.qryAllCategories.thepathexpanded,
					rootPath = local.qryAllCategories.rootPath, 
					directItemCount = local.qryAllCategories.directItemCount, 
					itemCount = local.qryAllCategories.itemCount, 
					numCategories = local.qryAllCategories.numCategories
				}>
				<cfset arrayAppend(local.arrAllCategories, local.thisCategory)>
			</cfloop>
			<cftry>
				<cfwddx action = "cfml2wddx" input = "#local.arrAllCategories#" output = "local.cachedData" />
				<cfif not directoryExists(GetDirectoryFromPath(local.cachedFileName))>
					<cfdirectory action="create" directory="#GetDirectoryFromPath(local.cachedFileName)#"/>
				</cfif>
				<cffile action="write" file="#local.cachedFileName#" output="#local.cachedData#" />
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="Error writing cached to disk.") />
				</cfcatch>
			</cftry>
		</cfif>
		<cfreturn local.arrAllCategories/>
	</cffunction>
	<cffunction name="getStoreCategoryRootPaths" access="public" output="false" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true">

		<cfset var local = structNew()/>
		<cfset local.cachedFileName = getStoreCategoryRootPathsCachedFileName(storeID =arguments.storeID)/>
		<cfset local.cacheMiss = false>

		<cftry>
			<cffile action="read" file="#local.cachedFileName#" variable="local.cachedFileData" />
			<cfwddx action = "wddx2cfml" input = "#local.cachedFileData#" output = "local.returnStruct" />
			<cfcatch type="Any">
				<cfset local.cacheMiss = true>
				<!--- Send alert if failed for any reason other than file not existing --->
				<cfif fileExists(local.cachedFileName)>
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="Cached File Exists, but error reading. Will attempt to run query instead.") />
				</cfif>
			</cfcatch>
		</cftry>

		<cfif local.cacheMiss>
			<cfquery name="local.qryAllCategories" datasource="#application.dsn.memberCentral.dsn#">
				SELECT c.categoryID, left (c.thePath,4) as rootPath
				FROM fn_getRecursiveStoreCategories(<cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_INTEGER">, null, null) as c 
				WHERE visibility = 'N'
			</cfquery>
			<cfset local.returnStruct = structNew()/>
			<cfloop query="local.qryAllCategories">
				<cfset local.returnStruct[local.qryAllCategories.categoryID] = local.qryAllCategories.rootPath />
			</cfloop>
			<cftry>
				<cfwddx action = "cfml2wddx" input = "#local.returnStruct#" output = "local.cachedData" />
				<cfif not directoryExists(GetDirectoryFromPath(local.cachedFileName))>
					<cfdirectory action="create" directory="#GetDirectoryFromPath(local.cachedFileName)#"/>
				</cfif>
				<cffile action="write" file="#local.cachedFileName#" output="#local.cachedData#" />
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="Error writing cached to disk.") />
				</cfcatch>
			</cftry>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>

	<cffunction name="getStoreIDFromItemID" access="private" output="false" returntype="numeric" hint="Get the store ID">
		<cfargument name="itemID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStoreID">
			SELECT storeid
			from dbo.store_products
			where itemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfreturn local.qryStoreID.storeID>
	</cffunction>

	<cffunction name="getStoreIDFromFormatID" access="private" output="false" returntype="numeric" hint="Get the store ID">
		<cfargument name="formatID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStoreID">
			SELECT p.storeid
			from dbo.store_products p
			inner join dbo.store_productFormats pf
				on pf.itemID = p.itemID
				and pf.formatid = <cfqueryparam value="#arguments.formatID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfreturn local.qryStoreID.storeID>
	</cffunction>

	<cffunction name="getStoreIDFromRateID" access="private" output="false" returntype="numeric" hint="Get the store ID">
		<cfargument name="rateID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStoreID">
			SELECT p.storeid
			from dbo.store_products p
			inner join dbo.store_productFormats pf
				on pf.itemID = p.itemID
			inner join dbo.store_rates r
				on r.formatID = pf.formatID
				and r.rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfreturn local.qryStoreID.storeID>
	</cffunction>

	<cffunction name="saveStreamProfile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="said" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="providerID" type="numeric" required="true">
		<cfargument name="profileName" type="string" required="true">
		<cfargument name="profileCode" type="string" required="true">
		<cfargument name="isActive" type="boolean" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySaveProfile" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @profileID int;

					BEGIN TRAN;
						<cfif arguments.profileID gt 0>
							SET @profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.profileID#">;

							UPDATE dbo.stream_profiles
							SET profileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.profileName#">,
								profileCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.profileCode#">,
								isActive = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isActive#">
							WHERE profileID = @profileID;

							DELETE FROM dbo.stream_profileFields
							WHERE profileID = @profileID;
						<cfelse>
							INSERT INTO dbo.stream_profiles (providerID, siteID, profileName, profileCode, isActive)
							VALUES (
								<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.providerID#">,
								<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.profileName#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.profileCode#">,
								<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isActive#">
							);

							SELECT @profileID = SCOPE_IDENTITY();
						</cfif>

						<cfloop collection="#arguments#" item="local.thisField">
							<cfif left(local.thisField,5) eq "prov_">
								insert into dbo.stream_profileFields (profileID, providerFieldID, value)
								values (@profileID, #GetToken(local.thisField,2,"_")#, <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments["#local.thisField#"]#">);
							</cfif>
						</cfloop>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteStreamProfile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="pid" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteProductsRights(siteID=arguments.mcproxy_siteID, storeSRID=arguments.storeSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryStoreID">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @profileID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pid#">;

					BEGIN TRAN;
						delete from dbo.stream_profileFields
						where profileID = @profileID;

						delete from dbo.stream_profiles
						where profileID = @profileID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasEditProductsRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.storeSRID, memberID=session.cfcuser.memberData.memberID, siteID=arguments.siteID)>

		<cfreturn tmpRights.EditProducts is 1>
	</cffunction>

	<cffunction name="hasDeleteProductsRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="storeSRID" type="numeric" required="true">

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.storeSRID, memberID=session.cfcuser.memberData.memberID, siteID=arguments.siteID)>

		<cfreturn tmpRights.DeleteProducts is 1>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rateIDList" type="string" required="true">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="shippingID" type="numeric" required="true" />
		
		<cfscript>
		var local = structNew();
		local.objCart = CreateObject("component", "shoppingCart");
		
		local.returnStruct = { success=false, isvalidcoupon=false, couponID=0, couponResponse="Invalid Promo Code", qualifiedRateIDList="", totalItemDiscountExcTax=0, qryDiscountItems=QueryNew("") };

		arguments.couponCode = trim(arguments.couponCode);
		
		// if no length 
		if (len(arguments.couponCode) is 0) return local.returnStruct;

		var memberID = arguments.memberID;

		cfxml(variable="local.cartItemsXML") {
			writeOutput('<cart>');
			ListEach(arguments.rateIDList,function(rateid, index) {
				writeOutput('<item mid="#memberID#" rateid="#rateid#" itemtype="store" />');
			});
			writeOutput('</cart>');
		}

		local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','');

		var sqlParams = {
			siteID = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" },
			applicationType = { value="Store", cfsqltype="CF_SQL_VARCHAR" },
			cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
			couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
		};

		var qryValidCoupon = queryExecute("
			SET NOCOUNT ON;
			
			DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
				@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml,
				@isAvailable bit;
			
			EXEC dbo.tr_isValidCouponCodeForAdmin @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
				@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
				@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT, @isAvailable=@isAvailable OUTPUT;
			
			SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML, @isAvailable AS isAvailable;
			", 
			sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
		);

		local.couponID = val(qryValidCoupon.couponID);
		local.returnStruct.couponResponse = qryValidCoupon.couponMessage;
		local.isAvailable = val(qryValidCoupon.isAvailable);
		local.arrRates = xmlSearch(qryValidCoupon.qualifiedCartItemsXML,"/cart/item").map(function(thisItem) { return arguments.thisItem.XmlAttributes.rateid; });
		local.qualifiedRateIDList = arrayLen(local.arrRates) ? arrayToList(local.arrRates) : "";

		// valid coupon
		if (local.couponID AND listLen(local.qualifiedRateIDList)) {
			local.returnStruct.isValidCoupon = true;
			
			local.strOrderDiscount = getStoreItemsDiscount(siteID=arguments.siteID, couponID=local.couponID, qualifiedRateIDList=local.qualifiedRateIDList, 
				storeID=arguments.storeID, orderNumber=arguments.orderNumber, shippingID=arguments.shippingID);
			local.returnStruct.couponID = local.couponID;
			if(not local.isAvailable){
				local.returnStruct.couponResponse &= "<br/>(This coupon is outside the allowed date range, but has been applied anyway)";
			}
			local.returnStruct.qualifiedRateIDList = local.qualifiedRateIDList;
			local.returnStruct.totalItemDiscountExcTax = local.strOrderDiscount.totalItemDiscountExcTax;
			local.returnStruct.qryDiscountItems = local.strOrderDiscount.qryDiscountItems;
			local.returnStruct.success = true;
		}		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getStoreItemsDiscount" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="qualifiedRateIDList" type="string" required="true">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="shippingID" type="numeric" required="true" />
		
		<cfset var local = structNew()>

		<cfset local.objCart = CreateObject("component", "shoppingCart")>
		<cfset local.strDiscount = { success=false, qryDiscountItems=queryNew(""), totalItemDiscountExcTax=0, totalItemDiscount=0 }>

		<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.siteID, couponID=arguments.couponID)>

		<cfset local.strCoupon = { couponID=local.qryCoupon.couponID, couponCode=local.qryCoupon.couponCode, pctOff=local.qryCoupon.pctOff, pctOffMaxOff=local.qryCoupon.pctOffMaxOff,
			amtOff=local.qryCoupon.amtOff, redeemDetail=local.qryCoupon.redeemDetail, invoiceDetail=local.qryCoupon.invoiceDetail }>

		<cfset local.qryStoreCart = local.objCart.getCartData(storeid=arguments.storeID, orderNumber=arguments.orderNumber, shippingid=arguments.shippingID)>

		<!--- coupon qualified cart items --->
		<cfquery name="local.qryQualifiedStoreCart" dbtype="query">
			SELECT cartItemID, quantity, rateID, rate, rateOverride, rateGLAccountID
			from [local].qryStoreCart
			where rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.qualifiedRateIDList#">)
		</cfquery>

		<cfif local.qryQualifiedStoreCart.recordCount>
			<cfset local.qryDiscountItems = queryNew("cartItemID,quantity,itemAmount,itemDiscountExcTax","integer,integer,decimal,decimal")>
		<cfelse>
			<cfreturn local.strDiscount>
		</cfif>

		<!--- insert qualified items and calculate discounts later --->
		<cfloop query="local.qryQualifiedStoreCart">
			<cfif len(local.qryQualifiedStoreCart.rateOverride)>
				<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rateOverride) * local.qryQualifiedStoreCart.quantity>
			<cfelse>
				<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rate) * local.qryQualifiedStoreCart.quantity>
			</cfif>

			<cfif QueryAddRow(local.qryDiscountItems)>
				<cfset QuerySetCell(local.qryDiscountItems,"cartItemID",local.qryQualifiedStoreCart.cartItemID)>
				<cfset QuerySetCell(local.qryDiscountItems,"quantity",local.qryQualifiedStoreCart.quantity)>
				<cfset QuerySetCell(local.qryDiscountItems,"itemAmount",local.totalItemAmount)>
				<cfset QuerySetCell(local.qryDiscountItems,"itemDiscountExcTax",0)>
			</cfif>
		</cfloop>

		<cfquery name="local.qryTotals" dbtype="query">
			SELECT sum(itemAmount) as totalItemAmount
			from [local].qryDiscountItems
		</cfquery>

		<cfset local.totalDiscountExcTax = getCouponDiscount(strCoupon=local.strCoupon, totalItemAmount=local.qryTotals.totalItemAmount)>
		<cfset local.allocateDiscountAmountExcTax = local.totalDiscountExcTax>

		<cfloop query="local.qryDiscountItems">
			<cfset local.thisItemDiscountExcTax = min(local.qryDiscountItems.itemAmount,local.allocateDiscountAmountExcTax)>

			<cfset QuerySetCell(local.qryDiscountItems,"itemDiscountExcTax",local.thisItemDiscountExcTax,local.qryDiscountItems.currentrow)>

			<cfset local.allocateDiscountAmountExcTax = local.allocateDiscountAmountExcTax - local.thisItemDiscountExcTax>

			<cfif local.allocateDiscountAmountExcTax eq 0>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfquery name="local.qryDiscountTotals" dbtype="query">
			SELECT sum(itemDiscountExcTax) as totalItemDiscountExcTax
			from [local].qryDiscountItems
		</cfquery>

		<cfset local.strDiscount["qryDiscountItems"] = local.qryDiscountItems>
		<cfset local.strDiscount["totalItemDiscountExcTax"] = local.qryDiscountTotals.totalItemDiscountExcTax>
		<cfset local.strDiscount["success"] = true>

		<cfreturn local.strDiscount>
	</cffunction>

	<cffunction name="getCouponDiscount" access="private" output="false" returntype="numeric">
		<cfargument name="strCoupon" type="struct" required="true">
		<cfargument name="totalItemAmount" type="numeric" required="true" hint="total amount excluding tax">

		<cfset var local = structNew()>
		<cfset local.totalDiscountExcTax = 0>

		<cfif arguments.totalItemAmount eq 0>
			<cfreturn local.totalDiscountExcTax>
		</cfif>

		<cfif val(arguments.strCoupon.pctOff) gt 0>
			<cfset local.totalDiscountExcTax = NumberFormat((arguments.totalItemAmount * (arguments.strCoupon.pctOff / 100)),"9.99")>

			<cfif val(arguments.strCoupon.pctOffMaxOff) gt 0 and max(arguments.strCoupon.pctOffMaxOff,local.totalDiscountExcTax) eq local.totalDiscountExcTax>
				<cfset local.totalDiscountExcTax = arguments.strCoupon.pctOffMaxOff>
			</cfif>
		<cfelseif val(arguments.strCoupon.amtOff) gt 0>
			<cfset local.totalDiscountExcTax = arguments.strCoupon.amtOff>
		</cfif>

		<cfset local.grandTotalItemAmountExcTax = NumberFormat(arguments.totalItemAmount - local.totalDiscountExcTax,"9.99")>

		<cfif local.grandTotalItemAmountExcTax lte 0>
			<cfset local.totalDiscountExcTax = arguments.totalItemAmount>
		</cfif>

		<cfreturn local.totalDiscountExcTax>
	</cffunction>

	<cffunction name="addDiscountToOrderQuery" access="public" output="false" returntype="void">
		<cfargument name="qryOrder" type="query" required="true">
		<cfargument name="qryDiscountItems" type="query" required="true">

		<cfset var local = structNew()>
		
		<cfloop query="arguments.qryOrder">
			<cfset local.itemDiscountExcTax = 0>
			<cfif arguments.qryDiscountItems.recordCount and listFindNoCase(valueList(arguments.qryDiscountItems.cartItemID), arguments.qryOrder.cartItemID)>
				<cfquery name="local.qryDiscountTmp" dbtype="query">
					SELECT itemDiscountExcTax
					from [arguments].qryDiscountItems
					where cartItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryOrder.cartItemID#">
				</cfquery>
				<cfif local.qryDiscountTmp.recordCount>
					<cfset local.itemDiscountExcTax = local.qryDiscountTmp.itemDiscountExcTax>
				</cfif>
			</cfif>
			<cfset QuerySetCell(arguments.qryOrder,"itemDiscountExcTax",local.itemDiscountExcTax,arguments.qryOrder.currentrow)>
		</cfloop>
	</cffunction>

	<cffunction name="hasValidCouponsForStore" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rateIDList" type="string" required="true">

		<cfset var local = structNew()>
	
		<cfxml variable="local.cartItemsXML">
			<cfoutput>
				<cart>
					<cfloop list="#arguments.rateIDList#" item="local.thisRateID">
						<item mid="#arguments.memberID#" rateid="#local.thisRateID#" itemtype="store" />
					</cfloop>
				</cart>
			</cfoutput>
		</cfxml>
		<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>
		
		<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="Store">
			<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.cartItemsXML#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.hasValidCoupon">
		</cfstoredproc>

		<cfreturn local.hasValidCoupon>
	</cffunction>

	<cffunction name="getStoreOrderCouponAppliedItems" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orderID" type="numeric" required="true">

		<cfset var qryStoreOrderCouponAppliedItems = "">
		
		<cfquery name="qryStoreOrderCouponAppliedItems" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @orgID int, @orderID int;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			set @orderID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">;

			SELECT t.amount, tra.itemID
			from dbo.tr_transactionDiscounts as d
			inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = d.transactionID and t.statusID = 1
			inner join dbo.tr_coupons as c on c.couponID = d.couponID
			inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = 1 and tr.transactionID = t.transactionID
			inner join dbo.tr_applications as tra on tra.orgID = @orgID and tra.transactionID = tr.appliedToTransactionID and tra.status = 'A'
			where d.orgID = @orgID
			and d.isActive = 1
			and d.itemType = 'StoreOrder'
			and d.itemID = @orderID;
		</cfquery>

		<cfreturn qryStoreOrderCouponAppliedItems>
	</cffunction>
	
	<cffunction name="getStoreDetails" access="public" output="FALSE" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="applicationInstanceID" type="numeric" required="true">

		<cfset var qryStore = "">
	
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryStore">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT s.storeID, sr.siteResourceID
			FROM dbo.store s
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID and ai.applicationInstanceID = s.applicationInstanceID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			WHERE s.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#"> 
			AND s.siteID = @siteID;
		</cfquery>
		
		<cfreturn qryStore>
	</cffunction>

</cfcomponent>