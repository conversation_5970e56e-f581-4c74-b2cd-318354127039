use membercentral
GO

DECLARE @crlf VARCHAR(10) = CHAR(13) + CHAR(10);

SELECT s.siteCode, mp.profileID, mp.profileName, o.accountingEmail,  
	CONCAT('{ "c":"auditLog", "d": { "AUDITCODE":"PP", "ORGID":',o.orgID,', "SITEID":',s.siteID,', "ACTORMEMBERID":11, "ACTIONDATE":"',
		CONVERT(VARCHAR(20),GETDATE(),120),'", "MESSAGE":"Payment Profile ',dbo.fn_cleanInvalidXMLChars(mp.profileName),' has been updated.',@crlf,
		'The following changes have been made:',@crlf,
		'maxFailedAutoAttempts changed from 0 to 3',@crlf,
		'minDaysFailedCleanup changed from ',ISNULL(mp.minDaysFailedCleanup,0),' to 7',@crlf,
		'" } }') as mongoEntry,
	(SELECT COUNT(historyID) FROM dbo.tr_paymentHistory where profileID = mp.profileID and isSuccess = 1 and paymentType = 'payment' and datePaid > '6/1/2025') as numPayments
INTO #tmpMPChanges
FROM dbo.mp_profiles as mp
INNER JOIN dbo.sites as s on s.siteID = mp.siteID
INNER JOIN dbo.organizations as o on o.orgID = s.orgID
WHERE mp.gatewayID IN (10,12,17,19)
AND mp.status <> 'D'
AND mp.maxFailedAutoAttempts IS NULL;

UPDATE mp
SET mp.maxFailedAutoAttempts = 3, 
	mp.minDaysFailedCleanup = 7
FROM dbo.mp_profiles as mp
INNER JOIN #tmpMPChanges as tmp on tmp.profileID = mp.profileID;

INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
SELECT mongoEntry
FROM #tmpMPChanges;

SELECT *
FROM #tmpMPChanges
ORDER BY siteCode, ProfileName;

DROP TABLE #tmpMPChanges;
GO
