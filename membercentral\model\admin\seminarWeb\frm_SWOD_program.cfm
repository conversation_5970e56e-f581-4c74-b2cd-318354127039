<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.programTabsJS">
<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/seminarweb.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/ext/dhtmlxgrid_drag.js"></script>
	<script type="text/javascript" src="/assets/admin/javascript/resourceCustomFields.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/admin/javascript/featuredImages.js#local.assetCachingKey#"></script>
	<style type="text/css">
		div##issuePanel ul { margin-top:5px; margin-bottom:0; }
		div##issuePanel ul li { padding:3px 0; }
		div##statementPanel ul { margin-top:5px; margin-bottom:0; }
		div##statementPanel ul li { padding:3px 0; }
	</style>
	<script language="javascript">
		var #ToScript(local.seminarID,"swod_seminarid")#;
		var #ToScript(local.editSWBProgramLink,'link_editswbprogram')#
		var #ToScript(arguments.event.getValue('mc_siteInfo.sitecode'),"sw_sitecode")#;
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteid'),"sw_siteid")#;
		var #ToScript(len(local.qrySeminar.dateCatalogStart),"sw_hascatalogstartdate")#;
		var #ToScript(local.editSWODProgram,'link_editswodprogram')#
		var #toScript(local.editSWODTitleLink,'link_editswodtitle')#;
		var #ToScript(local.viewProgressLink,'link_viewswodprogress')#;
		var #ToScript(local.manageCreditLink,'link_managecredit')#;
		var #ToScript(this.link.viewCertificate,'link_viewcertificate')#;
		var #ToScript(local.getCommunicationLink,'link_viewcommunication')#;
		var #ToScript(local.resendInstructionsLink,'link_resendinstructions')#;
		var #ToScript(this.link.editMember,'link_editmember')#;
		var #ToScript(local.programAdded,'programAdded')#;
		programAdded = programAdded === "true";
		<cfif local.hasEditRights>
			let SWLinksTable, SWSubjectsTable;
			var titlesListTable;
			var #toScript(local.SWLinksLink,'link_SWLinks')#
			var #toScript(local.editSWLink,'link_editSWLink')#
			var #toScript(local.categoriesListLink,'categoriesListLink')#;
			var #toScript(local.addSWCategoryLink,'link_addSWCategory')#;
			var #toScript(local.titlesListLink,'titlesListLink')#;
			var #toScript(local.addSWODTitleLink,'link_addSWODTitle')#;
		</cfif>
		<cfif local.hasManageSWODRatesRights>
			var swProgramRatesTable;
			var #toScript(local.ratesListLink,'link_listSWRates')#
			var #toScript(local.permissionsGridAddLink,'mca_permsadd_link')#
			<cfif local.hasSWODRateChangeRights>
				var #toScript(local.editSWRate,'link_editSWRate')#
				var #ToScript(local.manageCopyRatesLink,'link_manageCopyRates')#
				var #toScript(local.copySWRate,'link_copySWRate')#
			</cfif>
		</cfif>
		<cfif local.hasManageSWODRegistrantsRights>
			var #ToScript(local.exportRegPromptLink,'link_exportswregprompt')#
			var #ToScript(local.exportRegistrantsFormResponsesLink,'link_exportswformresponses')#
			var #ToScript(local.changeRegistrantPriceLink,'link_changeregprice')#
			var #ToScript(local.removeEnrollmentLink,'link_removeenrollment')#
			var SWODRegistrantsListTable;
			var #ToScript(local.SWODRegistrantsLink,'link_swodregistrantslist')#;
			var sw_reggridmode = 'grid';

			var #ToScript(local.addSWODPaymentLink,'link_addswpayment')#
			var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,'sw_hastransallocaterights')#
			<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
				var #ToScript(local.allocateSWODPaymentLink,'link_allocateswpayment')#
			</cfif>
		</cfif>
		<cfif local.hasMassEmailSWODRegistrantsRights>
			var #ToScript(local.massEmailRegistrantsLink,'link_massemailreg')#
		</cfif>
		<cfif local.hasMassEmailSWODCertificatesRights>
			var #ToScript(local.massEmailCertificatesLink,'link_massemailcert')#
		</cfif>
		<cfif local.hasMassEmailSWODResendInstructionsRights>
			var #ToScript(local.massEmailConnectionInstructionsLink,'link_massemailresendinstructions')#
		</cfif>
		<cfif local.hasAddSWODRegistrantRights>
			var #ToScript(local.addSWRegLink,'link_addSWReg')#
			var #ToScript(encodeForHTML(local.qrySeminar.seminarName),'seminarName')#
		</cfif>
		<cfif local.hasToggleSWODRegistrantsRights>
			var #ToScript(local.allowRegistrants,'sw_allowregistrants')#;

			$(function() {
				setToggleSWProgramRegistrantsButton(!Number(sw_allowregistrants));
			});
		</cfif>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			var swBillingLogsTable;
			var #ToScript(local.swBillingLogsLink,'link_swBillingLogs')#;
		</cfif>
		
		var sw_seminarid = swod_seminarid;
		var sw_itemtype = 'SWOD';
		var #ToScript(local.qryAssociation.participantID,"sw_participantid")#
		var #ToScript(local.hasLockSWProgramRights,'sw_haslockprogramrights')#
		var sw_lockprogramsettings = #local.qrySeminar.lockSettings is 1#;
		var disabledElements = [];
		
		var gridInitArray = new Array();
		gridInitArray["programTab"] = false;
		gridInitArray["registrantsTab"] = false;
		<cfif local.showBillingTab>
			gridInitArray["billingTab"] = false;
		</cfif>
	
		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "programTab":
						initProgramTab(); break
					case "registrantsTab":
						loadSWODRegistrantSearchTab(); break;
					<cfif local.showBillingTab>
						case "billingTab":
							initSWBillingLogsTable(); break;
					</cfif>
				}
			}
		}
		
		function closeBox() { MCModalUtils.hideModal(); }

		<cfif NOT local.programAdded>
			function showHideSWIssuesPanel() {
				$('##issuePanel, ##showIssue, ##hideIssue').toggle();
			}
			function showHideSWStatementPanel() {
				$('##statementPanel, ##showStatement, ##hideStatement').toggle();
			}
		</cfif>
		<cfif !local.isPublisher>
			function onOptOutOfSyndicatedProgram() {
				self.location.href = '#local.listProgramsLink#';
			}
		</cfif>
		$(function() {
			mca_initNavPills('SWODProgramTabs', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
			manageSWProgramSettings('swod');
		});
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.programTabsJS#">

<cfoutput>
<cfif local.programAdded>
	<div class="progress mb-3" style="height: 20px;">
		<div id="progressBarTop" class="progress-bar bg-success" role="progressbar" style="text-indent: 10px; width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
	</div>
</cfif>
<button class="btn btn-sm btn-warning float-right <cfif NOT local.showSyndicationWarning>d-none</cfif>" id="syndicationWarning" type="button" data-container="body" data-trigger="hover" data-toggle="popover" data-placement="left" data-content="This seminar has enabled syndication, but no associations are opted in. We'll apply the syndication fee schedule to this seminar."><i class="fa-regular fa-triangle-exclamation"></i> Syndication Warning</button>
<cfif local.canOptOutOfSyndicateSite AND local.enrollmentCount EQ 0>
	<button class="btn btn-sm btn-primary float-right" <cfif local.isSWProgramLocked>disabled</cfif> id="optOutOfProgram" onclick="this.disabled=true; removeSWProgramPartner('#encodeForHTML(local.qrySeminar.seminarName)#');" type="button" data-container="body" data-trigger="hover" data-toggle="popover" data-placement="left" >Opt-out of Syndicated Program</button>
</cfif>

<h4 id="dispSeminarTitle" class="<cfif len(local.qrySeminar.seminarSubTitle)>mb-1<cfelse>mb-2</cfif>">#encodeForHTML(local.qrySeminar.seminarName)#</h4>
<h5 id="dispSeminarSubTitle" class="mb-3<cfif not len(local.qrySeminar.seminarSubTitle)> d-none</cfif>">#encodeForHTML(local.qrySeminar.seminarSubTitle)#</h5>
<cfif NOT local.programAdded>
	<div class="alert alert-warning mb-3 mt-3 seminarSetupIssues <cfif NOT arrayLen(local.arrSeminarSetupIssues)>d-none</cfif> ">
		This seminar is not accepting registrations at this time. &nbsp; &nbsp;
		<a href="##" onclick="showHideSWIssuesPanel();return false;">
			<span id="showIssue">Why not?</span>
			<span id="hideIssue" style="display:none;">Hide details.</span>
		</a>
		<div id="issuePanel" style="display:none;">
			<ul>
			<cfloop array="#local.arrSeminarSetupIssues#" index="local.thisIssue">	
				<li>#local.thisIssue#</li>
			</cfloop>
			</ul>
		</div>
	</div>
	<div class="alert alert-success mb-3 mt-3 seminarSetupStatements <cfif arrayLen(local.arrSeminarSetupIssues)>d-none</cfif> ">
		This seminar is accepting registrations at this time. &nbsp; &nbsp;
		<a href="##" onclick="showHideSWStatementPanel();return false;">
			<span id="showStatement">Learn more.</span>
			<span id="hideStatement" style="display:none;">Hide details.</span>
		</a>
		<div id="statementPanel" style="display:none;">
			<ul>
			<cfloop array="#local.arrSeminarSetupSuccess#" index="local.thisStatement">	
				<li>#local.thisStatement#</li>
			</cfloop>
			</ul>
		</div>
	</div>
</cfif>

<div id="lockedProgramSetting" class= "<cfif local.qrySeminar.lockSettings is not 1>d-none</cfif>">
	<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show " role="alert">
		<span class="font-size-lg d-block d-40 mr-2 text-center">
			<i class="fa-regular fa-lock"></i>
		</span>
		<span>
			<strong class="d-block">This Program's Settings are Locked</strong> 
			When locked, changes cannot be made to this program's settings. Contact your Client Administrator to unlock.
		</span>
	</div>
</div>

<ul class="nav nav-pills nav-pills-dotted" id="SWODProgramTabs">
	<cfset local.thisTabName = "program">
	<cfset local.thisTabID = "programTab">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
			aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#" onclick="openProgramTabSection('program-basics');">Program</a>
	</li>
	<cfif local.hasManageSWODRegistrantsRights AND NOT local.programAdded>
		<cfset local.thisTabName = "registrants">
		<cfset local.thisTabID = "registrantsTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Registrants</a>
		</li>
	</cfif>
	<cfif local.showBillingTab AND NOT local.programAdded>
		<cfset local.thisTabName = "billing">
		<cfset local.thisTabID = "billingTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Billing <span class="superuser small"></span></a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="divSWODTabContent">
	<div class="tab-pane fade" id="pills-programTab" role="tabpanel" aria-labelledby="programTab">
		<cfinclude template="frm_SWOD_program_overall.cfm">
	</div>
	<cfif local.hasManageSWODRegistrantsRights AND NOT local.programAdded>
		<div class="tab-pane fade" id="pills-registrantsTab" role="tabpanel" aria-labelledby="registrantsTab">
			<cfinclude template="frm_SWOD_program_registrants.cfm">
		</div>
	</cfif>
	<cfif local.showBillingTab AND NOT local.programAdded>
		<div class="tab-pane fade" id="pills-billingTab" role="tabpanel" aria-labelledby="billingTab">
			<cfinclude template="frm_SWOD_program_billing.cfm">
		</div>
	</cfif>
</div>
</cfoutput>