ALTER PROC dbo.cache_members_populateMemberConditionCache_CONSENTLIST_ENTRY

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @GlobalOptOutModeID int;
	SELECT TOP 1 @orgID = orgID FROM #tblCondALL;
	SELECT @GlobalOptOutModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'GlobalOptOut';


	-- email types, no dates
	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NULL
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTypes AS clSplitET 
		ON clSplitET.conditionID = clSplit.conditionID
		AND clSplitET.emailTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		AND me.emailTypeID = clSplitET.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END;


	-- email types, with dates
	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND (clSplit.clDateAddedLower IS NOT NULL OR clSplit.clDateAddedUpper IS NOT NULL)
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTypes AS clSplitET 
		ON clSplitET.conditionID = clSplit.conditionID
		AND clSplitET.emailTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		AND me.emailTypeID = clSplitET.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE 
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated BETWEEN clSplit.clDateAddedLower AND clSplit.clDateAddedUpper THEN 1
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NULL AND clm.dateCreated >= clSplit.clDateAddedLower THEN 1
			WHEN clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated <= clSplit.clDateAddedUpper THEN 1
			ELSE 0 
			END;



	-- email tags, no dates

	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NULL
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTagTypes AS clSplitETag 
		ON clSplitETag.conditionID = clSplit.conditionID
		AND clSplitETag.emailTagTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmailTags AS metag 
		ON metag.orgID = @orgID
		AND metag.memberID = m.memberID
		and metag.emailTagTypeID = clSplitETag.emailTagTypeID
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		and me.emailTypeID = metag.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END;



	-- email tags, with dates

	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND (clSplit.clDateAddedLower IS NOT NULL OR clSplit.clDateAddedUpper IS NOT NULL)
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTagTypes AS clSplitETag 
		ON clSplitETag.conditionID = clSplit.conditionID
		AND clSplitETag.emailTagTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmailTags AS metag 
		ON metag.orgID = @orgID
		AND metag.memberID = m.memberID
		and metag.emailTagTypeID = clSplitETag.emailTagTypeID
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		and me.emailTypeID = metag.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE 
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated BETWEEN clSplit.clDateAddedLower AND clSplit.clDateAddedUpper THEN 1
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NULL AND clm.dateCreated >= clSplit.clDateAddedLower THEN 1
			WHEN clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated <= clSplit.clDateAddedUpper THEN 1
			ELSE 0 
			END;


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
