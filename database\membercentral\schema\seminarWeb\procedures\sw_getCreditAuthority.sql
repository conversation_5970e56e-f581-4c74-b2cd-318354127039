ALTER PROC dbo.sw_getCreditAuthority
@authorityID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT ca.authorityID, ca.code, ca.jurisdiction, ca.authorityName, ca.contact, 
		ca.address, ca.city, ca.state, ca.ZIP, ca.phone, ca.email, ca.website, 
		ca.wddxCreditTypes, ca.creditIDText, 
		caswl.promptInterval as SWLpromptInterval, 
		caswl.promptTypeID as SWLpromptTypeID, 
		caswl.mustAttend as SWLmustAttend, 
		caswl.mustAttendMinutes as SWLmustAttendMinutes,
		caswl.preExamRequired as SWLPreExamRequired,
		caswl.examRequired as SW<PERSON>examRequired,
		caswl.evaluationRequired as SWLEvaluationRequired,
		caswl.daysToCompleteExam as SWLdaysToCompleteExam,
		caswl.daysToCompleteEvaluation as SWLdaysToCompleteEvaluation,		
		casod.promptInterval AS swodpromptInterval, 
		casod.promptTypeID AS swodpromptTypeID, 
		casod.preExamRequired AS swodPreExamRequired, 
		casod.examRequired AS swodexamRequired, 
		casod.evaluationRequired AS swodevaluationRequired, 
		casod.mediaRequiredPct AS swodmediaRequiredPct,  
		casod.daysToComplete AS swoddaysToComplete,
		casod.mustAttendMinutes as swodMustAttendMinutes
	FROM dbo.tblCreditAuthorities AS ca 
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON ca.authorityID = caswl.authorityID 
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON ca.authorityID = casod.authorityID
	WHERE ca.authorityID = @authorityID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
