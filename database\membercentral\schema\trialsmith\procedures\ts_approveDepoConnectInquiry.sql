ALTER PROC dbo.ts_approveDepoConnectInquiry
@inquiryID int,
@emailSubject varchar(250),
@emailContent varchar(max),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @conversationID int, @messageTypeID int, @adminToolSiteResourceID int, @openStatusID int, @newContentID int,
		@newResourceID int, @resourceTypeID int, @contentVersionID int, @sendOnDate datetime = getdate(), @messageID int, @conversationSubject varchar(400),
		@recipientID int, @languageID int, @fromName varchar(200), @fromFirm varchar(200), @fromNameAndFirm varchar(200), @tier varchar(12), @defaultMarketingSubUserID int, @fromEmail varchar(200),
		@replyToEmail varchar(200), @emailTypeID int, @respondentDepoMemberDataID int, @respondentName varchar(200), @respondentFirm varchar(200), @respondentEmail varchar(200),
		@recipientMemberID int, @fieldID int, @inquiryOptOutListID int, @globalOptOutListID int, @orgSysMemberID int, @consentListIDs VARCHAR(MAX);

	IF OBJECT_ID('tempdb..#tmpConversations') IS NOT NULL
		DROP TABLE #tmpConversations;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL
		DROP TABLE #tmpRecipientDetails;
	CREATE TABLE #tmpConversations (conversationID int, requestorDepoMemberDataID int, fromName varchar(200), fromFirm varchar(200), replyToEmail varchar(200),
		respondentDepoMemberDataID int, respondentName varchar(200), respondentFirm varchar(200), respondentEmail varchar(200));
	CREATE TABLE #tmpRecipientDetails (recipientID INT, recipientMemberID INT, recipientEmail varchar(255), messageID int, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));

	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @languageID = membercentral.dbo.fn_getLanguageID('en');
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = 'TS';
	SELECT @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SELECT @openStatusID = statusID FROM dbo.expertConnectInquiryStatuses WHERE statusCode = 'Open';
	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EXPERTINQUIRY';
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @defaultMarketingSubUserID = defaultMarketingSubUserID FROM membercentral.dbo.platform_environments WHERE environmentName = @tier;
	SELECT @fromEmail = 'noreply@' + sendingHostName FROM platformMail.dbo.sendgrid_subuserDomains WHERE subuserID = @defaultMarketingSubUserID;
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @adminToolSiteResourceID = ast.siteResourceID
	FROM membercentral.dbo.admin_siteTools ast
	INNER JOIN membercentral.dbo.admin_toolTypes att ON att.tooltypeID = ast.toolTypeID
 		AND att.toolType = 'TrialSmithTools'
	WHERE ast.siteID = @siteID;

	SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
	FROM dbo.expertConnectInquirySettings;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND clt.consentListTypeName = 'Global Lists'
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
		AND modeName = 'GlobalOptOut';

	INSERT INTO #tmpConversations (conversationID, requestorDepoMemberDataID, fromName, fromFirm, replyToEmail, respondentDepoMemberDataID,
		respondentName, respondentFirm, respondentEmail)
	SELECT c.conversationID, req.depoMemberDataID, req.firstName + ' ' + req.lastName, d1.BillingFirm,
		i.emailAddressSlug + '_' + CAST(i.inquiryID as varchar(10)) + '_' + CAST(req.participantID as varchar(10)) + '@depoconnect.trialsmith.com',
		resp.depomemberdataID, resp.firstName + ' ' + resp.lastName, d2.BillingFirm, resp.email
	FROM dbo.expertConnectInquiryConversations c
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS req
		INNER JOIN dbo.expertConnectInquiryRoles AS r1 ON r1.roleID = req.roleID
			AND r1.roleCode = 'Requestor'
		INNER JOIN dbo.depomemberdata AS d1 ON d1.depoMemberDataID = req.depoMemberDataID
		ON req.conversationID = c.conversationID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS resp
		INNER JOIN dbo.expertConnectInquiryRoles AS r2 ON r2.roleID = resp.roleID
			AND r2.roleCode = 'Respondent'
		INNER JOIN dbo.depomemberdata AS d2 ON d2.depoMemberDataID = resp.depoMemberDataID
		ON resp.conversationID = c.conversationID
	WHERE c.inquiryID = @inquiryID;

	DELETE tmp
	FROM #tmpConversations AS tmp
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
		AND clm.email = tmp.respondentEmail;

	UPDATE dbo.expertConnectInquiries
	SET statusID = @openStatusID
	WHERE inquiryID = @inquiryID;

	EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID,
		@parentSiteResourceID=@adminToolSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
		@languageID=@languageID, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', @rawContent=@emailContent,
		@memberID=@recordedByMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

	SELECT TOP 1 @contentVersionID = cv.contentVersionID
	FROM membercentral.dbo.cms_content AS c 
	INNER JOIN membercentral.dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID and cl.languageID = @languageID
	INNER JOIN membercentral.dbo.cms_contentVersions AS cv ON cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	WHERE c.contentID = @newContentID;

	EXEC platformMail.dbo.email_insertMetadataField @fieldName='emailOptOutURL', @isMergeField=1, @fieldID=@fieldID OUTPUT;

	SELECT @conversationID = MIN(conversationID) FROM #tmpConversations;
	WHILE @conversationID IS NOT NULL BEGIN
		BEGIN TRAN;
			SELECT @fromName = fromName, @fromFirm = fromFirm, @replyToEmail = replyToEmail, @respondentName = respondentName, @respondentFirm = respondentFirm,
				@respondentEmail = respondentEmail, @respondentDepoMemberDataID = respondentDepoMemberDataID
			FROM #tmpConversations
			WHERE conversationID = @conversationID;

			SET @fromNameAndFirm = @fromName + ' - ' + @fromFirm;
			SET @conversationSubject = @emailSubject + ' (' + @fromName + ' and ' + @respondentName + ')'

			EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode='TS', @depomemberdataid=@respondentDepoMemberDataID,
				@memberID=@recipientMemberID OUTPUT;

			IF @recipientMemberID > 0 BEGIN
				
				-- get consentListIDS
				SELECT @consentListIDs = cast(NULLIF(@inquiryOptOutListID,0) as varchar(10));

				-- add email_message
				EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=NULL,
					@sendingSiteResourceID=@adminToolSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID,
					@fromName=@fromNameAndFirm, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',  @subject=@conversationSubject,
					@contentVersionID=@contentVersionID, @messageWrapper='', @referenceType='EXPERTINQUIRY_START',
					@referenceID=@inquiryID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;
		
				-- add recipient
				EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@recipientMemberID, 
					@toName=@respondentName, @toEmail=@respondentEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID,
					@recipientID=@recipientID OUTPUT;

				INSERT INTO #tmpRecipientDetails (recipientID, recipientMemberID, recipientEmail, messageID)
				SELECT @recipientID, @recipientMemberID, @respondentEmail, @messageID;

				-- add cc recipient
				EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@orgSysMemberID, 
					@toName='DepoConnect Message Logs', @toEmail='<EMAIL>', @emailTypeID=@emailTypeID,
					@statusCode='I', @siteID=@siteID, @recipientID=@recipientID OUTPUT;

				INSERT INTO #tmpRecipientDetails (recipientID, recipientMemberID, recipientEmail, messageID)
				SELECT @recipientID, @orgSysMemberID, '<EMAIL>', @messageID;
			END
		COMMIT TRAN;
		SELECT @conversationID = MIN(conversationID) FROM #tmpConversations WHERE conversationID > @conversationID;
	END

	SELECT tmp.recipientID, tmp.recipientMemberID, tmp.recipientEmail, m.memberNumber, tmp.messageID
	FROM #tmpRecipientDetails AS tmp
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = tmp.recipientMemberID;

	IF OBJECT_ID('tempdb..#tmpConversations') IS NOT NULL
		DROP TABLE #tmpConversations;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL
		DROP TABLE #tmpRecipientDetails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
