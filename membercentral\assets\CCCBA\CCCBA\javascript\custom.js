$(document).ready(function() {

    $('.fa').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
    $('.fas').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
    $('.fa-brands').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
    $('.fa-solid').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
	$('.fa-regular').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
	
    if ($(".zoneB1Wrapper > ul > li:eq(0) ul").length) {
        $(".zoneB11Holder").replaceWith($(".zoneB1Wrapper > ul > li:eq(0) ul").html());
        $(".zoneB1Wrapper > ul > li:eq(0) ul > li a").addClass("item-link");
        $(".zoneB1Wrapper > ul > li:eq(0) ul > li i").addClass("img-ico");
        $(".zoneB111Holder").replaceWith(
            $(".zoneB1Wrapper > ul > li:eq(0) ul > li").map(function() {
                return $(this).html();
            }).get().join('')
        );
    }
    if ($(".zoneB1Wrapper > ul > li:eq(1) ul").length) {
        $(".zoneB12Holder").replaceWith($(".zoneB1Wrapper > ul > li:eq(1) ul").html());
        $(".zoneB122Holder").replaceWith(
            $(".zoneB1Wrapper > ul > li:eq(1) ul > li").map(function() {
                return $(this).html();
            }).get().join('')
        );
    }
    if ($(".zoneB1Wrapper > ul > li:eq(2) ul").length) {
        $(".zoneB13Holder").replaceWith($(".zoneB1Wrapper > ul > li:eq(2) ul").html());
    }

    $(".zoneC1Holder a").addClass("OrangeButton");

    if ($(".zoneQ1Wrapper > ul > li").length) {
        var htmlContent = '<h3>' + $(".zoneQ1Wrapper h3").html() + '</h3><div class="footer-links-wrap">';

        $(".zoneQ1Wrapper > ul > li").each(function() {
            var listHtml = $(this).find("ul").first().prop("outerHTML");
            htmlContent += listHtml;
        });

        htmlContent += '</div>';

        $(".zoneQ1Holder").replaceWith(htmlContent);
    }

    if ($(".zoneR1Wrapper ul").length) {
        var $wrapper = $(".zoneR1Wrapper");

        var officeTitle = $wrapper.find("> ul > li:eq(0) ul > li:eq(0)").text();
        var officeContent = $wrapper.find("> ul > li:eq(0) ul > li:eq(1)").html();
		
        var contactTitle = $wrapper.find("> ul > li:eq(1) ul > li:eq(0)").text();
        var contactList = $wrapper.find("> ul > li:eq(1) ul > li:eq(1) ul").html();

        var finalHtml = `
		<div class="col3 footer-links">
			<h3>${officeTitle}</h3>
			<p>${officeContent}</p>
		</div>
		<div class="col3 contact-links">
			<h3>${contactTitle}</h3>
			<ul>${contactList}</ul>
		</div>
	`;

        $(".zoneR1Holder").replaceWith(finalHtml);
    }

    $(".foot-logo-wrap img").addClass("footlogo");

    if ($('.zoneM1Wrapper ul').length) {
        var $wrapper = $('.zoneM1Wrapper');
        var imageSrc = $wrapper.find('img').attr('src');
        var headerText = $wrapper.find('h3').text();
        var $menu = $wrapper.find('> ul').clone();

        // Add required classes
        $menu.find('> li').addClass('dropdown');
        $menu.find('> li > a').addClass('dropdown-toggle').attr('data-toggle', 'dropdown');
        $menu.find('> li > ul').addClass('dropdown-menu');

        // Final HTML structure
        var html = `
	<div class="quicklink-desktop">
	  <div class="side-title-center">
		<img src="${imageSrc}" alt="" />
		<h3 class="ColumnHeader">${headerText}</h3>
	  </div>
	  <div class="DiamondBullets">
		${$('<div>').append($menu).html()}
	  </div>
	</div>`;

        // Replace target element
        $('.zoneM1Holder').replaceWith(html);
    }

    if ($('.zoneN1Wrapper ul').length) {
        var $wrapper = $('.zoneN1Wrapper');
        var $uls = $wrapper.find('ul');
        var $viewAll = $wrapper.find('>a'); // Direct child <a>
        var imgSrc = $wrapper.find('img').attr('src') || '';
        var headingText = $wrapper.find('h3').text().trim() || '';

        var eventsHtml = `
	<div class="events">
	  <div class="side-title-center">
		<img src="${imgSrc}" alt="" />
		<h3 class="ColumnHeader">${headingText}</h3>
	  </div>
	  <div class="eventbox-list">
	`;

        // Loop through each event <ul>
        $uls.each(function() {
            var $li = $(this).find('li');

            var title = $li.eq(0).text().trim();
            var link = $li.eq(0).find('a').attr('href') || "#";
            var date = $li.eq(1).text().trim();
            var time = $li.eq(2).text().trim();
            var mcleText = $li.eq(3).text().trim();
            var mcle = mcleText ? `<br />${mcleText}` : '';

            eventsHtml += `
		<div class="event-box">
		  <span class="e-date">${date}</span>
		  <a href="${link}"><p>${title}</p></a>
		  <span class="e-time">
			${time}${mcle}
		  </span>
		</div>
	  `;
        });

        // Add dynamic VIEW ALL EVENTS button
        if ($viewAll.length) {
            var viewAllHref = $viewAll.attr('href');
            var viewAllText = $viewAll.text().trim();
            eventsHtml += `
		<div class="event-box event-btn">
		  <a href="${viewAllHref}" class="TextButton">${viewAllText}</a>
		</div>
	  `;
        }

        eventsHtml += `
	  </div>
	</div>
	`;

        // Replace the content in .zoneN1Holder
        $('.zoneN1Holder').replaceWith(eventsHtml);
    }

    if ($('.zoneO1Wrapper ul').length) {
        var $wrapper = $('.zoneO1Wrapper');
        var $uls = $wrapper.find('ul');
        var $viewAll = $wrapper.find('> a'); // Direct child <a>
        var imgSrc = $wrapper.find('img').attr('src') || '';
        var headingText = $wrapper.find('h3').text().trim() || '';

        var eventsHtml = `
	<div class="siedebar-blog">
	  <div class="side-title-center">
		<img src="${imgSrc}" alt="" />
		<h3 class="ColumnHeader">${headingText}</h3>
	  </div>
	  <div class="eventbox-list">
	`;

        // Loop through each event <ul>
        $uls.each(function() {
            var $li = $(this).find('li');
            var title = $li.eq(0).text().trim();
            var link = $li.eq(0).find('a').attr('href') || "#";

            eventsHtml += `
		<div class="event-box">
		  <a href="${link}"><p>${title}</p></a>
		</div>
	  `;
        });

        // Add dynamic VIEW ALL EVENTS button
        if ($viewAll.length) {
            var viewAllHref = $viewAll.attr('href');
            var viewAllText = $viewAll.text().trim();
            eventsHtml += `
		<div class="event-btn">
		  <a href="${viewAllHref}" class="TextButton">${viewAllText}</a>
		</div>
	  `;
        }

        eventsHtml += `
	  </div>
	</div>
	`;

        // Replace the content in .zoneN1Holder
        $('.zoneO1Holder').replaceWith(eventsHtml);
    }

    $(".sponsorSliderHolder").html($(".sponsorSliderWrapper h2").html());
    $(".sponsorSliderWrapper h2").remove();

    if ($('.zoneMain1Wrapper ul').length) {
        var $wrapper = $('.zoneMain1Wrapper');
        var $li = $wrapper.find('ul > li');
        var imgSrc = $li.eq(0).find('img').attr('src');
        var text = $li.eq(1).text();

        var heroHtml = `
	<div class="hero-banner">
	  <div class="item">
		<img src="${imgSrc}" alt="" class="fixed-bg" />
		<div class="carousel-caption">
		  <div class="captionFrame">
			<ul>
			  <li>
				<span class="HeaderTextSmall">${text}</span>
			  </li>
			</ul>
		  </div>
		</div>
	  </div>
	</div>
	`;

        $('.zoneMain1Holder').replaceWith(heroHtml);
    }

    if ($('.zoneD1Wrapper ul').length) {
        var $wrapper = $('.zoneD1Wrapper');
        var $items = $wrapper.find('> ul > li');
        var html = `
	<div class="pd_70 quick-links-sec">
	  <div class="container containerCustom">
		<div class="row">
		  <div class="span12">
			<div class="sbm-icon-card-wrap">
			  <ul>
	`;

        $items.each(function() {
            var $inner = $(this).find('ul > li');
            var imgTag = $inner.eq(0).find('img').prop('outerHTML');
            var title = $inner.eq(1).text();
            var desc = $inner.eq(2).text();

            // Convert title to lowercase, replace spaces with dashes for href
            var href = $($inner.find('a')[0]).attr("href");

            html += `
				<li>
				  <a class="sbm-iconbox" href="${href}">
					${imgTag}
					<h3>${title}</h3>
					<p>${desc}</p>
				  </a>
				</li>
	  `;
        });

        html += `
			  </ul>
			</div>
		  </div>
		</div>
	  </div>
	</div>
	`;

        $('.zoneD1Holder').replaceWith(html);
    }

    if ($('.zoneE1Wrapper ul').length) {
        var $wrapper = $('.zoneE1Wrapper');
        var $items = $wrapper.find('ul');
        var newsHtml = `
	<div class="news-sec">
	  <img src="/images/top-shap.png" alt="" class="top-shap" />
	  <div class="container containerCustom">
		<div class="row d-flex-wrap">
		  <div class="span12">
			<h2 class="SectionHeader text-center">` + $wrapper.find('h3').text() + `</h2>
		  </div>
	`;

        $items.each(function() {
            var title = $(this).find('li').eq(0).text().trim();
            var summary = $(this).find('li').eq(1).text().trim();
            var linkText = $(this).find('li').eq(2).text().trim();
            var href = $(this).find('li').eq(2).find('a').attr('href') || '#';

            newsHtml += `
		  <div class="span3">
			<div class="news-card">
			  <h3>${title}</h3>
			  <p>${summary}</p>
			  <a href="${href}" class="NavyButton">${linkText}</a>
			</div>
		  </div>
	  `;
        });

        newsHtml += `
		</div>
	  </div>
	  <img src="/images/Texture-1.png" alt="" class="texture-1" />
	</div>
	`;

        $('.zoneE1Holder').replaceWith(newsHtml);
    }

    if ($('.zoneF1Wrapper ul').length) {
        var $wrapper = $('.zoneF1Wrapper');
        var $items = $wrapper.find('ul');
        var $viewAll = $wrapper.find('>a');
        var viewAllHref = $viewAll.attr('href') || '#';
        var viewAllText = $viewAll.text().trim();

        var html = `
	<div class="span6">
	  <div class="white-box">
		<h2 class="SubHeading">` + $wrapper.find('h3').text() + `</h2>
	`;

        $items.each(function(i) {
            var $li = $(this).find('li');
            var link = $li.eq(0).find('a').attr('href') || '#';
            var title = $li.eq(0).text().trim();
            var month = $li.eq(1).text().trim();
            var day = $li.eq(2).text().trim();
            var time = $li.eq(3).text().trim();
            var mcle = $li.eq(4).text().trim();
            var suffix = getSuffixByday(day);
            html += `
		<div class="news-box">
		  <div class="newsdate">
			<div>
			  <p>${month}</p>
			  <span>${day}<sup>${suffix}</sup></span>
			</div>
		  </div>
		  <div class="newscontent">
			<h3><a href="${link}">${title}</a></h3>
			<p>${time} <br /> ${mcle}</p>
		  </div>
		</div><hr />
	  `;

          
        });

        html += `
		<a href="${viewAllHref}" class="TextButton">${viewAllText}</a>
	  </div>
	</div>
	`;

        $('.zoneF1Holder').replaceWith(html);
    }

    if ($('.zoneI1Wrapper ul').length) {
        var $wrapper = $('.zoneI1Wrapper');
        var $lists = $wrapper.find('> ul > li > ul');
        var html = `
	<div class="img-card-sec">
		<img src="/images/Texture-2.png" alt="" class="fixed-bg texture-2" />
		<div class="container containerCustom">
			<div class="flex-row">
	`;

        $lists.each(function() {
            var $ul = $(this);
            var title = $ul.find('h3').text().trim();
            var imgSrc = $ul.find('img').attr('src');
            var imgAlt = $ul.find('img').attr('alt') || '';
            var desc = $ul.find('li').eq(2).text().trim();
            var $link = $ul.find('a');
            var href = $link.attr('href') || '#';
            var linkText = $link.text().trim();

            html += `
			<div class="span4">
				<div class="img-card">
					<h3>${title}</h3>
					<div class="img-holder">
						<a href="${href}">
							<img src="${imgSrc}" alt="${imgAlt}" />
						</a>
					</div>
					<div class="img-card-content">
						<p>${desc}</p>
						<a href="${href}" class="OrangeButton">${linkText}</a>
					</div>
				</div>
			</div>
		`;
        });

        html += `
			</div>
		</div>
	</div>
	`;

        $('.zoneI1Holder').replaceWith(html);
    }

    if ($('.zoneH1Wrapper ul').length) {
      strTitle = '';
      strImage1 = '';
      strImage2 = '';
      strLink1 = '';
      strLink2 = '';
      strScript = '';
      $('.zoneH1Wrapper ul > li').each(function(key1,val1){
        
        if($(val1).find('img').length > 0 ){
          strImage1 = $(val1).html();
        }

        if($(val1).find('a').length > 0 && key1 == 2){
          strLink1 = $(val1).html();
        }else if($(val1).find('a').length > 0 && strLink1 != ''){
          strLink2 = $(val1).html();
        }
        
        if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strScript == ''){
          strScript = $(val1).html();
        } 

        if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
          strTitle = $(val1).html();
        } 
      });

        // Final HTML
        var finalHtml = `
        <div class="job-board-sec">
          <div class="container containerCustom">
            <div class="row">
              <div class="span6 job-post-action-wrap">
                <h2 class="SubHeading visible-phone">${strTitle}</h2>
                ${strImage1}
                ${strLink1}					
              </div>
              <div class="span6 job-search-col">
                <div class="job-search-box">
                  <h2 class="SubHeading hidden-phone hidden-print">${strTitle}</h2>
                  <div class="job-listings">                   
                    ${strLink2}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>`;

        // Replace the content
        $('.zoneH1Holder').replaceWith(finalHtml);
        
        setTimeout(function(){
          if($('.zoneH1Wrapper > ul > li').eq(3).html() != undefined){
            
           $('.job-listings').prepend($('.zoneH1Wrapper > ul > li').eq(3).html());
          }
          $('.job-listings > a').addClass('OrangeButton');
          $('.job-post-action-wrap > a').addClass('OrangeButton');
        },2000);
        

    }

    if ($('.zoneG1Wrapper ul').length) {
        var $wrapper = $('.zoneG1Wrapper');
        var $ul = $wrapper.find('> ul');
        var magazineTitle = $ul.find('> li').eq(0).text();
        var issueInfo = $ul.find('> li').eq(1).text();
        var imgSrc = $ul.find('> li').eq(2).find('img').attr('src');
        var highlightsHeading = $ul.find('> li').eq(3).text(); // dynamic HIGHLIGHTS text
        var highlights = $ul.find('> li').eq(4).find('> ul > li');
        var readMoreLink = $ul.find('> li').last().find('a').attr('href');
        var readMoreLinkText = $ul.find('> li').last().find('a').html();

        var highlightsHtml = '';
        highlights.each(function() {
            var $items = $(this).find('ul > li');
            var title = $items.eq(0).html();
            var author = $items.eq(1).text();
            highlightsHtml += `
			<div class="mb-15">
				<h4>${title}</h4>
				<p>${author}</p>
			</div>
			<hr />
		`;
        });

        var finalHtml = `
	<div class="span6">
		<div class="magzone-wrapper">
			<h2 class="SubHeading">${magazineTitle}</h2>
			<p>${issueInfo}</p>
			<div class="magzone-box">
				<div class="magzone-img">
					<img src="${imgSrc}" alt="" />
				</div>
				<div class="magzone-content">
					<h3>${highlightsHeading}</h3>
					<hr />
					${highlightsHtml}
					<a href="${readMoreLink}" class="TextButton">${readMoreLinkText}</a>
				</div>
			</div>
		</div>
	</div>
	`;

        $('.zoneG1Holder').replaceWith(finalHtml);
    }
    $(".bannerInner img").eq(1).addClass("fixed-bg rightImg");
	
	var $wrapper = $('.zoneJ1Wrapper');
	if($wrapper.find('li').length){
		var title = $wrapper.find('h2').text();
		var $listItems = $wrapper.find('li');

		var html = `
		<div class="facebook-section pd_70">
			<div class="container containerCustom">
				<h2 class="SectionHeader text-center">${title}</h2>
				<div class="row d-flex-wrap facebook-cards-row" id="fb-root">
		`;

		$listItems.each(function () {
			var $fbPost = $(this).find('.fb-post');
			var href = $fbPost.data('href');
			html += `
					<div class="span3 facebook-card">
						<div class="fb-post" data-href="${href}"></div>
					</div>
			`;
		});

		html += `
				</div>
			</div>
		</div>
		`;

		$('.zoneJ1Holder').replaceWith(html);
	}
	
	if($(".tableLayoutWrapper").length){
		$("#tableLayoutWrapper").replaceWith('<div class="column-4-sec pd_50">' + $(".tableLayoutWrapper").html() + '</div>');
		$(".tableLayoutWrapper").remove();
	}

    $('.nav-collapse ul > li').each(function() {
        if ($(this).children('ul').length) {
            $(this).addClass('dropdown').children('a').addClass('dropdown-toggle');
            $(this).children('ul').addClass('dropdown-menu');
        }
    });

    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li').addClass('droplist-1');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1 > ul').addClass('droplist-2');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1 > ul.droplist-2 > li > ul').addClass('subMenu');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1:has(form)').addClass('formDiv clearfix');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1.formDiv > div ').addClass('formframe clearfix');
    $('.droplist-2 > li:has(ul)').addClass('subMenuParent');


    $(document).on('click', ".btn-navbar", function() {
        $("body").toggleClass("overlay");
    });

    $(document).on('click', '.menu-arrow', function() {
        $(this).parent(".dropdown").toggleClass('open-droupdown');
        $(this).parent(".dropdown").children(".dropdown-menu").slideToggle();
    });

    function mobclickmenu() {
        if ($(window).width() < 980) {
            $(".dropdown").each(function() {
                var this__ = $(this);
                if (!this__.find('.menu-arrow').length) {
                    this__.append("<span class='menu-arrow'></span>");
                }
            });
        } else {
            $(".dropdown").find('.menu-arrow').remove();
        }
    }
    mobclickmenu();

    /*****************/
	var sponsorSliderCount = 0;
	if($(".sponsorSliderWrapper ul li").length){
		sponsorSliderCount = $(".sponsorSliderWrapper ul li").length;
	}
	
    $('.sponsorSlider ul').addClass('owl-carousel owl-theme').owlCarousel({
        loop: (sponsorSliderCount > 1)? true:false, nav: (sponsorSliderCount > 1)? true:false, autoplay: (sponsorSliderCount > 1)? true:false, 
        dots: false,
        drag: false,
        mouseDrag: false,
        autoPlaySpeed: 7000,
        smartSpeed: 1000,
        margin: 30,
        items: 1,
        navText: ['<i class="fa-solid fa-chevron-left"></i>', '<i class="fa-solid fa-chevron-right"></i>'],
        responsive: {
            0:{
				items:1,loop: (sponsorSliderCount > 1)? true:false, nav: (sponsorSliderCount > 1)? true:false, autoplay: (sponsorSliderCount > 1)? true:false
			},
			767:{
				items:2,loop: (sponsorSliderCount > 2)? true:false, nav: (sponsorSliderCount > 2)? true:false, autoplay: (sponsorSliderCount > 2)? true:false
			},
			991:{
				items:4,loop: (sponsorSliderCount > 4)? true:false, nav: (sponsorSliderCount > 4)? true:false, autoplay: (sponsorSliderCount > 4)? true:false
			}
        }
    });

    $(document).on('click', '.searchBtnFn>a', function(e) {
        e.preventDefault();
        $(this).closest('li').addClass('show-search-bar');
    });

    $(document).on('click', '.searchclose', function(e) {
        e.preventDefault();
        $(this).closest('li.show-search-bar').removeClass('show-search-bar');
    });

    $(document).on('click', '.toggle-form', function(e) {
        e.preventDefault();
        $(this).closest('li').toggleClass('show-form');
    });

    $(window).on('resize', function() {
        mobclickmenu();
    });
});
function getSuffixByday(day) {
    let suffix = "th";

    if (day % 10 === 1 && day % 100 !== 11) {
        suffix = "st";
    } else if (day % 10 === 2 && day % 100 !== 12) {
        suffix = "nd";
    } else if (day % 10 === 3 && day % 100 !== 13) {
        suffix = "rd";
    }

    return suffix;
}