<cfcomponent output="no">

	<cffunction name="getColumnData" access="public" output="false" returntype="query">
		<cfargument name="columnID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qrydataTypeCode" datasource="#application.dsn.memberCentral.dsn#">
			select dt.dataTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
				and c.columnID = <cfqueryparam value="#arguments.columnID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfquery name="local.qryColumnData" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			
			declare @memberHasMultipleValues int = 0;
			declare @columnID int = <cfqueryparam value="#arguments.columnID#" cfsqltype="CF_SQL_INTEGER">;

			<!--- did it this way because the fragmented index would slow this down tremendously --->
			IF @columnID > 0 AND EXISTS (select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1) BEGIN
				IF OBJECT_ID('tempdb..##tmpvalueIDs') IS NOT NULL 
					DROP TABLE ##tmpvalueIDs;
				IF OBJECT_ID('tempdb..##tmpvalueIDs2') IS NOT NULL 
					DROP TABLE ##tmpvalueIDs2;

				select valueID
				into ##tmpvalueIDs
				from dbo.ams_memberDataColumnValues
				where columnid = @columnID;

				select md.memberid
				into ##tmpvalueIDs2
				from ##tmpvalueIDs as v 
				inner join dbo.ams_memberData as md on v.valueID = md.valueID
				group by md.memberid
				having count(*) > 1
				OPTION(RECOMPILE);

				set @memberHasMultipleValues = @@ROWCOUNT;

				IF OBJECT_ID('tempdb..##tmpvalueIDs') IS NOT NULL 
					DROP TABLE ##tmpvalueIDs;
				IF OBJECT_ID('tempdb..##tmpvalueIDs2') IS NOT NULL 
					DROP TABLE ##tmpvalueIDs2;
			END

			select c.orgID, c.columnID, c.columnName, datat.dataTypeCode, dispt.displayTypeCode, datat.dataType, dispt.displayType, c.columnDesc,
				c.allowNull, c.allowNewValuesOnImport, c.defaultValueID, c.isReadOnly, c.allowMultiple, c.minChars, c.maxChars, c.minSelected, c.maxSelected, 
				c.minValueInt, c.maxValueInt, c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate, c.uid, 
				c.linkedDateColumnID, c.linkedDateCompareDate, c.linkedDateCompareDateAFID, c.linkedDateAdvanceDate, c.linkedDateAdvanceAFID,
				case when c.allowMultiple = 1 and @memberHasMultipleValues > 0 then 0 else 1 end as allowMultipleOff,
				case when c.defaultValueID is not null then (select 
					<cfswitch expression="#local.qrydataTypeCode.dataTypeCode#">
					<cfcase value="STRING">columnValueString</cfcase>
					<cfcase value="DECIMAL2">columnValueDecimal2</cfcase>
					<cfcase value="INTEGER">columnValueInteger</cfcase>
					<cfcase value="DATE">convert(varchar(10),columnValueDate,101)</cfcase>
					<cfcase value="BIT">columnValueBit</cfcase>
					<cfdefaultcase>''</cfdefaultcase>
					</cfswitch>
				from dbo.ams_memberDataColumnValues
				where columnID = c.columnID
				and valueID = c.defaultValueID) else null end as defaultValue		
			from dbo.ams_memberDataColumns as c 
			inner join dbo.ams_memberDataColumnDataTypes as datat on datat.dataTypeID = c.dataTypeID
			inner join dbo.ams_memberDataColumnDisplayTypes as dispt on dispt.displayTypeID = c.displayTypeID
			where c.columnID = @columnID;
		</cfquery>

		<cfreturn local.qryColumnData>
	</cffunction>

	<cffunction name="getFieldValueData" access="public" output="false" returntype="query">
		<cfargument name="valueID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qrydataTypeCode" datasource="#application.dsn.memberCentral.dsn#">
			select dt.dataTypeCode
			from dbo.ams_memberDataColumnValues as cv
			inner join dbo.ams_memberDataColumns as c on c.columnID = cv.columnID
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
			where cv.valueID = <cfqueryparam value="#arguments.valueID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfquery name="local.qryColumnValue" datasource="#application.dsn.membercentral.dsn#">
			select valueID, columnID, 
				<cfswitch expression="#local.qrydataTypeCode.dataTypeCode#">
				<cfcase value="STRING">columnValueString</cfcase>
				<cfcase value="DECIMAL2">columnValueDecimal2</cfcase>
				<cfcase value="INTEGER">columnValueInteger</cfcase>
				<cfcase value="DATE">convert(varchar(10),columnValueDate,101)</cfcase>
				<cfcase value="BIT">columnValueBit</cfcase>
				<cfdefaultcase>''</cfdefaultcase>
				</cfswitch> as columnValue,
				(select count(distinct vgc.conditionID)
				from dbo.ams_memberDataColumnValues as mdcv
				inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
				inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
				inner join dbo.ams_virtualGroupConditionvalues as vgcv on vgcv.conditionID = vgc.conditionID
					and vgcv.conditionValue = cast(mdcv.valueID as varchar(10))
				where mdcv.valueID = <cfqueryparam value="#arguments.valueID#" cfsqltype="CF_SQL_INTEGER">) as conditionCount
			from dbo.ams_memberDataColumnValues
			where valueID = <cfqueryparam value="#arguments.valueID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryColumnValue>
	</cffunction>

	<cffunction name="insertColumn" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="columnName" type="string" required="yes">
		<cfargument name="columnDesc" type="string" required="yes">
		<cfargument name="allowMultiple" type="boolean" required="yes">
		<cfargument name="allowNull" type="boolean" required="yes">
		<cfargument name="defaultValue" type="string" required="yes">
		<cfargument name="allowNewValuesOnImport" type="boolean" required="yes">
		<cfargument name="dataTypeCode" type="string" required="yes">
		<cfargument name="displayTypeCode" type="string" required="yes">
		<cfargument name="isReadOnly" type="boolean" required="yes">
		<cfargument name="minChars" type="numeric" required="no">
		<cfargument name="maxChars" type="numeric" required="no">
		<cfargument name="minSelected" type="numeric" required="no">
		<cfargument name="maxSelected" type="numeric" required="no">
		<cfargument name="minValueInt" type="numeric" required="no">
		<cfargument name="maxValueInt" type="numeric" required="no">
		<cfargument name="minValueDecimal2" type="numeric" required="no">
		<cfargument name="maxValueDecimal2" type="numeric" required="no">
		<cfargument name="minValueDate" type="date" required="no">
		<cfargument name="maxValueDate" type="date" required="no">
		<cfargument name="linkedDateColumnID" type="numeric" required="no" default="0">
		<cfargument name="linkedDateCompareDate" type="string" required="no" default="">
		<cfargument name="linkedDateCompareDateAFID" type="numeric" required="no" default="0">
		<cfargument name="linkedDateAdvanceDate" type="string" required="no" default="">
		<cfargument name="linkedDateAdvanceAFID" type="numeric" required="no" default="0">

		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberDataColumn">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(trim(arguments.columnName),128)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.columnDesc,255)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowMultiple#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowNull#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.defaultValue#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowNewValuesOnImport#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dataTypeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.displayTypeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isReadOnly#">
				<cfif isDefined("arguments.minChars")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minChars#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxChars")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxChars#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minSelected")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minSelected#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxSelected")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxSelected#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueInt")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minValueInt#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueInt")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxValueInt#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueDecimal2")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.minValueDecimal2#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueDecimal2")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.maxValueDecimal2#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueDate")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.minValueDate#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueDate")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.maxValueDate#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif arguments.linkedDateColumnID>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateColumnID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.linkedDateCompareDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateCompareDateAFID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.linkedDateAdvanceDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateAdvanceAFID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.columnID">
			</cfstoredproc>


		<cfcatch type="any">
			<cfset local.columnID = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.columnID>
	</cffunction>

	<cffunction name="updateColumn" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="columnID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="true">
		<cfargument name="columnDesc" type="string" required="true">
		<cfargument name="allowMultiple" type="boolean" required="yes">
		<cfargument name="allowNull" type="boolean" required="yes">
		<cfargument name="defaultValue" type="string" required="yes">
		<cfargument name="allowNewValuesOnImport" type="boolean" required="yes">
		<cfargument name="dataTypeCode" type="string" required="yes">
		<cfargument name="displayTypeCode" type="string" required="yes">
		<cfargument name="isReadOnly" type="boolean" required="yes">
		<cfargument name="minChars" type="numeric" required="no">
		<cfargument name="maxChars" type="numeric" required="no">
		<cfargument name="minSelected" type="numeric" required="no">
		<cfargument name="maxSelected" type="numeric" required="no">
		<cfargument name="minValueInt" type="numeric" required="no">
		<cfargument name="maxValueInt" type="numeric" required="no">
		<cfargument name="minValueDecimal2" type="numeric" required="no">
		<cfargument name="maxValueDecimal2" type="numeric" required="no">
		<cfargument name="minValueDate" type="date" required="no">
		<cfargument name="maxValueDate" type="date" required="no">
		<cfargument name="uid" type="string" required="no" default="">
		<cfargument name="linkedDateColumnID" type="numeric" required="no" default="0">
		<cfargument name="linkedDateCompareDate" type="string" required="no" default="">
		<cfargument name="linkedDateCompareDateAFID" type="numeric" required="no" default="0">
		<cfargument name="linkedDateAdvanceDate" type="string" required="no" default="">
		<cfargument name="linkedDateAdvanceAFID" type="numeric" required="no" default="0">
		
		<cfset var local = StructNew()>
		<cfset local.strReturn = { errCode=0, errMsg='' }>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_updateMemberDataColumn">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.columnID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.columnName)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.columnDesc#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowMultiple#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowNull#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.defaultValue#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowNewValuesOnImport#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dataTypeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.displayTypeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isReadOnly#">
				<cfif isDefined("arguments.minChars")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minChars#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxChars")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxChars#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minSelected")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minSelected#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxSelected")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxSelected#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueInt")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.minValueInt#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueInt")>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxValueInt#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueDecimal2")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.minValueDecimal2#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueDecimal2")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.maxValueDecimal2#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="true">
				</cfif>
				<cfif isDefined("arguments.minValueDate")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.minValueDate#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif isDefined("arguments.maxValueDate")>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.maxValueDate#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif arguments.linkedDateColumnID>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateColumnID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.linkedDateCompareDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateCompareDateAFID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.linkedDateAdvanceDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkedDateAdvanceAFID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>	

			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.uid)>
				<cfquery name="local.qryCheckUID" datasource="#application.dsn.membercentral.dsn#">
					select columnID
					from dbo.ams_memberDataColumns 
					where uid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#trim(arguments.uid)#">
					and columnID <> #arguments.columnID#;
				</cfquery>
				<cfif local.qryCheckUID.recordcount>
					<cfthrow detail="XX:Unable to change UID. UIDs must be unique among all Custom Fields.">
				</cfif>

				<cfquery name="local.qryChangeUID" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;

					declare @uid uniqueidentifier, @newuid uniqueidentifier, @columnName varchar(128), @msg varchar(max),
						@orgID int, @siteID int;

					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
					select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
					select @columnName = columnName, @uid = [uid] from dbo.ams_memberDataColumns where columnID = #arguments.columnID#;
					set @newuid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#trim(arguments.uid)#">;

					IF @uid <> @newuid BEGIN
						update dbo.ams_memberDataColumns
						set [uid] = @newuid
						where columnID = #arguments.columnID#;

						SET @msg = 'Custom Field [' + @columnName + '] UID changed from [' + CAST(@uid as varchar(36)) + '] to [' + CAST(@newuid as varchar(36)) + '].';

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES('{ "c":"auditLog", "d": {
							"AUDITCODE":"MEMCF",
							"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
							"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#"> as varchar(20)) + ',
							"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msg),'"','\"') + '" } }');
					END

					set nocount off;
				</cfquery>
			</cfif>

		<cfcatch type="Any">
			<cfset local.strReturn = { errCode=-1, errMsg=trim(removeChars(cfcatch.detail,1,find(":",cfcatch.detail))) }>
		</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>
	</cffunction>	

	<cffunction name="insertColumnValue" access="public" output="false" returntype="numeric">
		<cfargument name="columnID" type="numeric" required="true">
		<cfargument name="columnValue" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberDataColumnValue">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.columnID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.columnValue#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.valueID">
		</cfstoredproc>		

		<cfreturn local.valueID>
	</cffunction>
	
	<cffunction name="updateColumnValue" access="public" output="false" returntype="void">
		<cfargument name="valueID" type="numeric" required="true">
		<cfargument name="columnValue" type="string" required="true">
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_updateMemberDataColumnValue">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.columnValue#">
		</cfstoredproc>		
	</cffunction>

	<cffunction name="getAdvanceFormulas" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryAdvanceFormulas = "">
		
		<cfquery name="qryAdvanceFormulas" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT AFID, siteID, datePart, dateNum, adjustTerm, nextWeekday, weekNumber, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam cfsqltype="INTEGER" value="#arguments.siteID#">
			ORDER BY afName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryAdvanceFormulas>
	</cffunction>

	<cffunction name="getDateCustomFields" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		
		<cfset var qryMemberDataDateCustomFields = "">
		
		<cfquery name="qryMemberDataDateCustomFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mdc.columnID, mdc.columnName
			FROM dbo.ams_memberDataColumns as mdc
			INNER JOIN dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
			WHERE mdc.orgID = <cfqueryparam cfsqltype="INTEGER" value="#arguments.orgID#">
			AND mdt.dataTypeCode = 'DATE'
			ORDER BY mdc.columnName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryMemberDataDateCustomFields>
	</cffunction>

</cfcomponent>