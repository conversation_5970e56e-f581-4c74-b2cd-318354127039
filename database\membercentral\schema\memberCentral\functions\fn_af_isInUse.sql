ALTER FUNCTION dbo.fn_af_isInUse (@afid int)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @siteID int, @trashCount int;

	SELECT @siteID = siteID
	FROM dbo.af_advanceFormulas
	WHERE afID = @afID;

	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE rateStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE rateEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE termStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE termEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE graceAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE recogStartDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.sub_rates WHERE recogEndDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ams_memberDataColumns WHERE linkedDateCompareDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ams_memberDataColumns WHERE linkedDateAdvanceAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.email_EmailBlasts WHERE afID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ams_virtualGroupConditionValues WHERE AFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE creditFromAdvanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE creditToAdvanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cms_myCEPages WHERE advanceDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE advanceAFDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE nextAdvancementDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE nextPaymentDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.cp_programs WHERE payThruDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE startDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE fromRefDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE toRefDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE cutoffDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE lastUpdatedDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE fromFollowUpDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;
	SELECT @trashCount = count(*) FROM dbo.ref_scheduledReports WHERE toFollowUpDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.ev_recurringSeries WHERE afid = @afid;
		IF @trashCount > 0 GOTO in_use;

	SELECT @trashCount = count(*) FROM dbo.scheduledTasks WHERE periodDateAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	-- report extra
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/extra/*[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- report sub widget under d
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/subrule//condition/d/*[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- report sub widget d
	SELECT @trashCount = COUNT(*) FROM dbo.rpt_SavedReports WHERE siteID = @siteID AND otherXML.exist('/report/subrule//condition/d[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- task automation run date
	SELECT @trashCount = COUNT(*) FROM dbo.tasks_automations WHERE runAFID = @afid;
		IF @trashCount > 0 GOTO in_use;

	-- task automation filters
	SELECT @trashCount = COUNT(*)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.filterXML.exist('/filter/field[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	-- task automation actions
	SELECT @trashCount = COUNT(*)
	FROM dbo.tasks_automations AS ta
	INNER JOIN dbo.tasks_projects AS p ON p.projectID = ta.projectID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = p.siteResourceID
	WHERE ta.actionXML.exist('/action/field[@afid = sql:variable("@afID")]') = 1;
		IF @trashCount > 0 GOTO in_use;

	GOTO on_done;

	in_use:
	SET @inUse = 1;

	on_done:
	RETURN @inUse;

END
GO
