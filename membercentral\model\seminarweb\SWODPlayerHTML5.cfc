<cfcomponent extends="model.system.utility.CFCRemoteDocumenter">
	<cfsetting showdebugoutput="false"/>	
	<cfset variables.sendDebugEmail = 0 />
	
	<!--- *********************** --->
	<!--- seminar level functions --->
	<!--- *********************** --->
	<cffunction name="getSeminarforPlayer" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfscript>
		var local = StructNew();

		try {
			// create objects
			local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars");
			local.objSWTitle = CreateObject("component","model.seminarweb.SWTitles");
			local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles");
			local.objSWP = CreateObject("component","model.seminarweb.SWParticipants");
			local.objAdminSWCommon = createObject("component","model.admin.seminarWeb.seminarWebSWCommon");
	
			// setup return structure and variables
			structInsert(local,"returnStruct",structNew());
			structInsert(local.returnStruct,"returnData",structNew());
			local.missingFilesArray = ArrayNew(1);
			local.rsrd = local.returnStruct.returnData;		// shortcut used in sets below 
			local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode);
			local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.mc_siteInfo.orgID);
	
			// is user logged in?
			structInsert(local.returnStruct,"userLoggedIn",isLoggedIn());
			local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID);
			
			// if bad enrollmentid, set logged in to false
			if (arguments.enrollmentid lte 0) 
				structInsert(local.returnStruct,"userLoggedIn",0,true);
			
			// if logged in
			if (local.returnStruct.userLoggedIn AND  local.memberID eq local.qryEnrollment.MCMemberID) {
				// set to false and overwrite with true upon completion
				structInsert(local.rsrd,"loadSuccess",0);
				structInsert(local.rsrd,"isAdminMode",0);
				local.strAccessIDs = setupLogAccessIDs(arguments.enrollmentID);
				local.strSeminar = local.objSWOD.getSeminarByAccessID(local.strAccessIDs.seminarAccessID);
				
				if(local.strSeminar.qrySeminar.seminarID eq arguments.seminarID AND local.strSeminar.qrySeminar.isPublished neq 0){
					// setup access log ids, get seminar data, orgcode branding
					
					local.qrySWP = local.objSWP.getAssociationDetails(arguments.mcproxy_siteCode).qryAssociation;
					local.qrySeminarSyndicateObj = local.objAdminSWCommon.getSeminarSyndicateData(local.strSeminar.qrySeminar.seminarID,arguments.mcproxy_siteCode);
					
					local.endOfSeminarText = local.strSeminar.qrySeminar.endOfSeminarText;
					local.introMessageText = local.strSeminar.qrySeminar.introMessageText;
					
					if(local.qrySeminarSyndicateObj.recordcount gt 0 AND local.strSeminar.qrySeminar.publisherOrgCode neq arguments.mcproxy_siteCode){
						local.endOfSeminarText = local.qrySeminarSyndicateObj.endOfSeminarText;
						local.introMessageText = local.qrySeminarSyndicateObj.introMessageText;
					}
					// load seminar metadata
					structInsert(local.rsrd,"seminarID",local.strSeminar.qrySeminar.seminarID);
					structInsert(local.rsrd,"logAccessID",local.strAccessIDs.seminarAccessID);
					structInsert(local.rsrd,"seminarName",local.strSeminar.qrySeminar.seminarName);
					structInsert(local.rsrd,"seminarDesc",local.strSeminar.qrySeminar.seminarDesc);
					structInsert(local.rsrd,"publisher",local.strSeminar.qrySeminar.description);
					structInsert(local.rsrd,"seminarLayout",local.strSeminar.qrySeminar.layout);
					structInsert(local.rsrd,"offerCertificate",local.strSeminar.qrySeminar.offerCertificate);
					structInsert(local.rsrd,"offerQA",local.strSeminar.qrySeminar.offerQA);
					structInsert(local.rsrd,"endOfSeminarText",local.endOfSeminarText);
					structInsert(local.rsrd,"blankOnInactivity",local.strSeminar.qrySeminar.blankOnInactivity);
					structInsert(local.rsrd,"dspNotes",1);
					structInsert(local.rsrd,"seminarLength","Not Entered");
					if (val(local.strSeminar.qrySeminar.seminarLength) gt 0)
						structInsert(local.rsrd,"seminarLength",generateTimeLengthString(val(local.strSeminar.qrySeminar.seminarLength)),true);
					structInsert(local.rsrd,"topRightLogo","");
					structInsert(local.rsrd,"topLeftLogo",local.objSWP.getAssociationAds(orgcode=arguments.mcproxy_siteCode).primaryLogo);
					structInsert(local.rsrd,"orgcode",arguments.mcproxy_siteCode);
					structInsert(local.rsrd,"signuporgcode",local.qryEnrollment.signupOrgCode);
					structInsert(local.rsrd,"isFastForwardPermitted",local.objSWOD.checkStaffCanForward(memberID=local.memberID,siteID=local.mc_siteInfo.siteID,orgID=local.mc_siteInfo.orgID));
					structInsert(local.rsrd,"introMessageText",local.introMessageText);
					if(len(local.qryEnrollment.dateCompleted)){					
						structInsert(local.rsrd,"dateCompleted",dateformat(local.qryEnrollment.dateCompleted,"mm/dd/yyyy"));
					}else{
						structInsert(local.rsrd,"dateCompleted",'');
					}
					
					// load orgcode branding and details
					structInsert(local.rsrd,"sponsor",local.qrySWP.description);
					structInsert(local.rsrd,"seminarBrand",local.qrySWP.brandSWODTab);
					structInsert(local.rsrd,"supportPhone",local.qrySWP.supportPhone);
					structInsert(local.rsrd,"supportEmail",local.qrySWP.supportEmail);
					structInsert(local.rsrd,"supportHours",local.qrySWP.supportHours);
					structInsert(local.rsrd,"myProgramsURL","/?pg=semwebCatalog&panel=My");
					structInsert(local.rsrd,"myProgramsLinkText",local.qrySWP.brandMyCLETab);
			
					// load enrollee information
					structInsert(local.rsrd,"firstName",local.qryEnrollment.firstname);
					structInsert(local.rsrd,"lastName",local.qryEnrollment.lastName);
					structInsert(local.rsrd,"email",local.qryEnrollment.email);
		
					// admin mode overrides
					if (isDefined("session.SWODadminMode")) {
						structInsert(local.rsrd,"seminarLayout","adminMode",true);
						structInsert(local.rsrd,"isAdminMode",1,true);
						structDelete(session,"SWODadminMode");
					}
			
					// load seminar credit and completion settings
					structInsert(local.rsrd,"isCompleted",0);
					structInsert(local.rsrd,"promptUseActivity",0);
					structInsert(local.rsrd,"promptFrequency",local.strSeminar.qrySeminarSettings.promptInterval);
					structInsert(local.rsrd,"isEvaluationRequired",local.strSeminar.qrySeminarSettings.evaluationRequired);
					structInsert(local.rsrd,"isPostTestRequired",local.strSeminar.qrySeminarSettings.examRequired);
					structInsert(local.rsrd,"isPreTestRequired",local.strSeminar.qrySeminarSettings.preexamRequired);
					structInsert(local.rsrd,"enrollmentCreditObj",getEnrollmentCreditJSon(arguments.enrollmentID));
					structInsert(local.rsrd,"completionCheckObj",checkSeminarforCompletion(arguments.mcproxy_siteCode,arguments.enrollmentID));
					structInsert(local.rsrd,"certificateURL","");
					if (len(local.strSeminar.qrySeminarCompletion.dateSeminarCompleted))
						structInsert(local.rsrd,"isCompleted",1,true);
					if (local.strSeminar.qrySeminarSettings.promptTypeID is 1)
						structInsert(local.rsrd,"promptUseActivity",1,true);
					if (local.rsrd.offerCertificate is 1)
						structInsert(local.rsrd,"certificateURL",'/?pg=semWebCatalog&panel=viewCert&eId=#Replace(URLEncodedFormat(ToBase64(Encrypt(arguments.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")#&mode=stream',true);
					
					// load seminar load points settings
					structInsert(local.rsrd,"seminarLoadPointsObj",getSeminarLoadPointsObj(local.strSeminar.qrySeminar.seminarID));
					
					// load seminar links
					structInsert(local.rsrd,"linkArray",ArrayNew(1));
					for (local.i = 1; local.i lte local.strSeminar.qrySeminarLinks.recordcount; local.i = local.i + 1) {
						local.tmpLen = ArrayLen(local.rsrd.linkArray) + 1;
						local.rsrd.linkArray[local.tmpLen] = StructNew();
						structInsert(local.rsrd.linkArray[local.tmpLen],"linkid",local.strSeminar.qrySeminarLinks.linkid[local.i]);
						structInsert(local.rsrd.linkArray[local.tmpLen],"linkName",local.strSeminar.qrySeminarLinks.linkName[local.i]);
						structInsert(local.rsrd.linkArray[local.tmpLen],"linkDesc",local.strSeminar.qrySeminarLinks.linkDesc[local.i]);
						structInsert(local.rsrd.linkArray[local.tmpLen],"linkURL",local.strSeminar.qrySeminarLinks.linkurl[local.i]);
						structInsert(local.rsrd.linkArray[local.tmpLen],"purchaseURL",local.strSeminar.qrySeminarLinks.purchaseurl[local.i]);
					}
					// load seminar titles
					structInsert(local.rsrd,"titleArray",ArrayNew(1));
					for (local.t = 1; local.t lte local.strSeminar.qrySeminarTitles.recordcount; local.t = local.t + 1) {
						// load title info
						local.TALen = ArrayLen(local.rsrd.titleArray) + 1;
						local.rsrd.titleArray[local.TALen] = StructNew();
						structInsert(local.rsrd.titleArray[local.TALen],"titleid",local.strSeminar.qrySeminarTitles.titleID[local.t]);
						structInsert(local.rsrd.titleArray[local.TALen],"titleName",local.strSeminar.qrySeminarTitles.titleName[local.t]);
		
						// get links and files for this title
						local.strTitle = local.objSWTitle.getTitle(local.strSeminar.qrySeminarTitles.titleID[local.t]);
			
						// There is at lease 1 downloaded content
						local.hasDownloadedContent = 0;

						// load title files
						structInsert(local.rsrd.titleArray[local.TALen],"fileArray",ArrayNew(1));
						local.qrySyncPoints = getSeminarSyncPoints(local.strSeminar.qrySeminar.seminarID);
						for (local.tf = 1; local.tf lte local.strTitle.qryTitleFiles.recordcount; local.tf = local.tf + 1) {
							if (local.strTitle.qryTitleFiles.isDownloadable[local.tf] == "1") {
								local.hasDownloadedContent = 1;
							}
							local.FALen = ArrayLen(local.rsrd.titleArray[local.TALen].fileArray) + 1;
							local.rsrd.titleArray[local.TALen].fileArray[local.FALen] = StructNew();
							local.rsrdtafa = local.rsrd.titleArray[local.TALen].fileArray[local.FALen];		// shortcut for sets below
							structInsert(local.rsrdtafa,"fileid",local.strTitle.qryTitleFiles.fileID[local.tf]);
							structInsert(local.rsrdtafa,"orgcode",local.strTitle.qryTitleFiles.orgcode[local.tf]);
							structInsert(local.rsrdtafa,"participantID",local.strTitle.qryTitleFiles.participantID[local.tf]);
							structInsert(local.rsrdtafa,"fileLogAccessID",local.strAccessIDs.files[local.strTitle.qryTitleFiles.fileID[local.tf]].accessid);
							structInsert(local.rsrdtafa,"lastTimeCode",local.strAccessIDs.files[local.strTitle.qryTitleFiles.fileID[local.tf]].lasttimecode);
							structInsert(local.rsrdtafa,"fileName",local.strTitle.qryTitleFiles.fileName[local.tf]);
							structInsert(local.rsrdtafa,"fileTitle",local.strTitle.qryTitleFiles.fileTitle[local.tf]);
							structInsert(local.rsrdtafa,"fileDesc",local.strTitle.qryTitleFiles.fileDesc[local.tf]);
							structInsert(local.rsrdtafa,"preview",local.strTitle.qryTitleFiles.previewPct[local.tf]);
							structInsert(local.rsrdtafa,"isDownloadable",local.strTitle.qryTitleFiles.isDownloadable[local.tf]);
							structInsert(local.rsrdtafa,"isSupportingDoc",local.strTitle.qryTitleFiles.isSupportingDoc[local.tf]);
							structInsert(local.rsrdtafa,"defaultPaper",local.strTitle.qryTitleFiles.isDefaultPaper[local.tf]);
							structInsert(local.rsrdtafa,"defaultStream",local.strTitle.qryTitleFiles.isDefaultStream[local.tf]);
							structInsert(local.rsrdtafa,"duration",local.strTitle.qryTitleFiles.duration[local.tf]);
							structInsert(local.rsrdtafa,"type",local.strTitle.qryTitleFiles.fileType[local.tf]);
							structInsert(local.rsrdtafa,"syncArray",getFileSyncPoints(local.qrySyncPoints,local.strTitle.qryTitleFiles.fileID[local.tf]));
							
								// Ben Nadal trick to convert java array into CF array without looping
								local.tempArray = ArrayNew(1);
								local.tempArray.AddAll(
									CreateObject("java","java.util.Arrays").AsList(
										ToString(local.strAccessIDs.files[local.strTitle.qryTitleFiles.fileID[local.tf]].accessDetails).Split("")
									)
								);
								if (not len(local.tempArray[1])) 
									ArrayDeleteAt(local.tempArray, 1);
								structInsert(local.rsrdtafa,"accessDetails",local.tempArray);						
							
							// file formats for downloads
							structInsert(local.rsrdtafa,"formatArray",ArrayNew(1));
							if (local.rsrdtafa.isDownloadable is 1) {
								local.xmlFormats = XMLSearch(local.strTitle.qryTitleFiles.formatsAvailable[local.tf],"/formats/format[@accesstype='D' and not (@ext='flv' or @ext='swf' or translate(@ext,'PRV','prv')='pvr')]");
								for (local.ff = 1; local.ff lte arraylen(local.xmlFormats); local.ff = local.ff + 1) {
									local.rsrdtafa.formatArray[local.ff] = StructNew();
									structInsert(local.rsrdtafa.formatArray[local.ff],"extension",local.xmlFormats[local.ff].XMLAttributes.ext);
									structInsert(local.rsrdtafa.formatArray[local.ff],"formatURL","/?pg=semwebcatalog&panel=downloadFile&mode=stream&fileid=" & local.rsrdtafa.fileid & "&titleID=" & local.rsrd.titleArray[local.TALen].titleid & "&enrollmentID=" & arguments.enrollmentID & "&extension=" & local.rsrdtafa.formatArray[local.ff].extension);
								}						
							}
		
							// file info for viewing in player
							structInsert(local.rsrdtafa,"viewerFileAvailable",0);
							structInsert(local.rsrdtafa,"fileURL","");
		
							// video 
							if (local.rsrdtafa.isSupportingDoc) {
								//no need to look for viewer files
							}
							else if (local.strTitle.qryTitleFiles.fileType[local.tf] eq "video") {
								local.xmlFormats = XMLSearch(local.strTitle.qryTitleFiles.formatsAvailable[local.tf],"/formats/format[@ext='mp4' and @accesstype='S']");
								if (arrayLen(local.xmlFormats)) {
									structInsert(local.rsrdtafa,"viewerFileAvailable",1,true);								
									structInsert(local.rsrdtafa,"fileURL","swod/" & lcase(local.strTitle.qryTitleFiles.orgcode[local.tf]) & "/" & local.strTitle.qryTitleFiles.participantID[local.tf] & "/" & local.strTitle.qryTitleFiles.fileID[local.tf],true);
								}
		
							// audio
							} else if (local.strTitle.qryTitleFiles.fileType[local.tf] eq "audio") {
								local.xmlFormats = XMLSearch(local.strTitle.qryTitleFiles.formatsAvailable[local.tf],"/formats/format[@ext='mp3' and @accesstype='S']");
								if (arrayLen(local.xmlFormats)) {
									structInsert(local.rsrdtafa,"viewerFileAvailable",1,true);
									structInsert(local.rsrdtafa,"fileURL","swod/" & lcase(local.strTitle.qryTitleFiles.orgcode[local.tf]) & "/" & local.strTitle.qryTitleFiles.participantID[local.tf] & "/" & local.strTitle.qryTitleFiles.fileID[local.tf],true);
								}
								
							// paper (fileURL is really an xml node of pages/urls)
							} else if (local.strTitle.qryTitleFiles.fileType[local.tf] eq "paper") {
								local.xmlFormats = XMLSearch(local.strTitle.qryTitleFiles.formatsAvailable[local.tf],"/formats/format[translate(@ext,'PRV','prv')='pvr' and @accesstype='D']");
								if (arrayLen(local.xmlFormats)) {
									structInsert(local.rsrdtafa,"viewerFileAvailable",1,true);
									local.xmlPages2 = xmlParse(replacenocase(local.objSWFiles.preparePVRFileHTML5(enrollmentID=arguments.enrollmentID, fileID=local.strTitle.qryTitleFiles.fileID[local.tf], titleID=local.strSeminar.qrySeminarTitles.titleID[local.t], formatsAvailable=local.strTitle.qryTitleFiles.formatsAvailable[local.tf]),"<?xml version=""1.0"" encoding=""UTF-8""?>",''));
									local.pagesNode = XMLSearch(local.xmlPages2,"/document/page/@url");
									local.thisPagesArr = arrayNew(1);
									for (local.i = 1; local.i lte arraylen(local.pagesNode); local.i = local.i + 1) {
										arrayAppend(local.thisPagesArr,local.pagesNode[local.i].XmlValue);
									}								
									structInsert(local.rsrdtafa,"fileURL",local.thisPagesArr,true);	
								} else {
									structInsert(local.rsrdtafa,"viewerFileAvailable",0,true);
									structInsert(local.rsrdtafa,"fileURL","<document/>",true);
								}
							}
						}
						structInsert(local.rsrd.titleArray[local.TALen],"hasDownloadedContent", local.hasDownloadedContent);
			
						// load title links (no longer supported)
						structInsert(local.rsrd.titleArray[local.TALen],"linkArray",ArrayNew(1));
					}
		
					// set to true upon completion
					structInsert(local.rsrd,"loadSuccess",1,true);
				}else{
					if(local.memberID neq ''){
						local.memberInfo = application.objMember.getMemberInfo(memberID=local.memberID);
						local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=session.mcstruct.sitecode, membernumber=local.memberInfo.membernumber);
						structInsert(local.rsrd,"mk",local.memberKey);
					}else{
						structInsert(local.rsrd,"mk",'');
					}
				}
			}else{
				if(local.memberID neq ''){
					local.memberInfo = application.objMember.getMemberInfo(memberID=local.memberID);
					local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=session.mcstruct.sitecode, membernumber=local.memberInfo.membernumber);
					structInsert(local.rsrd,"mk",local.memberKey);
				}else{
					structInsert(local.rsrd,"mk",'');
				}
			}
		} catch(any e){

			structInsert(local.rsrd,"loadSuccess",0,true);
			local.arguments = arguments;
			reportErrorToAdmin(message="Error in SWODPlayerHTML5.getSeminarForPlayer() for EnrollmentID: #arguments.enrollmentID#",errorStruct=e,objectToDump=local);
		}
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="setupLogAccessIDs" access="private" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfstoredproc procedure="swod_createLogAccessIDs" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocresult name="local.qrySeminarAccessID" resultset="1">
			<cfprocresult name="local.qryFileAccessID" resultset="2">
			<cfprocresult name="local.qryFileAccessDetails" resultset="3">
		</cfstoredproc>

		<cfset local.seminarAccessID = local.qrySeminarAccessID.logAccessID>
		<cfset local.files = StructNew()>
		<cfloop query="local.qryFileAccessID">
			<cfquery name="local.qryfiledetails" dbtype="query">
				select *
				from [local].qryFileAccessDetails
				where fileID = #local.qryFileAccessID.fileID#
			</cfquery>
			<cfset local.files[local.qryFileAccessID.fileID] = StructNew()>
			<cfset local.files[local.qryFileAccessID.fileID]['accessid'] = local.qryFileAccessID.accessID>
			<cfset local.files[local.qryFileAccessID.fileID]['lasttimecode'] = val(local.qryFileAccessID.lastTimeCode)>
			<cfset local.files[local.qryFileAccessID.fileID]['accessDetails'] = local.objSWOD.bitORFiles(local.qryfiledetails)>
		</cfloop>
		
		<cfreturn local>
	</cffunction>	

	<cffunction name="finalCheckSeminarforCompletion" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="userTimeSpent" type="numeric" required="yes">

		<cfset var local = Structnew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfif local.returnStruct.userLoggedIn>
			<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID)>
			<cftry>
				<cfset local.strCompletion = checkSeminarforCompletion(arguments.mcproxy_siteCode,arguments.enrollmentID)>
				<cfset local.returnStruct.rawData = local.strCompletion>
 				<cfif local.strCompletion.returnData.isCompleted is not 1 
					OR local.strCompletion.returnData.allPostTestCompleted is 0>
					<cfthrow>
				</cfif>

				<!--- convert totalTimeSpent from seconds to minutes --->
				<cfset local.minutes = round(local.strCompletion.returnData.totalTimeSpent / 60)>

				<cfstoredproc procedure="swod_recordCompletion" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.userTimeSpent#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.minutes#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollment.MCMemberID#" null="No">
				</cfstoredproc>
			<cfcatch type="any">
				<cfset local.returnStruct["returnData"] = 0>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
			<cfset local.returnStruct["returnData"] = 1>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkSeminarforCompletion" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="false" >
		<cfargument name="enrollmentID" type="numeric" required="false" >
		<cfargument name="logAccessID" type="numeric" required="false" default="0">
		<cfargument name="reportObj" type="struct" required="false" default="#structNew()#">		

		<cfset var local = Structnew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles")>
		
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["returnData"] = structnew()>
		
		<cfset local.argumentsStruct = structnew() />
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.orgcode = arguments.mcproxy_siteCode />
			<cfset local.argumentsStruct.enrollmentID = arguments.enrollmentID />
			<cfset local.argumentsStruct.logAccessID = arguments.logAccessID />
			<cfset local.argumentsStruct.reportObj = arguments.reportObj />
		</cfif>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(local.argumentsStruct.enrollmentID)>
		<cfif local.returnStruct["userLoggedIn"]>
			<cfif arguments.logAccessID and IsDefined("local.argumentsStruct.reportObj.logAccessID") and local.argumentsStruct.reportObj.logAccessID gt 0>
				<cfset local.argumentsStruct.sendReminderEmailFlag = false />	
				<cfset local.argumentsStruct.includeLoginDetails = false />	
				<cfset saveProgressFromPlayer(serializeJSON(local.argumentsStruct))>
			</cfif>
		
			<cfstoredproc procedure="swod_getSeminarProgress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.enrollmentID#" null="No">
				<cfprocresult name="local.qrySeminarSettings" resultset="1">
				<cfprocresult name="local.qrySeminarProgress" resultset="2">
				<cfprocresult name="local.qryCreditsPassed" resultset="3">
				<cfprocresult name="local.qryFiles" resultset="4">
				<cfprocresult name="local.qryTimeSpent" resultset="5">
			</cfstoredproc>
	
			<cfscript>
			// step 1 : check for incomplete exams and complete them for this enrollmentID
			local.objSWOD.completeIncompleteExams(enrollmentid=local.argumentsStruct.enrollmentID,loadPoint='postTest',recordedByMemberID=local.qryEnrollment.MCMemberID);

			local.returnStruct["returnData"]["isCompleted"] = 1;
			local.returnStruct["returnData"]["totalTimeSpent"] = val(local.qryTimeSpent.totalTimeSpent);
			local.returnStruct["returnData"]["seminarRulesArray"] = ArrayNew(1);
			local.returnStruct["returnData"]["fileRulesArray"] = ArrayNew(1);
			local.returnStruct["returnData"]["askedQA"] = local.qrySeminarProgress.askedQA;
			
			// check for test, survey 
			if (local.qrySeminarProgress.allPreTestCompleted)
				local.returnStruct["returnData"]["allPreTestCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allPreTestCompleted"] = 0;

			if (local.qrySeminarProgress.allPostTestCompleted )
				local.returnStruct["returnData"]["allPostTestCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allPostTestCompleted"] = 0;

			if (local.qrySeminarProgress.allEvaluationCompleted )
				local.returnStruct["returnData"]["allEvaluationCompleted"] = 1;
			else
				local.returnStruct["returnData"]["allEvaluationCompleted"] = 0;			
				
			// check for credit deadlines
			if (local.qryCreditsPassed.recordcount) {
				local.tmpArrRules = structnew();
				local.tmpArrRules.code = 2;
				local.tmpArrRules.description = "You have missed the course completion deadline for the authorities listed below.  You will not receive credit in these states.";
				if (local.qryCreditsPassed.recordcount eq 1)
					local.tmpArrRules.name = "You Have Missed 1 Completion Deadline";
				else 
					local.tmpArrRules.name = "You Have Missed #local.qryCreditsPassed.recordcount# Completion Deadlines";
				local.tmpArrRules.data = ArrayNew(1);
				for (local.i = 1; local.i lte local.qryCreditsPassed.recordcount; local.i = local.i + 1) {
					local.tmpStrData = StructNew();
					local.tmpStrData.authority = local.qryCreditsPassed.authorityName[local.i];
					local.tmpStrData.deadline = local.qryCreditsPassed.lastDateToComplete[local.i];
					ArrayAppend(local.tmpArrRules.data,local.tmpStrData);
				}
				ArrayAppend(local.returnStruct["returnData"]["seminarRulesArray"],local.tmpArrRules);
			}
			</cfscript>
	
			<!--- if we need to check completion percentage --->
			<cfset local.returnStruct["returnData"]["mediaRequiredPct"] = 0 />
			<cfif local.qrySeminarSettings.mediaRequiredPct gt 0>
				<!--- for each file, get the final accessDetails --->
				<cfset local.strAccessDetails = structNew()>
				<cfoutput query="local.qryFiles" group="fileID">
					<cfquery name="local.qryfiledetails" dbtype="query">
						select *
						from [local].qryFiles
						where fileID = #local.qryFiles.fileID#
					</cfquery>
					<cfset local.strAccessDetails[local.qryFiles.fileid] = local.objSWOD.bitORFiles(local.qryfiledetails)>
				</cfoutput>
			
				<!--- setup structure --->
				<cfset local.tmpArrRules = structnew()>
				<cfset local.tmpArrRules.code = 3>
				<cfset local.tmpArrRules.description = "You must play at least #local.qrySeminarSettings.mediaRequiredPct#% of each video/audio file in this seminar.">
				<cfset local.tmpArrRules.name = "You Have Not Played the Required Amount of Media Files">
				<cfset local.tmpArrRules.data = ArrayNew(1)>
				<cfset local.returnStruct["returnData"]["mediaRequiredPct"] = local.qrySeminarSettings.mediaRequiredPct />
	
				<!--- loop over each title, then over each file matching the percentage --->
				<cfoutput query="local.qryFiles" group="titleID">
					<cfset local.tmpStrData = StructNew()>
					<cfset local.tmpStrData["titleID"] = local.qryFiles.titleID>
					<cfset local.tmpStrData["fileArray"] = arrayNew(1)>
					<cfoutput group="fileID">
						<cfif NOT local.objSWFiles.checkFileCompletion(local.strAccessDetails[local.qryFiles.fileid],local.qrySeminarSettings.mediaRequiredPct)>
							<cfset local.tmpStrFileData = StructNew()>
							<cfset local.tmpStrFileData["fileID"] = local.qryFiles.fileID>
							<cfif len(local.strAccessDetails[local.qryFiles.fileid]) gt 0>
								<cfset local.tmpStrFileData["playRatio"] = len(replacenocase(local.strAccessDetails[local.qryFiles.fileid],"0","","all")) / len(local.strAccessDetails[local.qryFiles.fileid])>
							<cfelse>
								<cfset local.tmpStrFileData["playRatio"] = 0>
							</cfif>
							<cfset local.tmpStrFileData["accessDetailsArray"] = getPlayReportFromAccessDetails(local.strAccessDetails[local.qryFiles.fileid],false)>
							<cfset ArrayAppend(local.tmpStrData.fileArray,local.tmpStrFileData)>
						</cfif>
					</cfoutput>
					<cfif Arraylen(local.tmpStrData.fileArray)>
						<cfset ArrayAppend(local.tmpArrRules.data,local.tmpStrData)>
					</cfif>
				</cfoutput>
				<cfif arrayLen(local.tmpArrRules.data)>
					<cfset ArrayAppend(local.returnStruct["returnData"]["fileRulesArray"],local.tmpArrRules)>
					<cfset local.returnStruct["returnData"]["isCompleted"] = 0>
				</cfif>
			</cfif>	
			
			<!--- total time spent requirement --->
			<cfif local.qrySeminarSettings.mustAttendMinutes gt 0 and val(local.qryTimeSpent.totalTimeSpent) lt (local.qrySeminarSettings.mustAttendMinutes * 60)>
				<cfset local.tmpArrRules = structnew()>
				<cfset local.tmpArrRules.code = 4>
				<cfset local.tmpArrRules.description = "This seminar has a minimum time requirement of #generateTimeLengthString(local.qrySeminarSettings.mustAttendMinutes)#. According to our records, you have only spent #generateTimeLengthString(val(local.qryTimeSpent.totalTimeSpent)\60)#.">
				<cfset local.tmpArrRules.name = "You Have Not Satisfied the Minimum Time Requirement">
				<cfset local.tmpArrRules.data = "">
				<cfset ArrayAppend(local.returnStruct["returnData"]["seminarRulesArray"],local.tmpArrRules)>
				<cfset local.returnStruct["returnData"]["isCompleted"] = 0>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="saveFileSyncPoints" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="syncPointArray" type="any" required="yes">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		
		<cfif local.returnStruct.userLoggedIn>
			<cfquery name="local.deleteExistingSyncPoint" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				delete from dbo.tblSeminarAndFilesSyncPoints 
				where fileID = <cfqueryparam value="#arguments.fileID#" cfsqltype="CF_SQL_INTEGER">
				and seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfloop index="local.x" from="1" to="#arraylen(arguments.syncPointArray)#">
				<cfquery name="local.insertSyncPoint" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					insert into dbo.tblSeminarAndFilesSyncPoints (seminarID, fileID, timecode, linkedFileID, page)
					values (
						<cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#arguments.fileID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#arguments.syncPointArray[local.x].timecode#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#arguments.syncPointArray[local.x].fileID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#arguments.syncPointArray[local.x].page#" cfsqltype="CF_SQL_INTEGER">
					)
				</cfquery>
			</cfloop>
			<cfset local.returnStruct["returnData"] = 1>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="saveFileSyncPointsHTML" access="remote" returntype="struct" output="no">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.requestData = deserializeJSON(toString( getHttpRequestData().content ))>
		<cfset local.seminarID = local.requestData.seminarID>
		<cfset local.fileID = local.requestData.fileID>
		<cfset local.syncPointArray = local.requestData.syncPointArray>
		
		<cfif local.returnStruct.userLoggedIn>
			<cfquery name="local.deleteExistingSyncPoint" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				delete from dbo.tblSeminarAndFilesSyncPoints 
				where fileID = <cfqueryparam value="#local.fileID#" cfsqltype="CF_SQL_INTEGER">
				and seminarID = <cfqueryparam value="#local.seminarID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfloop index="local.x" from="1" to="#arraylen(local.syncPointArray)#">
				<cfquery name="local.insertSyncPoint" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					insert into dbo.tblSeminarAndFilesSyncPoints (seminarID, fileID, timecode, linkedFileID, page)
					values (
						<cfqueryparam value="#local.seminarID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#local.fileID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#local.syncPointArray[local.x].timecode#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#local.syncPointArray[local.x].fileID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#local.syncPointArray[local.x].page#" cfsqltype="CF_SQL_INTEGER">
					)
				</cfquery>
			</cfloop>
			<cfset local.returnStruct["returnData"] = 1>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="loadFormsByLoadPoint" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">

		<cfscript>
		var local = StructNew();
		local.returnStruct = structnew();
		structInsert(local.returnStruct,"userLoggedIn",isLoggedIn());

		if (local.returnStruct.userLoggedIn) {
			structInsert(local.returnStruct,"returnData",StructNew());
			try {
				local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars");
				local.qryEnrollment = local.objSeminarsSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID)
				// step 1 : check for incomplete exams and complete them for this enrollmentID
				local.objSeminarsSWOD.completeIncompleteExams(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,recordedByMemberID=local.qryEnrollment.MCMemberID);

				if(arguments.loadPoint eq 'posttestall'){
					arguments.loadPoint = 'posttest';
					local.strForms = getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,depomemberdataid=local.qryEnrollment.depomemberdataid);					
				}else if(arguments.loadPoint eq 'evaluationall'){
					arguments.loadPoint = 'evaluation';
					local.strForms = getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint,depomemberdataid=local.qryEnrollment.depomemberdataid);					
				}else{
					// step 2 : return all forms (formID, formtitle) for this seminar that user needs to take
					local.strForms = local.objSeminarsSWOD.getFormsFromLoadpointJSon(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint);
				}
				// step 3 : Get custom failed message if any
				local.strFormsMessage = local.objSeminarsSWOD.getFormsCustomFailedMessage(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint);
				if (arrayLen(local.strFormsMessage)) {
					if(local.strFormsMessage[1].PASSFAIL eq 'Failed' AND local.strFormsMessage[1].ATTEMPTSREMAINING eq 0){
						local.customFailMessage = getCustomFailMessage(enrollmentid=arguments.enrollmentID,loadPoint=arguments.loadPoint);
						if (arrayLen(local.customFailMessage)) {
							local.strFormsMessage[1].OVERRIDEMESSAGE = local.customFailMessage[1].OVERRIDEMESSAGE;
						}
					}

				}
				
				structInsert(local.returnStruct.returnData,"examObj",local.strForms);
				structInsert(local.returnStruct.returnData,"strFormsMessage",local.strFormsMessage);
			}
			catch (any e) {
				structInsert(local.returnStruct.returnData,"examObj",structnew());
				reportErrorToAdmin(message="Error in SWODPlayerHTML5.loadFormsByLoadPoint() for EnrollmentID: #arguments.enrollmentID#",errorStruct=e,objectToDump=local);
			}
		}		
		
		return local.returnStruct;
		</cfscript>
	</cffunction>
	
	<cffunction name="getFormsFromLoadpointJSon" access="public" returntype="Any" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="depomemberdataid" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objSeminarsSWOD = CreateObject("component","model.admin.seminarweb.seminarWebSWOD")>
		
		<cfquery name="local.qryForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;

			SELECT form.formid, f.formtitle, isnull(f.formintro,'') as formintro, form.isrequired, 
				form.maxminutesallowed, isnull(form.certifiedstatement,'') as certifiedstatement, 
				form.maxtriesperquestion, form.allowskipbackward,
				form.enforceqreqstatuscorrectmaxtries, form.showimmediateanswer,
				f.passingPct, form.numResponsesPerEnrollment, form.overrideMessage, 
				dbo.sw_getNumFormAttemptsRemaining(e.enrollmentID,form.seminarFormID) as attemptsRemaining,
				form.orderBy,form.seminarFormID,0 as completePercent
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
				AND form.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="cf_sql_varchar">				
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid			
				AND f.isPublished = 1 and f.isDeleted = 0
			WHERE e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
			ORDER BY form.orderBy ASC
		</cfquery>

		<cfset local.arrForms = ArrayNew(1,false)>
		<cfloop query="local.qryForms">
			<cfset local.seminarFormDetail = local.objSeminarsSWOD.getSeminarFormDetail(seminarFormID=local.qryForms.seminarFormID,depoMemberDataID=arguments.depomemberdataid,isActive=1)>
			
			<cfset local.passingPct = 0>
			<cfif local.seminarFormDetail.recordcount gt 0>
				<cfset local.passingPct = local.seminarFormDetail.passingPct[local.seminarFormDetail.recordcount]>
			<cfelse>
				<cfset local.passingPct = -1>
			</cfif>	
			
			
			<cfset local.arrFormsRow = StructNew()>
			<cfset local.arrFormsRow.formid = local.qryForms.formid>
			<cfset local.arrFormsRow.formtitle = local.qryForms.formtitle>
			<cfset local.arrFormsRow.passingPct = local.passingPct>
			<cfset local.arrFormsRow.completePercent = local.qryForms.completePercent>
			<cfset local.arrFormsRow.formAttended = 0>
			<cfif arguments.loadPoint eq 'evaluation'>
				<cfquery name="local.qryFormsAttended" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET XACT_ABORT, NOCOUNT ON;

					SELECT form.formid
					FROM dbo.tblEnrollments AS e 
					INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
						AND form.loadPoint = 'evaluation'				
					INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid			
						AND f.isPublished = 1 and f.isDeleted = 0
					INNER JOIN  dbo.tblSeminarsAndFormResponses as safr on safr.enrollmentID = e.enrollmentID
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.formID = form.formid	
					WHERE e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
					  AND r.isActive = 1 AND r.formID = <cfqueryparam value="#local.qryForms.formid#" cfsqltype="cf_sql_integer">
					ORDER BY form.orderBy ASC
					
				</cfquery>
				<cfif local.qryFormsAttended.recordcount gt 0>
					<cfset local.arrFormsRow.formAttended = 1>
				<cfelse>
					<cfset local.arrFormsRow.formAttended = 0>
				</cfif>
				
			</cfif>
			<cfset arrayAppend(local.arrForms,local.arrFormsRow)>
			
		</cfloop>
		
		<cfreturn local.arrForms>
	</cffunction>

	<cffunction name="getPlayReportFromAccessDetails" access="remote" returntype="array" output="no" hint="Breaks an Access Details string into a array of structures that indicate which time ranges have been played and which have not.">
		<cfargument name="accessDetails" type="string" required="yes">
		<cfargument name="includePlayedSegments" type="boolean" required="yes" hint="Determines whether or not array will contain info on played segments.">

		<cfset var local = StructNew()>
		
		<cfset local.secondsPerBit = 15>
		<cfset local.accessDetailsArray = ArrayNew(1)>
		<cfset local.accessNodePrefix = "">  <!--- Contains accessDetails string before current loop iteration --->
		<cfset local.accessDetailList = replace(replace(arguments.accessDetails,"01","0,1","all"),"10","1,0","all")>
		
		<cfloop index="local.currentItem" list="#local.accessDetailList#">
			<cfif left(local.currentItem,1) is 1>
				<cfset local.hasBeenPlayed = true>
			<cfelse>
				<cfset local.hasBeenPlayed = false>
			</cfif>
			<cfif (arguments.includePlayedSegments) or (not local.hasBeenPlayed)>
				<cfset local.accessDetailsArrayNode = StructNew()>
				<cfset local.accessDetailsArrayNode.starttime = len(local.accessNodePrefix) * local.secondsPerBit>
				<cfset local.accessDetailsArrayNode.endtime = local.accessDetailsArrayNode.starttime + (len(local.currentItem) * local.secondsPerBit)>
				<cfset local.accessDetailsArrayNode.hasBeenPlayed = local.hasBeenPlayed>
				<cfset arrayAppend(local.accessDetailsArray,local.accessDetailsArrayNode)>
			</cfif>
			<cfset local.accessNodePrefix = local.accessNodePrefix & local.currentItem>
		</cfloop>

		<cfreturn local.accessDetailsArray>
	</cffunction>
	
	<cffunction name="loadForm" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset structInsert(local.returnStruct,"userLoggedIn",isLoggedIn())>

		<cfif (local.returnStruct.userLoggedIn)>
			<cfset structInsert(local.returnStruct,"returnData",StructNew())>
			<cftry>
				<cfset local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
				<cfset local.xmlForm = local.objSeminarsSWOD.loadFormXMLHTML5(enrollmentid=arguments.enrollmentID,loadpoint=arguments.loadPoint,formID=arguments.formID,mode='SWOD')>
				<cfset local.returnStructTemp = structnew()>
				<cfset local.returnStructTemp = formXMLToJSon(xmlString=local.xmlForm,loadPoint=arguments.loadPoint)>
				<cfset structInsert(local.returnStruct.returnData,"examObj",local.returnStructTemp)>
				<cfset structInsert(local.returnStruct,"formConfigObj",loadFormJSon(enrollmentid=arguments.enrollmentID,loadpoint=arguments.loadPoint,formID=arguments.formID))>
			<cfcatch type="any">
				<cfset structInsert(local.returnStruct.returnData,"examObj",structnew(),true)>
				<cfset reportErrorToAdmin(message="Error in SWODPlayerHTML5.loadForm() for EnrollmentID: #arguments.enrollmentID#",errorStruct=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>	
	
	<cffunction name="formXMLToJSon" access="private" output="no" returntype="Struct">
		<cfargument name="xmlString" type="string" required="true" />
		<cfargument name="loadPoint" type="string" required="true" />
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		
		<cfset local.arrFormNode = XMLSearch(arguments.xmlString,"//form")>
		<cfloop array="#local.arrFormNode#" index="local.thisItem">
			<cfset structInsert(local.returnStruct,"alloweditresponses",local.thisItem.xmlAttributes.alloweditresponses)>
			<cfset structInsert(local.returnStruct,"formclose",local.thisItem.xmlAttributes.formclose)>
			<cfset structInsert(local.returnStruct,"formid",local.thisItem.xmlAttributes.formid)>				
			<cfset structInsert(local.returnStruct,"formintro",local.thisItem.xmlAttributes.formintro)>
			<cfset structInsert(local.returnStruct,"formtitle",local.thisItem.xmlAttributes.formtitle)>
			<cfset structInsert(local.returnStruct,"formtype",local.thisItem.xmlAttributes.formtype)>				
			<cfset structInsert(local.returnStruct,"passingpct",local.thisItem.xmlAttributes.passingpct)>
			<cfset structInsert(local.returnStruct,"responseid",local.thisItem.xmlAttributes.responseid)>
			<cfset structInsert(local.returnStruct,"submitbtntext",local.thisItem.xmlAttributes.submitbtntext)>
		</cfloop>
		
		<cfset local.arrQuestionNode = XMLSearch(arguments.xmlString,"//questiontypes")>
		<cfset local.arrQuestions = arrayNew(1)>	
		<cfloop array="#local.arrQuestionNode#" index="local.thisItem">
			<cfloop array="#local.thisItem.xmlChildren#" index="local.thisChildrenItem">
				<cfset local.tempQuestionStruct = structNew()>
				<cfset structInsert(local.tempQuestionStruct,"questiontype",local.thisChildrenItem.xmlAttributes.questionType)>
				<cfset structInsert(local.tempQuestionStruct,"questiontypecode",local.thisChildrenItem.xmlAttributes.questiontypecode)>
				<cfset structInsert(local.tempQuestionStruct,"questiontypeid",local.thisChildrenItem.xmlAttributes.questiontypeid)>
				<cfset arrayAppend(local.arrQuestions,local.tempQuestionStruct)>
			</cfloop>
		</cfloop>
		<cfset structInsert(local.returnStruct,"questionarr",local.arrQuestions)>
		
		<cfset local.arrSectionNode = XMLSearch(arguments.xmlString,"//section")>
		<cfset local.tempSectionStruct = structNew()>
		<cfset local.arrSections = arrayNew(1)>
		<cfset local.arrPages = arrayNew(1)>
		<cfloop array="#local.arrSectionNode#" index="local.thisItem">		
			<cfset structInsert(local.tempSectionStruct,"sectionid",local.thisItem.xmlAttributes.sectionid)>
			<cfset structInsert(local.tempSectionStruct,"sectiontitle",local.thisItem.xmlAttributes.sectiontitle)>
			<cfif isDefined("local.thisItem.xmlAttributes.sectiondesc")>
				<cfset structInsert(local.tempSectionStruct,"sectiondesc",local.thisItem.xmlAttributes.sectiondesc)>
			<cfelse>
				<cfset structInsert(local.tempSectionStruct,"sectiondesc","")>
			</cfif>
			
			<cfset local.arrPageNode = XMLSearch(local.thisItem,"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page")>
			<cfloop array="#local.arrPageNode#" index="local.thisChildrenItem">
				<cfset local.arrQuestionNode = XMLSearch(local.thisChildrenItem,"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page/question")>
				<cfloop array="#local.arrQuestionNode#" index="local.thisQuestionNodeItem">
					<cfset local.tempPageQuestionStruct = structNew()>
					<cfset structInsert(local.tempPageQuestionStruct,"qkey",local.thisQuestionNodeItem.xmlAttributes.qkey)>
					<cfif arguments.loadPoint neq "evaluation">
						<cfset structInsert(local.tempPageQuestionStruct,"controlfield",local.thisQuestionNodeItem.xmlAttributes.controlfield)>
					</cfif>
					<cfset structInsert(local.tempPageQuestionStruct,"controlstyle",local.thisQuestionNodeItem.xmlAttributes.controlstyle)>
					<cfset structInsert(local.tempPageQuestionStruct,"displayquestionnumber",local.thisQuestionNodeItem.xmlAttributes.displayquestionnumber)>
					<cfset structInsert(local.tempPageQuestionStruct,"displayquestiontext",local.thisQuestionNodeItem.xmlAttributes.displayquestiontext)>
					<cfset structInsert(local.tempPageQuestionStruct,"isdisplayedinline",local.thisQuestionNodeItem.xmlAttributes.isdisplayedinline)>
					<cfset structInsert(local.tempPageQuestionStruct,"isrequired",local.thisQuestionNodeItem.xmlAttributes.isrequired)>
					<cfset structInsert(local.tempPageQuestionStruct,"questionid",local.thisQuestionNodeItem.xmlAttributes.questionid)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontext",local.thisQuestionNodeItem.xmlAttributes.questiontext)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontypecode",local.thisQuestionNodeItem.xmlAttributes.questiontypecode)>
					<cfset structInsert(local.tempPageQuestionStruct,"questiontypeid",local.thisQuestionNodeItem.xmlAttributes.questiontypeid)>	

					<cfset local.arrOptions = arrayNew(1)>
					<cfif arguments.loadPoint neq "evaluation">
						<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren)#" index="local.thisOptionNodeItem">
							<cfset local.tempQuestionOptionStruct = structNew()>
							<cfset local.arrOptionNode = XMLSearch(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem],"//section[@sectionid=#local.thisItem.xmlAttributes.sectionid#]/page/question[@questionid=#local.thisQuestionNodeItem.xmlAttributes.questionid#]/options")>
							<cfif arraylen(local.arrOptionNode)>								
								<cfloop from="1" to="#arraylen(local.arrOptionNode)#" index="local.thisOptionItem">
		 							<cfif not structKeyExists(local, "tempQuestionOptionStruct")>
		 								<cfset local.tempQuestionOptionStruct = structNew()>	 								
		 							</cfif>	 							
		 							<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.inputtext)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.optionid)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.optiontext)>
									<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionItem].xmlAttributes.showinput)>
									<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",arrayNew(1))>	
									<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>
									<cfset structDelete(local,"tempQuestionOptionStruct")>								
								</cfloop>
								<cfbreak>
							<cfelse>
								<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.inputtext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optionid)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optiontext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.showinput)>	
								<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",arrayNew(1))>	
								<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>
							</cfif>
						</cfloop>
					<cfelse>
						<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren)#" index="local.thisOptionNodeItem">
							<cfset local.tempQuestionOptionStruct = structNew()>	
							<cfset local.arrOptionsX = arrayNew(1)>	
							<cfif not structIsEmpty(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes)>
								<cfset structInsert(local.tempQuestionOptionStruct,"inputtext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.inputtext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optionid)>
								<cfset structInsert(local.tempQuestionOptionStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.optiontext)>
								<cfset structInsert(local.tempQuestionOptionStruct,"showinput",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlAttributes.showinput)>	
								<cfloop from="1" to="#arraylen(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren)#" index="local.thisOptionxItem">
									<cfset local.tempOptionxStruct = structNew()>	
									<cfif not structIsEmpty(local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes)>	
										<cfset structInsert(local.tempOptionxStruct,"optiontext",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes.optiontext)>
										<cfset structInsert(local.tempOptionxStruct,"optionid",local.thisQuestionNodeItem.xmlChildren[local.thisOptionNodeItem].xmlChildren[local.thisOptionxItem].xmlAttributes.optionid)>
										<cfset arrayAppend(local.arrOptionsX,local.tempOptionxStruct)>
									</cfif>
								</cfloop>	
								<cfset structInsert(local.tempQuestionOptionStruct,"optionsxArr",local.arrOptionsX)>						
							</cfif>
							<cfset arrayAppend(local.arrOptions,local.tempQuestionOptionStruct)>								
						</cfloop>				
					</cfif>
					<cfset structDelete(local,"tempQuestionOptionStruct")>
					<cfset structInsert(local.tempPageQuestionStruct,"optionsarr",local.arrOptions)>							
					<cfset arrayAppend(local.arrPages,local.tempPageQuestionStruct)>					
				</cfloop> <!--- // local.arrQuestionNode --->
				<cfbreak>
			</cfloop> <!---// local.arrPageNode --->
			<cfset structInsert(local.tempSectionStruct,"pagesArr",local.arrPages)>
			
			<!--- Ensure the player can handle multiple sections --->
			<cfset arrayAppend(local.arrSections,local.tempSectionStruct)>
			<cfset structDelete(local,"tempSectionStruct")>	
			<cfset local.tempSectionStruct = structNew()>
			<cfset local.arrPages = []>
			
		</cfloop>	<!---// local.arrSectionNode --->			

		<cfset structInsert(local.returnStruct,"sectionObj",local.arrSections)>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEnrollmentCreditXML" access="private" returntype="string" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cfstoredproc procedure="swod_getEnrollmentCreditXML" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocresult name="local.qryEnrollmentCreditXML" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryEnrollmentCreditXML.enrollmentCreditXML>
	</cffunction>
	
	<cffunction name="getEnrollmentCreditJSon" access="private" returntype="Any" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfquery name="local.qryEnrollmentCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT ca.authorityName, eac.lastDateToComplete,
				CASE 
				WHEN (DATEADD(dd, DATEDIFF(dd, 0, eac.lastDateToComplete), '23:59:59') < GETDATE()) THEN 1 
				ELSE 0 END as completeByDatePassed
			FROM dbo.tblSeminars as s 
			INNER JOIN dbo.tblEnrollments as e ON s.seminarID = e.seminarID
			INNER JOIN dbo.tblEnrollmentsAndCredit as eac ON e.enrollmentID = eac.enrollmentID 
			INNER JOIN dbo.tblSeminarsAndCredit as sac ON s.seminarID = sac.seminarID 
				AND eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa ON sac.CSALinkID = csa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities as ca ON csa.authorityID = ca.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD as caswod ON ca.authorityID = caswod.authorityID 
			where e.enrollmentid = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
			order by eac.lastDateToComplete, ca.authorityName
		</cfquery>		

		<cfset local.returnStruct = application.objCommon.queryToArrayOfStructures(local.qryEnrollmentCredit)>		
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="getFileSyncPoints" access="private" returntype="array" output="no">
		<cfargument name="qrySyncPoints" type="query" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnArray = arrayNew(1)>

		<cfquery name="local.getSyncPoints" dbtype="query">
			select * 
			from arguments.qrySyncPoints
			where fileID = #arguments.fileID#
			order by timecode
		</cfquery>

		<cfloop query="local.getSyncPoints">
			<cfset local.thisNode = structNew()>
			<cfset local.thisNode["syncpointid"] = local.getSyncPoints.syncpointid>
			<cfset local.thisNode["seminarID"] = local.getSyncPoints.seminarID>
			<cfset local.thisNode["timecode"] = local.getSyncPoints.timecode>
			<cfset local.thisNode["fileID"] = local.getSyncPoints.linkedFileID>
			<cfset local.thisNode["page"] = local.getSyncPoints.page>
			<cfset ArrayAppend(local.returnArray,local.thisNode)>
		</cfloop>

		<cfreturn local.returnArray>
	</cffunction>

	<cffunction name="getSeminarSyncPoints" access="private" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="swod_getSeminarSyncPoints" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocresult name="local.qryPoints" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryPoints>
	</cffunction>
	
	<cffunction name="getSeminarLoadPointsObj" access="private" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["semninarHasPreTest"] = 0>
		<cfset local.returnStruct["semninarHasPostTest"] = 0>
		<cfset local.returnStruct["semninarHasEvaluation"] = 0>
		
		<cfstoredproc procedure="sw_getSeminarForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocresult name="local.qryPoints" resultset="1">
		</cfstoredproc>

		<cfloop query="local.qryPoints">
			<cfswitch expression="#local.qryPoints.loadPoint#">
				<cfcase value="evaluation">
					<cfset local.returnStruct["semninarHasEvaluation"] = 1>
				</cfcase>
				<cfcase value="postTest">
					<cfset local.returnStruct["semninarHasPostTest"] = 1>
				</cfcase>		
				<cfcase value="preTest">
					<cfset local.returnStruct["semninarHasPreTest"] = 1>
				</cfcase>							
			</cfswitch>
		</cfloop>	

		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="sendReminderEmail" access="private" returntype="void" output="no">
		<cfargument name="logAccessID" type="numeric" required="yes">
		<cfargument name="includeLoginDetails" type="boolean" required="yes">
		
		<cfif not structKeyExists(session,"SWODSeminarLogAccessIDReminderSent")>
			<cfset session.SWODSeminarLogAccessIDReminderSent = structNew()>
		</cfif>
		<cfif not structKeyExists(session.SWODSeminarLogAccessIDReminderSent,arguments.logAccessID)>
			<cfset createObject("component","model.seminarweb.SWODEmails").sendSaveAndExitEmail(arguments.logAccessID,arguments.includeLoginDetails)>
			<cfset session.SWODSeminarLogAccessIDReminderSent[arguments.logAccessID] = true>
		</cfif>
	</cffunction>
	
	<cffunction name="getNote" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = Structnew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.qryGetEnrollmentNote = local.objSWOD.getEnrollmentNote(enrollmentID=arguments.enrollmentID)>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["returndata"] = 1>
		<cfset local.returnStruct["noteid"] = local.qryGetEnrollmentNote.noteID>
		<cfset local.returnStruct["enrollmentid"] = local.qryGetEnrollmentNote.enrollmentID>
 		<cfset local.returnStruct["notetext"] = local.qryGetEnrollmentNote.noteText>
		<cfset local.returnStruct["notedt"] = local.qryGetEnrollmentNote.noteDt>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="saveNote" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" required="false">
		<cfargument name="noteText" required="false">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["returndata"] = 0>
		
		<cfset local.argumentsStruct = structnew() />
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.noteText = arguments.noteText />
			<cfset local.argumentsStruct.enrollmentID = arguments.enrollmentID />
		</cfif>
		
		<!--- enrollmentID and noteText are really required, but setting them as required above triggers exceptions if not provided by direct link. --->
		<cfset local.isValidArguments = true>
		<cfif 
			(NOT StructKeyExists(local.argumentsStruct, "enrollmentID") OR NOT IsNumeric(local.argumentsStruct.enrollmentID)) OR 
			(NOT StructKeyExists(local.argumentsStruct, "noteText") OR NOT len(local.argumentsStruct.noteText))>
			<cfset local.isValidArguments = false>
		</cfif>
		
		<cfif local.isValidArguments>
			<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
			<cfif local.returnStruct.userLoggedIn>
				<cfset local.objSWOD.saveEnrollmentNote(enrollmentID=local.argumentsStruct.enrollmentID, noteText=local.argumentsStruct.noteText)>
				<cfset local.returnStruct["returndata"] = 1>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="saveAndDownloadNotes" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" required="false">
		<cfargument name="noteText" required="false">
		<cfargument name="seminarname" required="false">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["returndata"] = 0>
		
		<cfset local.argumentsStruct = structnew() />
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.noteText = arguments.noteText />
			<cfset local.argumentsStruct.seminarname = arguments.seminarname />
			<cfset local.argumentsStruct.enrollmentID = arguments.enrollmentID />
		</cfif>
		
		<!--- enrollmentID and noteText are really required, but setting them as required above triggers exceptions if not provided by direct link. --->
		<cfset local.isValidArguments = true>
		<cfif 
			(NOT StructKeyExists(local.argumentsStruct, "enrollmentID") OR NOT IsNumeric(local.argumentsStruct.enrollmentID)) OR 
			(NOT StructKeyExists(local.argumentsStruct, "noteText") OR NOT len(local.argumentsStruct.noteText))>
			<cfset local.isValidArguments = false>
		</cfif>
		
		<cfif local.isValidArguments>
			<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
			<cfif local.returnStruct.userLoggedIn>
				<cfset local.objSWOD.saveEnrollmentNote(enrollmentID=local.argumentsStruct.enrollmentID, noteText=local.argumentsStruct.noteText)>
				<cfsavecontent variable="local.noteContent">
					<cfoutput>
						<div style="padding:30px;">
							<div style="font-weight:bold;padding-bottom:3px;margin-bottom:20px;border-bottom:1px solid ##000;">My Notes for #local.argumentsStruct.seminarname#</div>
							<div>#local.argumentsStruct.noteText#</div>
						</div>
					</cfoutput>
				</cfsavecontent>
				<!--- create temp directory for pdf --->
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='sw')>
				<!--- create pdf --->
				<cfset generateNotePDF(local.noteContent,"#local.strFolder.folderPath#/note_#local.argumentsStruct.enrollmentID#.pdf")>
				<cffile action="COPY" source="#local.strFolder.folderPath#/note_#local.argumentsStruct.enrollmentID#.pdf" destination="#local.strFolder.folderPath#/sw_11#local.argumentsStruct.enrollmentID#.pdf">
				<cfset local.returnStruct["returndata"] = 1>
				<cfset local.returnStruct["pdfPath"] = "#local.strFolder.folderPath#/sw_11#local.argumentsStruct.enrollmentID#.pdf">
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="sendNoteEmail" access="remote" returntype="struct" output="no">
		<cfargument name="fromEmail" type="string" required="false">
		<cfargument name="toEmail" type="string" required="false">
		<cfargument name="seminarName" type="string" required="false">
		<cfargument name="noteText" type="string" required="false">
		<cfargument name="enrollmentID" type="string" required="false">
		<cfargument name="signUpOrgCode" type="string" required="false">
		
		<cfset var local = StructNew()>		

		<cfset local.emailStruct = structNew()>
		
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["success"] = 0>
		
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.fromEmail = arguments.fromEmail />
			<cfset local.argumentsStruct.toEmail = arguments.toEmail />
			<cfset local.argumentsStruct.seminarName = arguments.seminarName />
			<cfset local.argumentsStruct.noteText = arguments.noteText />
			<cfset local.argumentsStruct.signUpOrgCode = arguments.signUpOrgCode />
			<cfset local.argumentsStruct.enrollmentID = int(val(arguments.enrollmentID))>
		</cfif>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(enrollmentID=local.argumentsStruct.enrollmentID)>
		
		<cfif local.returnStruct.userLoggedIn and isValid("email",local.argumentsStruct.fromEmail) and isValid("email",local.argumentsStruct.toEmail)>			
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryEnrollment.signUpOrgCode)>
			<cfset local.memberID = val(local.qryEnrollment.MCMemberID)>
			<cfif NOT local.memberID>
				<cfset local.memberID = local.mc_siteInfo.sysMemberID>
			</cfif>
			
			<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.argumentsStruct.signUpOrgCode).qryAssociation>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.argumentsStruct.signUpOrgCode)>

			<cfsavecontent variable="local.emailStruct.emailTitle">
				<cfoutput>
				<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:3px;text-align:center;"><i>#local.mc_siteInfo.siteName#</i></div>
				<div style="text-align:center;">#encodeForHTML(local.argumentsStruct.seminarName)#</div>
				</cfoutput>
			</cfsavecontent>
			<cfset local.emailStruct.html = local.argumentsStruct.noteText>
			
			<cfset local.emailStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailStruct.emailTitle, emailContent=local.emailStruct.html, sitecode=local.argumentsStruct.signUpOrgCode)>
				
			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(replace(local.argumentsStruct.toEmail,",",";","all"),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				
				if (arrayLen(local.arrEmailTo)) {
					local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryAssociation.emailFrom, email='<EMAIL>' },
						emailto=local.arrEmailTo,
						emailreplyto=local.argumentsStruct.fromEmail,
						emailsubject="My Notes: #local.argumentsStruct.seminarName#",
						emailtitle=local.emailStruct.emailTitle,
						emailhtmlcontent=local.emailStruct.html,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWODNOTES"),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					);
				}
			</cfscript>
			<cfset local.returnStruct["success"] = 1>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<!--- ************* --->
	<!--- Q/A functions --->
	<!--- ************* --->
	<cffunction name="getQuestionAndAnswers" access="remote" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfscript>
			var local = StructNew();
			local.returnStuct = structnew();
			local.rsrd =  structnew();
			try {
				local.returnStruct["userLoggedIn"] = isLoggedIn();
				local.SWODQAObj = createObject("component","model.seminarweb.SWODQA");
		
				if (local.returnStruct.userLoggedIn) 
					local.returnStruct["returnData"] = local.SWODQAObj.getQuestionTreeJSON(arguments.seminarID);	
			} catch (any e) {			
				structInsert(local.rsrd,"loadSuccess",0,true);
				local.arguments = arguments;
				reportErrorToAdmin("Error in SWODPlayerHTML5.getQuestionAndAnswers() for seminarID: #arguments.seminarID#",e,local);
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="addQuestion" access="remote" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="message" type="string" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.SWODQAObj = createObject("component","model.seminarweb.SWODQA")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID)>
		<cfif local.returnStruct.userLoggedIn>
			<cfset local.addQuestion = local.SWODQAObj.addQuestion(arguments.seminarID,local.qryEnrollment.depomemberdataid,arguments.subject,arguments.message)>
			<cfif local.addQuestion>
				<cfset local.returnStruct["returnData"] = "true">
			<cfelse>
				<cfset local.returnStruct["returnData"] = "false">
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="loadFormXML" access="private" returntype="xml" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">		

		<cfset var local = StructNew()>
		
		<cfquery name="local.qryGetSFID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT TOP 1 saf.seminarFormID, saf.includeQuestions, saf.maxminutesallowed, 
				isnull(saf.certifiedstatement,'') as certifiedstatement, saf.maxtriesperquestion,  
				saf.allowskipbackward, saf.enforceqreqstatuscorrectmaxtries, 
				saf.showimmediateanswer
			FROM dbo.tblSeminarsAndForms as saf 
			INNER JOIN dbo.tblEnrollments as e ON saf.seminarID = e.seminarID
			WHERE saf.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
			AND saf.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="CF_SQL_VARCHAR">
			AND e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryGetSFID.recordcount>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(arguments.enrollmentID)>

			<cfscript>
			// get form xml from FormBuilder
			local.strArgs = StructNew();
			local.strArgs.FBFormid = arguments.formID;
			local.strArgs.FBAction = "displayFormXML";
			local.strArgs.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.FBEnrollmentID = local.qryEnrollment.enrollmentID;
			local.strArgs.insertEmptyResponse = true;
			local.strArgs.emptyResponseInfo = StructNew();
			local.strArgs.emptyResponseInfo.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.emptyResponseInfo.FBorgcode = local.qryEnrollment.orgcode;
			local.strArgs.paging = 1;
			local.strArgs.includeQuestions = local.qryGetSFID.includeQuestions;
			local.strArgs.loadPoint = arguments.loadPoint;

			local.objForm = CreateObject("component","model.formBuilder.FBForms");
			local.xmlForm = local.objForm.doAction(FBAction='displayFormXML',strArgs=local.strArgs);

			// append seminarweb settings
			structInsert(local.xmlForm.form.xmlAttributes,'maxminutesallowed',local.qryGetSFID.maxminutesallowed);
			structInsert(local.xmlForm.form.xmlAttributes,'certifiedstatement',local.qryGetSFID.certifiedstatement);
			structInsert(local.xmlForm.form.xmlAttributes,'maxtriesperquestion',local.qryGetSFID.maxtriesperquestion);
			structInsert(local.xmlForm.form.xmlAttributes,'allowskipbackward',local.qryGetSFID.allowskipbackward);
			structInsert(local.xmlForm.form.xmlAttributes,'enforceqreqstatuscorrectmaxtries',local.qryGetSFID.enforceqreqstatuscorrectmaxtries);
			structInsert(local.xmlForm.form.xmlAttributes,'showimmediateanswer',local.qryGetSFID.showimmediateanswer);
			</cfscript>

			<cfquery name="local.qrySaveResponse" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				INSERT INTO dbo.tblSeminarsAndFormResponses (seminarFormID, enrollmentID, responseID)
				VALUES (
					<cfqueryparam value="#local.qryGetSFID.seminarFormID#" cfsqltype="CF_SQL_INTEGER">, 
					<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">, 
					<cfqueryparam value="#local.xmlForm.form.xmlattributes.responseid#" cfsqltype="CF_SQL_INTEGER">
				)
			</cfquery>
			<cfif arguments.loadPoint eq 'evaluation'>
				<cfquery name="local.qryFormsAttended" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET XACT_ABORT, NOCOUNT ON;

					SELECT form.formid
					FROM dbo.tblEnrollments AS e 
					INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
						AND form.loadPoint = 'evaluation'				
					INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid			
						AND f.isPublished = 1 and f.isDeleted = 0
					INNER JOIN  dbo.tblSeminarsAndFormResponses as safr on safr.enrollmentID = e.enrollmentID
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.formID = form.formid	
					WHERE e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
					  AND r.isActive = 1 AND r.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="cf_sql_integer">
					ORDER BY form.orderBy ASC
					
				</cfquery>
				<cfif local.qryFormsAttended.recordcount gt 0>
					<cfset structInsert(local.xmlForm.form.xmlAttributes,'formAttended',1)>
				<cfelse>
					<cfset structInsert(local.xmlForm.form.xmlAttributes,'formAttended',0)>
				</cfif>
				
			</cfif>

			<cfreturn local.xmlForm>
		<cfelse>
			<cfreturn xmlParse("<form/>")>
		</cfif>
	</cffunction>
	
	<cffunction name="loadFormJSon" access="private" returntype="Struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		<cfargument name="formID" type="numeric" required="yes">		

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		
		<cfquery name="local.qryGetSFID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT TOP 1 saf.seminarFormID, saf.includeQuestions, saf.maxminutesallowed, 
				isnull(saf.certifiedstatement,'') as certifiedstatement, saf.maxtriesperquestion,  
				saf.allowskipbackward, saf.enforceqreqstatuscorrectmaxtries, 
				saf.showimmediateanswer, f.passingPct, saf.numResponsesPerEnrollment,
				dbo.sw_getNumFormAttemptsRemaining(<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">,saf.seminarFormID) as attemptsRemaining
			FROM dbo.tblSeminarsAndForms as saf 
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = saf.formid	
			WHERE saf.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
			AND saf.loadPoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>
		
		<cfif local.qryGetSFID.recordcount>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(arguments.enrollmentID)>

			<cfscript>
			// get form xml from FormBuilder
			local.strArgs = StructNew();
			local.strArgs.FBFormid = arguments.formID;
			local.strArgs.FBAction = "displayFormXML";
			local.strArgs.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.FBEnrollmentID = local.qryEnrollment.enrollmentID;
			local.strArgs.insertEmptyResponse = true;
			local.strArgs.emptyResponseInfo = StructNew();
			local.strArgs.emptyResponseInfo.FBdepoID = local.qryEnrollment.depomemberdataid;
			local.strArgs.emptyResponseInfo.FBorgcode = local.qryEnrollment.orgcode;
			local.strArgs.paging = 1;
			local.strArgs.includeQuestions = local.qryGetSFID.includeQuestions;
			local.strArgs.loadPoint = arguments.loadPoint;

			// append seminarweb settings
			structInsert(local.returnStruct,'maxminutesallowed',local.qryGetSFID.maxminutesallowed);
			structInsert(local.returnStruct,'certifiedstatement',local.qryGetSFID.certifiedstatement);
			structInsert(local.returnStruct,'maxtriesperquestion',local.qryGetSFID.maxtriesperquestion);
			structInsert(local.returnStruct,'allowskipbackward',local.qryGetSFID.allowskipbackward);
			structInsert(local.returnStruct,'enforceqreqstatuscorrectmaxtries',local.qryGetSFID.enforceqreqstatuscorrectmaxtries);
			structInsert(local.returnStruct,'showimmediateanswer',local.qryGetSFID.showimmediateanswer);
			structInsert(local.returnStruct,'passingPct',local.qryGetSFID.passingPct);
			structInsert(local.returnStruct,'numResponsesPerEnrollment',local.qryGetSFID.numResponsesPerEnrollment);
			structInsert(local.returnStruct,'attemptsRemaining',local.qryGetSFID.attemptsRemaining);
			</cfscript>

			<cfif arguments.loadPoint eq 'evaluation'>
				<cfquery name="local.qryFormsAttended" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET XACT_ABORT, NOCOUNT ON;

					SELECT form.formid
					FROM dbo.tblEnrollments AS e 
					INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
						AND form.loadPoint = 'evaluation'				
					INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid			
						AND f.isPublished = 1 and f.isDeleted = 0
					INNER JOIN  dbo.tblSeminarsAndFormResponses as safr on safr.enrollmentID = e.enrollmentID
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.formID = form.formid	
					WHERE e.enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">
					  AND r.isActive = 1 AND r.formID = <cfqueryparam value="#arguments.formID#" cfsqltype="cf_sql_integer">
					ORDER BY form.orderBy ASC
					
				</cfquery>
				<cfif local.qryFormsAttended.recordcount gt 0>
					<cfset structInsert(local.returnStruct,'formAttended',1)>
				<cfelse>
					<cfset structInsert(local.returnStruct,'formAttended',1)>
				</cfif>
				
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>		
	</cffunction>
	
	<cffunction name="saveFormResponse" access="public" returntype="struct" output="no">
		<cfargument name="json" type="string">
		
		<cfset var local = StructNew()>
		<cfset local.objForms = CreateObject("component","model.formBuilder.FBForms")>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnstruct["returnData"] = ArrayNew(1)>	
		<cfset local.returnstruct["success"] = true>
		<cfset local.argumentsStruct = structnew()>

		<cftry>
			<cfif isDefined("arguments.json") and len(arguments.json)>
				<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
			<cfelse>
				<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
			</cfif>

			<!--- 
			FB's postFormDetail function needs to called once per question. 
			We assume the SWOD player only has 1 question per page.
			Take the responseArray -- the form elements -- and make a formVars collection to pass into FB.
			If responseArray is empty, nothing was selected so there is nothing to record.
			--->
			<cfif arrayLen(local.argumentsStruct.responseArray)>
				<cfif local.argumentsStruct.responseID gt 0>
					<cfset local.thisQuestionResponse = { responseID=local.argumentsStruct.responseID }>
					<cfloop array="#local.argumentsStruct.responseArray#" index="local.thisResponse">
						<!--- if a name comes in with no value, it defaults to blank --->
						<cfif NOT StructKeyExists(local.thisResponse,"value")>
							<cfset local.thisResponse.value="">
						</cfif>
						<cfset structInsert(local.thisQuestionResponse, local.thisResponse.name, local.thisResponse.value)>
					</cfloop>
					<cftry>
						<cfset ArrayAppend(local.returnstruct.returnData,local.objForms.doAction("postFormDetail",local.thisQuestionResponse))>
					<cfcatch type="Any">
						<cfset local.arguments = arguments>
						<cfset reportErrorToAdmin(message="Error in SWODPlayerHTML5.saveFormResponse()",errorStruct=cfcatch,objectToDump=local)>
						<cfset local.returnstruct["success"] = false>
					</cfcatch>
					</cftry>				
				<cfelse>
					<cfset local.returnstruct["success"] = false>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnstruct["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateResponseID" access="private" returntype="struct" output="no">
		<cfargument name="encResponseID" type="string" required="yes">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = { responseID=0, enrollmentID=0 }>

		<cftry>
			<cfset local.decResponseID = deserializeJSON(decrypt(arguments.encResponseID, "S3mW3b_fb!MC2015", "CFMX_COMPAT", "Hex"))>
			<cfset local.returnStruct.responseID = local.decResponseID.responseID>
			<cfset local.returnStruct.enrollmentID = local.decResponseID.enrollmentID>

			<cfquery name="local.qryisValid" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SELECT top 1 safr.sfrid
				FROM dbo.tblSeminarsAndFormResponses as safr
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
				WHERE safr.enrollmentID = <cfqueryparam value="#local.decResponseID.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
				AND safr.responseID = <cfqueryparam value="#local.decResponseID.responseID#" cfsqltype="CF_SQL_INTEGER">
				AND r.isActive = 1
				AND r.dateCompleted is null
			</cfquery>
			<cfif local.qryisValid.recordcount is 0>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct.responseID = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="encryptResponseID" access="public" returntype="String" output="no">
		<cfargument name="responseID" type="string" required="yes">
		<cfreturn encrypt(serializeJSON(arguments.responseID), "S3mW3b_fb!MC2015", "CFMX_COMPAT", "Hex")>
	</cffunction>	

	<cffunction name="gradeExam" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="responseID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.objSeminarsSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objSeminarsSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnstruct["returnData"] = StructNew()>

		<cfif local.returnStruct.userLoggedIn>
			<cftry>
				<cfset local.qryExamResult = local.objSeminarsSWOD.completeExam(enrollmentid=arguments.enrollmentid,responseid=arguments.responseID)>
				<cfset structInsert(local.returnStruct.returnData,"pctcorrect",local.qryExamResult.passingPct)>
				<cfset structInsert(local.returnStruct.returnData,"passfail",local.qryExamResult.passFail)>
				<cfset structInsert(local.returnStruct.returnData,"attemptsremaining",local.qryExamResult.attemptsRemaining)>
				<cfset structInsert(local.returnStruct.returnData,"numcorrect",local.qryExamResult.numCorrect)>
				<cfset structInsert(local.returnStruct.returnData,"numgiven",local.qryExamResult.numGiven)>
			<cfcatch type="Any">
				<cfset reportErrorToAdmin(message="Error in SWODPlayerHTML5.gradeExam()",errorStruct=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>
			
			<!--- Check if this is SWLive Seminar --->
			<cftry>
				<cfset local.qryEnrollment = local.objSeminarsSWOD.getEnrollmentByEnrollmentID(enrollmentid=arguments.enrollmentid)>
				<cfstoredproc procedure="swl_getSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryEnrollment.seminarID)#" null="No">
					<cfprocresult name="local.qrySWLDetails" resultset="1">
				</cfstoredproc>
				<cfif local.qrySWLDetails.recordCount gt 0>
					<cfset local.strReturn = local.objSeminarsSWL.checkCompletion(enrollmentid=arguments.enrollmentid,passFail=local.qryExamResult.passFail)>
				</cfif>
			<cfcatch type="Any">
				<cfset reportErrorToAdmin(message="Error in SWODPlayerHTML5.gradeExam() SWLive",errorStruct=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>	

	<!--- **************** --->
	<!--- common functions --->
	<!--- **************** --->
	<cffunction name="isLoggedIn" access="private" returntype="numeric" output="no">
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
		<cfset local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.mc_siteInfo.orgID)>
		
		<cfif local.memberID gt 0>
			<cfreturn 1>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>
	
	<cffunction name="getCurrentTime" access="remote" returntype="date" output="false">
		<cfreturn now()>
	</cffunction>
	<cffunction name="getSystemData" access="remote" returntype="struct" output="false">
		
		<cfset var local = StructNew()>
		<cfset local.argumentsStruct = getHttpRequestData()>
		<cfset local.returnStruct = StructNew()>		
		<cfset local.returnstruct.success = true>
		<cfset local.returnstruct.language = ''>
		<cfset local.returnstruct.userAgent = ''>
		<cfset local.returnstruct.hostAddress = ''>
		<cfif structKeyExists(local.argumentsStruct.headers, "Accept-Language")>
			<cfset local.returnstruct.language = local.argumentsStruct.headers['Accept-Language']>
		</cfif>
		<cfif structKeyExists(local.argumentsStruct.headers, "User-Agent")>
			<cfset local.returnstruct.userAgent = local.argumentsStruct.headers['User-Agent']>	
		</cfif>
		<cfif structKeyExists(local.argumentsStruct.headers, "Host")>
			<cfset local.returnstruct.hostAddress = local.argumentsStruct.headers['Host']>
		</cfif>	
			
		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="saveSeminarStartTime" access="remote" returntype="struct" output="false">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.currentTime = getCurrentTime()>
		<cfset local.requestData = deserializeJSON(toString( getHttpRequestData().content ))>		
		<cfset local.enrollmentID = local.requestData.enrollmentid>
		
		<cftry>
			<cfquery name="local.objEnrollment"  datasource="#application.dsn.tlasites_seminarweb.dsn#">
				
				update tblEnrollments set dtInstanceStartDate =  CONVERT(VARCHAR(23), <cfqueryparam value="#local.currentTime#" cfsqltype="cf_sql_timestamp">  , 120) where enrollmentID =  <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">
				
				select convert(varchar(23), dtInstanceStartDate , 120) as dtInstanceStartDate from tblEnrollments where enrollmentID =  <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">		
				
			</cfquery>	
				
			<cfset local.returnstruct.success = true>
			<cfset local.returnstruct.savedTime = local.objEnrollment['dtInstanceStartDate']>
		<cfcatch type="Any">
			<cfset local.returnstruct.success = false>
			<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #local.enrollmentID#",cfcatch,arguments)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkSeminarInstanceExists" access="remote" returntype="struct" output="false">
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.requestData = deserializeJSON(toString(getHttpRequestData().content))>

		<!--- this is protect against bots calling this function directly with no valid request data. we dont need the exception sent to us. --->
		<cfif NOT isDefined("local.requestData.enrollmentid")>
			<cfset local.returnstruct.success = false>
		<cfelse>
			<cfset local.enrollmentID = local.requestData.enrollmentid>
			<cfset local.dateInstanceStarted = local.requestData.dateInstanceStarted>
			
			<cftry>
				<cfquery name="local.objEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					select enrollmentID 
					from dbo.tblEnrollments 
					where convert(varchar(23), dtInstanceStartDate , 120) > convert(varchar(23),<cfqueryparam value="#local.dateInstanceStarted#" cfsqltype="cf_sql_timestamp">,120) 
					AND enrollmentID = <cfqueryparam value="#local.enrollmentID#" cfsqltype="cf_sql_integer">
				</cfquery>	
				
				<cfset local.returnstruct.isExist = 0>
				<cfif local.objEnrollment.recordcount>
					<cfset local.returnstruct.isExist = 1>
				</cfif>
				
				<cfset local.returnstruct.success = true>
			<cfcatch type="Any">
				<cfset local.returnstruct.success = false>
				<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #local.enrollmentID#",cfcatch,arguments)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="resetEnrollment" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="true">
		<cfargument name="clearLogAccess" type="boolean"   required="false">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = { success = true }>
		<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(arguments.enrollmentID)>
		
		<cfquery name="local.getForms"  datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select formID from dbo.tblSeminarsAndForms where seminarID = <cfqueryparam value="#local.qryEnrollment.seminarID#" cfsqltype="cf_sql_integer">
		</cfquery>		

		<cftry>
			<cfquery name="local.qryResetEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				set nocount on				
				
				declare @enrollmentID int
				select @enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
			
				delete from formbuilder.dbo.tblResponseDetails
				where responseID in (
					select responseid from dbo.tblseminarsandformresponses where enrollmentid = @enrollmentID
				)
			
				delete from formbuilder.dbo.tblResponses 
				where responseID in (
					select responseid from dbo.tblseminarsandformresponses where enrollmentid = @enrollmentID
				)
				delete from dbo.tblseminarsandformresponses where enrollmentid = @enrollmentID
				
				<cfloop query="local.getForms">
					delete 	from 
						formBuilder.dbo.tblResponseDetails 
					where	
						responseID	in (select 
											responseID
										from 
											formBuilder.dbo.tblResponses 
										where 
											formID = <cfqueryparam value="#local.getForms.formID#" cfsqltype="cf_sql_integer">  
											and depomemberDataID = <cfqueryparam value="#local.qryEnrollment.depomemberDataID#" cfsqltype="cf_sql_integer">
											and isActive = 1)
					
					delete 	from 
						formBuilder.dbo.tblResponses 
					where 
						formID = <cfqueryparam value="#local.getForms.formID#" cfsqltype="cf_sql_integer">  
						and depomemberDataID = <cfqueryparam value="#local.qryEnrollment.depomemberDataID#" cfsqltype="cf_sql_integer">
						and isActive = 1					
				</cfloop>				
			
				update dbo.tblEnrollments set dateCompleted = null, passed = 0 where enrollmentid = @enrollmentID
				update dbo.tblEnrollmentsSWOD set calcTimeSpent = null where enrollmentID = @enrollmentID
				
				<cfif isDefined("arguments.clearLogAccess") and arguments.clearLogAccess>
					declare @fileTbl as table (fileid int)
					declare @logAccessTbl as table (logAccessID int)
					
					insert into @fileTbl
					select distinct fileID
					from (
						SELECT 
							sat.titleID, taf.fileID, f.filetypeID, fd.bitString, fd.bitStringLen, t.titleName, f.fileTitle
						FROM 
							dbo.tblEnrollments AS e 
						INNER JOIN dbo.tblSeminarsAndTitles AS sat ON e.seminarID = sat.seminarID 
						INNER JOIN dbo.tblTitlesAndFiles AS taf ON sat.titleID = taf.titleID
						INNER JOIN dbo.tblFiles AS f ON f.fileID = taf.fileID
						INNER JOIN dbo.tblTitles as t on t.titleID = sat.titleID
						LEFT OUTER JOIN (
							SELECT laf.fileID, laf.accessDetails AS bitString, len(laf.accessDetails) as bitStringLen
							FROM dbo.tblLogAccessSWODFiles AS laf 
							INNER JOIN dbo.tblLogAccessSWOD AS la ON laf.logAccessID = la.logAccessID
							WHERE la.enrollmentID = @enrollmentID
							AND laf.accessDetails IS NOT NULL
							AND LEN(laf.accessDetails) > 0
						) as fd on fd.fileID = taf.fileID
						WHERE e.enrollmentID = @enrollmentID
						AND taf.isSupportingDoc = 0
						AND t.isDeleted = 0
						AND f.isDeleted = 0
					) temp					
					
					insert into @logAccessTbl
					select
						distinct logAccessID
					from (
						SELECT laf.fileID, laf.accessDetails AS bitString, len(laf.accessDetails) as bitStringLen, la.logAccessID
						FROM dbo.tblLogAccessSWODFiles AS laf 
						INNER JOIN dbo.tblLogAccessSWOD AS la ON laf.logAccessID = la.logAccessID
						inner join @fileTbl f on f.fileID = laf.fileID
						WHERE la.enrollmentID = @enrollmentID
						AND laf.accessDetails IS NOT NULL
						AND LEN(laf.accessDetails) > 0
					) temp2
					
					delete from 
						dbo.tblLogAccessSWODFiles
					where
						logAccessID in (SELECT 
											la.logAccessID
										FROM 
											dbo.tblLogAccessSWODFiles AS laf 
											INNER JOIN @logAccessTbl AS la ON 
												laf.logAccessID = la.logAccessID)
					
					delete from
						tblLogAccessSWOD
					where
						logAccessID in (SELECT 
											la.logAccessID
										FROM 
											dbo.tblLogAccessSWOD AS a
											INNER JOIN @logAccessTbl AS la ON 
												la.logAccessID = a.logAccessID)
				</cfif>
				
				set nocount off
			</cfquery>
		<cfcatch type="Any">
			<cfset local.returnstruct.success = false>
			<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #arguments.enrollmentID#",cfcatch,arguments)>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="reportError" access="remote" returntype="boolean" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="errorStruct" type="any" required="yes" hint="changed to any to support retro players still sending in strings">

		<cftry>
			<cfthrow message="Error in SWOD player with enrollmentID #arguments.enrollmentID# at #now()#">
			<cfcatch type="any">
				<cfset reportErrorToAdmin("Error reported from SWOD Player for Enrollment ID: #arguments.enrollmentID#",cfcatch,arguments)>
			</cfcatch>
		</cftry>

		<cfreturn true>
	</cffunction>

	<cffunction name="reportErrorToAdmin" access="private" returntype="boolean" output="no">
		<cfargument name="message" type="string" required="Yes">
		<cfargument name="errorStruct" type="any" required="yes" hint="changed to any to support retro players still sending in strings">
		<cfargument name="objectToDump" type="struct" required="no">
		
		<cfset var local = structNew()>
		
		<cfif isdefined("arguments.objectToDump") and isStruct(arguments.objectToDump) and structCount(arguments.objectToDump)>
			<cfif (isDefined("arguments.objectToDump.ERRORSTRUCT.message") and FindNoCase("All Streaming server ports and protocols failed.",arguments.objectToDump.ERRORSTRUCT.message))
				OR (isDefined("arguments.objectToDump.ERRORSTRUCT.faultDetail") and FindNoCase("NetConnection.Call.Failed: HTTP: Failed",arguments.objectToDump.ERRORSTRUCT.faultDetail))>
				<!--- do nothing. supress error message. Nothing will be done about it. --->
			<cfelse>
				<cfset application.objError.sendError(cfcatch=arguments.errorStruct, customMessage=arguments.message, domain="seminarweb", objectToDump=arguments.objectToDump )>
			</cfif>
		<cfelse>
			<cfset application.objError.sendError(cfcatch=arguments.errorStruct, customMessage=arguments.message, domain="seminarweb" )>
		</cfif>

		<cfreturn true>
	</cffunction>

	<cffunction name="generateTimeLengthString" access="private" returntype="String" output="no">
		<cfargument name="minutes" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfset local.numhours = round(arguments.minutes) \ 60>
		<cfset local.numminutes = round(arguments.minutes) mod 60>
		
		<cfset local.returnString = "">
		<cfif local.numhours is 1>
			<cfset local.returnString = "#local.numhours# Hour">
		<cfelseif local.numhours gt 1>
			<cfset local.returnString = "#local.numhours# Hours">
		</cfif>
		<cfif local.numminutes is 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numminutes# Minute">
		<cfelseif local.numminutes is 1>
			<cfset local.returnString = local.returnString & "#local.numminutes# Minute">
		<cfelseif local.numminutes gt 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numminutes# Minutes">
		<cfelseif local.numminutes gt 1>
			<cfset local.returnString = local.returnString & "#local.numminutes# Minutes">
		</cfif>

		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="saveProgressFromPlayer" access="remote" returntype="struct" output="no">
		<cfargument name="json" type="string" />

		<cfset var local = StructNew()>
		<cfset local.CRLF = Chr(13) & chr(10)>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.argumentsStruct = structnew() />
		<cfset local.argumentsStruct = deserializeJSON(arguments.json) />		

		<cftry>
			<cfif local.argumentsStruct.sendReminderEmailFlag and isLoggedIn()>
				<cfset sendReminderEmail(local.argumentsStruct.logAccessID,local.argumentsStruct.includeLoginDetails)>
			</cfif>

			<cfif local.returnStruct.userLoggedIn>
				<cfif arraylen(local.argumentsStruct.progressObj.fileArray)>
					<cfloop index="local.currentfile" from="1" to="#arraylen(local.argumentsStruct.progressObj.fileArray)#">
						<cfstoredproc procedure="swod_updateLogAccessSWODFiles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].timespent? : 0#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].lasttimecode? : 0#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].accessDetails? : ''#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.fileArray[local.currentfile].fileLogAccessID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.logAccessID#">
						</cfstoredproc>
					</cfloop>
				</cfif>

				<cfset local.activityLogText = createObject("java","java.lang.StringBuffer")>
				<cfif arraylen(local.argumentsStruct.progressObj.activityLogArray) gt 0>
					<cfloop index="local.currentrow" from="1" to="#arraylen(local.argumentsStruct.progressObj.activityLogArray)#">
						<cfif isSimpleValue(local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["dateentered"]) and isSimpleValue(local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["message"])>
							<cfset local.activityLogText.append(local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["dateentered"] & chr(9) & local.argumentsStruct.progressObj.activityLogArray[local.currentrow]["message"] & local.CRLF)>
						</cfif>
					</cfloop>
				</cfif>

				<cfset local.debugLogText = createObject("java","java.lang.StringBuffer")>
				<cfif arraylen(local.argumentsStruct.progressObj.debugLogArray) gt 0>
					<cfloop index="local.currentrow" from="1" to="#arraylen(local.argumentsStruct.progressObj.debugLogArray)#">
						<cfif isSimpleValue(local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["dateentered"]) and isSimpleValue(local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["message"])>
							<cfset local.debugLogText.append(local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["dateentered"] & chr(9) & local.argumentsStruct.progressObj.debugLogArray[local.currentrow]["message"] & local.CRLF)>
						</cfif>
					</cfloop>
				</cfif>
	
				<cfstoredproc procedure="swod_updateLogAccessSWOD" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.timespent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.activityLogText.toString()#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.debugLogText.toString()#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.logAccessID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.argumentsStruct.progressObj.enrollmentID#">
				</cfstoredproc>
	
				<cfset local.returnStruct["returnData"] = 1>
			</cfif>
		<cfcatch type="any">
			<cfif isDefined("local.argumentsStruct.progressObj.enrollmentID")>
				<cfset reportErrorToAdmin("Error in SWODPlayerHTML5.saveProgressFromPlayer() for EnrollmentID: #local.argumentsStruct.progressObj.enrollmentID#",cfcatch,local)>
			</cfif>
			<cfset local.returnStruct["returnData"] = 0>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFileForMediaPlayer" access="remote" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="Yes">
		<cfargument name="fileID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfscript>
		// init structures
		local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles");
		local["returnStruct"] = structNew();
		local["returnStruct"]["returnData"] = structNew();

		// load application variables
		
		local["returnStruct"]["userLoggedIn"] = isLoggedIn();
		
		if (local.returnStruct.userLoggedIn) {
			// set to false and overwrite with true upon completion
			local["returnStruct"]["returnData"]["loadSuccess"] = false;
	
			// load file info
			local.qryFile = local.objSWFiles.getFile(arguments.fileid);
			local["returnStruct"]["returnData"]["recordCount"] = local.qryFile.recordCount;
			if (local.qryFile.recordCount gt 0)
			{
				local["returnStruct"]["returnData"]["fileIDFound"] = 1;
				local["returnStruct"]["returnData"]["orgcode"] = local.qryFile.orgcode;
				local["returnStruct"]["returnData"]["participantID"] = local.qryFile.participantID;
				local["returnStruct"]["returnData"]["fileTitle"] = local.qryFile.fileTitle;
				local["returnStruct"]["returnData"]["type"] = local.qryFile.fileType;
				if (local.qryFile.fileType eq "video")
				{
					local["returnStruct"]["returnData"]["fileURL"] = "swod/" & lcase(local.qryFile.orgcode) & "/" & local.qryFile.participantID & "/" & local.qryFile.fileID;
					local["returnStruct"]["returnData"]["viewerFileAvailable"] = 1;
				}
				else if (local.qryFile.fileType eq "audio")
				{
					local["returnStruct"]["returnData"]["fileURL"] = "mp3:swod/" & lcase(local.qryFile.orgcode) & "/" & local.qryFile.participantID & "/" & local.qryFile.fileID;
					local["returnStruct"]["returnData"]["viewerFileAvailable"] = 1;
				}
			}
			else
			{
				local["returnStruct"]["returnData"]["fileIDFound"] = 0;
			}
			// set to true upon completion
			
		}
		local["returnStruct"]["returnData"]["loadSuccess"] = true;
		</cfscript>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getURLForMediaPlayer" access="remote" returntype="struct" output="no">
		<cfargument name="fileID" type="numeric" required="true">
		<cfargument name="fileURL" type="string" required="true" >
		<cfargument name="mediaType" type="string" required="true">
		<cfargument name="createdTimeStamp" type="date" required="no" default="#now()#">
		<cfargument name="enrollmentID" type="numeric" required="no" default="0">
		
		<cfset var local = StructNew()>	
		<cfset local.returnStruct = StructNew()>	
			
		<cfset local.mediaTypeExt = "mp4">
		<cfif lCase(arguments.mediaType) eq "audio">
			<cfset local.mediaTypeExt = "mp3">
		</cfif>
		<cfset local.s3bucket = "seminarweb">
		<cfset local.s3objectKey = arguments.fileURL & "." & local.mediaTypeExt>
		<cfset local.s3requesttype = "ssl">
		<cfset local.forceDownload = 0>
		<cfset local.expireInMinutes =  240>
		<cfset local.returnStruct.mediaURL = "">
		
		<cfif NOT (DateDiff("n",arguments.createdTimeStamp, now()) gt 15)>
			<!--- if s3 info provided, try to get file from there. --->
			<cfif len(local.s3objectKey)>
				<cfif application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.s3objectKey, requestType=local.s3requesttype)>
					<cfset local.returnStruct.mediaURL = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.s3objectKey, requestType=local.s3requesttype, expireInMinutes=local.expireInMinutes)>
					<cfset local.returnStruct.mediaURL = local.returnStruct.mediaURL & "&enrollmentID=" & arguments.enrollmentID>
				</cfif>
			</cfif>		
		</cfif>		
		<cfreturn local.returnStruct>
	</cffunction>	
	<cffunction name="completeSeminarAfterEvaluation" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="responseid" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset structInsert(local.returnStruct,"userLoggedIn",isLoggedIn())>

		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID)>

		<cfif (local.returnStruct.userLoggedIn)>
			<cfquery name="local.qrySaveResponse" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				DECLARE @seminarFormID int;

				SELECT top 1 @seminarFormID = seminarFormID from dbo.tblSeminarsAndForms 				
				WHERE formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
				AND seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">;

				INSERT INTO dbo.tblSeminarsAndFormResponses (seminarFormID, enrollmentID, responseID)
				VALUES (
					@seminarFormID, 
					<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">, 
					<cfqueryparam value="#arguments.responseid#" cfsqltype="CF_SQL_INTEGER">
				)
			</cfquery>

			<!--- check for incomplete exams and complete them for this enrollmentID --->
			<cfset CreateObject("component","model.seminarweb.SWODSeminars").completeIncompleteExams(enrollmentid=arguments.enrollmentID, loadPoint='postTest',recordedByMemberID=local.qryEnrollment.MCMemberID)>

			<cfstoredproc procedure="swod_getSeminarProgress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
				<cfprocresult name="local.qrySeminarSettings" resultset="1">
				<cfprocresult name="local.qrySeminarProgress" resultset="2">
				<cfprocresult name="local.qryCreditsPassed" resultset="3">
				<cfprocresult name="local.qryFiles" resultset="4">
				<cfprocresult name="local.qryTimeSpent" resultset="5">
			</cfstoredproc>

			<cfset local.seminarLoadPointsObj = getSeminarLoadPointsObj(seminarID=arguments.seminarID)>

			<cfif local.seminarLoadPointsObj.semninarHasPreTest EQ 1>
				<cfset local.allPreTestCompleted = local.qrySeminarProgress.allPreTestCompleted EQ 1>
			<cfelse>
				<cfset local.allPreTestCompleted = true>
			</cfif>

			<cfif local.seminarLoadPointsObj.semninarHasPostTest EQ 1>
				<cfset local.allPostTestCompleted = local.qrySeminarProgress.allPostTestCompleted EQ 1>
			<cfelse>
				<cfset local.allPostTestCompleted = true>
			</cfif>

			<cfif local.seminarLoadPointsObj.semninarHasEvaluation EQ 1>
				<cfset local.allEvaluationCompleted = local.qrySeminarProgress.allEvaluationCompleted EQ 1>
			<cfelse>
				<cfset local.allEvaluationCompleted = true>
			</cfif>

			<cfif local.allPreTestCompleted AND local.allPostTestCompleted AND local.allEvaluationCompleted>
				<!--- convert totalTimeSpent from seconds to minutes --->
				<cfset local.realTimeSpent = round(val(local.qryTimeSpent.totalTimeSpent) / 60)>

				<cftry>
					<cfstoredproc procedure="swod_recordCompletion" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.realTimeSpent#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.realTimeSpent#" null="No">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollment.MCMemberID#" null="No">
					</cfstoredproc>
				<cfcatch type="any">
					<cfset local.data.success = false>
				</cfcatch>
				</cftry>

				<cfset local.data.success = true>
			<cfelse>
				<cfset local.data.success = false>
			</cfif>

			<cfreturn local.data>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="saveEvaluationResponse" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="responseid" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structnew()>
		<cfset structInsert(local.returnStruct,"userLoggedIn",isLoggedIn())>
		<cfset local.returnStruct.success = false>
		<cfif (local.returnStruct.userLoggedIn)>
			<cfquery name="local.qrySaveResponse" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				DECLARE @seminarFormID int;

				SELECT top 1 @seminarFormID = seminarFormID from dbo.tblSeminarsAndForms 				
				WHERE formID = <cfqueryparam value="#arguments.formID#" cfsqltype="CF_SQL_INTEGER">
				AND seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">;

				INSERT INTO dbo.tblSeminarsAndFormResponses (seminarFormID, enrollmentID, responseID)
				VALUES (
					@seminarFormID, 
					<cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">, 
					<cfqueryparam value="#arguments.responseid#" cfsqltype="CF_SQL_INTEGER">
				)
			</cfquery>
			<cfset local.returnStruct.success = true>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="emailCertificate" access="remote" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="emailToUse" type="string" required="yes">
		<cfargument name="signUpOrgCode" type="string" required="yes">	
		
		<cfset var local = StructNew()>		
		<cfset local.emailStruct = structNew()>
		
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["success"] = 0>
		
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(arguments.enrollmentID)>
		<cfif isValid("email",arguments.emailToUse) AND arguments.enrollmentID > 0>
			<cftry>
				<cfset CreateObject("component","model.seminarweb.SWCertificates").sendCertificateByEmail(enrollmentID=arguments.enrollmentID, 
				performedBy=local.qryEnrollment.depomemberdataid, outgoingType="manualCertificate", emailToUse=arguments.emailToUse)>
				<cfset local.returnStruct["success"] = 1>
			<cfcatch type="any">
				<cfset local.returnStruct["success"] = 0>
			</cfcatch>
			</cftry>			
		<cfelse>
			<cfset local.returnStruct["success"] = 0>
		</cfif>	

		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="getDepoMemberDataIDByMemberId" access="private" returntype="query" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		<cfquery name="local.qryGetTLASITESDepoMemberDataIDByMemberID" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			declare 
			  @depomemberdataid int, 
			  @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">, 
			  @memberID int = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			
			EXEC memberCentral.dbo.ams_getTLASITESDepoMemberDataIDByMemberID @memberID=@memberID, @siteID=@siteID, @depomemberdataid=@depoMemberDataID OUTPUT;
			select @depomemberdataID as depomemberdataID
		</cfquery>

		<cfreturn local.qryGetTLASITESDepoMemberDataIDByMemberID>
	</cffunction>
	<cffunction name="generateNotePDF" access="private" returntype="void" output="no">
		<cfargument name="pdfTextIn" type="string" required="yes">
		<cfargument name="filepath" type="string" required="yes">
		
		<cfset var local = structnew()>
		
		<cfif findNoCase("landscape {page: land;}", arguments.pdfTextIn) eq 0>
			<cfdocument filename="#arguments.filepath#" pagetype="letter" margintop="0" marginbottom="0" marginright="0" format="PDF" marginleft="0" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>#arguments.pdfTextIn#</cfoutput>
			</cfdocument>		
		<cfelse>
			<cfdocument filename="#arguments.filepath#" pagetype="letter" margintop="0" marginbottom="0" marginright="0" format="PDF" marginleft="0" backgroundvisible="Yes" orientation="landscape" unit="in" fontembed="Yes" scale="100">
			<cfoutput>#arguments.pdfTextIn#</cfoutput>
			</cfdocument>				
		</cfif>
	</cffunction>

	<cffunction name="emailMaterials" access="remote" returntype="struct" output="no">
		<cfargument name="json" type="string" />
		
		<cfset var local = StructNew()>		

		<cfset local.emailStruct = structNew()>
		
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["userLoggedIn"] = isLoggedIn()>
		<cfset local.returnStruct["success"] = 0>
		
		<cfif isDefined("arguments.json")>
			<cfset local.argumentsStruct = deserializeJSON(arguments.json)>
		<cfelseif structKeyExists(getHttpRequestData(),"content") AND getHttpRequestData().content NEQ ''>
			<cfset local.argumentsStruct = deserializeJSON(deserializeJSON(getHttpRequestData().content).params.json)>
		<cfelse>
			<cfset local.argumentsStruct.emailToUse = arguments.emailToUse />
			<cfset local.argumentsStruct.signUpOrgCode = arguments.signUpOrgCode />
			<cfset local.argumentsStruct.seminarName = arguments.seminarName />
			<cfset local.argumentsStruct.enrollmentID = int(val(arguments.enrollmentID))>
			<cfset local.argumentsStruct.fromEmail = arguments.supportEmail>
			<cfset local.argumentsStruct.materialListEmail = arguments.materialListEmail>
			<cfset local.argumentsStruct.materialsOthersEmail = arguments.materialsOthersEmail>
		</cfif>
		<cfset local.qryEnrollment = local.objSWOD.getEnrollmentByEnrollmentID(enrollmentID=local.argumentsStruct.enrollmentID)>
		
		<cfif local.returnStruct.userLoggedIn and isValid("email",local.argumentsStruct.fromEmail) and isValid("email",local.argumentsStruct.emailToUse)>			
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryEnrollment.signUpOrgCode)>
			<cfset local.memberID = val(local.qryEnrollment.MCMemberID)>
			<cfif NOT local.memberID>
				<cfset local.memberID = local.mc_siteInfo.sysMemberID>
			</cfif>
			
			<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.argumentsStruct.signUpOrgCode).qryAssociation>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.argumentsStruct.signUpOrgCode)>
			<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=local.memberID)>
			<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.argumentsStruct.signUpOrgCode, membernumber=local.memberInfo.membernumber)>
			<cfsavecontent variable="local.emailStruct.emailTitle">
				<cfoutput>
				<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:3px;text-align:center;"><i>#local.mc_siteInfo.siteName#</i></div>
				<div style="text-align:center;">Program Materials for #encodeForHTML(local.argumentsStruct.seminarName)#</div>
				</cfoutput>
			</cfsavecontent>			

			<cfsavecontent variable="local.emailStruct.html">
				<cfoutput>
					<cfset local.prevTitleId = 0>
					<cfloop index="local.x" from="1" to="#arraylen(local.argumentsStruct.materialListEmail)#">
						<cfset local.eachMaterialObj = local.argumentsStruct.materialListEmail[local.x]>
						<cfset local.LinkToProgram = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname##local.eachMaterialObj.downloadURL#&mk=#local.memberKey#">
			
						<cfif local.prevTitleId neq local.eachMaterialObj.titleId>
							<div style="font-weight:bold;font-size:16px;padding-bottom:20px;">#local.eachMaterialObj.titleName#</div>
						</cfif>
						
						<cfset local.prevTitleId = local.eachMaterialObj.titleId>
						<div style="font-size:14px;padding-left:5px;margin-bottom:20px;"><a  style="font-size: 14px;  padding: 5px 5px; background-color: ##808080; color: ##fff; border-radius: 5px; text-decoration: none;" href="#local.LinkToProgram#">Download</a> &nbsp;#local.eachMaterialObj.downloadTitleName#</div>
					</cfloop>
					<cfloop index="local.x" from="1" to="#arraylen(local.argumentsStruct.materialsOthersEmail)#">
						<cfset local.eachMaterialOthersObj = local.argumentsStruct.materialsOthersEmail[local.x]>
						<cfif local.x eq 1>
							<div style="font-weight:bold;font-size:16px;padding-bottom:20px;margin-top:8px;">Links to Related Web Content</div>
						</cfif>
						
						<div style="font-size:14px;padding-left:0px;margin-bottom:10px;"><a  style="text-decoration:underline;font-size: 14px;color:##000; padding: 5px 5px;" href="#local.eachMaterialOthersObj.linkURL#">#local.eachMaterialOthersObj.linkName#</a></div>
						<div style="font-size:14px;padding-left:0px;margin-bottom:20px;">#local.eachMaterialOthersObj.linkDesc#</div>
					</cfloop>
					<div style="text-align:center;font-weight:bold;font-size:14px;font-family:sans-serif;color:##c9372c;">Do not forward email, as it may cause your download links to break.</div>
				</cfoutput>
			</cfsavecontent>
			
			<cfset local.emailStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailStruct.emailTitle, emailContent=local.emailStruct.html, sitecode=local.argumentsStruct.signUpOrgCode)>
				
			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(replace(local.argumentsStruct.emailToUse,",",";","all"),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				
				if (arrayLen(local.arrEmailTo)) {
					local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryAssociation.emailFrom, email='<EMAIL>' },
						emailto=local.arrEmailTo,
						emailreplyto=local.argumentsStruct.fromEmail,
						emailsubject="Program Materials: #local.argumentsStruct.seminarName#",
						emailtitle=local.emailStruct.emailTitle,
						emailhtmlcontent=local.emailStruct.html,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWODMATERIALS"),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					);
				}
			</cfscript>
			<cfset local.returnStruct["success"] = 1>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getCustomFailMessage" access="private" returntype="any" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="loadPoint" type="string" required="yes">
		
		<cfquery name="local.qryForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			declare @enrollmentID int, @loadpoint varchar(15), @overrideMessage varchar(max), @responseId int;
			set @enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="cf_sql_integer">;
			set @loadpoint = <cfqueryparam value="#arguments.loadPoint#" cfsqltype="cf_sql_varchar">;
			
			SELECT top 1  form.overrideMessage as overrideMessage
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblSeminarsAndForms AS form ON e.seminarID = form.seminarID
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = form.formid AND f.isDeleted = 0
			INNER JOIN dbo.tblseminarsandformresponses as f1 on f1.enrollmentid = e.enrollmentid	
			WHERE e.enrollmentID = @enrollmentID
			AND form.loadPoint = @loadpoint				
			AND f.isPublished = 1
			-- once an exam has been passed, dont allow more
			AND NOT EXISTS (
				select safr.sfrid
				FROM dbo.tblseminarsandformresponses as safr
				INNER JOIN formbuilder.dbo.tblResponses as fbr on safr.responseID = fbr.responseID
				where safr.enrollmentid = e.enrollmentid
				and safr.seminarFormID = form.seminarFormID
				and fbr.isActive = 1
				and fbr.passingPct > 0
				and fbr.passingPct >= f.passingPct
				and fbr.dateCompleted is not null
				);
			
		</cfquery>	

		<cfreturn application.objCommon.queryToArrayOfStructures(local.qryForms)>
	</cffunction>
</cfcomponent>