<cfsavecontent variable="local.paymentJS">
	<cfoutput>
	<script language="javascript">
		var #ToScript(local.paymentProfilesDownloadLink,'link_downloadPaymentProfile')#
		let allPaymentProfilesListTable;
		
		function viewCrossSavingsCalculator() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Processing Fee Donation / Surcharge Savings Calculator',
				iframe: true,
				contenturl: '#local.viewSavingsCalculator#&mpid=-1',
				strmodalbody : {
					classlist: 'p-2'
				},
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}

		function viewSavingsCalculator(ftid,pid){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (ftid == 1 ? 'Processing Fee Donation' : 'Surcharge') + ' Savings Calculator',
				iframe: true,
				contenturl: '#local.viewSavingsCalculator#&mpid=' + pid,
				strmodalbody : {
					classlist: 'p-2'
				},
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}

		function initallPaymentProfilesListTable(){
			allPaymentProfilesListTable = $('##allPaymentProfilesListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.listAllPaymentProfilesLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div><a href="'+mca_getSiteToolLink(data.siteCode,'MerchantProfilesAdmin','list','edit','profileID='+data.profileID)+'" title="Edit This Payment Profile" target="_blank">'+data.profileName + ' (' +data.profileCode + ')</a></div>';
								renderData += '<div class="text-dim small">' + data.siteCode + ' - ' + data.siteName + '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"orderable": false 
					},
					{ "data": "allowPayments", "className": "text-center","width": "5%", "orderable": false },
					{ "data": "allowPayInvoicesOnline", "className": "text-center","width": "5%", "orderable": false },
					{ "data": "allowRefunds", "className": "text-center","width": "5%", "orderable": false },
					{ "data": "maxFailedAutoAttempts", "className": "text-center mcdtbl2","width": "5%", "orderable": false  },
					{ "data": "daysBetweenAutoAttempts", "className": "text-center","width": "4%", "orderable": false },
					{ "data": "minDaysFailedCleanup", "className": "text-center","width": "4%", "orderable": false  },
					{ "data": "enableMCPay", "className": "text-center mcdtbl2","width": "5%", "orderable": false },
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if (data.enableProcessingFeeDonation == 'Yes') {
									renderData += '<div class="small">' + data.processFeeDonationFeePercent + '% PFD</div>';
									renderData += '<div class="small btn btn-sm" onclick="viewSavingsCalculator(1,'+data.profileID+');return false;"><i class="fas fa-analytics"></i></div>';
								} else if (data.enableSurcharge == 'Yes') {
									renderData += '<div class="small">' + data.surchargePercent + '% SRCHG</div>';
									renderData += '<div class="small btn btn-sm" onclick="viewSavingsCalculator(2,'+data.profileID+');return false;"><i class="fas fa-analytics"></i></div>';
								} else {
									renderData += '<div>No</div>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "10%",
						"className": "text-center",
						"orderable": false
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div>$' + formatCurrency(data.SumPayments) + '</div><div><span class="badge badge-success mr-1">' + data.CountPayments + '</span><span class="badge badge-danger">' + data.CountFailedPayments + '</span></div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "9%",
						"className": "text-center",
						"orderable": false
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div>$' + formatCurrency(data.SumRefunds) + '<br/>' + data.CountRefunds + '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "7%",
						"className": "text-center",
						"orderable": false
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								let badgespanOn = '<span class="badge badge-success mr-1">';
								let badgespanOff = '<span class="badge badge-dark mr-1">';
								let badgespanEnd = '</span>';

								renderData += (data.enableVISA == 1 ? badgespanOn : badgespanOff) + 'V' + badgespanEnd;
								renderData += (data.enableMasterCard == 1 ? badgespanOn : badgespanOff) + 'M' + badgespanEnd;
								renderData += (data.enableAmericanExpress == 1 ? badgespanOn : badgespanOff) + 'A' + badgespanEnd;
								renderData += (data.enableDiscover == 1 ? badgespanOn : badgespanOff) + 'D' + badgespanEnd + '<br/>';
								renderData += (data.enableApplePay == 1 ? badgespanOn : badgespanOff) + 'AP' + badgespanEnd;
								renderData += (data.enableGooglePay == 1 ? badgespanOn : badgespanOff) + 'GP' + badgespanEnd;
							}
							return type === 'display' ? renderData : data;
						},
						"width": "12%",
						"className": "text-center",
						"orderable": false 
					}
				],
				"searching": false,
				"ordering": false,
				"createdRow": function( row, data, index ) {
					if (data.status.toUpperCase() == 'I') $(row).addClass('table-warning');
				}
			});
		}
		function filterAuthorizeProfiles() {
			$('##divFilterForm').show();;
		}
		function doFilterAuthorizeProfiles() {
			allPaymentProfilesListTable.draw();
		}
		function clearFilters() {
			$('##frmFilter')[0].reset();
			doFilterAuthorizeProfiles();
		}
		function toggleMCPaySettings(x) {
			let hasSurcharge = $('##fenableSurcharge').length;
			let hasPFD = $('##fenablePFD').length;
			if ($(x).prop('checked')) {
				if (hasPFD) {
					$('##fenablePFD').prop('disabled',false).change();
				}
				if (hasSurcharge) {
					$('##fenableSurcharge').prop('disabled',false).change();
				}			
				
			} else {
				if (hasPFD) {
					$('##fenablePFD').prop('checked',false).change();
				}
				if (hasSurcharge) {
					$('##fenableSurcharge').prop('checked',false).change();
				}
			}
		}
		function toggleSurchargeSettings(x) {
			let hasMCPay = $('##fenableMCPay').prop('checked');
			let hasPFD = $('##fenablePFD').prop('checked');
			if ($(x).prop('checked')) {
				if (hasMCPay) {					
					if (hasPFD) {
						$('##fenablePFD').prop('checked',false).change();
					}
				}
				else {
					$('##fenableSurcharge').prop('checked',false).change();
				}	
			}
		}
		function togglePFDSettings(x) {
			let hasMCPay = $('##fenableMCPay').prop('checked');
			let hasSurcharge = $('##fenableSurcharge').prop('checked');
			if ($(x).prop('checked')) {
				if (hasMCPay) {					
					if (hasSurcharge) {
						$('##fenableSurcharge').prop('checked',false).change();
					}
				}
				else {
					$('##fenablePFD').prop('checked',false).change();
				}	
			}
		}
		function downLoadPaymentProfiles() {
			location.href = link_downloadPaymentProfile + '&' + $('##frmFilter').serialize();
		}
		$(function() { 
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupSelect2($('##frmFilter'));
			initallPaymentProfilesListTable();
		});
	</script>
	<style>
		.mcdtbl2 { border-left:2px solid ##000 !important; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.paymentJS#">	

<cfoutput>
<h4>Authorize.NET Pay Profiles</h4>
<div class="toolButtonBar">
	<div><a href="javascript:filterAuthorizeProfiles();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter Authorize.NET Pay Profiles."><i class="fa-regular fa-filter"></i> Filter Pay Profiles</a></div>
	<div><a href="javascript:downLoadPaymentProfiles();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download Pay Profiles."><i class="fa-regular fa-download"></i> Download Pay Profiles</a></div>
	<div><a href="javascript:viewCrossSavingsCalculator();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to report on PFD/Surcharge Savings."><i class="fa-solid fa-analytics"></i> PFD/Surcharge Fee Savings Calculator</a></div>
</div>
<div id="divFilterForm" class="mt-2 mb-4" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="doFilterAuthorizeProfiles();return false;">
		<div class="card card-box mb-1">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Filter Payment Profiles
				</div>
			</div>
			<div class="card-body pb-3">				
				<div class="form-label-group">
					<select name="fSiteID" id="fSiteID" class="custom-select">
						<option value="">All Sites</option>
						<cfloop query="local.qrySites">
							<option value="#local.qrySites.siteID#">#local.qrySites.siteCode# - #local.qrySites.siteName#</option>
						</cfloop>
					</select>
					<label for="fSiteID">Site</label>
				</div>
				<div class="form-group row">
					<div class="col-6">
						<div class="form-label-group">
							<select name="fPaymentTypes" id="fPaymentTypes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Payment Type" multiple="multiple">		
								<option value="A">American Express</option>
								<option value="D">Discover</option>
								<option value="M">MasterCard</option>
								<option value="V">VISA</option>
								<option value="GP">Google Pay</option>
								<option value="AP">Apple Pay</option>									
							</select>
							<label for="fSiteID">Accepts Any of These Selected Payment Types</label>
						</div>
					</div>
					<div class="col-6">
						<div class="form-group">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="fenableMCPay" id="fenableMCPay" value="1" class="custom-control-input" onchange="toggleMCPaySettings(this);">
								<label class="custom-control-label" for="fenableMCPay">MC Pay enabled</label>	
							</div>
						</div>
					</div>
				</div>
				<div class="form-group row">
					<div class="col-3">
						<div class="form-label-group">
							<div class="input-group dateFieldHolder">
								<input type="text" name="fDateFrom" id="fDateFrom" value="#DateFormat(local.startDate,'m/d/yyyy')#" class="form-control dateControl" autocomplete="off">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="fDateFrom"><i class="fa-solid fa-calendar"></i></span>
								</div>
								<label for="fDateFrom">Sum/Counts From</label>
							</div>
						</div>
					</div>
					<div class="col-3">
						<div class="form-label-group">
							<div class="input-group dateFieldHolder">
								<input type="text" name="fDateTo" id="fDateTo" value="#DateFormat(local.endDate,'m/d/yyyy')#" class="form-control dateControl" autocomplete="off">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="fDateTo"><i class="fa-solid fa-calendar"></i></span>
								</div>
								<label for="fDateTo">Sum/Counts To</label>
							</div>
						</div>
					</div>
					<div class="col-3">
						<div class="form-group">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="fenablePFD" id="fenablePFD" value="1" class="custom-control-input" onchange="togglePFDSettings(this);">
								<label class="custom-control-label" for="fenablePFD">PFD Enabled</label>	
							</div>
						</div>
					</div>
					<div class="col-3">
						<div class="form-group">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="fenableSurcharge" id="fenableSurcharge" value="1" class="custom-control-input" onchange="toggleSurchargeSettings(this);">
								<label class="custom-control-label" for="fenableSurcharge">Surcharge Enabled</label>	
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer text-right">
				<button type="button" class="btn btn-sm btn-secondary" onclick="clearFilters();">Clear Filters</button>
				<button type="submit" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Filter Profiles</button>
			</div>
		</div>
	</form>
</div>
<table id="allPaymentProfilesListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Pay Profile</th>
			<th>Payments</th>
			<th>Pay Invoices</th>
			<th>Refunds</th>
			<th>Max Fail</th>
			<th>Days Btwn</th>
			<th>Min Fail Clean</th>
			<th>MCPay</th>
			<th>Proc Fee</th>
			<th>Payments</th>
			<th>Refunds</th>
			<th>Accepted</th>
		</tr>
	</thead>
</table>
</cfoutput>