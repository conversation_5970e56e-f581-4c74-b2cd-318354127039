CREATE PROC dbo.ams_advanceMemberDataCompareDates

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpAdvanceDates') is not null
		DROP TABLE #tmpAdvanceDates;
	CREATE TABLE #tmpAdvanceDates (columnID int, newCompareDate date, newAdvanceDate date);

	INSERT INTO #tmpAdvanceDates (columnID, newCompareDate, newAdvanceDate)
	SELECT mdc.columnID, 
		dbo.fn_af_getAFDate(mdc.linkedDateCompareDate, afADCD.[datePart], afADCD.dateNum, afADCD.adjustTerm, afADCD.nextWeekday, afADCD.weekNumber),
		dbo.fn_af_getAFDate(mdc.linkedDateAdvanceDate, afAD.[datePart], afAD.dateNum, afAD.adjustTerm, afAD.nextWeekday, afAD.weekNumber)
	FROM dbo.ams_memberDataColumns AS mdc
	INNER JOIN dbo.af_advanceFormulas AS afAD ON afAD.AFID = mdc.linkedDateAdvanceAFID
	INNER JOIN dbo.af_advanceFormulas AS afADCD ON afADCD.AFID = mdc.linkedDateCompareDateAFID
	WHERE mdc.linkedDateAdvanceDate IS NOT NULL
	AND mdc.linkedDateAdvanceDate <= GETDATE();

	IF @@ROWCOUNT = 0
		GOTO on_done;

	UPDATE mdc
	SET mdc.linkedDateCompareDate = tmp.newCompareDate,
		mdc.linkedDateAdvanceDate = tmp.newAdvanceDate
	FROM dbo.ams_memberDataColumns as mdc
	INNER JOIN #tmpAdvanceDates as tmp on tmp.columnID = mdc.columnID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpAdvanceDates') is not null
		DROP TABLE #tmpAdvanceDates;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
