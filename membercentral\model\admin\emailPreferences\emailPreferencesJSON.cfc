<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getConsentListMembers" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.listMode = arguments.event.getValue('listMode','');

			if(local.listMode EQ 'Opt-Out'){
				arguments.event.setValue('fConsentListID',arguments.event.getValue('fOptOutConsentListID',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptOutCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptOutCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptOutAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptOutAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptOutEmailAddressMode',0));
			} else {
				arguments.event.setValue('fConsentListID',arguments.event.getValue('fOptInConsentListID',''));
				arguments.event.setValue('fCreatedFrom',arguments.event.getValue('fOptInCreatedFrom',''));
				arguments.event.setValue('fCreatedTo',arguments.event.getValue('fOptInCreatedTo',''));
				arguments.event.setValue('fAssignedMemberID',arguments.event.getValue('fOptInAssignedMemberID',''));
				arguments.event.setValue('fAssignedGroupID',arguments.event.getValue('fOptInAssignedGroupID',''));
				arguments.event.setValue('fEmailAddressMode',arguments.event.getValue('fOptInEmailAddressMode',0));
			}

			local.operationMode = arguments.event.getTrimValue('opMode','allConsentLists');
			local.qryConsentListMembers = createObject("component","emailPreferences").getConsentListMembersFromFilters(event=arguments.event, operationMode=local.operationMode, listMode=local.listMode);
		</cfscript>
		
		<cfset local.data = []>
		<cfif local.qryConsentListMembers.recordCount>
			<cfloop query="local.qryConsentListMembers">
				<cfset local.tmpStr = {
					"consentlistmemberid": local.qryConsentListMembers.consentListMemberID,
					"listname": local.qryConsentListMembers.listName,
					"datecreated": DateTimeFormat(local.qryConsentListMembers.dateCreated,"m/d/yy h:nn tt") & " CT",
					"emailaddress": local.qryConsentListMembers.emailAddress,
					"memberstiedtoemailaddrdisp": "",
					"DT_RowId": "clm_#local.qryConsentListMembers.consentListMemberID#",
					"DT_RowData": {
						"listname": local.qryConsentListMembers.listName,
						"emailaddress": local.qryConsentListMembers.emailAddress
					}
				}>

				<cfif local.operationMode EQ 'allConsentLists' AND listLen(local.qryConsentListMembers.memberIDList)>
					<cfsavecontent variable="local.tmpStr.memberstiedtoemailaddrdisp">
						<cfset local.index = 0>
						<cfloop list="#local.qryConsentListMembers.memberIDList#" index="local.thisMemberID">
							<cfset local.index++>
							<cfoutput><a href='javascript:editMember(#local.thisMemberID#)'>#getToken(local.qryConsentListMembers.memberDisplayNameList,local.index,'|')#</a>#local.index NEQ listLen(local.qryConsentListMembers.memberIDList) ? ', ' : ''#</cfoutput>
						</cfloop>
					</cfsavecontent>
				</cfif>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryConsentListMembers.totalCount),
			"recordsFiltered": val(local.qryConsentListMembers.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getConsentListMemberHistory" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.operationMode = arguments.event.getTrimValue('opMode','specificConsentListHistory');
			local.listMode = arguments.event.getValue('listMode','');

			if (local.operationMode EQ 'memberConsentListHistory') {
				if(local.listMode EQ 'Opt-Out'){
					arguments.event.setValue('fMemberID',arguments.event.getValue('fOptOutAssignedMemberID',''));
				} else {
					arguments.event.setValue('fMemberID',arguments.event.getValue('fOptInAssignedMemberID',''));
				}
			}

			local.qryConsentListMemberHistory = createObject("component","emailPreferences").getConsentListMemberHistory(event=arguments.event, operationMode=local.operationMode);
		</cfscript>

		<cfset local.data = []>
		<cfif local.qryConsentListMemberHistory.recordCount>
			<cfloop query="local.qryConsentListMemberHistory">
				<cfset local.tmpStr = {
					"historyid": local.qryConsentListMemberHistory.historyID,
					"consentlistname": local.qryConsentListMemberHistory.consentListName,
					"updatedate": DateTimeFormat(local.qryConsentListMemberHistory.updateDate,"m/d/yy h:nn tt") & " CT",
					"emailaddress": local.qryConsentListMemberHistory.email,
					"action": local.qryConsentListMemberHistory.action,
					"enteredbymemberid": local.qryConsentListMemberHistory.enteredByMemberID,
					"enteredbymemberorgid": local.qryConsentListMemberHistory.enteredByMemberOrgID,
					"enteredbymembername": local.qryConsentListMemberHistory.enteredByMemberName,
					"enteredbymembercompany": local.qryConsentListMemberHistory.enteredByMemberCompany,
					"DT_RowId": "clmhistory_#local.qryConsentListMemberHistory.historyID#"
				}>
				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryConsentListMemberHistory.totalCount),
			"recordsFiltered": val(local.qryConsentListMemberHistory.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getConsentLists" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryConsentLists" datasource="#application.dsn.platformmail.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpConsentLists') IS NOT NULL 
				DROP TABLE ##tmpConsentLists;
			CREATE TABLE ##tmpConsentLists (consentListTypeID int, consentListTypeName varchar(100), consentListID int, consentListName varchar(100), modeName varchar(25), siteResourceID int,
				listTypeOrderNum int, listOrderNum int, maxConsentListTypeOrderNum int, minConsentListOrderNum int, maxConsentListOrderNum int, listMembersCount int, isSystemType bit, isHidden bit, 
				numConsentLists int, rowNum int);

			INSERT INTO ##tmpConsentLists (consentListTypeID, consentListTypeName, consentListID, consentListName, modeName, siteResourceID, listTypeOrderNum, listOrderNum, listMembersCount, 
				isSystemType, isHidden, numConsentLists, rowNum)
			select clt.consentListTypeID, clt.consentListTypeName, cl.consentListID, cl.consentListName, clm.modeName, cl.siteResourceID, clt.orderNum as listTypeOrderNum, cl.orderNum as listOrderNum,
				(select count(consentListMemberID) from dbo.email_consentListMembers where consentListID = cl.consentListID) as listMembersCount, clt.isSystemType, cl.isHidden,
				(select count(consentListID) from dbo.email_consentLists where consentListTypeID = clt.consentListTypeID) as numConsentLists,
				ROW_NUMBER() OVER (order by clt.orderNum, cl.orderNum) as rowNum
			from dbo.email_consentListTypes as clt
			left outer join dbo.email_consentLists as cl 
				inner join dbo.email_consentListModes as clm on clm.consentListModeID = cl.consentListModeID
				on cl.consentListTypeID = clt.consentListTypeID
				and cl.[status] = 'A'
			where clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

			update tmp
			set tmp.maxConsentListTypeOrderNum = tmpAggr.maxListTypeSort
			from ##tmpConsentLists as tmp
			cross apply (
				select max(listTypeOrderNum) as maxListTypeSort
				from ##tmpConsentLists
			) as tmpAggr;

			update tmp
			set tmp.minConsentListOrderNum = tmpAggr.minListSort,
				tmp.maxConsentListOrderNum = tmpAggr.maxListSort
			from ##tmpConsentLists as tmp
			inner join (
				select consentListTypeID, min(listOrderNum) as minListSort, max(listOrderNum) as maxListSort
				from ##tmpConsentLists
				group by consentListTypeID
			) as tmpAggr on isnull(tmpAggr.consentListTypeID,0) = isnull(tmp.consentListTypeID,0);

			select consentListTypeID, consentListTypeName, consentListID, consentListName, modeName, siteResourceID, listTypeOrderNum, 
				listOrderNum, maxConsentListTypeOrderNum, listMembersCount, isSystemType, isHidden, numConsentLists,
				case when listOrderNum = minConsentListOrderNum then 1 else 0 end as listFirstItem,
				case when listOrderNum = maxConsentListOrderNum then 1 else 0 end as listLastItem
			from ##tmpConsentLists
			order by rowNum;

			IF OBJECT_ID('tempdb..##tmpConsentLists') IS NOT NULL 
				DROP TABLE ##tmpConsentLists;
		</cfquery>

		<cfset local.arrConsentLists = []>
		<cfoutput query="local.qryConsentLists" group="consentListTypeID">
			<cfset local.consentListTypeRowID = "clt#val(local.qryConsentLists.consentListTypeID)#">
			<cfset local.arrConsentLists.append({
				"level": 1,
				"rowType": "consentListType",
				"consentListID": val(local.qryConsentLists.consentListID),
				"consentListTypeID": local.qryConsentLists.consentListTypeID,
				"displayName": local.qryConsentLists.consentListTypeName,
				"modeName": "",
				"listMembersCount": "",
				"isHidden": "",
				"canEdit": not local.qryConsentLists.isSystemType,
				"canDelete": val(local.qryConsentLists.numConsentLists) is 0 and not local.qryConsentLists.isSystemType,
				"canMoveUp": local.qryConsentLists.listTypeOrderNum NEQ 1,
				"canMoveDown": local.qryConsentLists.listTypeOrderNum NEQ local.qryConsentLists.maxConsentListTypeOrderNum,
				"hasChildren": val(local.qryConsentLists.consentListID) gt 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.consentListTypeRowID,
				"DT_RowClass": "child-of-gridRoot"
			})>
			<cfoutput>
				<cfif val(local.qryConsentLists.consentListID) gt 0>
					<cfset local.arrConsentLists.append({
						"level": 2,
						"rowType": "consentList",
						"consentListID": val(local.qryConsentLists.consentListID),
						"consentListTypeID": local.qryConsentLists.consentListTypeID,
						"displayName": local.qryConsentLists.consentListName,
						"modeName": local.qryConsentLists.modeName,
						"listMembersCount": val(local.qryConsentLists.listMembersCount),
						"isHidden": YesNoFormat(not local.qryConsentLists.isHidden),
						"canEdit": not local.qryConsentLists.isSystemType,
						"canDelete": val(local.qryConsentLists.listMembersCount) is 0 and not local.qryConsentLists.isSystemType,
						"canMoveUp": local.qryConsentLists.listFirstItem neq 1 and not local.qryConsentLists.isSystemType,
						"canMoveDown": local.qryConsentLists.listLastItem neq 1 and not local.qryConsentLists.isSystemType,
						"parentRowID": local.consentListTypeRowID,
						"DT_RowId": "clt#local.qryConsentLists.consentListTypeID#-#local.qryConsentLists.consentListID#",
						"DT_RowClass": "child-of-#local.consentListTypeRowID#"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrConsentLists),
			"recordsFiltered": arrayLen(local.arrConsentLists),
			"data": local.arrConsentLists
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>