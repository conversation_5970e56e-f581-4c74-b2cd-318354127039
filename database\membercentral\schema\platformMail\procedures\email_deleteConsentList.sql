ALTER PROC dbo.email_deleteConsentList
@orgID int,
@consentListID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @consentListTypeID int, @footerContentID int;

	IF EXISTS (
		SELECT 1
		FROM dbo.email_consentLists AS l
		INNER JOIN dbo.email_consentListTypes AS t ON t.consentListTypeID = l.consentListTypeID
			AND t.orgID = @orgID
			AND t.isSystemType = 1
		WHERE l.consentListID = @consentListID
	)
		RAISERROR('system type', 16, 1);

	IF EXISTS (select top 1 consentListID from dbo.email_consentListMembers where consentListID = @consentListID)
		RAISERROR('Consent List has members assigned to it', 16, 1);

	SELECT @consentListTypeID = l.consentListTypeID, @footerContentID = l.footerContentID
	FROM dbo.email_consentLists AS l
	INNER JOIN dbo.email_consentListTypes AS t ON t.consentListTypeID = l.consentListTypeID
		AND t.orgID = @orgID
	WHERE l.consentListID = @consentListID
	AND l.[status] = 'A';
	
	IF @consentListTypeID IS NULL
		RAISERROR('invalid consent list',16,1);

	BEGIN TRAN;
		UPDATE dbo.email_consentLists
		SET [status] = 'D'
		WHERE consentListID = @consentListID;

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
