<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script language="javascript">
		let customFieldTable;
		function initializecustomFieldTable() {
			
			customFieldTable = $('##customFieldTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.customFieldList#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div class="d-flex">';
								switch(data.displayTypeCode) {
									case 'TEXTBOX':
									renderData +=  '<span ><i class="far fa-text px-2"></i></span>'; break;
									case 'RADIO':
									renderData +=  '<span ><i class="fas fa-list-ul px-2"></i></span>';break;
									case 'CHECKBOX':
									renderData +=  '<span ><i class="fas fa-list-ul px-2"></i></span>';break;
									case 'SELECT':
									renderData +=  '<span ><i class="far fa-list px-2"></i></span>';break;
									case 'DATE':
									renderData +=  '<span ><i class="far fa-calendar-alt px-2"></i></span>';break;
									case 'DOCUMENT':
									renderData +=  '<span ><i class="fas fa-file-pdf px-2"></i></span>';break;
									case 'TEXTAREA':
									renderData +=  '<span ><i class="far fa-bars px-2"></i></span>';break;
									case 'HTMLCONTENT':
									renderData +=  '<span ><i class="fas fa-code px-2"></i></span>';break;
								}
								renderData += '<span>'+data.columnName+'</span><span id="featuredBadgeContainer_'+data.columnID+'" class="ml-auto">'+(data.isReadOnly ? '<span class="badge badge-info">Read Only</span>' : '')+'</span></div>';								
							}
							return type === 'display' ? renderData : data;
						}, "width": "80%", 
						"className": "align-top text-center"},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="javascript:editColumn('+data.columnID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Edit Custom Field"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="javascript:removeColumn('+data.columnID+');" id="btnDel'+data.columnID+'" class="btn btn-xs btn-outline-danger p-1 m-1" title="Delete Custom Field"><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "30%",
						"className": "align-top text-center",
						"orderable": false
					}
				],
				"order": [[0, 'asc']],
				"searching": false
			});
		}
		
		function editColumn(cID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: cID > 0 ? 'Edit Custom Field' : 'Add Custom Field',
				iframe: true,
				contenturl: '#this.link.editCustomField#&cID='+cID
			});
		}		
		function removeColumn(cID) {
			var removeColumnData = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadcustomFieldsTable();
				} else {
					delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					if(r.msg) alert(r.msg);
					else alert('We were unable to delete this custom field. Try again.');
				}
			};
			let delElement = $('##btnDel'+cID);
			mca_initConfirmButton(delElement, function(){
				var objParams = { columnID:cID };
				TS_AJX('CUSTOMFIELDS','removeColumn',objParams,removeColumnData,removeColumnData,30000,removeColumnData);
			});
		}
		function reloadcustomFieldsTable() {
			customFieldTable.draw();
		}
		function doFilterCustomFields() { 
			reloadcustomFieldsTable();
			$('##divFilterForm').hide('slow');
		}
		function clearFilterCustomFields() {
			$('##frmFilter')[0].reset();
			$('##frmFilter [data-toggle="custom-select2"]').trigger('change.select2');
			doFilterCustomFields();
		}
		function filterCustomFields() {
			if (!$('##divFilterForm').is(':visible')) {
				$('div.grpToolBarItem').hide();
				$('##divFilterForm').show();
			}
		}
		 $(function() {	
			mca_setupSelect2();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

<cfoutput>
<div class="toolButtonBar">
	<div><a href="javascript:editColumn(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add new custom field."><i class="fa-regular fa-circle-plus"></i> Add Custom Field</a></div>
	<div><a href="javascript:filterCustomFields();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter custom fields ."><i class="fa-regular fa-filter"></i> Filter Custom Fields</a></div>
</div>
<div id="divFilterForm" class="my-3 grpToolBarItem" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="doFilterCustomFields();return false;">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Filter Custom Fields
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="form-row">
					<div class="col-6">
						<div class="form-label-group mb-0">
							<input type="text" name="fcolumnName" id="fcolumnName" class="form-control" value="" style="height:57px;">
							<label for="fcolumnName">Field Name</label>
						</div>
					</div>
					<div class="col-6">
						<div class="form-label-group mb-0">
							<select name="fdisplayTypeID" id="fdisplayTypeID" multiple="true" data-toggle="custom-select2" class="custom-select">
								<option value="0"></option>
								<cfloop query="local.qryDisplayTypes">
									<option value="#local.qryDisplayTypes.displayTypeID#">#local.qryDisplayTypes.displayTypeCode#</option>
								</cfloop>	
																	
							</select>
							<label for="fdisplayTypeID">Field Type</label>
						</div>
					</div>
					
				</div>			
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnResetFilterCustomFields" class="btn btn-sm btn-secondary" onclick="clearFilterCustomFields();">Clear Filters</button>
				<button type="submit" name="btnFilterCustomFields" class="btn btn-sm btn-primary">Filter Custom Fields</button>
			</div>
		</div>
	</form>
</div>
<div id="CustomFields">
	<h5>Custom Fields</h5>
	<table id="customFieldTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>CUSTOM FIELDS</th>				
				<th>ACTIONS</th>
			</tr>
		</thead>
	</table>
</div>
<div id="saveCFloadingDIV" style="display:none;">
	<div class="c">
		<br/>
		<img src="/assets/common/images/indicator.gif" width="100" height="100">
		<br/><br/>
		<b>Please wait while we remove this custom field value.</b>
		<br/>
	</div>
</div>
</cfoutput>
