ALTER PROC dbo.ams_prepareVirtualGroupRulesImport
@siteID int,
@pathToImport varchar(400),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgConditionsChanged') IS NOT NULL 
		DROP TABLE #tmpOrgConditionsChanged;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRules;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRulesChanged') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRulesChanged;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpRules;

	CREATE TABLE #tblImportErrors (rowid int IDENTITY(1,1), msg varchar(600), errorCode varchar(20));
	CREATE TABLE #tmpOrgGrpConditions (uid uniqueidentifier, datatypeid int, displaytypeid int, expressionid int, datepart varchar(8), dateexpressionid int, 
		verbose varchar(max), fieldCode varchar(40), subProc varchar(30), processArea varchar(25), processValuesSection tinyint, hashvalue varchar(max), 
		cvid int, conditionkeyid int, conditionKey varchar(50), conditionvalue varchar(100), afid int);
	CREATE TABLE #tmpOrgConditionsChanged (uid uniqueidentifier);
	CREATE TABLE #tmpOrgGrpRules (uid uniqueidentifier, rulename varchar(400), rulexml varchar(max), isActive bit, groupid int);
	CREATE TABLE #tmpOrgGrpRulesChanged (uid uniqueidentifier);
	CREATE TABLE #tmpOrgDeleteGrpConditions (uid uniqueidentifier, verbose varchar(max));
	CREATE TABLE #tmpOrgDeleteGrpRules (uid uniqueidentifier, rulename varchar(400));

	DECLARE @orgID int, @orgcode varchar(10), @cmd varchar(400), @svrName varchar(40);
	SET @svrName = CAST(serverproperty('servername') as varchar(40));

	SELECT @orgID = o.orgID, @orgcode = o.orgcode 
	FROM dbo.sites as s
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	-- ensure files are present
	IF dbo.fn_FileExists(@pathToImport + 'sync_vgc_conditions.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_rules.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allrulegroups.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allfields.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allvalueids.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_supporting.bcp') = 0
	BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('One or more required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync condition rows
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgcode = @orgcode;

	-- import data
	set @cmd = 'bcp datatransfer.dbo.sync_vgc_conditions in ' + @pathToImport + 'sync_vgc_conditions.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp datatransfer.dbo.sync_vgc_rules in ' + @pathToImport + 'sync_vgc_rules.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp datatransfer.dbo.sync_vgc_allrulegroups in ' + @pathToImport + 'sync_vgc_allrulegroups.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_allfields in ' + @pathToImport + 'sync_vgc_allfields.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_allvalueids in ' + @pathToImport + 'sync_vgc_allvalueids.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_supporting in ' + @pathToImport + 'sync_vgc_supporting.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_vgc_conditions SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_rules SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allrulegroups SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allfields SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allvalueids SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_supporting SET orgID = @orgID WHERE orgcode = @orgcode;

	-- existing org conditions
	INSERT INTO #tmpOrgGrpConditions (uid, datatypeid, displaytypeid, expressionid, datepart, dateexpressionid, verbose, fieldCode, 
		subProc, processArea, processValuesSection, hashvalue, cvid, conditionkeyid, conditionKey, conditionvalue, afid)
	select condition.[uid], condition.datatypeid, condition.displaytypeid, condition.expressionid, isnull(condition.[datepart],'') as [datepart], 
		isnull(condition.dateexpressionid,0) as dateexpressionid, condition.[verbose], condition.fieldCode, condition.subProc, 
		condition.processArea, isnull(condition.processValuesSection,0) as processValuesSection, 
		'0x' + CAST('' AS XML).value('xs:hexBinary(sql:column("condition.hashValue"))', 'VARCHAR(MAX)') as hashvalue,
		condvalue.cvid, condvalue.conditionkeyid, k.conditionKey, condvalue.conditionvalue, isnull(condvalue.afid,0) as afid
	from dbo.ams_virtualGroupConditions as condition
	left outer join dbo.ams_virtualGroupConditionValues as condvalue 
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		on condvalue.conditionID = condition.conditionID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	-- existing org rules
	INSERT INTO #tmpOrgGrpRules (uid, rulename, rulexml, isActive, groupid)
	select vgrule.uid, vgrule.rulename, cast(rv.rulexml as varchar(max)) as rulexml, vgrule.isActive, [group].groupid
	from dbo.ams_virtualGroupRules as vgrule
	inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		and rv.ruleVersionID = vgrule.activeVersionID
	left outer join dbo.ams_virtualGroupRuleGroups as [group] on [group].ruleID = vgrule.ruleid
	where vgrule.orgID = @orgID
	and vgrule.ruleTypeID = 1;

	/*** Process the Data for Comparisons ***/
	
	-- get the useFieldCode in allFields
	update af
	set af.useFieldCode = af.fieldCode
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join membercentral.dbo.organizations as o on o.orgID = @orgID and o.orgID = af.orgID
	where af.orgID = @orgID
	AND (
		af.fieldcode in ('m_firstname','m_lastname','m_membernumber','m_company','m_membertypeid','m_status','m_hasmemberphoto')
		OR (af.fieldcode = 'm_prefix' and o.hasPrefix = 1)
		OR (af.fieldcode = 'm_middlename' and o.hasMiddleName = 1)
		OR (af.fieldcode = 'm_suffix' and o.hasSuffix = 1)
		OR (af.fieldcode = 'm_professionalsuffix' and o.hasProfessionalSuffix = 1)
		OR (left(af.fieldcode,21) = 'm_earliestdatecreated')
		OR (left(af.fieldcode,16) = 'm_datelastlogin_')
	);

	update af
	set af.useFieldCode = 'md_' + cast(mdc.columnID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.orgID = af.orgID and mdc.columnName = af.fieldLabel
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'md_';

	update af
	set af.useFieldCode = 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'ma' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'ma_'
	and (
		parsename(replace(af.fieldCode,'_','.'),1) in ('address1','city','stateprov','postalcode','country','tagged')
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'attn' AND mat.hasAttn = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'address2' AND mat.hasAddress2 = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'address3' AND mat.hasAddress3 = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'county' AND mat.hasCounty = 1)
	);

	update af
	set af.useFieldCode = 'mat_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mat' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = s.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mat_'
	and parsename(replace(af.fieldCode,'_','.'),1) in ('attn','address1','address2','address3','city','stateprov','postalcode','country','county');

	update af
	set af.useFieldCode = 'mp_' + cast(mat.addressTypeID as varchar(10)) + '_' + cast(mpt.phoneTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'ma' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = sA.itemUID
	inner join dataTransfer.dbo.sync_vgc_supporting as sP on sP.orgID = @orgID and sP.cat = 'mp' and sP.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID and mpt.[uid] = sP.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'mp_';

	update af
	set af.useFieldCode = 'mpt_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + cast(mpt.phoneTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'mat' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = sA.itemType
	inner join dataTransfer.dbo.sync_vgc_supporting as sP on sP.orgID = @orgID and sP.cat = 'mp' and sP.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID and mpt.[uid] = sP.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mpt_';

	update af
	set af.useFieldCode = 'mad_' + cast(mat.addressTypeID as varchar(10)) + '_' + cast(mdt.districtTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'ma' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = sA.itemUID
	inner join dataTransfer.dbo.sync_vgc_supporting as sD on sD.orgID = @orgID and sD.cat = 'mad' and sD.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberDistrictTypes as mdt on mdt.orgID = @orgID and mdt.districttype = sD.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mad_';

	update af
	set af.useFieldCode = 'madt_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + cast(mdt.districtTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'mat' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = sA.itemType
	inner join dataTransfer.dbo.sync_vgc_supporting as sD on sD.orgID = @orgID and sD.cat = 'mad' and sD.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberDistrictTypes as mdt on mdt.orgID = @orgID and mdt.districttype = sD.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,5) = 'madt_';

	update af
	set af.useFieldCode = 'mw_' + cast(mw.websiteTypeID as varchar(10)) + '_website'
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mw' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberWebsiteTypes as mw on mw.orgID = @orgID and mw.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'mw_';

	update af
	set af.useFieldCode = 'me_' + cast(me.emailTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'me' 
		and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
		and parsename(replace(af.fieldCode,'_','.'),1) in ('email','tagged')
	inner join membercentral.dbo.ams_memberEmailTypes as me on me.orgID = @orgID and me.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'me_';

	update af
	set af.useFieldCode = 'met_' + cast(met.emailTagTypeID as varchar(10)) + '_email'
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'met' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'met_';

	update af
	set af.useFieldCode = 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mpl' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberProfessionalLicenseTypes as mplt on mplt.orgID = @orgID and mplt.PLName = s.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mpl_';

	update dataTransfer.dbo.sync_vgc_allfields
	set useFieldCode = 'IGNORE'
	where orgID = @orgID
	and fieldCode = 'ev_entry';

	update dataTransfer.dbo.sync_vgc_allfields
	set useFieldCode = fieldCode
	where orgID = @orgID
	and fieldCode in ('acct_allocsum','acct_allocsumrecog','acct_inv','acct_balance','acct_cc','cl_entry','cp_entry','cp_valuesum','l_entry','mh_entry','mn_entry','ref_entry','rel_entry','rt_role','sub_entry','sup_entry','sw_entry','task_entry');

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allfields where orgID = @orgID and useFieldCode is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useFieldCode in allfields.', 'USEFIELDCODENULL');
		GOTO on_done;
	END

	-- get the useValue in allValueIDs
	update v
	set v.useValue = mdcv.valueID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = parsename(replace(af.useFieldCode,'_','.'),1)
		and coalesce(mdcv.columnValueString,cast(mdcv.columnValueDecimal2 as varchar(10)),cast(mdcv.columnValueInteger as varchar(10)),convert(varchar(10),mdcv.columnValueDate,101)) = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'md_';

	update v
	set v.useValue = mat.addressTagTypeID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'ma_'
	and parsename(replace(af.fieldCode,'_','.'),1) = 'tagged';

	update v
	set v.useValue = met.emailTagTypeID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.emailTagType = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'me_'
	and parsename(replace(af.fieldCode,'_','.'),1) = 'tagged';

	update v
	set v.useValue = v.valueid
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	where v.orgID = @orgID
	and not (
		left(c.fieldCode,3) = 'md_'
		or (left(c.fieldCode,3) = 'ma_' and parsename(replace(c.fieldCode,'_','.'),1) = 'tagged')
		or (left(c.fieldCode,3) = 'me_' and parsename(replace(c.fieldCode,'_','.'),1) = 'tagged')
	);

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allvalueids where orgID = @orgID and useValue is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in allvalueids.', 'USEVALUENULL');
		GOTO on_done;
	END
	
	-- get system ids for advance formulas
	update s
	set s.useID = af.AFID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.sites as mcs on mcs.orgID = s.orgID
	inner join dbo.af_advanceFormulas as af on af.siteID = mcs.siteID and af.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'af';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'af' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for af.', 'USEIDAFNULL');
		GOTO on_done;
	END

	-- get system ids for email types
	update s
	set s.useID = met.emailTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'me';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'me' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for me.', 'USEIDMENULL');
		GOTO on_done;
	END

	-- get system ids for email tag types
	update s
	set s.useID = met.emailTagTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'met';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'met' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for me.', 'USEIDMETNULL');
		GOTO on_done;
	END

	-- get system ids for history categories
	;WITH allMHCats AS (
		SELECT categoryID, thePathExpanded
		FROM dbo.fn_getRecursiveCategoriesByResourceType(@siteID,'MemberHistoryAdmin')
	)
	update s
	set s.useID = mh.categoryID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allMHCats as mh on mh.thePathExpanded = s.itemType2
	where s.orgID = @orgID
	and s.cat = 'mh';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'mh' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for mh.', 'USEIDMHNULL');
		GOTO on_done;
	END

	-- get system ids for merchant profiles
	; WITH allMPs AS (
		SELECT profileID, profileCode
		FROM membercentral.dbo.mp_profiles
		WHERE siteID = @siteID
		and [status] IN ('A','I')
	)
	update s
	set s.useID = mp.profileID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allMPs as mp on mp.profileCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctmp';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctmp' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctmp.', 'USEIDACCTMPNULL');
		GOTO on_done;
	END

	-- get system ids for invoice profiles
	; WITH allIPs AS (
		SELECT profileID, profileName
		FROM membercentral.dbo.tr_invoiceProfiles
		WHERE orgID = @orgID
		and [status] <> 'D'
	)
	update s
	set s.useID = inp.profileID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allIPs as inp on inp.profileName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctip';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctip' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctip.', 'USEIDACCTIPNULL');
		GOTO on_done;
	END

	-- get system ids for card types
	; WITH allCCTypes AS (
		SELECT cardTypeID, cardType
		FROM membercentral.dbo.mp_cardTypes
	)
	update s
	set s.useID = cct.cardTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allCCTypes as cct on cct.cardType = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctcctype';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctcctype' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctcctype.', 'USEIDACCTCCTYPENULL');
		GOTO on_done;
	END


	-- get system ids for GLAccounts
	; WITH allGLs AS (
		SELECT GLAccountID, [uid]
		FROM membercentral.dbo.tr_GLAccounts
		WHERE orgID = @orgID
		and [status] <> 'D'
	)
	update s
	set s.useID = gl.GLAccountID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allGLs as gl on gl.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'acctgl';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctgl' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctgl.', 'USEIDACCTGLNULL');
		GOTO on_done;
	END

	-- get system ids for record types
	update s
	set s.useID = rt.recordTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join membercentral.dbo.ams_recordTypes as rt on rt.orgID = @orgID and rt.recordtypecode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'rt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rt.', 'USEIDRTNULL');
		GOTO on_done;
	END

	-- get system ids for relationship types
	update s
	set s.useID = rrt.relationshipTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join membercentral.dbo.ams_recordRelationshipTypes as rrt on rrt.orgID = @orgID and rrt.relationshipTypeCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'rrt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rrt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rrt.', 'USEIDRRTNULL');
		GOTO on_done;
	END

	-- get system ids for record relationship types
	;WITH orgrtrt as (
		SELECT rtrt.recordTypeRelationshipTypeID, rtrt.relationshipTypeID, rtrt.masterRecordTypeID, rtrt.childRecordTypeID
		FROM dbo.ams_recordTypesRelationshipTypes as rtrt
		INNER JOIN dbo.ams_recordTypes as rM on rM.orgID = @orgID and rM.recordTypeID = rtrt.masterRecordTypeID
		WHERE rtrt.isActive = 1
	)
	update s
	set s.useID = rtrt.recordTypeRelationshipTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dataTransfer.dbo.sync_vgc_supporting as sRT on sRT.orgID = @orgID and sRT.cat = 'rt' and sRT.itemID = s.itemID4
	inner join dataTransfer.dbo.sync_vgc_supporting as sRT2 on sRT2.orgID = @orgID and sRT2.cat = 'rt' and sRT2.itemID = s.itemID3
	inner join dataTransfer.dbo.sync_vgc_supporting as sRRT on sRRT.orgID = @orgID and sRRT.cat = 'rrt' and sRRT.itemID = s.itemID2
	inner join orgrtrt as rtrt on rtrt.relationshipTypeID = sRRT.useID
		and rtrt.masterRecordTypeID = sRT2.useID
		and rtrt.childRecordTypeID = sRT.useID
	where s.orgID = @orgID
	and s.cat = 'rtrt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rtrt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rtrt.', 'USEIDRTRTNULL');
		GOTO on_done;
	END

	-- get system ids for sendgrid subusers
	update s
	set s.useID = su.subuserID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.sendgrid_subusers as su on su.siteID = @siteID and su.username = s.itemType
	where s.orgID = @orgID
	and s.cat = 'sgsubuser';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'sgsubuser' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for sgsubuser.', 'USEIDSGSUBUSERNULL');
		GOTO on_done;
	END

	-- get system ids for siteID
	update s
	set s.useID = mcs.siteID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.sites as mcs on mcs.orgID = @orgID and mcs.siteCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'mcsiteid';

	-- get system id for consent list mode
	update s
	set s.useID = clm.consentListModeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentListModes as clm on clm.modeName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'clmodeid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'clmodeid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for clmodeid.', 'USEIDCLMODEIDNULL');
		GOTO on_done;
	END

	-- get system ids for consent list types
	update s
	set s.useID = clt.consentListTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentListTypes as clt on clt.orgID = @orgID 
		and clt.consentListTypeName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'cltypeid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'cltypeid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for cltypeid.', 'USEIDCLTYPEIDNULL');
		GOTO on_done;
	END

	-- get system ids for consent lists. don't limit to active consent lists here
	update s
	set s.useID = cl.consentListID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentLists as cl on cl.consentListName = s.itemType
	inner join platformMail.dbo.email_consentListTypes as clt on clt.orgID = @orgID 
		and clt.consentListTypeID = cl.consentListTypeID
	where s.orgID = @orgID
	and s.cat = 'clid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'clid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for clid.', 'USEIDCLIDNULL');
		GOTO on_done;
	END

	-- get system ids for referral statuses
	update s
	set s.useID = rs.clientReferralStatusID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_clientReferralStatus as rs on rs.statusName = s.itemType2
	inner join dbo.ref_referrals as r on r.referralID = rs.referralID
	inner join dbo.cms_applicationInstances	as ai on ai.applicationInstanceID = r.applicationInstanceID
		and ai.siteID = @siteID
	where s.orgID = @orgID
	and s.cat = 'refstatus';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'refstatus' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for refstatus.', 'USEIDREFSTATUSNULL');
		GOTO on_done;
	END
	
	-- get system ids for referral panels
	update s
	set s.useID = p.panelID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_panels as p
		inner join dbo.ref_referrals as r on r.referralID = p.referralID
		inner join dbo.cms_applicationInstances	as ai on ai.applicationInstanceID = r.applicationInstanceID
			and ai.siteID = @siteID
		left outer join dbo.ref_panels AS p2 ON p2.panelID = p.panelParentID
		on s.itemType2 = CASE WHEN p2.panelID IS NOT NULL THEN p2.[name] + ' \ ' ELSE '' END + p.[name]
	where s.orgID = @orgID
	and s.cat = 'refpanel';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'refpanel' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for refpanel.', 'USEIDREFPANELNULL');
		GOTO on_done;
	END

	-- get system ids for referral fee discrepancy statuses
	update s
	set s.useID = st.feeDiscrepancyStatusID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_feeDiscrepancyStatuses as st on st.statusName = s.itemType2
	where s.orgID = @orgID
	and s.cat = 'reffdstat';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'reffdstat' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for reffdstat.', 'USEIDREFFDSTATNULL');
		GOTO on_done;
	END

	-- get condvalue useValue for special lookups
	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType in ('acctBalMP','acctInvCOFMP','acctCCProf')
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctmp' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'acctInvProf'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctip' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'acctCCCardType'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctcctype' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'revenueGL'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctgl' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'recordType'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'rt' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'role'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'rtrt' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType in ('historyCategory','historySubCategory')
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'mh' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'emailTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'me' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'emailTagTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'met' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'sendGridSubUser'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'sgsubuser' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'suppListSiteID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'mcsiteid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListModeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'clmodeid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'cltypeid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'clid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralStatusID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'refstatus' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'panelID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'refpanel' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	WITH qryAllFields AS (
		SELECT f.fieldID, f.[uid]
		FROM membercentral.dbo.cf_fields as f
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
			AND sr.siteResourceID = f.controllingSiteResourceID
		INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
			AND ai.siteResourceID = f.controllingSiteResourceID
		INNER JOIN dbo.ref_referrals r ON r.applicationInstanceID = ai.applicationInstanceID
		WHERE f.usageID = dbo.fn_cf_getUsageID('ClientReferrals','ClientReferrals', NULL)
	)
	update c
	set c.useValue = r.fieldID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
		and sK.cat = 'ck'
		and c.conditionkeyID = sK.itemID
		and sK.itemType = 'clientReferralsCustomFieldID'
	inner join qryAllFields as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	-- setting useValue as valueID for clientReferralsCustomFieldValue just to find invalid values for the site.
	update c
	set c.useValue = fv.valueID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralsCustomFieldValue'
	inner join dataTransfer.dbo.sync_vgc_conditions as c2 on c2.orgID = @orgID and c2.conditionID = c.conditionID
	inner join dataTransfer.dbo.sync_vgc_supporting as sK2 on sK2.orgID = @orgID and sK2.cat = 'ck' and c2.conditionkeyID = sK2.itemID and sK2.itemType = 'clientReferralsCustomFieldID'
	inner join dbo.cf_fields as f on f.fieldID = c2.useValue
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	where c.orgID = @orgID
	and c.conditionvalue = coalesce(fv.valueString,cast(fv.valueInteger as varchar(10)),cast(fv.valueDecimal2 as varchar(15)),convert(varchar(12), fv.valueDate, 101),case when fv.valueBit = 1 then 'Yes' else 'No' end);

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'feeDiscrepancyStatusID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'reffdstat' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = t.typeID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subSubType'
	inner join dbo.sites as s on s.orgID = sK.orgID
	inner join dbo.sub_types as t on t.siteID = s.siteID and cast(t.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = s.subscriptionID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subSubscription'
	inner join membercentral.dbo.sub_subscriptions as s on s.orgID = @orgID and cast(s.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllRates AS (
		SELECT r.rateID, r.[uid]
		FROM membercentral.dbo.sub_rates r
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID
	)
	update c
	set c.useValue = r.rateID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subRate'
	inner join qryAllRates as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = f.frequencyID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subFrequency'
	inner join membercentral.dbo.sub_frequencies as f on f.siteID = @siteID and cast(f.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllPrograms AS (
		select p.programID, p.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
	)
	update c
	set c.useValue = p.programID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programList'
	inner join qryAllPrograms as p on cast(p.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllRates AS (
		select r.rateID, r.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
		INNER JOIN membercentral.dbo.cp_rates as r on r.programID = p.programID
	)
	update c
	set c.useValue = r.rateID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programRate'
	inner join qryAllRates as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllDistrib AS (
		select d.distribID, d.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
		INNER JOIN membercentral.dbo.cp_distributions as d on d.programID = p.programID
	)
	update c
	set c.useValue = r.distribID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programDistribution'
	inner join qryAllDistrib as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllFields AS (
		select f.fieldID, f.[uid]
		FROM membercentral.dbo.cf_fields as f
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = f.controllingSiteResourceID
	)
	update c
	set c.useValue = r.fieldID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
		and sK.cat = 'ck'
		and c.conditionkeyID = sK.itemID
		and sK.itemType in ('programMonetaryCustomField','programNonMonetaryCustomField')
	inner join qryAllFields as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = f.frequencyID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programFrequency'
	inner join membercentral.dbo.cp_frequencies as f on f.siteID = @siteID and cast(f.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	-- setting useValue as valueID for programNonMonetaryCustomFieldValue just to find invalid values for the site.
	update c
	set c.useValue = fv.valueID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programNonMonetaryCustomFieldValue'
	inner join dataTransfer.dbo.sync_vgc_conditions as c2 on c2.orgID = @orgID and c2.conditionID = c.conditionID
	inner join dataTransfer.dbo.sync_vgc_supporting as sK2 on sK2.orgID = @orgID and sK2.cat = 'ck' and c2.conditionkeyID = sK2.itemID and sK2.itemType = 'programNonMonetaryCustomField'
	inner join dbo.cf_fields as f on f.fieldID = c2.useValue
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	where c.orgID = @orgID
	and c.conditionvalue = coalesce(fv.valueString,cast(fv.valueInteger as varchar(10)),cast(fv.valueDecimal2 as varchar(15)),convert(varchar(12), fv.valueDate, 101),case when fv.valueBit = 1 then 'Yes' else 'No' end);

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_conditions as c
			inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID 
			where c.orgID = @orgID
			and c.useValue is null
			and nullif (c.valueUID,'') is not null
			and sK.itemType in ('subSubType','subSubscription','subRate','subFrequency','programList','programRate',
				'programDistribution','programMonetaryCustomField','programNonMonetaryCustomField','clientReferralsCustomFieldID')
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('No matching UIDs found.', 'UNMATCHEDUID');
		GOTO on_done;
	END

	-- checking valid field values for client referrals / contribution programs only after custom field uid matching for both, so error message is displayed in right order
	IF EXISTS (
		select 1
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
			and sK.cat = 'ck'
			and c.conditionkeyID = sK.itemID
			and sK.itemType = 'clientReferralsCustomFieldValue'
		where c.orgID = @orgID
		and nullif(c.conditionvalue,'') is not null
		and c.useValue is null
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in table for clientReferralsCustomFieldValue.', 'USEVALUEREFCFVALNULL');
		GOTO on_done;
	END

	-- updating useValue back to null so the conditionvalue will be used for ams_virtualGroupConditionValues entries, because this field uses values rather than ids.
	update c
	set c.useValue = NULL
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralsCustomFieldValue'
	where c.orgID = @orgID;

	IF EXISTS (
		select 1
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
			and sK.cat = 'ck'
			and c.conditionkeyID = sK.itemID
			and sK.itemType = 'programNonMonetaryCustomFieldValue'
		where c.orgID = @orgID
		and nullif(c.conditionvalue,'') is not null
		and c.useValue is null
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in table for programNonMonetaryCustomFieldValue.', 'USEVALUECPCFVALNULL');
		GOTO on_done;
	END

	-- updating useValue back to null so the conditionvalue will be used for ams_virtualGroupConditionValues entries, because this field uses values rather than ids.
	update c
	set c.useValue = NULL
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programNonMonetaryCustomFieldValue'
	where c.orgID = @orgID;

	-- update remaining with allvalueids
	update c
	set c.useValue = v.useValue
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_allvalueids as v on v.orgID = @orgID and v.conditionID = c.conditionID
		and cast(v.valueID as varchar(10)) = c.conditionValue
	where c.orgID = @orgID
	and c.useValue is null;

	-- get rule groupIDs
	update rg
	set rg.useGroupID = g.groupID
	from dataTransfer.dbo.sync_vgc_allrulegroups as rg
	inner join dbo.ams_groups as g on g.orgID = @orgID 
		and g.uid = rg.uid 
		and g.isProtected = 0 
		and g.isSystemGroup = 0
	where rg.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allrulegroups where orgID = @orgID and useGroupID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Invalid groups in allrulegroups.', 'INVALIDRULEGRP');
		GOTO on_done;
	END

	-- inactivate event conditions and rules using event conditions
	update dataTransfer.dbo.sync_vgc_conditions
	set finalAction = 'I'
	where orgID = @orgID
	and fieldCode = 'ev_entry';

	update r
	set r.finalAction = 'I'
	from dataTransfer.dbo.sync_vgc_rules as r
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.orgID = r.orgID and c.finalAction = 'I'
	where r.orgID = @orgID
	and cast(r.ruleXML as xml).exist('//condition[@id=sql:column("c.uid")]') = 1

	-- new conditions
	update c
	set c.finalAction = 'A'
	from dataTransfer.dbo.sync_vgc_conditions as c
	left outer join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID 
		and vgc.uid = c.uid
		and vgc.conditionTypeID = 1
	where c.orgID = @orgID
	and vgc.conditionID is null;

	-- find conditions to be updated
	INSERT INTO #tmpOrgConditionsChanged
	select distinct [uid]
	from (
		select c.[uid], c.datatypeid, c.displaytypeid, c.expressionid, c.[datepart], c.dateexpressionid, c.[verbose], af.useFieldCode, 
			sCK.itemType as conditionkey, coalesce(cast(c.useValue as varchar(10)),c.conditionvalue) COLLATE Latin1_General_CS_AS as conditionValue, isnull(sAF.useID,0) as afid
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join datatransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
		left outer join dataTransfer.dbo.sync_vgc_supporting as sCK on sCK.orgID = @orgID and sCK.cat = 'ck' and sCK.itemID = c.conditionkeyID
		left outer join dataTransfer.dbo.sync_vgc_supporting as sAF on sAF.orgID = @orgID and sAF.cat = 'af' and sAF.itemID = c.afid
		where c.orgID = @orgID
		and c.finalAction is null
			except
		select [uid], datatypeid, displaytypeid, expressionid, [datepart], dateexpressionid, [verbose], fieldCode, 
			conditionKey, conditionvalue, afid
		from #tmpOrgGrpConditions
		where fieldCode <> 'ev_entry'
	) tmp;

	-- flip and test incase the org has more condition values
	INSERT INTO #tmpOrgConditionsChanged
	select distinct [uid]
	from (
		select [uid], datatypeid, displaytypeid, expressionid, [datepart], dateexpressionid, [verbose], fieldCode, 
			conditionKey, conditionvalue, afid
		from #tmpOrgGrpConditions
		where fieldCode <> 'ev_entry'
			except
		select c.[uid], c.datatypeid, c.displaytypeid, c.expressionid, c.[datepart], c.dateexpressionid, c.[verbose], af.useFieldCode, 
			sCK.itemType as conditionkey, coalesce(cast(c.useValue as varchar(10)),c.conditionvalue) COLLATE Latin1_General_CS_AS as conditionValue, isnull(sAF.useID,0) as afid
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join datatransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
		left outer join dataTransfer.dbo.sync_vgc_supporting as sCK on sCK.orgID = @orgID and sCK.cat = 'ck' and sCK.itemID = c.conditionkeyID
		left outer join dataTransfer.dbo.sync_vgc_supporting as sAF on sAF.orgID = @orgID and sAF.cat = 'af' and sAF.itemID = c.afid
		where c.orgID = @orgID
		and c.finalAction is null
	) tmp
	where not exists (select 1 from #tmpOrgConditionsChanged where [uid] = tmp.[uid]);

	-- update action
	update c
	set c.finalAction = 'C'
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join #tmpOrgConditionsChanged as tmp on tmp.uid = c.uid
	where c.orgID = @orgID 
	and c.finalAction is null;
	
	-- new rules
	update r
	set r.finalAction = 'A'
	from datatransfer.dbo.sync_vgc_rules as r
	left outer join dbo.ams_virtualGroupRules as vgr on vgr.orgID = @orgID 
		and vgr.uid = r.uid
		and vgr.ruleTypeID = 1
	where r.orgID = @orgID
	and vgr.ruleID is null
	and r.finalAction is null;

	-- find rules to be updated
	INSERT INTO #tmpOrgGrpRulesChanged
	select distinct [uid]
	from (
		select r.uid, r.rulename, r.rulexml, r.isActive, rg.useGroupID as groupid
		from dataTransfer.dbo.sync_vgc_rules as r
		left outer join dataTransfer.dbo.sync_vgc_allrulegroups as rg on rg.orgID = @orgID and rg.groupid = r.groupid
		where r.orgID = @orgID
		and r.finalAction is null
			except
		select uid, rulename, rulexml, isActive, groupid
		from #tmpOrgGrpRules
	) tmp;

	-- flip and test incase the org has more rule groups
	INSERT INTO #tmpOrgGrpRulesChanged
	select distinct [uid]
	from (
		select uid, rulename, rulexml, isActive, groupid
		from #tmpOrgGrpRules
			except
		select r.uid, r.rulename, r.rulexml, r.isActive, rg.useGroupID as groupid
		from dataTransfer.dbo.sync_vgc_rules as r
		left outer join dataTransfer.dbo.sync_vgc_allrulegroups as rg on rg.orgID = @orgID and rg.groupid = r.groupid
		where r.orgID = @orgID
		and r.finalAction is null
	) tmp
	where not exists (select 1 from #tmpOrgGrpRulesChanged where [uid] = tmp.[uid]);

	-- update action
	update r
	set r.finalAction = 'C'
	from dataTransfer.dbo.sync_vgc_rules as r
	inner join #tmpOrgGrpRulesChanged as tmp on tmp.uid = r.uid
	where r.orgID = @orgID
	and r.finalAction is null;


	-- to be deleted conditions
	INSERT INTO #tmpOrgDeleteGrpConditions (uid, verbose)
	select distinct c.[uid], c.verbose
	from dbo.ams_virtualGroupConditions as c
	left outer join dataTransfer.dbo.sync_vgc_conditions as sc on sc.orgID = @orgID and sc.uid = c.uid
	where c.orgID = @orgID
	and c.conditionTypeID = 1
	and sc.conditionID is null;

	-- to be deleted rules
	INSERT INTO #tmpOrgDeleteGrpRules (uid, rulename)
	select distinct r.[uid], r.rulename
	from dbo.ams_virtualGroupRules as r
	left outer join dataTransfer.dbo.sync_vgc_rules as sr on sr.orgID = @orgID and sr.uid = r.uid
	where r.orgID = @orgID
	and r.ruleTypeID = 1
	and sr.rulename is null;


	on_done:
	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('condition'), root('newconditions'), type),'<newconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('condition'), root('updateconditions'), type),'<updateconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'I'
			FOR XML path('condition'), root('ignoreconditions'), type),'<ignoreconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from #tmpOrgDeleteGrpConditions
			FOR XML path('condition'), root('removeconditions'), type),'<removeconditions/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('rule'), root('newrules'), type),'<newrules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('rule'), root('updaterules'), type),'<updaterules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'I'
			FOR XML path('rule'), root('ignorerules'), type),'<ignorerules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from #tmpOrgDeleteGrpRules
			FOR XML path('rule'), root('removerules'), type),'<removerules/>'),
			
			isnull((select dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", errorCode as "@errorcode"
			from #tblImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')

		for xml path('import'), TYPE);


	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgConditionsChanged') IS NOT NULL 
		DROP TABLE #tmpOrgConditionsChanged;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRules;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRulesChanged') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRulesChanged;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpRules;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
