USE seminarweb
GO

drop proc dbo.sw_getCreditBases
GO 

ALTER PROC dbo.sw_getCreditAuthority
@authorityID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT ca.authorityID, ca.code, ca.jurisdiction, ca.authorityName, ca.contact, 
		ca.address, ca.city, ca.state, ca.ZIP, ca.phone, ca.email, ca.website, 
		ca.wddxCreditTypes, ca.creditIDText, 
		caswl.promptInterval as SWLpromptInterval, 
		caswl.promptTypeID as SWLpromptTypeID, 
		caswl.mustAttend as SWLmustAttend, 
		caswl.mustAttendMinutes as SWLmustAttendMinutes,
		caswl.preExamRequired as SWLPreExamRequired,
		caswl.examRequired as SWLexamRequired,
		caswl.evaluationRequired as SWLEvaluationRequired,
		caswl.daysToCompleteExam as SWLdaysToCompleteExam,
		caswl.daysToCompleteEvaluation as SWLdaysToCompleteEvaluation,		
		casod.promptInterval AS swodpromptInterval, 
		casod.promptTypeID AS swodpromptTypeID, 
		casod.preExamRequired AS swodPreExamRequired, 
		casod.examRequired AS swodexamRequired, 
		casod.evaluationRequired AS swodevaluationRequired, 
		casod.mediaRequiredPct AS swodmediaRequiredPct,  
		casod.daysToComplete AS swoddaysToComplete,
		casod.mustAttendMinutes as swodMustAttendMinutes
	FROM dbo.tblCreditAuthorities AS ca 
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON ca.authorityID = caswl.authorityID 
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON ca.authorityID = casod.authorityID
	WHERE ca.authorityID = @authorityID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sw_getCreditsforSeminar
@seminarID int,
@siteCode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- get seminar type
	DECLARE @SWType varchar(4);
	SELECT @SWType = CASE
		WHEN sswod.ondemandid is null then 'SWL'
		WHEN sswl.liveid is null then 'SWOD'
		ELSE ''
		END
		FROM dbo.tblSeminars as s
		LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.seminarID
		WHERE s.seminarID = @seminarID;

	IF @SWType = 'SWOD'
		SELECT sac.seminarCreditID, ca.authorityID, ca.code AS authorityCode, ca.authorityName, 
			ca.jurisdiction AS authorityJurisdiction, sac.creditOfferedEndDate, sac.wddxCreditsAvailable, cs.sponsorName, 
			cs.statementAppProvider, cs.statementAppProgram, cs.statementPendProgram, cstat.status, 
			ca.website, ca.wddxCreditTypes, ca.creditIDText, sac.isCreditRequired, 
			sac.isIDRequired, sac.isCreditDefaulted, csa.creditMessage, s.seminarName
		FROM dbo.tblSeminarsAndCredit AS sac 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditAuthoritiesSWOD AS caswod ON caswod.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
		INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = sac.seminarID
		WHERE sac.seminarID = @seminarID
		AND cstat.status IN ('Approved', 'Pending','Self-Submitting')
		AND getdate() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
		ORDER BY case when cs.orgcode = @siteCode then 1 else 0 end desc, authorityCode, cs.sponsorName;

	ELSE
		SELECT sac.seminarCreditID, ca.authorityID, ca.code AS authorityCode, ca.authorityName, 
			ca.jurisdiction AS authorityJurisdiction, sac.wddxCreditsAvailable, cs.sponsorName, 
			cs.statementAppProvider, cs.statementAppProgram, cs.statementPendProgram, cstat.status, 
			ca.website, ca.wddxCreditTypes, ca.creditIDText, sac.isCreditRequired, 
			sac.isIDRequired, sac.isCreditDefaulted, csa.creditMessage, s.seminarName
		FROM dbo.tblSeminarsAndCredit AS sac 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
		INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
		INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = sac.seminarID
		WHERE sac.seminarID = @seminarID
		AND cstat.status IN ('Approved', 'Pending','Self-Submitting')
		ORDER BY case when cs.orgcode = @siteCode then 1 else 0 end desc, authorityCode, cs.sponsorName;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swl_getSeminarSettingsBasedOnCreditSelections
@enrollmentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select 
		isnull(min(promptInterval),0) as promptInterval,
		isnull(min(promptTypeID),1) as promptTypeID,
		isnull(max(preExamRequired),0) as preExamRequired,
		isnull(max(examRequired),0) as examRequired,
		isnull(max(evaluationRequired),0) as evaluationRequired,
		isnull(max(mustAttendMinutes),0) as mustAttendMinutes
	FROM (
		SELECT csaa.authorityID, 
			cast(caswl.preexamRequired as smallint) as preexamRequired,
			cast(caswl.examRequired as smallint) as examRequired,
			cast(caswl.evaluationRequired as smallint) as evaluationRequired,
			caswl.promptInterval, caswl.promptTypeID, caswl.mustAttendMinutes
		FROM dbo.tblEnrollmentsAndCredit AS eac 
		INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csaa ON sac.CSALinkID = csaa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON csaa.authorityID = caswl.authorityID
		WHERE eac.enrollmentID = @enrollmentID
	) as authBest;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swod_getSeminarSettingsBasedOnCreditSelections
@enrollmentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select 
		isnull(min(promptInterval),0) as promptInterval,
		isnull(min(promptTypeID),1) as promptTypeID,
		isnull(max(preExamRequired),0) as preExamRequired,
		isnull(max(examRequired),0) as examRequired,
		isnull(max(evaluationRequired),0) as evaluationRequired,
		isnull(max(mediaRequiredPct),0) as mediaRequiredPct,
		isnull(max(mustAttendMinutes),0) as mustAttendMinutes
	FROM (
		SELECT csaa.authorityID, 
			cast(casod.preexamRequired as smallint) as preexamRequired,
			cast(casod.examRequired as smallint) as examRequired,
			cast(casod.evaluationRequired as smallint) as evaluationRequired,
			casod.promptInterval, casod.promptTypeID, casod.mediaRequiredPct, 
			casod.mustAttendMinutes
		FROM dbo.tblEnrollmentsAndCredit AS eac 
		INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
		INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csaa ON sac.CSALinkID = csaa.CSALinkID 
		INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON csaa.authorityID = casod.authorityID
		WHERE eac.enrollmentID = @enrollmentID
	) as authBest;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swod_getEnrollmentCreditXML
@enrollmentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select (
		select authorityName, lastDateToComplete 
		from (
			SELECT tblCreditAuthorities.authorityName, tblEnrollmentsAndCredit.lastDateToComplete
			FROM dbo.tblSeminars 
			INNER JOIN dbo.tblEnrollments ON tblSeminars.seminarID = tblEnrollments.seminarID
			INNER JOIN dbo.tblEnrollmentsAndCredit ON tblEnrollments.enrollmentID = tblEnrollmentsAndCredit.enrollmentID 
			INNER JOIN dbo.tblSeminarsAndCredit ON tblSeminars.seminarID = tblSeminarsAndCredit.seminarID 
				AND tblEnrollmentsAndCredit.seminarCreditID = tblSeminarsAndCredit.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities ON tblSeminarsAndCredit.CSALinkID = tblCreditSponsorsAndAuthorities.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities ON tblCreditSponsorsAndAuthorities.authorityID = tblCreditAuthorities.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD ON tblCreditAuthorities.authorityID = tblCreditAuthoritiesSWOD.authorityID 
			where tblEnrollments.enrollmentid = @enrollmentID
		) as EnrollmentCredit
		order by lastDateToComplete, authorityName
		FOR XML auto,elements,type,root('EnrollmentCredits')
	) as enrollmentCreditXML;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.swod_recordCompletion
@enrollmentID int,
@userTimeSpent int,
@realTimeSpent int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @siteSRID int, @seminarID int, @registrantName varchar(300), @dataXML xml;
	DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

	if exists (select enrollmentID from dbo.tblenrollments where enrollmentID = @enrollmentID and dateCompleted is not null)
		RAISERROR('Already enrolled.',16,1);

	SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @seminarID = s.seminarID
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	WHERE e.enrollmentID = @enrollmentID;

	SELECT @siteSRID = siteResourceID from membercentral.dbo.sites where siteID = @siteID;

	BEGIN TRAN;
		-- update master enrollment
		UPDATE dbo.tblenrollments
		set dateCompleted = getdate(), passed = 1
		where enrollmentID = @enrollmentID;

		-- get date for now and when enrolled in course
		-- no matter the creditbase, time cannot be more than time from dateenrolled to now
		DECLARE @dateEnrolled datetime, @timepassed int;
		SELECT @dateEnrolled = dateEnrolled FROM dbo.tblEnrollments where enrollmentID = @enrollmentID;
		SELECT @timepassed = dateDiff(minute,@dateEnrolled,getdate());

		-- temp holder table for times
		DECLARE @tmpTimes TABLE (autoid int, timepassed int);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (1, @timepassed);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (2, @userTimeSpent);
		INSERT INTO @tmpTimes (autoid,timepassed) VALUES (3, @realTimeSpent);

		-- update final time spent column with realTimeSpent
		UPDATE dbo.tblEnrollmentsAndCredit
		SET finalTimeSpent = (select min(timepassed) from @tmpTimes where autoid in (1,3))
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID IN (
			SELECT eac.enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit AS eac 
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON csa.authorityID = casod.authorityID 
			WHERE eac.enrollmentID = @enrollmentID
		);

		UPDATE tblEnrollmentsSWOD
		SET calcTimeSpent = (select min(timepassed) from @tmpTimes where autoid in (1,3))
		WHERE enrollmentID = @enrollmentID;

		-- update any non-expired credits
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 1
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID NOT IN (
			SELECT eac.enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit AS eac 
			INNER JOIN dbo.tblEnrollments as e on e.enrollmentID = eac.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csaa ON sac.CSALinkID = csaa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csaa.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON casod.authorityID = ca.authorityID
			WHERE e.enrollmentID = @enrollmentID
			AND getdate() > eac.lastDateToComplete
		);

		IF @@ROWCOUNT > 0 BEGIN
			SELECT @dataXML = 
				ISNULL((
					SELECT 'semweb' AS itemtype, @enrollmentID AS itemid for xml path ('data')
				),'<data/>');

			INSERT INTO @tblHookListeners (executionType, objectPath)
			EXEC memberCentral.dbo.hooks_runHook @event='creditAwarded', @siteResourceID=@siteSRID, @dataXML=@dataXML;
		END
	COMMIT TRAN;

	-- process conditions based on seminarweb
	DECLARE @depomemberdataID int;
	SET @depomemberdataID = dbo.fn_getDepoMemberDataIDFromEnrollmentID(@enrollmentID);
	EXEC membercentral.dbo.ams_processSeminarWebConditionsByDepoMemberDataID @depomemberdataID=@depomemberdataID;

	SELECT @registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) + ']' + ISNULL(' (' + m2.membernumber + ')','')
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	LEFT OUTER JOIN membercentral.dbo.ams_members AS m
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
	ON m.memberID = e.MCMemberID
	WHERE e.enrollmentID = @enrollmentID;
	
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWOD-' + CAST(@seminarID AS VARCHAR(10)) + ' has been marked as completed for registrant '+ @registrantName + '.'),'"','\"') + '" } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- drop the tblCreditBases table
ALTER TABLE dbo.tblCreditAuthoritiesSWOD DROP FK_tblCreditAuthoritiesSWOD_tblCreditBases
GO
ALTER TABLE dbo.tblCreditAuthoritiesSWLive DROP FK_tblCreditAuthoritiesSWLive_tblCreditBases
GO
DROP TABLE dbo.tblCreditBases;
GO

ALTER TABLE dbo.tblCreditAuthoritiesSWOD DROP CONSTRAINT DF_tblCreditAuthoritiesSWOD_creditBaseID
GO
ALTER TABLE dbo.tblCreditAuthoritiesSWOD DROP COLUMN creditBaseID;
GO
ALTER TABLE dbo.tblCreditAuthoritiesSWLive DROP CONSTRAINT DF_tblCreditAuthoritiesSWLive_creditBaseID
GO
ALTER TABLE dbo.tblCreditAuthoritiesSWLive DROP COLUMN creditBaseID;
GO
