USE membercentral
GO

ALTER TABLE dbo.siteFeatures ADD subsAddV2 bit NOT NULL CONSTRAINT [DF_siteFeatures_subsAddV2] DEFAULT(0);
GO

ALTER PROC dbo.enableSiteFeature
@siteID int,
@toolTypeList varchar(1000)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @GLAccountID int, @sysMemberID int, @subscriptionAdminSiteResourceID int, @languageID int,
		@rootSectionID int, @applicationTypeID int, @zoneID int, @pgResourceTypeID int, @subscriptionsPageID int,
		@subscriptionsSiteResourceID int, @subscriptionsTitle varchar(50), @applicationInstanceID int;
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

	declare @tblTools TABLE (toolType varchar(100));
	insert into @tblTools (toolType)
	select listItem from dbo.fn_varCharListToTable(@toolTypeList,',');

	declare @toolType varchar(100);
	SELECT @toolType = min(toolType) from @tblTools;
	WHILE @toolType is not null BEGIN

		-- APIAccess
		if @toolType = 'APIAccess'
			update dbo.siteFeatures
			set mcAPI = 1
			where siteID = @siteID
			and mcAPI = 0;

		-- contributions
		if @toolType = 'ContributionAdmin' begin
			update dbo.siteFeatures
			set contributions = 1
			where siteID = @siteID
			and contributions = 0;

			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='PLEDGESRECEIVABLE', @GLAccountID=@GLAccountID OUTPUT;

			IF @GLAccountID IS NULL
				EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Pledges Receivable', @accountCode='', @GLCode='PLEDGESRECEIVABLE', 
					@parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, 
					@salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@sysMemberID, @GLAccountID=@GLAccountID output;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- email blast
		if @toolType = 'EmailBlast' begin
			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- member documents
		if @toolType = 'MemberDocs'
			update dbo.siteFeatures
			set memberDocuments = 1
			where siteID = @siteID
			and memberDocuments = 0;

		-- member history
		if @toolType = 'MemberHistoryAdmin'
			update dbo.siteFeatures
			set memberHistory = 1
			where siteID = @siteID
			and memberHistory = 0;

		-- relationships
		if @toolType = 'RelationshipAdmin' begin
			update dbo.siteFeatures
			set relationships = 1
			where siteID = @siteID
			and relationships = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- reports
		if @toolType = 'Reports' begin
			declare @siteCode varchar(10);
			select @sitecode = sitecode from dbo.sites where siteID = @siteID;

			insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
			select tooltypeid, @siteID 
			from dbo.admin_toolTypes 
			where (includeInAllReportsGrid = 1 or tooltype in ('rpt_ReportSettings','rpt_SavedReports')) 
			and tooltype not in (
				'AcctCreditBalancesReport',
				'AcctFailedPaymentReport',
				'AcctGrossSalesReport',
				'AcctInvoiceAgingReport',
				'AcctNegativeWriteOffsReport',
				'AcctPaymentReport',
				'AcctPendingPaymentReport',
				'AcctReconciliationsReport',
				'AcctTransactionReport',
				'AcctVoidTransactionReport',
				'AcctWriteOffsReport',
				'LiveWebinarRegistrantsAwardedCredit',
				'OnDemandExpiringCredit',
				'OnDemandExpiringFromCatalog',
				'OnDemandRegistrantsAwardedCredit',
				'OnDemandRegistrantsEnrollment',
				'UTMRevenueReport',
				'UTMRevenueTSReport'
			) and (
				left(toolCFC,15) <> 'Reports.custom.' 
				OR toolCFC like 'Reports.custom.' + @sitecode + '.%'
			)
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- Badges
		if @toolType = 'BadgeDeviceAdmin'
			update dbo.siteFeatures
			set badgePrinters = 1
			where siteID = @siteID
			and badgePrinters = 0;

		-- subscriptions
		if @toolType = 'SubscriptionAdmin' begin
			update dbo.siteFeatures
			set subscriptions = 1
			where siteID = @siteID
			and subscriptions = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = 'SubRenewalAdmin'
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;

			-- add/update default FULL Frequency
			if not exists(select frequencyID from dbo.sub_frequencies where frequencyShortName = 'F' and siteID = @siteID and status = 'A')
				insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
					rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
				values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A');
			else
				update dbo.sub_frequencies 
				set isSystemRate = 1 
				where frequencyShortName = 'F' 
					and siteID = @siteID 
					and status = 'A';
		end

		-- Subscriptions Renew V2
		if @toolType = 'EnableSubscriptionAddV2'
			update dbo.siteFeatures
			set subsAddV2 = 1
			where siteID = @siteID
			and subsAddV2 = 0;

		-- tasks
		if @toolType = 'TaskAdmin' begin
			update dbo.siteFeatures
			set tasks = 1
			where siteID = @siteID
			and tasks = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		if @toolType = 'EnableSitePasswords'
			EXEC dbo.enableSiteFeature_sitePasswords @siteID=@siteID;
			
		-- ads
		if @toolType = 'AdsAdmin' begin
			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- Referrals SMS
		if @toolType = 'referralsSMS'
			update dbo.siteFeatures
			set referralsSMS = 1
			where siteID = @siteID
			and referralsSMS = 0;

		-- Recurring Events
		if @toolType = 'RecurringEvents'
			update dbo.siteFeatures
			set recurringEvents = 1
			where siteID = @siteID
			and recurringEvents = 0;
		
		SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType;
	END

	-- refresh and assign resources
	exec dbo.createAdminSuite @siteID=@siteid;

	-- set cache siteResourceIDs once admin tool resource for SubscriptionAdmin is created
	IF EXISTS (SELECT 1 FROM @tblTools WHERE toolType = 'SubscriptionAdmin') BEGIN
		SELECT @subscriptionAdminSiteResourceID = sr.siteResourceID
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = sr.resourceTypeID
			AND srt.resourceType = 'subscriptionAdmin'
		WHERE sr.siteID = @siteID
		AND sr.siteResourceStatusID = 1;

		-- create front-end subscriptions app instance
		SELECT @languageID = defaultLanguageID from dbo.sites where siteID = @siteID;
		SELECT @rootSectionID = dbo.fn_getRootSectionID(@siteID);
		SET @zoneID = dbo.fn_getZoneID('Main');
		SET @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
		SET @subscriptionsTitle = 'Manage Membership';

		SELECT @applicationTypeID = applicationTypeID
		FROM dbo.cms_applicationTypes
		WHERE applicationTypeName = 'subscriptions';

		EXEC dbo.cms_createApplicationInstance @siteID=@siteID, @languageID=@languageID, @sectionID=@rootSectionID, @applicationTypeID=@applicationTypeID,
			@isVisible=1, @pageName='manageSubscriptions', @pageTitle=@subscriptionsTitle, @pagedesc=@subscriptionsTitle, @zoneID=@zoneID, @pageTemplateID=NULL,
			@pageModeID=NULL, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=NULL, @allowReturnAfterLogin=1, @applicationInstanceName=@subscriptionsTitle, 
			@applicationInstanceDesc=@subscriptionsTitle, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteResourceID=@subscriptionsSiteResourceID OUTPUT,
			@pageID=@subscriptionsPageID OUTPUT;

		UPDATE dbo.sites
		SET subscriptionAdminSiteResourceID = @subscriptionAdminSiteResourceID,
			subscriptionsSiteResourceID = @subscriptionsSiteResourceID
		WHERE siteID = @siteID;
	END

	IF EXISTS (select toolType from @tblTools where toolType = 'BadgeDeviceAdmin')
		EXEC dbo.cms_createDefaultBadgeTemplateCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
		EXEC dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID;

	IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
		EXEC dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
		EXEC dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'Reports')
		EXEC dbo.ams_createDefaultExtendedNameFieldset @siteID=@siteID;

	IF EXISTS (select toolType from @tblTools where toolType = 'SubscriptionAdmin')
		EXEC dbo.cms_createDefaultSubscriptionAdminFieldSets @siteID=@siteID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.site_getSiteInfo
@environmentName varchar(50),
@internalURL varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @environmentID int, @defaultTemplateID int;
	select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

	select @defaultTemplateID = templateID
	from dbo.cms_pageTemplates 
	where siteID is null 
	and templateFilename = 'DefaultTemplate'
	and [status] = 'A';
	
	select s.siteid, s.GA4MeasurementID, s.GTMContainerID, o.orgid, s.defaultLanguageID, s.defaultTimeZoneId, s.allowGuestAccounts, s.forceLoginPage, 
		s.affiliationRequired, s.immediateMemberUpdates, s.emailMemberUpdates,
		s.siteCode, s.siteName, s.defaultPostalState, s.joinURL, s.alternateGuestAccountCreationLink, 
		s.alternateGuestAccountPopup, s.alternateForgotPasswordLink, s.defaultAdminEmails,
		o.orgcode, oi.organizationName as orgname, oi.organizationShortName as orgshortname, oi.XUserName, o.useBatches, o.defaultPending, o.memNumPrefixGuest, o.memNumPrefixUser, o.accountingEmail, 
		o.sysMemberID, o.publicGroupPrintID, o.notifyBadCOF, o.notifyBadCOFMessage, o.userTokenSecret, o.useAccrualAcct,
		s.noRightsContentID, s.noRightsNotLoggedInContentID, s.firstTimeLoginContentID, 
		s.siteAgreementContentID, l.languageCode as defaultLanguageCode, s.enforceSiteAgreement, s.inactiveUserContentID,
		s.defaultCurrencyTypeID, ct.CurrencyType as defaultCurrencyType, s.showCurrencyType, s.siteResourceID as siteSiteResourceID, 
		s.memberAdminSiteResourceID, s.subscriptionAdminSiteResourceID, s.subscriptionsSiteResourceID,
		s.alternateUpdateMemberLink, sh.hostname as mainHostname, s.UserWayAccountCode,
		sh.hasssl, scheme = case when sh.hasssl = 1 then 'https' else 'http' end,
		n.networkID as loginNetworkID, n.networkName as loginNetworkName, n.networkCode as loginNetworkCode,
		n.supportProviderEmail, n.emailFrom as networkEmailFrom, n.supportProviderPhone, n.supportProviderName, 
		s.defaultCountryID, s.enableMobile, s.enableDeviceDetection, s.enableAdd2Home, s.defaultOrgIdentityID, s.loginOrgIdentityID,
		ps.ovTemplateID as defaultTemplateID, ps.ovTemplateIDMobile as defaultTemplateIDMobile,
		pt.siteResourceID as defaultTemplateSiteResourceID, ptmobile.siteResourceID as defaultTemplateSiteResourceIDMobile,
		s.showHomepageWarning,s.homePageWarningContentID, homePageWarning.rawContent as homePageWarningContent,
		s.customHeadContentID, customHead.rawContent as customHeadContent,
		s.deliveryPolicyURL, s.privacyPolicyURL, s.rrPolicyURL, s.tcURL,
		(select top 1 ai.siteResourceID
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			where ai.siteID = s.siteid
			and at.applicationTypeName = 'Ajax') as ajaxAppInstanceID,
		(select top 1 ai.siteResourceID
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			where ai.siteID = s.siteid
			and at.applicationTypeName = 'appProxy') as appProxyAppInstanceID,
		(select top 1 groupID
			from dbo.ams_groups
			where orgID = s.orgID
			and groupCode = 'SiteAdmins') as adminGroupID,
		usesMenuSystem = case when exists (
							select mu.usedBySiteResourceID
							from dbo.cms_menus as menu
							inner join dbo.cms_menuUsages mu on mu.menuID = menu.menuID
							inner join dbo.cms_siteResources sr on sr.siteResourceID = mu.usedBySiteResourceID and sr.siteID = s.siteID
							where menu.siteID = s.siteID
							)
							then 1
						else 0
						end,
		case when swp.isSWL = 1 then swp.brandSWLTab else '' end as swlBrand,
		case when swp.isSWOD = 1 then swp.brandSWODTab else '' end as swodBrand,
		isnull(swp.isSWCP,0) as isSWCP,
		case when swp.isSWCP = 1 then 'Certificate Programs' else '' end as swcpBrand,
		swp.brandBundleTab as swbBrand,
		internalAssetsURL = replace(@internalURL,'*SITECODE*',s.siteCode) + 'assets/' + o.orgcode + '/' + s.siteCode + '/',
		sllm.loginLimitModeCode, s.useRemoteLogin, s.useRemoteLoginForm, s.remoteLoginFormURL, s.dropboxAppKey,
		sf.memberDocuments as sf_memberDocuments, sf.tasks as sf_tasks, sf.relationships as sf_relationships, 
		sf.memberHistory as sf_memberHistory, sf.subscriptions as sf_subscriptions, sf.contributions as sf_contributions,
		sf.mcAPI as sf_mcAPI, sf.badgePrinters as sf_badgePrinters,
		sf.sitePasswords as sf_sitePasswords, sf.subsAddV2 as sf_subsAddV2,
		hasReferrals = case 
			when exists (
				select referralID 
				from dbo.ref_referrals rr
				inner join dbo.cms_applicationInstances AS ai on rr.applicationInstanceID = ai.applicationInstanceID and ai.siteID = s.siteID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = s.siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			) then 1
			else 0
			end,
		s.defaultConsentListID, s.subscriptionIssuesEmail, sf.referralsSMS, sf.recurringEvents as sf_recurringEvents
	from dbo.sites as s
	inner join dbo.siteFeatures as sf on sf.siteID = s.siteID
	inner join dbo.cms_pageSections ps on ps.siteID = s.siteID and ps.sectionCode = 'root' and ps.parentSectionID is null
	inner join dbo.cms_pageTemplates pt on pt.templateID = isnull(ps.ovTemplateID,@defaultTemplateID)
	inner join dbo.organizations as o on o.orgid = s.orgid
	inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = s.defaultOrgIdentityID
	inner join dbo.cms_languages as l on l.languageID = s.defaultLanguageID
	inner join dbo.currencyTypes as ct on ct.currencyTypeID = s.defaultCurrencyTypeID
	inner join dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
	inner join dbo.networks as n on n.networkID = ns.networkID
	inner join dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
	inner join dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
	inner join dbo.siteLoginLimitModes sllm on sllm.loginLimitModeID = s.loginLimitModeID
	left outer join dbo.cms_pageTemplates as ptmobile on ptmobile.templateID = ps.ovTemplateIDMobile
	left outer join seminarweb.dbo.tblParticipants as swp on swp.orgcode = s.sitecode
	CROSS APPLY dbo.fn_getContent(s.homePageWarningContentID,1) as homePageWarning
	CROSS APPLY dbo.fn_getContent(s.customHeadContentID,1) as customHead;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO