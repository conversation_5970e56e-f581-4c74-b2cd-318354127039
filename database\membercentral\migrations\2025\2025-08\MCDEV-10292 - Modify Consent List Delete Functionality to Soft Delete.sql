USE platformMail
GO

ALTER TABLE dbo.email_consentLists ADD [status] char(1) NOT NULL CONSTRAINT [DF_email_consentLists_status] DEFAULT ('A');
GO

USE membercentral
GO

ALTER FUNCTION dbo.ams_getVirtualGroupConditionVerbose_cl_entry (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	declare @verbose varchar(max);
	
	select top 1 @verbose = 'Has entry in '+
		case when len(isnull(consentListModeID.val,'')) > 0 then consentListModeID.val + ' ' else '' end +
		'Consent Lists' +
		case when len(isnull(emailTypeID.val,'')) > 0 then '; email types of ' + emailTypeID.val else '' end +
		case when len(isnull(emailTagTypeID.val,'')) > 0 then '; email tag types of ' + emailTagTypeID.val else '' end +
		
		case when len(isnull(consentListTypeID.val,'')) > 0 then '; consent list types of ' + consentListTypeID.val else '' end +
		case when len(isnull(consentListID.val,'')) > 0 then '; consent lists of ' + consentListID.val else '' end +
		case 
			when len(isnull(clDateAddedLower.val,'')) > 0 and len(isnull(clDateAddedUpper.val,'')) > 0 then '; date added between ' + clDateAddedLower.val + ' and ' + clDateAddedUpper.val
			when len(isnull(clDateAddedLower.val,'')) > 0 then '; date added after ' + clDateAddedLower.val
			when len(isnull(clDateAddedUpper.val,'')) > 0 then '; date added before ' + clDateAddedUpper.val
		else ''
		end
	from dbo.ams_virtualGroupConditions as c
	OUTER APPLY (
		SELECT STRING_AGG(met.emailType,' OR ') WITHIN GROUP (ORDER BY met.emailType ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'emailTypeID'
		INNER JOIN dbo.ams_memberEmailTypes as met on met.emailTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as emailTypeID(val)
	OUTER APPLY (
		SELECT STRING_AGG(met.emailTagType,' OR ') WITHIN GROUP (ORDER BY met.emailTagType ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'emailTagTypeID'
		INNER JOIN dbo.ams_memberEmailTagTypes as met on met.emailTagTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as emailTagTypeID(val)
	OUTER APPLY (
		SELECT clm.modeName
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListModeID'
		INNER JOIN platformMail.dbo.email_consentListModes as clm on clm.consentListModeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListModeID(val)
	OUTER APPLY (
		SELECT STRING_AGG(clt.consentListTypeName,' OR ') WITHIN GROUP (ORDER BY clt.consentListTypeName ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListTypeID'
		INNER JOIN platformMail.dbo.email_consentListTypes as clt on clt.consentListTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListTypeID(val)
	OUTER APPLY (
		-- don't limit to active consent lists here
		SELECT STRING_AGG(cl.consentListName,' OR ') WITHIN GROUP (ORDER BY cl.consentListName ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListID'
		INNER JOIN platformMail.dbo.email_consentLists as cl on cl.consentListID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListID(val)
	OUTER APPLY (
		SELECT cv.conditionValue + case when af.AFID is not null then ' (' + af.afName + ')' else '' end
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'clDateAddedLower'
		LEFT OUTER JOIN dbo.af_advanceFormulas as af on af.AFID = cv.AFID
		WHERE cv.conditionID = c.conditionID
	) as clDateAddedLower(val)
	OUTER APPLY (
		SELECT cv.conditionValue + case when af.AFID is not null then ' (' + af.afName + ')' else '' end
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'clDateAddedUpper'
		LEFT OUTER JOIN dbo.af_advanceFormulas as af on af.AFID = cv.AFID
		WHERE cv.conditionID = c.conditionID
	) as clDateAddedUpper(val)
	WHERE c.conditionID = @conditionID;

	RETURN @verbose;

END
GO

ALTER FUNCTION dbo.ams_isOrgIdentityInUse (@orgID INT, @orgIdentityID INT)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @count int;

	set @count = 0;
	select @count = count(listID) FROM dbo.lists_lists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and loginOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(publicationID) FROM dbo.pub_publications WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(blastID) FROM dbo.email_emailBlasts WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(orgID) FROM dbo.organizations WHERE orgID = @orgID AND defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(participantID) FROM seminarWeb.dbo.tblParticipants WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	-- dont look at status of consent list here because we hard delete org identities
	set @count = 0;
	select @count = count(consentListID) FROM platformMail.dbo.email_consentLists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(messageID) FROM platformMail.dbo.email_messages WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(itemID) FROM platformQueue.dbo.queue_completeSeminarReminder WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(storeID) FROM dbo.store WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(profileID) FROM dbo.mp_profiles WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(profileID) FROM dbo.tr_invoiceProfiles WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	on_done:
	RETURN @inUse;

END
GO

ALTER PROC dbo.ams_exportVirtualGroupRulesStructure
@orgID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgcode varchar(10), @cmd varchar(4000), @svrName varchar(40);
	DECLARE @tblMHCats TABLE (categoryID int, categoryName varchar(200), thePathExpanded varchar(MAX));
	DECLARE @tblRT TABLE (recordTypeID int);

	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;
	set @svrName = CAST(serverproperty('servername') as varchar(40));

	-- delete org sync condition rows
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgcode = @orgcode;

	INSERT INTO @tblMHCats 
	SELECT cat.categoryID, cat.categoryName, cat.thePathExpanded
	FROM dbo.sites as s
	cross apply dbo.fn_getRecursiveCategoriesByResourceType(s.siteID,'MemberHistoryAdmin') AS cat
	WHERE s.orgID = @orgID;

	INSERT INTO @tblRT
	select distinct recordtypeid
	from (
		select rt.recordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'recordType'
		inner join dbo.ams_recordTypes as rt on cast(rt.recordtypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
			union
		select rtrt.masterrecordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'role'
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
			union
		select rtrt.childrecordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'role'
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
	) as tmp;

	INSERT INTO dataTransfer.dbo.sync_vgc_conditions (orgcode, orgID, conditionID, datatypeid, displaytypeid, expressionid, 
		datepart, dateexpressionid, verbose, fieldCode, uid, subProc, processArea, processValuesSection, hashvalue, 
		cvid, conditionkeyid, conditionvalue, afid, valueUID)
	select @orgcode, @orgID, condition.conditionID, condition.datatypeid, condition.displaytypeid, condition.expressionid, 
		isnull(condition.[datepart],'') as [datepart], isnull(condition.dateexpressionid,0) as dateexpressionid, 
		condition.[verbose], condition.fieldCode, condition.[uid], condition.subProc, condition.processArea,
		isnull(condition.processValuesSection,0) as processValuesSection, 
		'0x' + CAST('' AS XML).value('xs:hexBinary(sql:column("condition.hashValue"))', 'VARCHAR(MAX)') as hashvalue,
		condvalue.cvid, condvalue.conditionkeyid, condvalue.conditionvalue, isnull(condvalue.afid,0) as afid, 
		isnull(
			case	
			when k.conditionkey = 'subSubType' then (select cast([uid] as varchar(36)) from dbo.sub_Types where typeID = condvalue.conditionvalue)	
			when k.conditionkey = 'subSubscription' then (select cast([uid] as varchar(36)) from dbo.sub_subscriptions where subscriptionID = condvalue.conditionvalue)
			when k.conditionkey = 'subRate' then (select cast([uid] as varchar(36)) from dbo.sub_rates where rateID = condvalue.conditionvalue)
			when k.conditionkey = 'subFrequency' then (select cast([uid] as varchar(36)) from dbo.sub_frequencies where frequencyID = condvalue.conditionvalue)
			when k.conditionkey = 'programList' then (select cast([uid] as varchar(36)) from dbo.cp_programs where programID = condvalue.conditionvalue)
			when k.conditionkey = 'programRate' then (select cast([uid] as varchar(36)) from dbo.cp_rates where rateID = condvalue.conditionvalue)
			when k.conditionkey = 'programDistribution' then (select cast([uid] as varchar(36)) from dbo.cp_distributions where distribID = condvalue.conditionvalue)
			when k.conditionkey in ('programMonetaryCustomField','programNonMonetaryCustomField','clientReferralsCustomFieldID')
				then (select cast([uid] as varchar(36)) from dbo.cf_fields where fieldID = condvalue.conditionvalue)
			when k.conditionkey = 'programFrequency' then (select cast([uid] as varchar(36)) from dbo.cp_frequencies where frequencyID = condvalue.conditionvalue)
			end,'') as valueUID
	from dbo.ams_virtualGroupConditions as condition
	left outer join dbo.ams_virtualGroupConditionValues as condvalue 
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		on condvalue.conditionID = condition.conditionID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_rules (orgcode, orgID, rulename, rulexml, ruleSQL, isActive, uid, groupid)
	select @orgcode, @orgID, vgrule.rulename, cast(vgrv.rulexml as varchar(max)) as rulexml, vgrv.ruleSQL, vgrule.isActive, vgrule.uid, [group].groupid
	from dbo.ams_virtualGroupRules as vgrule
	inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
		and vgrv.ruleVersionID = vgrule.activeVersionID
	left outer join dbo.ams_virtualGroupRuleGroups as [group] on [group].ruleID = vgrule.ruleid
	where vgrule.orgID = @orgID
	and vgrule.ruleTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allrulegroups (orgcode, orgID, groupID, groupcode, groupname, thePathExpanded, uid)
	select distinct @orgcode, @orgID, vgroup.groupID, isnull(g.groupcode,'') as groupcode, g.groupname, vgroup.thePathExpanded, isnull(g.[uid],'') as [uid]
	from dbo.ams_virtualGroupRuleGroups as rg
	inner join dbo.ams_virtualGroupRules as r on r.ruleID = rg.ruleID
	inner join dbo.ams_groups as g on g.orgID = @orgID
		AND g.groupID = rg.groupID
	inner join dbo.fn_getRecursiveGroups(@orgID) as vgroup on vgroup.groupID = g.groupID
	where r.orgID = @orgID
	and r.ruleTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allfields (orgcode, orgID, fieldCode, fieldLabel)
	select distinct @orgcode, @orgID, field.fieldCode, isnull(vf.fieldLabel,'') as fieldLabel
	from dbo.ams_virtualGroupConditions as field
	cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(@orgID,field.fieldCode) as vf
	where field.orgID = @orgID
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allvalueids (orgcode, orgID, conditionid, valueid, columnValue)
	select @orgcode, @orgID, condition.conditionid, 
		valueid = case 
			when left(condition.fieldCode,3) = 'md_' then mdcv.valueid
			when left(condition.fieldcode,4) = 'mad_' OR left(condition.fieldcode,5) = 'madt_' then dv.valueid
			when right(condition.fieldcode,10) = '_stateprov' then s.stateid
			when right(condition.fieldcode,8) = '_country' then c.countryid
			when left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED' then mat.addressTagTypeID
			when left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED' then met.emailTagTypeID
			else 0 end,
		columnValue = case 
			when left(condition.fieldCode,3) = 'md_' then coalesce(mdcv.columnValueString,cast(mdcv.columnValueDecimal2 as varchar(10)),cast(mdcv.columnValueInteger as varchar(10)),convert(varchar(10),mdcv.columnValueDate,101))
			when left(condition.fieldcode,4) = 'mad_' OR left(condition.fieldcode,5) = 'madt_' then dv.vendorValue
			when right(condition.fieldcode,10) = '_stateprov' then s.name
			when right(condition.fieldcode,8) = '_country' then c.country
			when left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED' then mat.addressTagType
			when left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED' then met.emailTagType
			else '' end
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupExpressions as exp on exp.expressionID = condition.expressionID
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as valuekey on valuekey.conditionKeyID = condvalue.conditionKeyID
		and valuekey.conditionKey = 'value'
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'md_'
	left outer join dbo.ams_states as s on s.stateid = condvalue.conditionvalue and right(condition.fieldcode,10) = '_stateprov'
	left outer join dbo.ams_countries as c on c.countryid = condvalue.conditionvalue and right(condition.fieldcode,8) = '_country'
	left outer join dbo.ams_memberDistrictValues as dv on dv.valueID = condvalue.conditionvalue and (left(condition.fieldCode,4) = 'mad_' or left(condition.fieldCode,5) = 'madt_')
	left outer join dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagTypeID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED'
	left outer join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.emailTagTypeID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED'
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1
	and (
		left(condition.fieldCode,3) = 'md_' 
		or left(condition.fieldCode,4) = 'mad_' 
		or left(condition.fieldCode,5) = 'madt_' 
		or right(condition.fieldcode,10) = '_stateprov' 
		or right(condition.fieldcode,8) = '_country'
		or (left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED')
		or (left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED')
	)
	and condition.displayTypeId in (2,3)
	and exp.expression in ('eq','neq','istaggedwith')
	and condition.dataTypeID <> 5;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'mw', mwt.websitetypeid, mwt.websitetype, mwt.[uid]
	from dbo.ams_memberWebsiteTypes as mwt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mwt.websitetypeid
	where mwt.orgID = @orgID
	and left(field.fieldcode,3) = 'mw_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'me', met.emailtypeid, met.emailtype, met.[uid]
	from dbo.ams_memberEmailTypes as met
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtypeid
	where met.orgID = @orgID
	and left(field.fieldcode,3) = 'me_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'me', met.emailtypeid, met.emailtype, met.[uid]
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'emailTypeID'
	inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID and cast(met.emailTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'met', met.emailtagtypeid, met.emailtagtype, met.[uid]
	from dbo.ams_memberEmailTagTypes as met
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtagtypeid
	where met.orgID = @orgID
	and left(field.fieldcode,4) = 'met_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'met', met.emailtagtypeid, met.emailtagtype, met.[uid]
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'emailTagTypeID'
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and cast(met.emailTagTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'ma', mat.addresstypeid, mat.addresstype, mat.[uid]
	from dbo.ams_memberAddressTypes as mat
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstypeid
	where mat.orgID = @orgID
	and (left(field.fieldcode,3) in ('ma_','mp_') or left(field.fieldcode,4) = 'mad_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mat', mat.addresstagtypeid, mat.addresstagtype
	from dbo.ams_memberAddressTagTypes as mat
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstagtypeid
	where mat.orgID = @orgID
	and (left(field.fieldcode,4) in ('mat_','mpt_') OR left(field.fieldcode,5) = 'madt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'mp', mpt.phonetypeid, mpt.phonetype, mpt.[uid]
	from dbo.ams_memberPhoneTypes as mpt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mpt.phonetypeid
	where mpt.orgID = @orgID
	and (left(field.fieldcode,3) = 'mp_' or left(field.fieldcode,4) = 'mpt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mad', mdt.districttypeid, mdt.districttype
	from dbo.ams_memberDistrictTypes as mdt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mdt.districttypeid
	where mdt.orgID = @orgID
	and (left(field.fieldcode,4) = 'mad_' OR left(field.fieldcode,5) = 'madt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mpl', mpl.PLTypeID, mpl.PLName
	from dbo.ams_memberProfessionalLicenseTypes as mpl
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mpl.PLTypeID
	where mpl.orgID = @orgID
	and left(field.fieldcode,4) = 'mpl_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgcode, @orgID, 'mh', cat.categoryID, cat.categoryName, cat.thePathExpanded
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('historyCategory','historySubCategory')
	inner join @tblMHCats as cat on cast(cat.categoryID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'af', af.afid, af.afname, af.uid
	from dbo.af_advanceFormulas as af
	inner join dbo.sites as s on s.siteID = af.siteID
	where s.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'rt', rt.recordtypeid, rt.recordtypecode
	from @tblRT as tmp
	inner join dbo.ams_recordTypes as rt on rt.recordtypeid = tmp.recordtypeid;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'rrt', rt.relationshiptypeid, rt.relationshiptypecode
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'role'
	inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
	inner join dbo.ams_recordRelationshipTypes as rt on rt.relationshiptypeid = rtrt.relationshipTypeID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemID2, itemID3, itemID4)
	select distinct @orgcode, @orgID, 'rtrt', rtrt.recordtyperelationshiptypeid, rtrt.relationshiptypeid, rtrt.masterrecordtypeid, rtrt.childrecordtypeid
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'role'
	inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgcode, @orgID, 'acctmp', mp.profileID, mp.profileCode, mp.profileName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('acctBalMP','acctInvCOFMP','acctCCProf')
	inner join dbo.mp_profiles as mp on cast(mp.profileID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'acctip', ip.profileID, ip.profileName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'acctInvProf'
	inner join dbo.tr_invoiceProfiles as ip on cast(ip.profileID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'acctcctype', mpct.cardTypeID, mpct.cardType
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'acctCCCardType'
	inner join dbo.mp_cardTypes as mpct on cast(mpct.cardTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'acctgl', gl.GLAccountID, gl.accountName, gl.uid
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'revenueGL'
	inner join dbo.tr_GLAccounts as gl on cast(gl.glAccountID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'sgsubuser', su.subuserID, su.username
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'sendGridSubUser'
	inner join platformMail.dbo.sendgrid_subusers as su on cast(su.subuserID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mcsiteid', s.siteID, s.siteCode
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('suppListSiteID','referralSiteID')
	inner join dbo.sites as s on cast(s.siteID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'clmodeid', clm.consentListModeID, clm.modeName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListModeID'
	inner join platformMail.dbo.email_consentListModes as clm on cast(clm.consentListModeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'cltypeid', clt.consentListTypeID, clt.consentListTypeName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListTypeID'
	inner join platformMail.dbo.email_consentListTypes as clt on cast(clt.consentListTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	-- don't limit to active consent lists here
	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'clid', cl.consentListID, cl.consentListName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListID'
	inner join platformMail.dbo.email_consentLists as cl on cast(cl.consentListID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'refstatus', rs.clientReferralStatusID, rs.statusName
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'clientReferralStatusID'
	INNER JOIN dbo.ref_clientReferralStatus AS rs ON CAST(rs.clientReferralStatusID as varchar(10)) = condvalue.conditionValue
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'refpanel', p.panelID, CASE WHEN p2.panelID IS NOT NULL THEN p2.[name] + ' \ ' ELSE '' END + p.[name]
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'panelID'
	INNER JOIN dbo.ref_panels AS p ON CAST(p.panelID as varchar(10)) = condvalue.conditionValue
	LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = p.panelParentID
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'reffdstat', st.feeDiscrepancyStatusID, st.statusName
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'feeDiscrepancyStatusID'
	INNER JOIN dbo.ref_feeDiscrepancyStatuses AS st ON CAST(st.feeDiscrepancyStatusID as varchar(10)) = condvalue.conditionValue
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select @orgcode, @orgID, 'ck', conditionkeyid, conditionkey
	from dbo.ams_virtualGroupConditionKeys;

	-- export to files
	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, conditionID, datatypeid, displaytypeid, expressionid, datepart, dateexpressionid, verbose, fieldCode, uid, subProc, processArea, processValuesSection, hashvalue, cvid, conditionkeyid, conditionvalue, afid, valueUID, useValue, finalAction from datatransfer.dbo.sync_vgc_conditions where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_conditions.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, rulename, rulexml, ruleSQL, isActive, uid, groupid, finalAction from datatransfer.dbo.sync_vgc_rules where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_rules.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, groupID, groupcode, groupname, thePathExpanded, uid, useGroupID from datatransfer.dbo.sync_vgc_allrulegroups where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allrulegroups.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, fieldCode, fieldLabel, useFieldCode from dataTransfer.dbo.sync_vgc_allfields where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allfields.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, conditionid, valueid, columnValue, useValue from dataTransfer.dbo.sync_vgc_allvalueids where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allvalueids.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, cat, itemID, itemID2, itemID3, itemID4, itemType, itemUID, itemType2, useID from dataTransfer.dbo.sync_vgc_supporting where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_supporting.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear sync tables
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_getOrgIdentityUsage
@orgID INT,
@orgIdentityID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- keep this in sync with ams_isOrgIdentityInUse
	DECLARE @resourceTypes TABLE (orgIdentityUsage varchar(200), usageCount int);

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'List Servers', COUNT(listID)
	FROM dbo.lists_lists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Login for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND loginOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Publications' AS usageType, COUNT(publicationID) AS usageCount
	FROM dbo.pub_publications
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Blasts' AS usageType, COUNT(blastID) AS usageCount
	FROM dbo.email_emailBlasts
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Org', COUNT(orgID)
	FROM dbo.organizations
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb' AS usageType, COUNT(participantID) AS usageCount
	FROM seminarWeb.dbo.tblParticipants
	WHERE orgIdentityID = @orgIdentityID;

	-- dont look at status of consent list here because we hard delete org identities
	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Consent Lists' AS usageType, COUNT(consentListID) AS usageCount
	FROM platformMail.dbo.email_consentLists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Messages' AS usageType, COUNT(messageID) AS usageCount
	FROM platformMail.dbo.email_messages
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb Complete Seminar Reminders' AS usageType, COUNT(itemID) AS usageCount
	FROM platformQueue.dbo.queue_completeSeminarReminder
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Store' AS usageType, COUNT(storeID) AS usageCount
	FROM dbo.store
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Merchant Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.mp_profiles
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Invoice Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.tr_invoiceProfiles
	WHERE orgIdentityID = @orgIdentityID;

	DELETE FROM @resourceTypes
	WHERE usageCount = 0;

	SELECT orgIdentityUsage as usageType, usageCount
	FROM @resourceTypes
	ORDER BY usageCount desc, orgIdentityUsage;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_prepareVirtualGroupRulesImport
@siteID int,
@pathToImport varchar(400),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgConditionsChanged') IS NOT NULL 
		DROP TABLE #tmpOrgConditionsChanged;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRules;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRulesChanged') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRulesChanged;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpRules;

	CREATE TABLE #tblImportErrors (rowid int IDENTITY(1,1), msg varchar(600), errorCode varchar(20));
	CREATE TABLE #tmpOrgGrpConditions (uid uniqueidentifier, datatypeid int, displaytypeid int, expressionid int, datepart varchar(8), dateexpressionid int, 
		verbose varchar(max), fieldCode varchar(40), subProc varchar(30), processArea varchar(25), processValuesSection tinyint, hashvalue varchar(max), 
		cvid int, conditionkeyid int, conditionKey varchar(50), conditionvalue varchar(100), afid int);
	CREATE TABLE #tmpOrgConditionsChanged (uid uniqueidentifier);
	CREATE TABLE #tmpOrgGrpRules (uid uniqueidentifier, rulename varchar(400), rulexml varchar(max), isActive bit, groupid int);
	CREATE TABLE #tmpOrgGrpRulesChanged (uid uniqueidentifier);
	CREATE TABLE #tmpOrgDeleteGrpConditions (uid uniqueidentifier, verbose varchar(max));
	CREATE TABLE #tmpOrgDeleteGrpRules (uid uniqueidentifier, rulename varchar(400));

	DECLARE @orgID int, @orgcode varchar(10), @cmd varchar(400), @svrName varchar(40);
	SET @svrName = CAST(serverproperty('servername') as varchar(40));

	SELECT @orgID = o.orgID, @orgcode = o.orgcode 
	FROM dbo.sites as s
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	-- ensure files are present
	IF dbo.fn_FileExists(@pathToImport + 'sync_vgc_conditions.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_rules.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allrulegroups.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allfields.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_allvalueids.bcp') = 0
		OR dbo.fn_FileExists(@pathToImport + 'sync_vgc_supporting.bcp') = 0
	BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('One or more required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync condition rows
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgcode = @orgcode;

	-- import data
	set @cmd = 'bcp datatransfer.dbo.sync_vgc_conditions in ' + @pathToImport + 'sync_vgc_conditions.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp datatransfer.dbo.sync_vgc_rules in ' + @pathToImport + 'sync_vgc_rules.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp datatransfer.dbo.sync_vgc_allrulegroups in ' + @pathToImport + 'sync_vgc_allrulegroups.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_allfields in ' + @pathToImport + 'sync_vgc_allfields.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_allvalueids in ' + @pathToImport + 'sync_vgc_allvalueids.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp dataTransfer.dbo.sync_vgc_supporting in ' + @pathToImport + 'sync_vgc_supporting.bcp -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_vgc_conditions SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_rules SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allrulegroups SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allfields SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_allvalueids SET orgID = @orgID WHERE orgcode = @orgcode;
	UPDATE datatransfer.dbo.sync_vgc_supporting SET orgID = @orgID WHERE orgcode = @orgcode;

	-- existing org conditions
	INSERT INTO #tmpOrgGrpConditions (uid, datatypeid, displaytypeid, expressionid, datepart, dateexpressionid, verbose, fieldCode, 
		subProc, processArea, processValuesSection, hashvalue, cvid, conditionkeyid, conditionKey, conditionvalue, afid)
	select condition.[uid], condition.datatypeid, condition.displaytypeid, condition.expressionid, isnull(condition.[datepart],'') as [datepart], 
		isnull(condition.dateexpressionid,0) as dateexpressionid, condition.[verbose], condition.fieldCode, condition.subProc, 
		condition.processArea, isnull(condition.processValuesSection,0) as processValuesSection, 
		'0x' + CAST('' AS XML).value('xs:hexBinary(sql:column("condition.hashValue"))', 'VARCHAR(MAX)') as hashvalue,
		condvalue.cvid, condvalue.conditionkeyid, k.conditionKey, condvalue.conditionvalue, isnull(condvalue.afid,0) as afid
	from dbo.ams_virtualGroupConditions as condition
	left outer join dbo.ams_virtualGroupConditionValues as condvalue 
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		on condvalue.conditionID = condition.conditionID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	-- existing org rules
	INSERT INTO #tmpOrgGrpRules (uid, rulename, rulexml, isActive, groupid)
	select vgrule.uid, vgrule.rulename, cast(rv.rulexml as varchar(max)) as rulexml, vgrule.isActive, [group].groupid
	from dbo.ams_virtualGroupRules as vgrule
	inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
		and rv.ruleVersionID = vgrule.activeVersionID
	left outer join dbo.ams_virtualGroupRuleGroups as [group] on [group].ruleID = vgrule.ruleid
	where vgrule.orgID = @orgID
	and vgrule.ruleTypeID = 1;

	/*** Process the Data for Comparisons ***/
	
	-- get the useFieldCode in allFields
	update af
	set af.useFieldCode = af.fieldCode
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join membercentral.dbo.organizations as o on o.orgID = @orgID and o.orgID = af.orgID
	where af.orgID = @orgID
	AND (
		af.fieldcode in ('m_firstname','m_lastname','m_membernumber','m_company','m_membertypeid','m_status','m_hasmemberphoto')
		OR (af.fieldcode = 'm_prefix' and o.hasPrefix = 1)
		OR (af.fieldcode = 'm_middlename' and o.hasMiddleName = 1)
		OR (af.fieldcode = 'm_suffix' and o.hasSuffix = 1)
		OR (af.fieldcode = 'm_professionalsuffix' and o.hasProfessionalSuffix = 1)
		OR (left(af.fieldcode,21) = 'm_earliestdatecreated')
		OR (left(af.fieldcode,16) = 'm_datelastlogin_')
	);

	update af
	set af.useFieldCode = 'md_' + cast(mdc.columnID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.orgID = af.orgID and mdc.columnName = af.fieldLabel
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'md_';

	update af
	set af.useFieldCode = 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'ma' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'ma_'
	and (
		parsename(replace(af.fieldCode,'_','.'),1) in ('address1','city','stateprov','postalcode','country','tagged')
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'attn' AND mat.hasAttn = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'address2' AND mat.hasAddress2 = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'address3' AND mat.hasAddress3 = 1)
		OR (parsename(replace(af.fieldCode,'_','.'),1) = 'county' AND mat.hasCounty = 1)
	);

	update af
	set af.useFieldCode = 'mat_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mat' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = s.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mat_'
	and parsename(replace(af.fieldCode,'_','.'),1) in ('attn','address1','address2','address3','city','stateprov','postalcode','country','county');

	update af
	set af.useFieldCode = 'mp_' + cast(mat.addressTypeID as varchar(10)) + '_' + cast(mpt.phoneTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'ma' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = sA.itemUID
	inner join dataTransfer.dbo.sync_vgc_supporting as sP on sP.orgID = @orgID and sP.cat = 'mp' and sP.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID and mpt.[uid] = sP.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'mp_';

	update af
	set af.useFieldCode = 'mpt_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + cast(mpt.phoneTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'mat' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = sA.itemType
	inner join dataTransfer.dbo.sync_vgc_supporting as sP on sP.orgID = @orgID and sP.cat = 'mp' and sP.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID and mpt.[uid] = sP.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mpt_';

	update af
	set af.useFieldCode = 'mad_' + cast(mat.addressTypeID as varchar(10)) + '_' + cast(mdt.districtTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'ma' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.uid = sA.itemUID
	inner join dataTransfer.dbo.sync_vgc_supporting as sD on sD.orgID = @orgID and sD.cat = 'mad' and sD.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberDistrictTypes as mdt on mdt.orgID = @orgID and mdt.districttype = sD.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mad_';

	update af
	set af.useFieldCode = 'madt_' + cast(mat.addressTagTypeID as varchar(10)) + '_' + cast(mdt.districtTypeID as varchar(10))
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as sA on sA.orgID = @orgID and sA.cat = 'mat' and sA.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = sA.itemType
	inner join dataTransfer.dbo.sync_vgc_supporting as sD on sD.orgID = @orgID and sD.cat = 'mad' and sD.itemID = parsename(replace(af.fieldCode,'_','.'),1)
	inner join membercentral.dbo.ams_memberDistrictTypes as mdt on mdt.orgID = @orgID and mdt.districttype = sD.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,5) = 'madt_';

	update af
	set af.useFieldCode = 'mw_' + cast(mw.websiteTypeID as varchar(10)) + '_website'
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mw' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberWebsiteTypes as mw on mw.orgID = @orgID and mw.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'mw_';

	update af
	set af.useFieldCode = 'me_' + cast(me.emailTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'me' 
		and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
		and parsename(replace(af.fieldCode,'_','.'),1) in ('email','tagged')
	inner join membercentral.dbo.ams_memberEmailTypes as me on me.orgID = @orgID and me.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,3) = 'me_';

	update af
	set af.useFieldCode = 'met_' + cast(met.emailTagTypeID as varchar(10)) + '_email'
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'met' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'met_';

	update af
	set af.useFieldCode = 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_' + parsename(replace(af.fieldCode,'_','.'),1)
	from dataTransfer.dbo.sync_vgc_allfields as af
	inner join dataTransfer.dbo.sync_vgc_supporting as s on s.orgID = @orgID and s.cat = 'mpl' and s.itemID = parsename(replace(af.fieldCode,'_','.'),2)
	inner join membercentral.dbo.ams_memberProfessionalLicenseTypes as mplt on mplt.orgID = @orgID and mplt.PLName = s.itemType
	where af.orgID = @orgID
	and left(af.fieldCode,4) = 'mpl_';

	update dataTransfer.dbo.sync_vgc_allfields
	set useFieldCode = 'IGNORE'
	where orgID = @orgID
	and fieldCode = 'ev_entry';

	update dataTransfer.dbo.sync_vgc_allfields
	set useFieldCode = fieldCode
	where orgID = @orgID
	and fieldCode in ('acct_allocsum','acct_allocsumrecog','acct_inv','acct_balance','acct_cc','cl_entry','cp_entry','cp_valuesum','l_entry','mh_entry','mn_entry','ref_entry','rel_entry','rt_role','sub_entry','sup_entry','sw_entry','task_entry');

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allfields where orgID = @orgID and useFieldCode is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useFieldCode in allfields.', 'USEFIELDCODENULL');
		GOTO on_done;
	END

	-- get the useValue in allValueIDs
	update v
	set v.useValue = mdcv.valueID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = parsename(replace(af.useFieldCode,'_','.'),1)
		and coalesce(mdcv.columnValueString,cast(mdcv.columnValueDecimal2 as varchar(10)),cast(mdcv.columnValueInteger as varchar(10)),convert(varchar(10),mdcv.columnValueDate,101)) = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'md_';

	update v
	set v.useValue = mat.addressTagTypeID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagType = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'ma_'
	and parsename(replace(af.fieldCode,'_','.'),1) = 'tagged';

	update v
	set v.useValue = met.emailTagTypeID
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	inner join dataTransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.emailTagType = v.columnValue
	where v.orgID = @orgID
	and left(af.fieldCode,3) = 'me_'
	and parsename(replace(af.fieldCode,'_','.'),1) = 'tagged';

	update v
	set v.useValue = v.valueid
	from dataTransfer.dbo.sync_vgc_allvalueids as v
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.conditionID = v.conditionID
	where v.orgID = @orgID
	and not (
		left(c.fieldCode,3) = 'md_'
		or (left(c.fieldCode,3) = 'ma_' and parsename(replace(c.fieldCode,'_','.'),1) = 'tagged')
		or (left(c.fieldCode,3) = 'me_' and parsename(replace(c.fieldCode,'_','.'),1) = 'tagged')
	);

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allvalueids where orgID = @orgID and useValue is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in allvalueids.', 'USEVALUENULL');
		GOTO on_done;
	END
	
	-- get system ids for advance formulas
	update s
	set s.useID = af.AFID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.sites as mcs on mcs.orgID = s.orgID
	inner join dbo.af_advanceFormulas as af on af.siteID = mcs.siteID and af.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'af';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'af' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for af.', 'USEIDAFNULL');
		GOTO on_done;
	END

	-- get system ids for email types
	update s
	set s.useID = met.emailTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'me';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'me' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for me.', 'USEIDMENULL');
		GOTO on_done;
	END

	-- get system ids for email tag types
	update s
	set s.useID = met.emailTagTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'met';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'met' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for me.', 'USEIDMETNULL');
		GOTO on_done;
	END

	-- get system ids for history categories
	;WITH allMHCats AS (
		SELECT categoryID, thePathExpanded
		FROM dbo.fn_getRecursiveCategoriesByResourceType(@siteID,'MemberHistoryAdmin')
	)
	update s
	set s.useID = mh.categoryID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allMHCats as mh on mh.thePathExpanded = s.itemType2
	where s.orgID = @orgID
	and s.cat = 'mh';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'mh' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for mh.', 'USEIDMHNULL');
		GOTO on_done;
	END

	-- get system ids for merchant profiles
	; WITH allMPs AS (
		SELECT profileID, profileCode
		FROM membercentral.dbo.mp_profiles
		WHERE siteID = @siteID
		and [status] IN ('A','I')
	)
	update s
	set s.useID = mp.profileID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allMPs as mp on mp.profileCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctmp';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctmp' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctmp.', 'USEIDACCTMPNULL');
		GOTO on_done;
	END

	-- get system ids for invoice profiles
	; WITH allIPs AS (
		SELECT profileID, profileName
		FROM membercentral.dbo.tr_invoiceProfiles
		WHERE orgID = @orgID
		and [status] <> 'D'
	)
	update s
	set s.useID = inp.profileID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allIPs as inp on inp.profileName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctip';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctip' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctip.', 'USEIDACCTIPNULL');
		GOTO on_done;
	END

	-- get system ids for card types
	; WITH allCCTypes AS (
		SELECT cardTypeID, cardType
		FROM membercentral.dbo.mp_cardTypes
	)
	update s
	set s.useID = cct.cardTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allCCTypes as cct on cct.cardType = s.itemType
	where s.orgID = @orgID
	and s.cat = 'acctcctype';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctcctype' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctcctype.', 'USEIDACCTCCTYPENULL');
		GOTO on_done;
	END


	-- get system ids for GLAccounts
	; WITH allGLs AS (
		SELECT GLAccountID, [uid]
		FROM membercentral.dbo.tr_GLAccounts
		WHERE orgID = @orgID
		and [status] <> 'D'
	)
	update s
	set s.useID = gl.GLAccountID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join allGLs as gl on gl.uid = s.itemUID
	where s.orgID = @orgID
	and s.cat = 'acctgl';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'acctgl' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for acctgl.', 'USEIDACCTGLNULL');
		GOTO on_done;
	END

	-- get system ids for record types
	update s
	set s.useID = rt.recordTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join membercentral.dbo.ams_recordTypes as rt on rt.orgID = @orgID and rt.recordtypecode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'rt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rt.', 'USEIDRTNULL');
		GOTO on_done;
	END

	-- get system ids for relationship types
	update s
	set s.useID = rrt.relationshipTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join membercentral.dbo.ams_recordRelationshipTypes as rrt on rrt.orgID = @orgID and rrt.relationshipTypeCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'rrt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rrt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rrt.', 'USEIDRRTNULL');
		GOTO on_done;
	END

	-- get system ids for record relationship types
	;WITH orgrtrt as (
		SELECT rtrt.recordTypeRelationshipTypeID, rtrt.relationshipTypeID, rtrt.masterRecordTypeID, rtrt.childRecordTypeID
		FROM dbo.ams_recordTypesRelationshipTypes as rtrt
		INNER JOIN dbo.ams_recordTypes as rM on rM.orgID = @orgID and rM.recordTypeID = rtrt.masterRecordTypeID
		WHERE rtrt.isActive = 1
	)
	update s
	set s.useID = rtrt.recordTypeRelationshipTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dataTransfer.dbo.sync_vgc_supporting as sRT on sRT.orgID = @orgID and sRT.cat = 'rt' and sRT.itemID = s.itemID4
	inner join dataTransfer.dbo.sync_vgc_supporting as sRT2 on sRT2.orgID = @orgID and sRT2.cat = 'rt' and sRT2.itemID = s.itemID3
	inner join dataTransfer.dbo.sync_vgc_supporting as sRRT on sRRT.orgID = @orgID and sRRT.cat = 'rrt' and sRRT.itemID = s.itemID2
	inner join orgrtrt as rtrt on rtrt.relationshipTypeID = sRRT.useID
		and rtrt.masterRecordTypeID = sRT2.useID
		and rtrt.childRecordTypeID = sRT.useID
	where s.orgID = @orgID
	and s.cat = 'rtrt';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'rtrt' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for rtrt.', 'USEIDRTRTNULL');
		GOTO on_done;
	END

	-- get system ids for sendgrid subusers
	update s
	set s.useID = su.subuserID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.sendgrid_subusers as su on su.siteID = @siteID and su.username = s.itemType
	where s.orgID = @orgID
	and s.cat = 'sgsubuser';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'sgsubuser' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for sgsubuser.', 'USEIDSGSUBUSERNULL');
		GOTO on_done;
	END

	-- get system ids for siteID
	update s
	set s.useID = mcs.siteID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.sites as mcs on mcs.orgID = @orgID and mcs.siteCode = s.itemType
	where s.orgID = @orgID
	and s.cat = 'mcsiteid';

	-- get system id for consent list mode
	update s
	set s.useID = clm.consentListModeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentListModes as clm on clm.modeName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'clmodeid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'clmodeid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for clmodeid.', 'USEIDCLMODEIDNULL');
		GOTO on_done;
	END

	-- get system ids for consent list types
	update s
	set s.useID = clt.consentListTypeID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentListTypes as clt on clt.orgID = @orgID 
		and clt.consentListTypeName = s.itemType
	where s.orgID = @orgID
	and s.cat = 'cltypeid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'cltypeid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for cltypeid.', 'USEIDCLTYPEIDNULL');
		GOTO on_done;
	END

	-- get system ids for consent lists. don't limit to active consent lists here
	update s
	set s.useID = cl.consentListID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join platformMail.dbo.email_consentLists as cl on cl.consentListName = s.itemType
	inner join platformMail.dbo.email_consentListTypes as clt on clt.orgID = @orgID 
		and clt.consentListTypeID = cl.consentListTypeID
	where s.orgID = @orgID
	and s.cat = 'clid';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'clid' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for clid.', 'USEIDCLIDNULL');
		GOTO on_done;
	END

	-- get system ids for referral statuses
	update s
	set s.useID = rs.clientReferralStatusID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_clientReferralStatus as rs on rs.statusName = s.itemType2
	inner join dbo.ref_referrals as r on r.referralID = rs.referralID
	inner join dbo.cms_applicationInstances	as ai on ai.applicationInstanceID = r.applicationInstanceID
		and ai.siteID = @siteID
	where s.orgID = @orgID
	and s.cat = 'refstatus';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'refstatus' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for refstatus.', 'USEIDREFSTATUSNULL');
		GOTO on_done;
	END
	
	-- get system ids for referral panels
	update s
	set s.useID = p.panelID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_panels as p
		inner join dbo.ref_referrals as r on r.referralID = p.referralID
		inner join dbo.cms_applicationInstances	as ai on ai.applicationInstanceID = r.applicationInstanceID
			and ai.siteID = @siteID
		left outer join dbo.ref_panels AS p2 ON p2.panelID = p.panelParentID
		on s.itemType2 = CASE WHEN p2.panelID IS NOT NULL THEN p2.[name] + ' \ ' ELSE '' END + p.[name]
	where s.orgID = @orgID
	and s.cat = 'refpanel';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'refpanel' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for refpanel.', 'USEIDREFPANELNULL');
		GOTO on_done;
	END

	-- get system ids for referral fee discrepancy statuses
	update s
	set s.useID = st.feeDiscrepancyStatusID
	from dataTransfer.dbo.sync_vgc_supporting as s
	inner join dbo.ref_feeDiscrepancyStatuses as st on st.statusName = s.itemType2
	where s.orgID = @orgID
	and s.cat = 'reffdstat';

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_supporting where orgID = @orgID and cat = 'reffdstat' and useID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useID in supporting for reffdstat.', 'USEIDREFFDSTATNULL');
		GOTO on_done;
	END

	-- get condvalue useValue for special lookups
	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType in ('acctBalMP','acctInvCOFMP','acctCCProf')
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctmp' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'acctInvProf'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctip' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'acctCCCardType'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctcctype' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'revenueGL'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'acctgl' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'recordType'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'rt' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'role'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'rtrt' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType in ('historyCategory','historySubCategory')
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'mh' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'emailTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'me' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'emailTagTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'met' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'sendGridSubUser'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'sgsubuser' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'suppListSiteID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'mcsiteid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListModeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'clmodeid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListTypeID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'cltypeid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'consentListID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'clid' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralStatusID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'refstatus' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'panelID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'refpanel' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	WITH qryAllFields AS (
		SELECT f.fieldID, f.[uid]
		FROM membercentral.dbo.cf_fields as f
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID
			AND sr.siteResourceID = f.controllingSiteResourceID
		INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
			AND ai.siteResourceID = f.controllingSiteResourceID
		INNER JOIN dbo.ref_referrals r ON r.applicationInstanceID = ai.applicationInstanceID
		WHERE f.usageID = dbo.fn_cf_getUsageID('ClientReferrals','ClientReferrals', NULL)
	)
	update c
	set c.useValue = r.fieldID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
		and sK.cat = 'ck'
		and c.conditionkeyID = sK.itemID
		and sK.itemType = 'clientReferralsCustomFieldID'
	inner join qryAllFields as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	-- setting useValue as valueID for clientReferralsCustomFieldValue just to find invalid values for the site.
	update c
	set c.useValue = fv.valueID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralsCustomFieldValue'
	inner join dataTransfer.dbo.sync_vgc_conditions as c2 on c2.orgID = @orgID and c2.conditionID = c.conditionID
	inner join dataTransfer.dbo.sync_vgc_supporting as sK2 on sK2.orgID = @orgID and sK2.cat = 'ck' and c2.conditionkeyID = sK2.itemID and sK2.itemType = 'clientReferralsCustomFieldID'
	inner join dbo.cf_fields as f on f.fieldID = c2.useValue
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	where c.orgID = @orgID
	and c.conditionvalue = coalesce(fv.valueString,cast(fv.valueInteger as varchar(10)),cast(fv.valueDecimal2 as varchar(15)),convert(varchar(12), fv.valueDate, 101),case when fv.valueBit = 1 then 'Yes' else 'No' end);

	update c
	set c.useValue = sM.useID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'feeDiscrepancyStatusID'
	inner join dataTransfer.dbo.sync_vgc_supporting as sM on sM.orgID = @orgID and sM.cat = 'reffdstat' and cast(sM.itemID as varchar(10)) = c.conditionvalue
	where c.orgID = @orgID;

	update c
	set c.useValue = t.typeID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subSubType'
	inner join dbo.sites as s on s.orgID = sK.orgID
	inner join dbo.sub_types as t on t.siteID = s.siteID and cast(t.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = s.subscriptionID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subSubscription'
	inner join membercentral.dbo.sub_subscriptions as s on s.orgID = @orgID and cast(s.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllRates AS (
		SELECT r.rateID, r.[uid]
		FROM membercentral.dbo.sub_rates r
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID
	)
	update c
	set c.useValue = r.rateID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subRate'
	inner join qryAllRates as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = f.frequencyID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'subFrequency'
	inner join membercentral.dbo.sub_frequencies as f on f.siteID = @siteID and cast(f.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllPrograms AS (
		select p.programID, p.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
	)
	update c
	set c.useValue = p.programID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programList'
	inner join qryAllPrograms as p on cast(p.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllRates AS (
		select r.rateID, r.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
		INNER JOIN membercentral.dbo.cp_rates as r on r.programID = p.programID
	)
	update c
	set c.useValue = r.rateID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programRate'
	inner join qryAllRates as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllDistrib AS (
		select d.distribID, d.[uid]
		FROM membercentral.dbo.cp_programs as p
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = p.siteResourceID
		INNER JOIN membercentral.dbo.cp_distributions as d on d.programID = p.programID
	)
	update c
	set c.useValue = r.distribID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programDistribution'
	inner join qryAllDistrib as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	WITH qryAllFields AS (
		select f.fieldID, f.[uid]
		FROM membercentral.dbo.cf_fields as f
		INNER JOIN membercentral.dbo.cms_siteResources as sr ON sr.siteID = @siteID and sr.siteResourceID = f.controllingSiteResourceID
	)
	update c
	set c.useValue = r.fieldID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
		and sK.cat = 'ck'
		and c.conditionkeyID = sK.itemID
		and sK.itemType in ('programMonetaryCustomField','programNonMonetaryCustomField')
	inner join qryAllFields as r on cast(r.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	update c
	set c.useValue = f.frequencyID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programFrequency'
	inner join membercentral.dbo.cp_frequencies as f on f.siteID = @siteID and cast(f.[uid] as varchar(36)) = c.valueUID
	where c.orgID = @orgID;

	-- setting useValue as valueID for programNonMonetaryCustomFieldValue just to find invalid values for the site.
	update c
	set c.useValue = fv.valueID
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programNonMonetaryCustomFieldValue'
	inner join dataTransfer.dbo.sync_vgc_conditions as c2 on c2.orgID = @orgID and c2.conditionID = c.conditionID
	inner join dataTransfer.dbo.sync_vgc_supporting as sK2 on sK2.orgID = @orgID and sK2.cat = 'ck' and c2.conditionkeyID = sK2.itemID and sK2.itemType = 'programNonMonetaryCustomField'
	inner join dbo.cf_fields as f on f.fieldID = c2.useValue
	inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
	where c.orgID = @orgID
	and c.conditionvalue = coalesce(fv.valueString,cast(fv.valueInteger as varchar(10)),cast(fv.valueDecimal2 as varchar(15)),convert(varchar(12), fv.valueDate, 101),case when fv.valueBit = 1 then 'Yes' else 'No' end);

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_conditions as c
			inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID 
			where c.orgID = @orgID
			and c.useValue is null
			and nullif (c.valueUID,'') is not null
			and sK.itemType in ('subSubType','subSubscription','subRate','subFrequency','programList','programRate',
				'programDistribution','programMonetaryCustomField','programNonMonetaryCustomField','clientReferralsCustomFieldID')
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('No matching UIDs found.', 'UNMATCHEDUID');
		GOTO on_done;
	END

	-- checking valid field values for client referrals / contribution programs only after custom field uid matching for both, so error message is displayed in right order
	IF EXISTS (
		select 1
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
			and sK.cat = 'ck'
			and c.conditionkeyID = sK.itemID
			and sK.itemType = 'clientReferralsCustomFieldValue'
		where c.orgID = @orgID
		and nullif(c.conditionvalue,'') is not null
		and c.useValue is null
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in table for clientReferralsCustomFieldValue.', 'USEVALUEREFCFVALNULL');
		GOTO on_done;
	END

	-- updating useValue back to null so the conditionvalue will be used for ams_virtualGroupConditionValues entries, because this field uses values rather than ids.
	update c
	set c.useValue = NULL
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'clientReferralsCustomFieldValue'
	where c.orgID = @orgID;

	IF EXISTS (
		select 1
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID
			and sK.cat = 'ck'
			and c.conditionkeyID = sK.itemID
			and sK.itemType = 'programNonMonetaryCustomFieldValue'
		where c.orgID = @orgID
		and nullif(c.conditionvalue,'') is not null
		and c.useValue is null
	) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('NULL useValue in table for programNonMonetaryCustomFieldValue.', 'USEVALUECPCFVALNULL');
		GOTO on_done;
	END

	-- updating useValue back to null so the conditionvalue will be used for ams_virtualGroupConditionValues entries, because this field uses values rather than ids.
	update c
	set c.useValue = NULL
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_supporting as sK on sK.orgID = @orgID and sK.cat = 'ck' and c.conditionkeyID = sK.itemID and sK.itemType = 'programNonMonetaryCustomFieldValue'
	where c.orgID = @orgID;

	-- update remaining with allvalueids
	update c
	set c.useValue = v.useValue
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join dataTransfer.dbo.sync_vgc_allvalueids as v on v.orgID = @orgID and v.conditionID = c.conditionID
		and cast(v.valueID as varchar(10)) = c.conditionValue
	where c.orgID = @orgID
	and c.useValue is null;

	-- get rule groupIDs
	update rg
	set rg.useGroupID = g.groupID
	from dataTransfer.dbo.sync_vgc_allrulegroups as rg
	inner join dbo.ams_groups as g on g.orgID = @orgID 
		and g.uid = rg.uid 
		and g.isProtected = 0 
		and g.isSystemGroup = 0
	where rg.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_vgc_allrulegroups where orgID = @orgID and useGroupID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Invalid groups in allrulegroups.', 'INVALIDRULEGRP');
		GOTO on_done;
	END

	-- inactivate event conditions and rules using event conditions
	update dataTransfer.dbo.sync_vgc_conditions
	set finalAction = 'I'
	where orgID = @orgID
	and fieldCode = 'ev_entry';

	update r
	set r.finalAction = 'I'
	from dataTransfer.dbo.sync_vgc_rules as r
	inner join dataTransfer.dbo.sync_vgc_conditions as c on c.orgID = @orgID and c.orgID = r.orgID and c.finalAction = 'I'
	where r.orgID = @orgID
	and cast(r.ruleXML as xml).exist('//condition[@id=sql:column("c.uid")]') = 1

	-- new conditions
	update c
	set c.finalAction = 'A'
	from dataTransfer.dbo.sync_vgc_conditions as c
	left outer join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID 
		and vgc.uid = c.uid
		and vgc.conditionTypeID = 1
	where c.orgID = @orgID
	and vgc.conditionID is null;

	-- find conditions to be updated
	INSERT INTO #tmpOrgConditionsChanged
	select distinct [uid]
	from (
		select c.[uid], c.datatypeid, c.displaytypeid, c.expressionid, c.[datepart], c.dateexpressionid, c.[verbose], af.useFieldCode, 
			sCK.itemType as conditionkey, coalesce(cast(c.useValue as varchar(10)),c.conditionvalue) COLLATE Latin1_General_CS_AS as conditionValue, isnull(sAF.useID,0) as afid
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join datatransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
		left outer join dataTransfer.dbo.sync_vgc_supporting as sCK on sCK.orgID = @orgID and sCK.cat = 'ck' and sCK.itemID = c.conditionkeyID
		left outer join dataTransfer.dbo.sync_vgc_supporting as sAF on sAF.orgID = @orgID and sAF.cat = 'af' and sAF.itemID = c.afid
		where c.orgID = @orgID
		and c.finalAction is null
			except
		select [uid], datatypeid, displaytypeid, expressionid, [datepart], dateexpressionid, [verbose], fieldCode, 
			conditionKey, conditionvalue, afid
		from #tmpOrgGrpConditions
		where fieldCode <> 'ev_entry'
	) tmp;

	-- flip and test incase the org has more condition values
	INSERT INTO #tmpOrgConditionsChanged
	select distinct [uid]
	from (
		select [uid], datatypeid, displaytypeid, expressionid, [datepart], dateexpressionid, [verbose], fieldCode, 
			conditionKey, conditionvalue, afid
		from #tmpOrgGrpConditions
		where fieldCode <> 'ev_entry'
			except
		select c.[uid], c.datatypeid, c.displaytypeid, c.expressionid, c.[datepart], c.dateexpressionid, c.[verbose], af.useFieldCode, 
			sCK.itemType as conditionkey, coalesce(cast(c.useValue as varchar(10)),c.conditionvalue) COLLATE Latin1_General_CS_AS as conditionValue, isnull(sAF.useID,0) as afid
		from dataTransfer.dbo.sync_vgc_conditions as c
		inner join datatransfer.dbo.sync_vgc_allfields as af on af.orgID = @orgID and af.fieldCode = c.fieldCode
		left outer join dataTransfer.dbo.sync_vgc_supporting as sCK on sCK.orgID = @orgID and sCK.cat = 'ck' and sCK.itemID = c.conditionkeyID
		left outer join dataTransfer.dbo.sync_vgc_supporting as sAF on sAF.orgID = @orgID and sAF.cat = 'af' and sAF.itemID = c.afid
		where c.orgID = @orgID
		and c.finalAction is null
	) tmp
	where not exists (select 1 from #tmpOrgConditionsChanged where [uid] = tmp.[uid]);

	-- update action
	update c
	set c.finalAction = 'C'
	from dataTransfer.dbo.sync_vgc_conditions as c
	inner join #tmpOrgConditionsChanged as tmp on tmp.uid = c.uid
	where c.orgID = @orgID 
	and c.finalAction is null;
	
	-- new rules
	update r
	set r.finalAction = 'A'
	from datatransfer.dbo.sync_vgc_rules as r
	left outer join dbo.ams_virtualGroupRules as vgr on vgr.orgID = @orgID 
		and vgr.uid = r.uid
		and vgr.ruleTypeID = 1
	where r.orgID = @orgID
	and vgr.ruleID is null
	and r.finalAction is null;

	-- find rules to be updated
	INSERT INTO #tmpOrgGrpRulesChanged
	select distinct [uid]
	from (
		select r.uid, r.rulename, r.rulexml, r.isActive, rg.useGroupID as groupid
		from dataTransfer.dbo.sync_vgc_rules as r
		left outer join dataTransfer.dbo.sync_vgc_allrulegroups as rg on rg.orgID = @orgID and rg.groupid = r.groupid
		where r.orgID = @orgID
		and r.finalAction is null
			except
		select uid, rulename, rulexml, isActive, groupid
		from #tmpOrgGrpRules
	) tmp;

	-- flip and test incase the org has more rule groups
	INSERT INTO #tmpOrgGrpRulesChanged
	select distinct [uid]
	from (
		select uid, rulename, rulexml, isActive, groupid
		from #tmpOrgGrpRules
			except
		select r.uid, r.rulename, r.rulexml, r.isActive, rg.useGroupID as groupid
		from dataTransfer.dbo.sync_vgc_rules as r
		left outer join dataTransfer.dbo.sync_vgc_allrulegroups as rg on rg.orgID = @orgID and rg.groupid = r.groupid
		where r.orgID = @orgID
		and r.finalAction is null
	) tmp
	where not exists (select 1 from #tmpOrgGrpRulesChanged where [uid] = tmp.[uid]);

	-- update action
	update r
	set r.finalAction = 'C'
	from dataTransfer.dbo.sync_vgc_rules as r
	inner join #tmpOrgGrpRulesChanged as tmp on tmp.uid = r.uid
	where r.orgID = @orgID
	and r.finalAction is null;


	-- to be deleted conditions
	INSERT INTO #tmpOrgDeleteGrpConditions (uid, verbose)
	select distinct c.[uid], c.verbose
	from dbo.ams_virtualGroupConditions as c
	left outer join dataTransfer.dbo.sync_vgc_conditions as sc on sc.orgID = @orgID and sc.uid = c.uid
	where c.orgID = @orgID
	and c.conditionTypeID = 1
	and sc.conditionID is null;

	-- to be deleted rules
	INSERT INTO #tmpOrgDeleteGrpRules (uid, rulename)
	select distinct r.[uid], r.rulename
	from dbo.ams_virtualGroupRules as r
	left outer join dataTransfer.dbo.sync_vgc_rules as sr on sr.orgID = @orgID and sr.uid = r.uid
	where r.orgID = @orgID
	and r.ruleTypeID = 1
	and sr.rulename is null;


	on_done:
	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('condition'), root('newconditions'), type),'<newconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('condition'), root('updateconditions'), type),'<updateconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from dataTransfer.dbo.sync_vgc_conditions
			where orgID = @orgID
			and finalAction = 'I'
			FOR XML path('condition'), root('ignoreconditions'), type),'<ignoreconditions/>'),

			isnull((select distinct [uid] as "@uid", verbose as "@verbose"
			from #tmpOrgDeleteGrpConditions
			FOR XML path('condition'), root('removeconditions'), type),'<removeconditions/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('rule'), root('newrules'), type),'<newrules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('rule'), root('updaterules'), type),'<updaterules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from dataTransfer.dbo.sync_vgc_rules
			where orgID = @orgID
			and finalAction = 'I'
			FOR XML path('rule'), root('ignorerules'), type),'<ignorerules/>'),

			isnull((select distinct [uid] as "@uid", rulename as "@rulename"
			from #tmpOrgDeleteGrpRules
			FOR XML path('rule'), root('removerules'), type),'<removerules/>'),
			
			isnull((select dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", errorCode as "@errorcode"
			from #tblImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')

		for xml path('import'), TYPE);


	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgConditionsChanged') IS NOT NULL 
		DROP TABLE #tmpOrgConditionsChanged;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRules;
	IF OBJECT_ID('tempdb..#tmpOrgGrpRulesChanged') IS NOT NULL 
		DROP TABLE #tmpOrgGrpRulesChanged;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpConditions') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpConditions;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteGrpRules') IS NOT NULL 
		DROP TABLE #tmpOrgDeleteGrpRules;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.an_emailNotice
@siteID int,
@noticeID int,
@message varchar(max),
@receiveRFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @recordedByMemberID int, @maxEmailsAllowedPerNotice int = 25000, @messageID int, @messageTypeID int, @contentVersionID int,
		@messageTypeCode varchar(15), @resourceTypeID int, @sendingSiteResourceID int, @newContentID int, 
		@newResourceID int,@contentTitle varchar(100), @messageStatusIDInserting int, @messageStatusIDQueued int, 
		@rawcontent varchar(max), @fieldID int, @fieldName varchar(300), @environmentID int, @orgID int, 
		@emailExtMergeCodeQueueTypeID int, @itemGroupUID uniqueidentifier = NEWID(), @emailExtMergeCodeInsertingQueueStatusID int, 
		@emailExtMergeCodeReadyQueueStatusID int, @globalTmpHoldTable varchar(100), @globalTmpTable varchar(100), 
		@colList varchar(max), @joinList varchar(max), @vwSQL varchar(max), @ParamDefinition nvarchar(100), 
		@dynSQL varchar(max), @fieldValueString varchar(max), @mcSQL nvarchar(max), @environmentName varchar(50);
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL, noticeID int);

	select @environmentName = tier from dbo.fn_getServerSettings();
	select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

	select @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'ANNOUNCE';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	
	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@emailExtMergeCodeQueueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@emailExtMergeCodeQueueTypeID, @queueStatus='insertingItems', @queueStatusID=@emailExtMergeCodeInsertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@emailExtMergeCodeQueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@emailExtMergeCodeReadyQueueStatusID OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotice') IS NOT NULL 
		DROP TABLE #tmpNotice;
	IF OBJECT_ID('tempdb..#tmpNoticeRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpNoticeRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpNoticeMessageIDs') IS NOT NULL
		DROP TABLE #tmpNoticeMessageIDs;
	CREATE TABLE #tmpNoticeRecipientsMID (noticeID int, memberID int, memberNumber varchar(50), fullname varchar(150), mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int, 
		recipientID int null, itemUID uniqueidentifier);
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40), noticeID int);
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName VARCHAR(300), fieldID int, isExtMergeCode bit, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpNoticeMessageIDs (noticeID int, messageID int, PRIMARY KEY (noticeID, messageID));

	SELECT n.noticeID, s.siteID, o.orgID, o.orgCode, s.siteCode, n.siteResourceID, ISNULL(optout_cl.consentListID,s.defaultConsentListID) AS optOutEmailConsentListID,
		coalesce(nullif(n.emailFromName,''),nullif(c.emailFromName,''),s.sitename) as emailFromName, net.emailFrom as emailFromEmail,
		coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailReplyToEmail,
		net.supportProviderEmail as emailSenderEmail,
		cl.contentTitle as emailSubject, n.noticeContentID, n.notifyEmail, NEWID() as messageUID, 
		0 as numRecipients, @maxEmailsAllowedPerNotice as maxEmailsAllowedPerNotice, 0 as hasExtendedMergeCodes, 100 as initialQueuePriority
	INTO #tmpNotice
	FROM dbo.an_notices as n 
	INNER JOIN dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND n.siteResourceID = sr.siteResourceID 
		AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.sites as s on s.siteID = sr.siteID
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	INNER JOIN dbo.siteHostNames as sh on sh.siteID = s.siteID 
	inner join dbo.siteEnvironments as se on se.siteID = sh.siteID
		AND se.environmentID = @environmentID
		AND se.mainHostnameID = sh.hostNameID
	inner join dbo.networkSites as ns on ns.siteID = s.siteID AND ns.isLoginNetwork = 1
	INNER JOIN dbo.networks as net on net.networkID = ns.networkID
	INNER JOIN dbo.an_centers as c on c.centerID = n.centerID
	INNER JOIN dbo.cms_applicationInstances as Cai on Cai.siteID = @siteID
		AND Cai.applicationInstanceID = c.applicationInstanceID
	INNER JOIN dbo.cms_siteResources as Csr on Csr.siteID = @siteID
		AND Cai.siteResourceID = Csr.siteResourceID 
		AND Csr.siteResourceStatusID = 1
	INNER JOIN dbo.cms_siteResources as CparentResource on CparentResource.siteID = @siteID
		AND CparentResource.siteResourceID = Csr.parentSiteResourceID
	LEFT OUTER JOIN dbo.cms_siteResources as CgrandparentResource
		INNER JOIN dbo.cms_applicationInstances as CCommunityInstances on CCommunityInstances.siteID = @siteID
			AND CcommunityInstances.siteResourceID = CgrandParentResource.siteResourceID
		INNER JOIN dbo.cms_siteResourceTypes as Csrt on Csrt.resourceTypeID = CgrandparentResource.resourceTypeID AND Csrt.resourceType = 'Community'
		on CgrandparentResource.siteResourceID = CparentResource.parentSiteResourceID
	INNER JOIN dbo.cms_contentLanguages as cl ON cl.siteID = @siteID
		AND cl.contentID = n.noticeContentID 
		AND cl.languageID = 1
	INNER JOIN dbo.cms_contentVersions as cv on cv.siteID = @siteID
		AND cv.contentLanguageID = cl.contentLanguageID 
		AND cv.isActive = 1
	CROSS APPLY dbo.fn_getContent(n.noticeContentID,1) as noticeContent
	LEFT OUTER JOIN platformMail.dbo.email_consentLists AS optout_cl 
		INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
			AND clt.consentListTypeID = optout_cl.consentListTypeID
		INNER JOIN platformMail.dbo.email_consentListModes AS clm ON clm.consentListModeID = optout_cl.consentListModeID
			AND clm.modeName = 'Opt-Out'
		ON optout_cl.consentListID = c.emailConsentListID
		AND optout_cl.[status] = 'A'
	WHERE n.emailNotice = 1
	AND n.emailDateScheduled < getDate()
	AND n.endDate > getDate()
	AND n.emailDateSent IS NULL
	AND noticeContent.rawContent LIKE '_%'
    AND n.noticeID = @noticeID AND s.siteID = @siteID;

    INSERT INTO #tmpNoticeRecipientsMID (noticeID, memberID, memberNumber, fullName, mc_emailBlast_email, mc_emailBlast_emailTypeID, itemUID)
    SELECT n.noticeID, min(m.memberID) as memberID, m.memberNumber, m.firstname + ' ' + m.lastname as fullname, me.email, metag.emailTypeID, NEWID()
    FROM dbo.an_notices as n
    INNER JOIN #tmpNotice as tmp on tmp.noticeID = n.noticeID and tmp.siteID = @siteID
    INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
        and srfrp.siteResourceID = n.siteResourceID
        and srfrp.functionID = @receiveRFID
    INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
        and gprp.rightPrintID = srfrp.rightPrintID
    INNER JOIN dbo.cache_perms_groupPrints as gp on gp.groupPrintID = gprp.groupPrintID
    INNER JOIN dbo.ams_members as m on m.orgID = @orgID 
		and m.groupPrintID = gp.groupPrintID
        and m.status = 'A'
    INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
        and me.memberID = m.memberID 
    INNER JOIN dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
		and metag.memberID = me.memberID
        and metag.emailTypeID = me.emailTypeID
    INNER JOIN dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
        and metagt.emailTagType = 'Primary'
    WHERE me.email <> ''
    GROUP BY n.noticeID, me.email, m.memberNumber, m.firstname, m.lastname, metag.emailTypeID;

	-- put recipient count in notices table
	update #tmpNotice set numRecipients = (select count(*) from #tmpNoticeRecipientsMID where noticeID = #tmpNotice.noticeID);

	update #tmpNotice set initialQueuePriority =  platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	numRecipients);

	-- cancel notices that are over the threshold of recipients or have no recipients
	update n
	set n.emailNotice = 0
	from dbo.an_notices as n
	inner join #tmpNotice as tmp on tmp.noticeID = n.noticeID
	where tmp.numRecipients >= @maxEmailsAllowedPerNotice
	or tmp.numRecipients = 0;

	-- delete notices where there are no recipients
	delete from #tmpNotice where numRecipients = 0;

	IF NOT EXISTS (select 1 from #tmpNotice)
		GOTO on_done;

	-- add any necessary metadata fields
    select @globalTmpHoldTable=null, @globalTmpTable=null, @rawcontent=null, @colList=null, @joinList=null;

    select @globalTmpHoldTable = '##tmpHold' + replace(cast(messageUID as varchar(36)),'-', ''), 
        @globalTmpTable = '##tmp' + replace(cast(messageUID as varchar(36)),'-', ''), @rawcontent = @message
    from #tmpNotice
    where noticeID = @noticeID;

    IF OBJECT_ID('tempdb..' + @globalTmpHoldTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpHoldTable);
    IF OBJECT_ID('tempdb..' + @globalTmpTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpTable);

    -- find/insert merge code fields
    EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@noticeID, @referenceType='emailnotice', @messageToParse=@rawcontent, @extraMergeCodeList='';

	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpNoticeRecipientsMID
	WHERE noticeID = @noticeID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@rawcontent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    set @vwSQL = null;
    IF @colList is null
        set @vwSQL = 'select memberID, noticeID into ' + @globalTmpHoldTable + ' from #tmpNoticeRecipientsMID where noticeID = ' + cast(@noticeID as varchar(10)) + ';'
    ELSE
        set @vwSQL = 'select m.memberID, m.noticeID, ' + @colList + ' 
            into ' + @globalTmpHoldTable + '
            from #tmpNoticeRecipientsMID as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID
            where m.noticeID = ' + cast(@noticeID as varchar(10)) + ';';
        
    EXEC(@vwSQL);

    set @dynSQL = null;
    set @dynSQL = 'select m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
                    m.membernumber, m.firstname + '' '' + m.lastname as fullname, 
                    m.firstname + isnull('' '' + nullif(m.middlename,''''),'''') + '' '' + m.lastname + isnull('' '' + nullif(m.suffix,''''),'''') as extendedname, 
                    tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, vw.*
                into ' + @globalTmpTable + '
                from #tmpNoticeRecipientsMID as tmp
                inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = tmp.memberID
                inner join ' + @globalTmpHoldTable + ' as vw on vw.memberID = m.memberID 
                where tmp.noticeID = ' + cast(@noticeID as varchar(10)) + ';'
    EXEC(@dynSQL);

    IF OBJECT_ID('tempdb..' + @globalTmpHoldTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpHoldTable);

    -- get tmpRecipients columns
    INSERT INTO #tmpRecipientsCols
    select c.column_id, c.name, t.name, @noticeID
    from tempdb.sys.columns as c
    INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
    where c.object_id = object_id('tempdb..' + @globalTmpTable);

		
	-- update tmpNotices having extendedMergeCodes
	update tmp 
	set tmp.hasExtendedMergeCodes = 1
	from #tmpNotice as tmp
	where exists (select 1 from #tmpEmailMetaDataFields where referenceID = tmp.noticeID and isExtMergeCode = 1);

    -- to get contentVersionID for the message
	SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @messageTypeCode = 'ANNOUNCE'

	select @messageTypeID = messageTypeID
	from platformMail.dbo.email_messageTypes 
	where messageTypeCode = @messageTypeCode;

	select @sendingSiteResourceID = ai.siteResourceID
	from dbo.cms_applicationInstances as ai
		inner join dbo.cms_siteResources as sr on 
			sr.siteID =  @siteID
			and sr.siteResourceID = ai.siteResourceID
		inner join dbo.an_notices as n  on
			n.noticeID = @noticeID
			and ai.siteResourceID = n.siteResourceID
	where ai.siteID =  @siteID
		and sr.siteResourceStatusID = 1;	
     
    SELECT @contentTitle =oi.organizationName 
    FROM #tmpNotice as tmpN
	INNER join membercentral.dbo.organizations o ON o.orgID = tmpN.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	where tmpN.noticeid= @noticeID;

	IF @recordedByMemberID = 0
		select @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
    EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
		@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1, 
		@languageID=1, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@rawContent, 
		@memberID=@recordedByMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

	select top 1 @contentVersionID = cv.contentVersionID
	from dbo.cms_content as c 
	inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
	inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	where c.contentID = @newContentID;

	-- each notice becomes its own email_message (as queued status)
	insert into platformMail.dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, 
		recordedByMemberID, fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, 
		messageWrapper, [uid], sendOnDate, referenceType, referenceID, isTestMessage)
		output inserted.messageID, inserted.referenceID into #tmpNoticeMessageIDs (messageID, noticeID)
	select @messageTypeID, siteID, siteResourceID, getdate(), @recordedByMemberID, emailFromName,
		emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject, @contentVersionID, 
		'', messageUID, getdate(), 'AnnounceNotice', noticeID, 0
	from #tmpNotice
	where numRecipients < @maxEmailsAllowedPerNotice;

	-- email consentLists
	INSERT INTO platformMail.dbo.email_messageConsentLists (siteID, messageID, consentListID, isPrimary)
	SELECT n.siteID, tmp.messageID, n.optOutEmailConsentListID, 1
	FROM #tmpNotice AS n
	INNER JOIN #tmpNoticeMessageIDs AS tmp ON tmp.noticeID = n.noticeID
	WHERE n.optOutEmailConsentListID IS NOT NULL;

	-- add recipients
	insert into platformMail.dbo.email_messageRecipientHistory(messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select tmp.messageID, tmpR.memberID, getdate(), tmpR.fullname, tmpR.mc_emailBlast_email, 
		@messageStatusIDInserting, null, null, tmpR.mc_emailBlast_emailTypeID, tmpN.siteID, tmpN.initialQueuePriority
	from #tmpNoticeMessageIDs tmp
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID

	-- no need to support multiple recipientIDs per memberID per noticeID, so simple join is sufficient
	update tmpR
	set tmpR.recipientID = r.recipientID
	from #tmpNoticeMessageIDs tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.siteID = @siteID
		and r.messageID = tmp.messageID
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID
		and tmpR.memberID = r.memberID;

	-- loop over each notice to add any necessary metadata fields

	select @messageID = min(tmp.messageID)
	from #tmpNoticeMessageIDs tmp

	while @messageID is not null BEGIN
		select @noticeID = null, @siteID = null, @globalTmpTable = null, @fieldID = null, @ParamDefinition = null;

		select @noticeID = tmpN.noticeID, @siteID = tmpN.siteID, @globalTmpTable = '##tmp' + replace(cast(tmpN.messageUID as varchar(36)),'-', '')
		from #tmpNoticeMessageIDs tmp
		inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
		where tmp.messageID = @messageID;
		
		-- add recipient metadata
		set @ParamDefinition = N'@messageID int, @fieldID int';	
		select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 0;
		while @fieldID is not null BEGIN
			select @fieldName = null, @mcSQL = null, @fieldValueString = null;
			select @fieldName = fieldName from #tmpEmailMetaDataFields where referenceID = @noticeID and fieldID = @fieldID and isExtMergeCode = 0;

			-- ensure field is available (could be a bad merge code)
			IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName and noticeID = @noticeID) BEGIN
				set @fieldValueString = 'isnull(cast(tmp.[' + @fieldName + '] as varchar(max)),'''')';

				set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
					select @messageID, @fieldID, tmp.memberID, fieldValue = ' + @fieldValueString + ', tmpR.recipientID
					from ' + @globalTmpTable +  ' as tmp
					inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmp.noticeID
						and tmpR.memberID = tmp.memberID;';
				exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
			END

			select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 0 and fieldID > @fieldID;
		END

		-- has extended merge codes
		IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 1) BEGIN
			
			-- queue recipient details with extended merge codes
			INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @emailExtMergeCodeInsertingQueueStatusID
			from #tmpNoticeRecipientsMID
			where noticeID = @noticeID;

			-- recipientID and messageID
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
			from #tmpNoticeRecipientsMID as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCRecipientID'
			where tmp.noticeID = @noticeID
				union
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
			from #tmpNoticeRecipientsMID as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCMessageID'
			where tmp.noticeID = @noticeID;

			-- ext merge code fields
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
			select distinct @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
			from #tmpEmailMetaDataFields as mdf
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
			inner join #tmpNoticeRecipientsMID as tmp on tmp.noticeID = @noticeID
			where mdf.referenceID = @noticeID
			and mdf.isExtMergeCode = 1;

			-- update queue item groups to show ready to process
			UPDATE qi
			SET qi.queueStatusID = @emailExtMergeCodeReadyQueueStatusID,
				qi.dateUpdated = GETDATE()
			FROM platformQueue.dbo.tblQueueItems as qi
			INNER JOIN #tmpNoticeRecipientsMID as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.noticeID = @noticeID;
		END

		select @messageID = min(tmp.messageID)
		from #tmpNoticeMessageIDs tmp
		where tmp.messageID > @messageID;
	END

	-- mark recipient as ready for notice recipients which doesn't have extended merge codes
	update r
	set r.emailStatusID = @messageStatusIDQueued
	from #tmpNoticeMessageIDs tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.siteID = @siteID
		and r.messageID = tmp.messageID
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID
		and tmpR.memberID = r.memberiD
		and tmpN.hasExtendedMergeCodes = 0;

	-- update notices with datesent
	update n
	set n.emailDateSent = getDate()
	from dbo.an_notices as n
	inner join #tmpNotice as tmp on tmp.noticeID = n.noticeID
	where tmp.numRecipients < @maxEmailsAllowedPerNotice;


	on_done:

	-- return notices
	select noticeID, siteID, orgID, orgCode, siteCode, siteResourceID, emailFromName, emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject,
		notifyEmail, messageUID, @message, numRecipients, maxEmailsAllowedPerNotice, hasExtendedMergeCodes
	from #tmpNotice;

	-- drop global tmp tables
	set @noticeID = null;
	select @noticeID = min(noticeID) from #tmpNotice;
	while @noticeID is not null BEGIN
		select @globalTmpTable=null;

		select @globalTmpTable = '##tmp' + replace(cast(messageUID as varchar(36)),'-', '')
		from #tmpNotice
		where noticeID = @noticeID;

		IF OBJECT_ID('tempdb..' + @globalTmpTable) IS NOT NULL 
			EXEC('DROP TABLE ' + @globalTmpTable);

		select @noticeID = min(noticeID) from #tmpNotice where noticeID > @noticeID;
	END


	IF OBJECT_ID('tempdb..#tmpNotice') IS NOT NULL 
		DROP TABLE #tmpNotice;
	IF OBJECT_ID('tempdb..#tmpNoticeRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpNoticeRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpNoticeMessageIDs') IS NOT NULL
		DROP TABLE #tmpNoticeMessageIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_CONSENTLIST_ENTRY

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @GlobalOptOutModeID int;
	SELECT TOP 1 @orgID = orgID FROM #tblCondALL;
	SELECT @GlobalOptOutModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'GlobalOptOut';


	-- email types, no dates
	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NULL
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTypes AS clSplitET 
		ON clSplitET.conditionID = clSplit.conditionID
		AND clSplitET.emailTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		AND me.emailTypeID = clSplitET.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END;


	-- email types, with dates
	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND (clSplit.clDateAddedLower IS NOT NULL OR clSplit.clDateAddedUpper IS NOT NULL)
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTypes AS clSplitET 
		ON clSplitET.conditionID = clSplit.conditionID
		AND clSplitET.emailTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		AND me.emailTypeID = clSplitET.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE 
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated BETWEEN clSplit.clDateAddedLower AND clSplit.clDateAddedUpper THEN 1
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NULL AND clm.dateCreated >= clSplit.clDateAddedLower THEN 1
			WHEN clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated <= clSplit.clDateAddedUpper THEN 1
			ELSE 0 
			END;



	-- email tags, no dates

	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NULL
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTagTypes AS clSplitETag 
		ON clSplitETag.conditionID = clSplit.conditionID
		AND clSplitETag.emailTagTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmailTags AS metag 
		ON metag.orgID = @orgID
		AND metag.memberID = m.memberID
		and metag.emailTagTypeID = clSplitETag.emailTagTypeID
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		and me.emailTypeID = metag.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END;



	-- email tags, with dates

	INSERT INTO #cache_members_conditions_shouldbe
	SELECT DISTINCT m.memberID, tblc.conditionID
	FROM #tblCondALL AS tblc
	INNER JOIN #tblConsentListSplit AS clSplit 
		ON clSplit.conditionID = tblc.conditionID
		AND (clSplit.clDateAddedLower IS NOT NULL OR clSplit.clDateAddedUpper IS NOT NULL)
	INNER JOIN platformMail.dbo.email_consentListModes AS mode ON mode.consentListModeID = clSplit.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListTypeID = clt.consentListTypeID
		AND cl.[status] = 'A'
		AND (
			(mode.modeName = 'Opt-Out' AND cl.consentListModeID IN (clSplit.consentListModeID, @GlobalOptOutModeID)) OR
			(mode.modeName = 'Opt-In' AND cl.consentListModeID = clSplit.consentListModeID)
		)
	INNER JOIN #tblConsentListSplitEmailTagTypes AS clSplitETag 
		ON clSplitETag.conditionID = clSplit.conditionID
		AND clSplitETag.emailTagTypeID IS NOT NULL
	CROSS JOIN #tblMCQCondCacheMem as m
	INNER JOIN dbo.ams_memberEmailTags AS metag 
		ON metag.orgID = @orgID
		AND metag.memberID = m.memberID
		and metag.emailTagTypeID = clSplitETag.emailTagTypeID
	INNER JOIN dbo.ams_memberEmails AS me 
		ON me.orgID = @orgID
		and me.emailTypeID = metag.emailTypeID
		and me.memberID = m.memberID
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm 
		ON clm.consentListID = cl.consentListID
		and me.email = clm.email
	LEFT OUTER JOIN #tblConsentListSplitListTypes AS clSplitLT ON clSplitLT.conditionID = clSplit.conditionID
		AND clSplitLT.consentListTypeID IS NOT NULL
	LEFT OUTER JOIN #tblConsentListSplitLists AS clSplitL ON clSplitL.conditionID = clSplit.conditionID
		AND clSplitL.consentListID IS NOT NULL
	WHERE tblc.subProc = 'CONSENTLIST_ENTRY'
	AND 1 = CASE WHEN clSplitLT.consentListTypeID IS NOT NULL THEN CASE WHEN cl.consentListTypeID = clSplitLT.consentListTypeID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE WHEN clSplitL.consentListID IS NOT NULL THEN CASE WHEN cl.consentListID = clSplitL.consentListID THEN 1 ELSE 0 END ELSE 1 END
	AND 1 = CASE 
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated BETWEEN clSplit.clDateAddedLower AND clSplit.clDateAddedUpper THEN 1
			WHEN clSplit.clDateAddedLower IS NOT NULL AND clSplit.clDateAddedUpper IS NULL AND clm.dateCreated >= clSplit.clDateAddedLower THEN 1
			WHEN clSplit.clDateAddedLower IS NULL AND clSplit.clDateAddedUpper IS NOT NULL AND clm.dateCreated <= clSplit.clDateAddedUpper THEN 1
			ELSE 0 
			END;


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.createSite
@orgID int,
@environmentName varchar(50),
@sitecode varchar(10),
@siteName varchar(60),
@mainNetworkID int,
@isLoginNetwork bit,
@isMasterSite bit,
@hasDepoTLA bit,
@defaultLanguageID int,
@defaultTimeZoneID int,
@defaultCurrencyTypeID int,
@showCurrencyType bit,
@allowGuestAccounts bit,
@forceLoginPage bit,
@useRemoteLogin bit,
@affiliationRequired bit,
@enforceSiteAgreement bit,
@immediateMemberUpdates bit,
@emailMemberUpdates varchar(200),
@defaultPostalState varchar(10),
@joinURL varchar(100),
@alternateGuestAccountCreationLink varchar(400),
@alternateGuestAccountPopup bit,
@alternateForgotPasswordLink varchar(400),
@norightsContent varchar(max),
@norightsNotLoggedInContent varchar(max),
@inactiveUserContent varchar(max),
@siteagreementContent varchar(max),
@welcomeMessageContent varchar(max),
@firstTimeLoginContent varchar(max),
@defaultOrgIdentityID int,
@qualifyFID int,
@siteID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- start in snapshot so we can go in and out as needed
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @templateID int, @modeID int, @sectionID int, @sectionResourceTypeID int, @siteResourceTypeID int, 
		@accountingAdminRoleID int, @clientAdminRoleID int, @contentEditorRoleID int, @siteAdminRoleID int, @superAdminRoleID int, 
		@reportAdminRoleID int, @eventAdminRoleID int, @platformWideNetworkID int, @superBillingAdminRoleID int, 
		@siteAdminGroupID int, @superAdminGroupID int, @trashID int, @superBillingAdminsGroupID int, 
		@siteResourceID int, @hostnameID int, @templateTypeID int, @sysCreatedContentResourceTypeID int, @nowDate datetime,
		@newContentid int, @newresourceid int, @superNetworkID int, @sysMemberID int, @loginLimitModeID int,
		@mcapiSecret varchar(40), @apiusername varchar(40), @apipassword varchar(40), @sysAdminRoleID int, @mcDevGroupID int,
		@consentListTypeID int, @consentListModeID int, @consentListID int, @generalCommunicationsConsentListTypeName varchar(100),
		@environmentID int, @searchJoinURL varchar(200),  @customHeadContent varchar(max), @loginPolicyID int, @policySRID int,
		@tier varchar(12), @recCountOfFactors int, @subscriptionIssuesEmail VARCHAR(MAX), @memberAdminSiteResourceID int;

	SET @siteID = null;
	SET @siteCode = UPPER(@sitecode);

	IF dbo.fn_isValidUsageCode(@siteCode,'site') = 0
		RAISERROR('Invalid SiteCode.',16,1);

	SET @siteResourceTypeID = dbo.fn_getResourceTypeID('Site');
	SET @accountingAdminRoleID = dbo.fn_getResourceRoleID('Accounting Administrator');
	SET @clientAdminRoleID = dbo.fn_getResourceRoleID('Client Administrator');
	SET @contentEditorRoleID = dbo.fn_getResourceRoleID('Content Editor');
	SET @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator');
	SET @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator');
	SET @reportAdminRoleID = dbo.fn_getResourceRoleID('Report Administrator');
	SET @eventAdminRoleID = dbo.fn_getResourceRoleID('Event Administrator');
	SET @sysAdminRoleID = dbo.fn_getResourceRoleID('System Operations Administrator');
	SET @superBillingAdminRoleID = dbo.fn_getResourceRoleID('Super Billing Administrator');
	select @siteAdminGroupID = groupID from dbo.ams_groups where orgID = @orgID and groupCode = 'SiteAdmins';
	select @superAdminGroupID = groupID from dbo.ams_groups where orgID = 1 and groupCode = 'SuperAdmins';
	select @mcDevGroupID = groupID from dbo.ams_groups where orgID = 1 and groupCode = 'Developers';
	SET @superBillingAdminsGroupID = dbo.fn_getGroupIDFromGroupName(1,'Super Billing Admins');
	select @loginLimitModeID = loginLimitModeID from dbo.siteLoginLimitModes where loginLimitModeCode = 'Unlimited';
	SET @templateTypeID = dbo.fn_getTemplateTypeID('Page');
	SET @modeID = dbo.fn_getModeID('Normal');
	SET @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection');
	SET @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent');
	SET @superNetworkID = dbo.fn_getNetworkID('MemberCentral Super Administrators');
	SET @sysMemberID = dbo.fn_ams_getMCSystemMemberID();
	SET @platformWideNetworkID = dbo.fn_getNetworkID('Platform Wide');
	select @tier = tier from dbo.fn_getServerSettings();
	SET @nowDate = getdate();
	SET @subscriptionIssuesEmail = '<EMAIL>';

	BEGIN TRAN;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		-- add to sites
		INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
			allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
			defaultPostalState, joinURL, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
			alternateForgotPasswordLink, showCurrencyType, enableMobile, enableDeviceDetection, loginLimitModeID, defaultOrgIdentityID, loginOrgIdentityID, subscriptionIssuesEmail)
		VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
			@allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
			@defaultPostalState, @joinURL, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
			@alternateForgotPasswordLink, @showCurrencyType, 0, 0, @loginLimitModeID, @defaultOrgIdentityID, @defaultOrgIdentityID, @subscriptionIssuesEmail);
		SET @siteID = SCOPE_IDENTITY();

		-- add to siteFeatures with all defaults
		INSERT INTO dbo.siteFeatures (siteID)
		VALUES (@siteID);

		-- createSiteLanguage		
		EXEC dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID;

		-- create a resourceID for the site
		exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
			@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT;
	
		-- update site with new resource
		UPDATE dbo.sites
		SET siteResourceID = @siteResourceID
		WHERE siteID = @siteID;

		-- update org defaultSiteID
		UPDATE dbo.organizations
		SET defaultSiteID = @siteID
		WHERE orgID = @orgID
		AND defaultSiteID IS NULL;
		
		-- give clientAdmin Role to SiteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@clientAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give accountingAdmin Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@accountingAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give reportAdminRoleID Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@reportAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give eventAdminRoleID Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@eventAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give ContentEditor Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@contentEditorRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give siteAdmin Role to siteAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@siteAdminRoleID, @groupID=@siteAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give superAdmin Role to superAdmin Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@superAdminRoleID, @groupID=@superAdminGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give sysOpsAdmin Role to developer Group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@sysAdminRoleID, @groupID=@mcDevGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- give superBillingAdmin to SuperBillingAdmin group
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
			@functionIDList=null, @roleID=@superBillingAdminRoleID, @groupID=@superBillingAdminsGroupID, 
			@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

		-- add default 4 part hostname for all environments
		insert into dbo.siteHostnames (siteID, hostname, environmentID, rootDomain, sslCertFilename,sslPrivateKeyFilename,internalIPAddress,hasSSL)
		select s.siteID, lower(s.sitecode + '.' + wh.wildcardHostname), e.environmentID, 
			dbo.fn_getRootDomainFromSiteHostName(wh.wildcardHostname),
			wh.sslCertFilename,wh.sslPrivateKeyFilename,wh.internalIPAddress, wh.hasSSL
		from dbo.platform_environments as e
		inner join dbo.platform_wildcardHostnames as wh on e.defaultWildcardHostnameID = wh.wildcardHostnameID
		inner join dbo.sites as s on s.siteID = @siteID;

		-- set siteEnvironment Defaults
		insert into dbo.siteEnvironments (siteID, environmentID, mainHostnameID, enableNGINXConf)
		select s.siteID, e.environmentID, sh.hostnameID, 1
		from dbo.sites as s
		inner join dbo.sitehostnames as sh on s.siteID = sh.siteID
			and s.siteID = @siteID
		inner join dbo.platform_wildcardHostnames as wh on wh.environmentID = sh.environmentID
		inner join dbo.platform_environments as e on e.defaultWildcardHostnameID = wh.wildcardHostnameID
		order by s.siteID, e.environmentID;

		select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

		select @searchJoinURL = 'http://' + sh.hostname
		from dbo.siteHostnames as sh 
		inner join dbo.siteEnvironments se on se.siteID = sh.siteID
			and se.environmentID = @environmentID
			and se.mainHostnameID = sh.hostNameID
			and sh.siteID = @siteID;

		-- add to depoTLA and memberCentralBilling if not exists
		IF @hasDepoTLA = 0 BEGIN
			INSERT INTO trialsmith.dbo.depoTLA ([state], [description], websitename, shortname, display, isLiveOnNewPlatform, 
				includeInTSEmailMarketing, includeInSWEmailMarketing, searchJoinURL)
			VALUES (@sitecode, @siteName, @siteName, @sitecode, -1, 1, 0, 0, @searchJoinURL);

			INSERT INTO trialsmith.dbo.memberCentralBilling (orgCode) VALUES (@sitecode);

			EXEC trialsmith.dbo.site_createDefaultBillingFeeSchedules @orgcode=@siteCode;
		END
		ELSE
			UPDATE trialsmith.dbo.depoTLA SET searchJoinURL = @searchJoinURL WHERE [state] = @sitecode;

		-- use default template
		select @templateID = templateID from dbo.cms_pageTemplates where siteID is null and templateName = 'MemberCentral Standard Template v1';

		-- add default page sections
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, 
			@sectionName='Root', @sectionCode='Root',@sectionBreadcrumb = null, @inheritPlacements=1, @sectionID=@sectionID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, 
			@sectionName='MCAMSMemberDocuments', @sectionCode='MCAMSMemberDocuments',@sectionBreadcrumb = null, @inheritPlacements=0, 
			@sectionID=@trashID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, 
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, 
			@sectionName='MCAMSEventDocuments', @sectionCode='MCAMSEventDocuments',@sectionBreadcrumb = null, @inheritPlacements=0, 
			@sectionID=@trashID OUTPUT;
		EXEC dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID,
			@ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID,
			@sectionName='MCAMSReportDocuments', @sectionCode='MCAMSReportDocuments',@sectionBreadcrumb = null, @inheritPlacements=0,
			@sectionID=@trashID OUTPUT;

		-- add default pages
		EXEC dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID;

		-- set memberAdminSiteResourceID column after creating admin tool resources
		SELECT @memberAdminSiteResourceID = sr.siteResourceID
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = sr.resourceTypeID
			AND srt.resourceType = 'memberAdmin'
		WHERE sr.siteID = @siteID
		AND sr.siteResourceStatusID = 1;

		UPDATE dbo.sites
		SET memberAdminSiteResourceID = @memberAdminSiteResourceID
		WHERE siteID = @siteID;

		-- default fieldsets - Needs to be run after admin has been created.
		EXEC dbo.cms_createDefaultFieldsets @siteid=@siteID;

		-- add default History (notes) Categories
		EXEC dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=@sysMemberID;

		-- add default email template trees
		EXEC dbo.cms_createDefaultEmailTemplateCategories @siteid=@siteID, @contributingMemberID=@sysMemberID;

		-- add default advance formulas
		EXEC dbo.cms_createDefaultAdvanceFormulas @siteid=@siteID;		

		-- add to networks
		EXEC dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite;
		EXEC dbo.createNetworkSite @networkID=@platformWideNetworkID, @siteID=@siteID, @isLoginNetwork=0, @isMasterSite=0;

		-- add content objects
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='NoRights', @contentDesc=null, @rawContent=@norightsContent, 
			@memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='NoRightsNotLoggedIn', @contentDesc=null, 
			@rawContent=@norightsNotLoggedInContent, @memberID=NULL, @contentID=@newContentID OUTPUT, 
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='InactiveUser', @contentDesc=null,
			@rawContent=@inactiveUserContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='SiteAgreement', @contentDesc=null,
			@rawContent=@siteagreementContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='Welcome Message', @contentDesc=null,
			@rawContent=@welcomeMessageContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID,
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle='First Time Login Message', @contentDesc=null,
			@rawContent=@firstTimeLoginContent, @memberID=NULL, @contentID=@newContentID OUTPUT,
			@siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='Initial Visit Warning', @contentDesc=null, 
			@rawContent='', @memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET HomePageWarningContentID = @newContentid where siteID = @siteID;

		SET @customHeadContent = '<meta name="viewport" content="width=device-width, initial-scale=1">';
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
			@parentSiteResourceID=null, @siteResourceStatusID=1, @isHTML=1, 
			@languageID=1, @isActive=1, @contentTitle='CustomHead', @contentDesc=null, 
			@rawContent=@customHeadContent, @memberID=NULL, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;
		UPDATE dbo.sites SET CustomHeadContentID = @newContentid where siteID = @siteID;

		-- link up superusers to all new sites
		INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
		SELECT distinct mnp.memberID, mnp.profileID, 'A', @nowDate, @siteID
		FROM dbo.ams_memberNetworkProfiles AS mnp 
		INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
		WHERE mnp.status = 'A'
		AND np.networkID = @superNetworkID
		AND np.status = 'A'
		AND mnp.siteID <> @siteID;

		-- enable site-specific passwords
		EXEC dbo.enableSiteFeature_sitePasswords @siteID=@siteID;

		-- add API token
		select @mcapiSecret = mcapiSecret from dbo.fn_getServerSettings();
		EXEC dbo.api_createLogin @siteID=@siteID, @nickname='Internal Use Only', @rmIDList=NULL, @ovPassword=@mcapiSecret,
			@isSystemToken=1, @username=@apiusername OUTPUT, @password=@apipassword OUTPUT;

		-- add default post type
		INSERT INTO dbo.cms_postTypes (siteID, typeName, typeUID, singularName, pluralName, description, dateCreated, dateUpdated, createdByMemberID)
		VALUES (@siteID, 'Articles', NEWID(), 'Article', 'Articles', NULL, @nowDate, @nowDate, @sysMemberID);

		-- add global opt-out consent list
		SELECT @consentListModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'GlobalOptOut';
		EXEC platformMail.dbo.email_addConsentListType @orgID=@orgID, @consentListTypeName='Global Lists', @isSystemType=1, 
			@consentListTypeID=@consentListTypeID OUTPUT;
		IF NOT EXISTS (SELECT TOP 1 consentListID FROM platformMail.dbo.email_consentLists WHERE consentListTypeID = @consentListTypeID AND consentListName = 'Global Opt-Out List' AND [status] = 'A')
			EXEC platformMail.dbo.email_addConsentList @siteID=@siteID, @consentListTypeID=@consentListTypeID, 
				@consentListName='Global Opt-Out List', @consentListDesc='You are currently on the global opt-out list.',
				@consentListModeID=@consentListModeID, @orgIdentityID=@defaultOrgIdentityID, @isHidden=1, 
				@enteredByMemberID=@sysMemberID, @consentListID=@consentListID OUTPUT;

		-- add general communications opt-out consent list
		select @generalCommunicationsConsentListTypeName = organizationShortName + ' Communications'
		from dbo.orgIdentities
		where orgIdentityID = @defaultOrgIdentityID;

		SET @consentListTypeID = null;
		EXEC platformMail.dbo.email_addConsentListType @orgID=@orgID, @consentListTypeName=@generalCommunicationsConsentListTypeName, @isSystemType=0, 
			@consentListTypeID=@consentListTypeID OUTPUT;
		IF NOT EXISTS (SELECT TOP 1 consentListID FROM platformMail.dbo.email_consentLists WHERE consentListTypeID = @consentListTypeID AND consentListName = 'General' AND [status] = 'A') BEGIN
			SELECT @consentListModeID = consentListModeID FROM platformMail.dbo.email_consentListModes WHERE modeName = 'Opt-Out';
			EXEC platformMail.dbo.email_addConsentList @siteID=@siteID, @consentListTypeID=@consentListTypeID, 
				@consentListName='General', @consentListDesc='General news and information',
				@consentListModeID=@consentListModeID, @orgIdentityID=@defaultOrgIdentityID, @isHidden=0, 
				@enteredByMemberID=@sysMemberID, @consentListID=@consentListID OUTPUT;
		END

		-- update site with consentListID
		UPDATE dbo.sites
		SET defaultConsentListID = @consentListID
		WHERE siteID = @siteID;

		-- add sendgrid subuser
		EXEC platformMail.dbo.sendgrid_createSubusers @siteID=@siteID, @siteCode=@siteCode;

		-- create Login Policy for Site Admin group
		SET @recCountOfFactors = CASE WHEN @tier = 'Production' THEN 2 ELSE 1 END;
		SELECT @loginLimitModeID = loginLimitModeID FROM dbo.siteLoginLimitModes WHERE loginLimitModeCode = 'SingleTest';

		EXEC dbo.site_createLoginPolicy
			@siteID=@siteID,
			@policyName='Site Admin Security',
			@policyCode='siteadminsecurity',
			@isMCStaffControlled=1,
			@loginLimitModeID=@loginLimitModeID,
			@maxDaysBetweenVerifications=14,
			@complianceDeadline=@nowDate,
			@complianceDaysForNewAccounts=2,
			@reqCountOfFactors=1,
			@recCountOfFactors=@recCountOfFactors,
			@noOfDaysForRecCountPrompts=14,
			@loginPolicyID=@loginPolicyID OUTPUT;

		SELECT @policySRID = siteResourceID FROM dbo.siteLoginPolicies WHERE loginPolicyID = @loginPolicyID;

		INSERT INTO dbo.siteLoginPolicyVerificationMethods (loginPolicyID, siteID, verificationMethodID, isRequired)
		SELECT @loginPolicyID, @siteID, verificationMethodID, isRequired = CASE methodCode WHEN 'Email' THEN 1 ELSE  0 END
		FROM dbo.platform_verificationMethods
		WHERE (@tier = 'Production' AND methodCode IN ('MFATOTP','MFASMS','Email'))
		OR (@tier <> 'Production' AND methodCode IN ('MFASMS'))

		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@policySRID, @include=1, 
			@functionIDList=@qualifyFID, @roleID=NULL, @groupID=@siteAdminGroupID, @inheritedRightsResourceID=NULL,
			@inheritedRightsFunctionID=NULL;


		-- add default soliciation message
		INSERT INTO dbo.tr_solicitationMessages (siteID, title, message)
		VALUES (@siteID, 'Voluntary Processing Fee Donation', 'Please help us recover our processing fees for accepting your payment.');
	COMMIT TRAN;

	EXEC dbo.cms_populateSiteResourceRightsCache @siteID=@siteID;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_previewEmailBlastSummary
@blastID int,
@mode tinyint,
@emailTypeID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @ruleID int, @orgID int, @globalOptOutListID INT, @excludeOptOuts bit = 0, @optOutMembersCount int = 0;

	select @ruleID = b.ruleID, @orgID = s.orgID
	from dbo.email_EmailBlasts as b
	inner join dbo.sites as s on s.siteID = b.siteID
	where b.blastID = @blastID;

	-- run rule to get members
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	CREATE TABLE #tmpVGRMembers (memberID int PRIMARY KEY);

	EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;


	IF OBJECT_ID('tempdb..#tblM') IS NOT NULL 
		DROP TABLE #tblM;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	CREATE TABLE #tblM (memberid int, emailTypeID int, hasEmail bit);
	CREATE TABLE #tmpRecipients (memberID INT, firstName varchar(75), lastName varchar(75), memberName varchar(200), memberCompany varchar(200), memberEmail varchar(200), emailTypeID int);
	CREATE TABLE #tmpFinalRecipients (memberID INT, firstName varchar(75), lastName varchar(75), memberName varchar(200), memberCompany varchar(200), memberEmail varchar(200), emailTypeID int);

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	IF @mode in (0,1,2,3,4,5)
		INSERT INTO #tblM (memberid, emailTypeID, hasEmail)
		SELECT m2.memberid, me.emailTypeID, case when len(me.email) > 0 then 1 else 0 end
		FROM #tmpVGRMembers rm
		INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
			AND m2.memberid = rm.memberID
		INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
			AND me.memberid = m2.memberid
		INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
			AND ebet.blastID = @blastID	
			UNION
		SELECT m2.memberid, me.emailTypeID, case when len(me.email) > 0 then 1 else 0 end
		FROM #tmpVGRMembers rm
		INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
			AND m2.memberid = rm.memberID
		INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
			AND me.memberid = m2.memberid
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
			AND metag.memberID = me.memberID
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.email_emailBlastEmailTagTypes as ebett on ebett.emailTagTypeID = metag.emailTagTypeID
			AND ebett.blastID = @blastID;
	IF @mode = 6
		INSERT INTO #tblM (memberid, emailTypeID, hasEmail)
		SELECT TOP 100 tmp.memberid, tmp.emailTypeID, tmp.hasEmail
		FROM (
			SELECT m2.memberid, me.emailTypeID, 1 as hasEmail
			FROM #tmpVGRMembers rm
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
				AND m2.memberid = rm.memberID
			INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
				AND me.memberid = m2.memberid
			INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
				AND ebet.blastID = @blastID
			WHERE me.email <> ''
				UNION
			SELECT m2.memberid, me.emailTypeID, 1 as hasEmail
			FROM #tmpVGRMembers rm
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
				AND m2.memberid = rm.memberID
			INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
				AND me.memberid = m2.memberid
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
				AND metag.memberID = me.memberID
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.email_emailBlastEmailTagTypes as ebett on ebett.emailTagTypeID = metag.emailTagTypeID
				AND ebett.blastID = @blastID
			WHERE me.email <> ''
		) tmp
		ORDER BY NEWID();
	
	-- get recipient member details
	INSERT INTO #tmpRecipients (memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT DISTINCT m.memberID, m.firstname, m.lastname, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company, me.email, me.emailTypeID
	FROM #tblM as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberid
	INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
		AND me.memberid = m.memberid 
		AND me.emailTypeID = tmp.emailTypeID
	WHERE tmp.hasEmail = 1;

	INSERT INTO #tmpFinalRecipients (memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID
	FROM #tmpRecipients;

	IF EXISTS (SELECT 1 FROM dbo.email_emailBlastConsentLists where blastID = @blastID) BEGIN
		SET @excludeOptOuts = 1;

		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		INNER JOIN platformMail.dbo.email_consentListMembers AS clm
			INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
				AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
			ON clm.email = tmp.memberEmail;

		SELECT @optOutMembersCount = COUNT(DISTINCT tmpR.memberID)
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
			AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
	END

	-- summary counts
	IF @mode = 0 BEGIN
		select summaryRow, summaryItem, emailTypeID, summaryItemCount, summaryLinkText = 
			case 
			when summaryRow in (1,3,4) and summaryItemCount = 0 then '0 recipients'
			when summaryRow in (1,3,4) and summaryItemCount > 0 then cast(summaryItemCount as varchar(10)) + ' recipients'
			when summaryRow in (2,5) and summaryItemCount = 0 then '0 recipients'
			when summaryRow in (2,5) and summaryItemCount > 0 then cast(summaryItemCount as varchar(10)) + ' recipients'
			end
		from (
			select 1 as summaryRow, 'Recipients matching filter criteria' as summaryItem, 0 as emailTypeID, count(distinct memberid) as summaryItemCount
			from #tblM
				union all
			select 2, 'Recipients using e-mail type: ' + met.emailType, met.emailTypeID, count(tmp.memberID)
			from #tmpRecipients as tmp
			inner join dbo.ams_memberEmailTypes as met on met.emailTypeID = tmp.emailTypeID
			group by met.emailType, met.emailTypeID
				union all
			SELECT 3, 'Recipients with no e-mail address', 0, COUNT(tmp.memberID)
			FROM #tblM AS tmp
			LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
			WHERE tmpR.memberID IS NULL
				union all
			SELECT 4, 'Recipients within Opt-Out lists', 0, @optOutMembersCount
			WHERE @excludeOptOuts = 1
				union all
			select 5, 'Total messages to be sent', 0, count(*)
			from (
				select memberID, memberEmail
				from #tmpFinalRecipients
				group by memberID, memberEmail
			) as tmp
		) as outertmp
		order by summaryRow;
	END

	-- row 1 detail
	IF @mode = 1
		select distinct m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' as memberName, m.company as memberCompany
		from #tblM as tblM
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = tblM.memberid
		order by memberName;

	-- row 2 detail
	IF @mode = 2
		select distinct memberName, memberCompany, memberEmail
		from #tmpRecipients
		where emailTypeID = @emailTypeID
		order by memberName;

	-- row 3 detail
	IF @mode = 3
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tblM AS tmp
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID and m.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
		WHERE tmpR.memberID IS NULL
		ORDER BY memberName;

	-- Members within Opt-Out list
	IF @mode = 4 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany, clm.email AS memberEmail
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.ams_members AS m on m.orgID = @orgID
			AND m.memberID = tmpR.memberID
		INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
			AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
		WHERE @excludeOptOuts = 1
		ORDER BY memberName;
	END

	-- row 5 detail and preview detail 
	IF @mode in (5,6)
		select memberID, firstName, lastName, memberName, memberCompany, memberEmail, min(emailTypeID) as emailTypeID
		from #tmpFinalRecipients
		group by memberID, firstName, lastName, memberName, memberCompany, memberEmail
		order by memberName, memberCompany, memberEmail;

	IF OBJECT_ID('tempdb..#tblM') IS NOT NULL 
		DROP TABLE #tblM;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_sendBlast
@recordedByMemberID int,
@siteID int,
@blastID int,
@messageHTML varchar(max),
@markRecipientAsReady bit,
@messageID int OUTPUT,
@hasExtendedMergeCodes bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	SELECT @messageID = NULL, @hasExtendedMergeCodes = 0;

	DECLARE @numRecipients int, @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int, 
		@sendingSiteResourceID int, @rawcontent varchar(max), @messageToParse varchar(MAX), @fieldID int,
		@fieldName varchar(300), @contentID int, @languageID int, @ruleID int, @fromName varchar(200), 
		@fromEmail varchar(200), @replyToEmail varchar(200), @subject varchar(400), @contentVersionID int, 
		@deliveryReportEmail varchar(200), @vwSQL varchar(max), @ParamDefinition nvarchar(100), @mcSQL nvarchar(max), 
		@orgID int, @afID int, @newEmailDateScheduled datetime, @fieldValueString varchar(200), @sendOnDate datetime = getdate(),
		@colList varchar(max), @contentLanguageID int, @orgSystemMemberID int,
		@itemGroupUID uniqueidentifier = NEWID(), @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
		@currentDateLastSent datetime, @currentEmailDateScheduled datetime, @globalOptOutListID INT, @orgIdentityID int, @consentListIDs varchar(MAX);

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;

	CREATE TABLE #tmpRecipientsMID (memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName varchar(300), fieldID int, isExtMergeCode bit, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	set @languageID = dbo.fn_getLanguageID('en');

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

	select @sendingSiteResourceID=st.siteResourceID, @orgID=s.orgID
	from dbo.sites as s
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	where s.siteID = @siteID;

	select @ruleID=ruleID, @fromName=fromName, @fromEmail=fromEmail, @replyToEmail=replyTo, @contentID=contentID, 
		@deliveryReportEmail=deliveryReportEmail, @afID=isnull(afID,0),
		@currentDateLastSent = emailDateSent,
		@currentEmailDateScheduled = emailDateScheduled,
		@orgIdentityID=orgIdentityID
	from dbo.email_EmailBlasts
	where blastID = @blastID;

	select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent=rawContent, @contentLanguageID=contentLanguageID
	from dbo.fn_getContent(@contentID,@languageID) as messageContent;
	
	-- run rule to get members
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	CREATE TABLE #tmpVGRMembers (memberID int PRIMARY KEY);

	EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;

	-- get recipients
	insert into #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
	select m.memberID, me.email, me.emailTypeID
	FROM #tmpVGRMembers as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID
	INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
		and me.memberid = m.memberid
		and len(me.email) > 0
	INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
		and ebet.blastID = @blastID
	UNION
	SELECT m.memberID, me.email, me.emailTypeID
	FROM #tmpVGRMembers as tmp
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberid = tmp.memberID
	INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
		AND me.memberid = m.memberid
		AND LEN(me.email) > 0
	INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
		AND metag.memberID = me.memberID
		AND metag.emailTypeID = me.emailTypeID
	INNER JOIN dbo.email_emailBlastEmailTagTypes AS ebett ON ebett.emailTagTypeID=metag.emailTagTypeID
		AND ebett.blastID = @blastID;

	SELECT @numRecipients = COUNT(*) FROM #tmpRecipientsMID;

	IF @numRecipients = 0 BEGIN
		-- update emailDateScheduled to reschedule if this was a recurring blast.
		IF @afID > 0
			EXEC dbo.email_sendBlastDateAdvance @siteID=@siteID, @blastID=@blastID;
		ELSE
			update dbo.email_EmailBlasts 
			set emailDateScheduled = NULL
			where blastID =  @blastID;

		RAISERROR('No recipients for message.',16,1);
	END

	-- add any necessary metadata fields for message
	SET @messageToParse = @messageHTML + isnull(@subject,'');
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@messageToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

	IF OBJECT_ID('tempdb..#tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE #tmpEmailBlastMemberData;
	CREATE TABLE #tmpEmailBlastMemberData (memberID int);

	if @colList is null
		insert into #tmpEmailBlastMemberData (memberID)
		select distinct memberID 
		from #tmpRecipientsMID;
	ELSE BEGIN
		set @vwSQL = 'select m.memberID, ' + @colList + ' 
			from (select distinct memberID from #tmpRecipientsMID) as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';

		declare @tblColumns TABLE (is_hidden bit, column_ordinal int, name sysname NULL, is_nullable bit, system_type_id int, system_type_name nvarchar(256) NULL,
			max_length smallint NULL, precision tinyint, scale tinyint, collation_name sysname NULL, user_type_id int NULL, user_type_database sysname NULL, 
			user_type_schema sysname NULL, user_type_name sysname NULL, assembly_qualified_type_name nvarchar(4000) NULL, xml_collection_id int NULL, 
			xml_collection_database sysname NULL, xml_collection_schema sysname NULL, xml_collection_name sysname NULL, is_xml_document bit, is_case_sensitive bit, 
			is_fixed_length_clr_type bit, source_server sysname NULL, source_database sysname NULL, source_schema sysname NULL, source_table sysname NULL, 
			source_column sysname NULL, is_identity_column bit NULL, is_part_of_unique_key bit NULL, is_updateable bit NULL, is_computed_column bit NULL, 
			is_sparse_column_set bit NULL, ordinal_in_order_by_list smallint NULL, order_by_list_length smallint NULL, order_by_is_descending smallint NULL, 
			tds_type_id int, tds_length int, tds_collation_id int NULL, tds_collation_sort_id tinyint NULL);
		declare @sqln nvarchar(max) = @vwSQL, @column_list NVARCHAR(MAX);
		INSERT INTO @tblColumns
		EXEC dbo.sp_describe_first_result_set @tsql=@sqln;

		SELECT @column_list = STUFF((SELECT N', ' + QUOTENAME(name) + N' ' + system_type_name + N' NULL'
		FROM @tblColumns
		WHERE [name] <> 'memberid'
		ORDER BY column_ordinal
		FOR XML PATH(N''), TYPE).value(N'.[1]', N'NVARCHAR(MAX)'), 1, 2, N'');

		IF @column_list IS NOT NULL
			EXEC (N'ALTER TABLE #tmpEmailBlastMemberData ADD ' + @column_list);

		INSERT INTO #tmpEmailBlastMemberData
        EXEC(@vwSQL);
    END

	select newid() as MCItemUID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.emailTypeID as mc_emailBlast_emailTypeID, vw.*
	into #tmpRecipients
	from (
		select tmpMID.memberID, tmpMID.mc_emailBlast_email, min(mc_emailBlast_emailTypeID) as emailTypeID
		from #tmpRecipientsMID as tmpMID
		group by tmpMID.memberid, tmpMID.mc_emailBlast_email
	) as tmp
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberID = tmp.memberID
	inner join #tmpEmailBlastMemberData as vw on vw.memberID = m.memberID
	inner join dbo.orgIdentities as i on i.orgIdentityID = @orgIdentityID
	inner join dbo.ams_states as s on s.stateID = i.stateID
	inner join dbo.ams_countries c on c.countryID = s.countryID;

	alter table #tmpRecipients add recipientID int;
	CREATE NONCLUSTERED INDEX [IX_tmpRecipients__mc_emailBlast_emailTypeID__memberID] ON #tmpRecipients ([mc_emailBlast_emailTypeID],[memberID]);

	IF OBJECT_ID('tempdb..#tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE #tmpEmailBlastMemberData;
	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');


	-- get consentListIDS
	SELECT @consentListIDs = STUFF((SELECT ',' + cast(bcl.consentListID AS VARCHAR(10))
	FROM platformMail.dbo.email_consentListTypes clt 
	INNER JOIN platformMail.dbo.email_consentLists cl ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID 
		AND modeName = 'Opt-Out'
	INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON cl.consentListID = bcl.consentListID
		AND  bcl.blastID = @blastID
	order by isPrimary desc, emailBlastConsentListID
	FOR XML PATH('')),1,1,'');

	IF NULLIF(@consentListIDs,'') is null 
		-- get default consent list for site 	
		SELECT @consentListIDs=cast(defaultConsentListID as varchar(max))
		from sites s 
		where siteID = @siteID


	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


	-- if changes were made to the content, create a new inactive contentversion and use it for the email.
	if @messageHTML <> @rawContent BEGIN
		set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID)

		exec membercentral.dbo.cms_createContentVersion @contentLanguageID = @contentLanguageID, @rawContent = @messageHTML,
			@isActive = 0, @memberID = @orgSystemMemberID, @contentVersionID = @contentVersionID OUTPUT;
	END

	-- insert merge code fields
	EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@blastID, @referenceType='emailblast', @messageToParse=@messageToParse, @extraMergeCodeList='';

	BEGIN TRAN
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=@orgIdentityID,
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
			@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='', 
			@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='',
			@referenceType='EmailBlast', @referenceID=@blastID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;
		
		-- update deliveryReportEmail
		IF nullIf(@deliveryReportEmail,'') is not null
			update platformMail.dbo.email_messages
			set deliveryReportEmail = @deliveryReportEmail
			where messageID = @messageID;

		-- update emailDateScheduled to reschedule or clear emailDateScheduled so scheduled task will not attempt to send again.
		set @newEmailDateScheduled = null;
		IF @afID > 0
			select @newEmailDateScheduled = dbo.fn_getAdvanceEmailDateScheduled(@siteID,@blastID);

		update dbo.email_EmailBlasts 
		set emailDateScheduled = @newEmailDateScheduled,
			emailDateSent = @sendOnDate
		where blastID =  @blastID;
	COMMIT TRAN;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);


	-- add recipients as I (not ready to be queued yet)
	INSERT INTO platformMail.dbo.email_messageRecipientHistory (messageID, memberID, dateLastUpdated, 
		toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, @sendOnDate, fullname, mc_emailBlast_email, @messageStatusIDInserting, 
		null, null, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	update tmp
	set tmp.recipientID = r.recipientID
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.memberID = tmp.memberiD
		and r.emailTypeID = tmp.mc_emailBlast_emailTypeID
		and r.messageID = @messageID;	

	-- add recipient metadata
	set @ParamDefinition = N'@messageID int, @fieldID int';		
	select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where isExtMergeCode = 0;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from #tmpEmailMetaDataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, memberID, fieldValue = ' + @fieldValueString + ', recipientID
				from #tmpRecipients;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where fieldID > @fieldID and isExtMergeCode = 0;
	END

	-- has extended merge codes
	IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where isExtMergeCode = 1) BEGIN
		SET @hasExtendedMergeCodes = 1;

		-- queue recipient details with extended merge codes
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		select MCItemUID, @insertingQueueStatusID
		from #tmpRecipients;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
		from #tmpRecipients as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCRecipientID'
			union
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
		from #tmpRecipients as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
		select @itemGroupUID, tmp.MCItemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
		from #tmpEmailMetaDataFields as mdf
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
		cross join #tmpRecipients as tmp
		where mdf.isExtMergeCode = 1;

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = @sendOnDate
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpRecipients as tmp on tmp.MCItemUID = qi.itemUID;
	END
	-- mark recipients as queued
	ELSE IF @markRecipientAsReady = 1 BEGIN
		UPDATE mrh 
		SET emailStatusID = @messageStatusIDQueued
		FROM platformMail.dbo.email_messages as m
		INNER JOIN platformMail.dbo.email_messageRecipientHistory as mrh ON m.messageID = mrh.messageID
		WHERE m.messageID = @messageID;
	END
	

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	IF @messageID is not null BEGIN
		--revert email blast dates
		update dbo.email_EmailBlasts 
		set emailDateScheduled = @currentEmailDateScheduled,
			emailDateSent = @currentDateLastSent
		where blastID =  @blastID;

		--mark sending queue message as deleted
		update platformMail.dbo.email_messages
		set status='D'
		where messageID = @messageID
	END
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_sendTestBlast
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@emailList varchar(max),
@messageHTML varchar(max),
@markRecipientAsReady bit,
@fromNameOverride varchar(200) = NULL,
@replyToEmailOverride varchar(200) = NULL,
@subjectOverride varchar(400) = NULL,
@messageID int OUTPUT,
@recipientIDList varchar(max) OUTPUT,
@membernumber varchar(50) OUTPUT,
@orgcode varchar(10) OUTPUT,
@outputMessage varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
		@rawcontent varchar(max), @messageToParse varchar(max), @fieldID int, @fieldName varchar(300), @contentID int, @orgID int, 
		@languageID int, @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @subject varchar(400), 
		@contentVersionID int, @vwSQL varchar(max), @ParamDefinition nvarchar(100), @fieldValueString varchar(200), @mcSQL nvarchar(max), 
		@emailTypeID int, @sendOnDate datetime = getdate(), @colList varchar(max), @contentLanguageID int, @orgSystemMemberID int, 
    	@orgIdentityID int, @consentListIDs varchar(MAX), @globalOptOutListID int, @isOptedOut BIT = 0, @consentListName VARCHAR(200),
		@optedOutEmails varchar(max) = '';
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL);
	declare @emailAddresses TABLE (email varchar(200));
	declare @optedOut TABLE (email VARCHAR(255), consentListName VARCHAR(255));
	SET @outputMessage = '';
	
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	set @messageID = null;
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	select @languageID = dbo.fn_getLanguageID('en');
	
	select @sendingSiteResourceID = st.siteResourceID, @orgcode = o.orgcode, @orgID = o.orgID, @emailTypeID = met.emailTypeID
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgID = s.orgID
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID and met.emailTypeOrder = 1;

	select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @orgIdentityID=orgIdentityID
	from dbo.email_EmailBlasts
	where blastID = @blastID;

	-- Parse multiple override email addresses if provided
	INSERT INTO @emailAddresses (email)
	SELECT listItem
	FROM dbo.fn_varcharListToTable(@emailList,';')

	set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);

	-- override memberID to use orgSystemMemberID for member org mismatch
	select @memberID = case when orgID = @orgID then @memberID else @orgSystemMemberID end
	from dbo.ams_members
	where memberID = @memberID;

	select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent, @contentLanguageID=contentLanguageID
	from dbo.fn_getContent(@contentID,@languageID) as messageContent;

	IF LEN(ISNULL(@fromNameOverride,'')) > 0
		SET @fromName = @fromNameOverride;

	IF LEN(ISNULL(@replyToEmailOverride,'')) > 0
	SET @replyToEmail = @replyToEmailOverride;

	IF LEN(ISNULL(@subjectOverride,'')) > 0
		SET @subject = '**TEST** ' + @subjectOverride;

	-- add any necessary metadata fields for message
	SET @messageToParse = @messageHTML + isnull(@subject,'');

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from dbo.fn_getServerSettings();
	
	insert into @metadataFields (fieldName)
	select distinct left([Text],300)
	from dbo.fn_RegexMatches(@messageToParse,@regexMergeCode);
		
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT @memberID as memberID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@messageToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    IF OBJECT_ID('tempdb..##tmpEmailBlastMemberData') IS NOT NULL 
        DROP TABLE ##tmpEmailBlastMemberData;

    if @colList is null BEGIN
        select @memberID as memberID 
		into ##tmpEmailBlastMemberData
    END ELSE BEGIN
		set @vwSQL = 'select m.memberID, ' + @colList + ' 
			into ##tmpEmailBlastMemberData 
			from dbo.ams_members as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
        EXEC(@vwSQL);
    END
	
	-- Create recipients for each valid email address
	select m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, ea.email as mc_emailBlast_email, 
		@emailTypeID as emailTypeID, vw.*
	into #tmpRecipients
	FROM dbo.ams_members as m
	INNER JOIN ##tmpEmailBlastMemberData as vw on vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities AS i ON i.orgIdentityID = @orgIdentityID
	INNER JOIN dbo.ams_states AS s ON s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID
	CROSS JOIN @emailAddresses ea
	WHERE m.memberID = @memberID;

	IF OBJECT_ID('tempdb..##tmpEmailBlastMemberData') IS NOT NULL 
		DROP TABLE ##tmpEmailBlastMemberData;

	-- get consentListIDS
	SELECT @consentListIDs = STUFF((SELECT ',' + cast(bcl.consentListID AS VARCHAR(10))
	FROM platformMail.dbo.email_consentListTypes clt 
	INNER JOIN platformMail.dbo.email_consentLists cl ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID 
		AND modeName = 'Opt-Out'
	INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON cl.consentListID = bcl.consentListID
		AND  bcl.blastID = @blastID
	order by isPrimary desc, emailBlastConsentListID
	FOR XML PATH('')),1,1,'');

	IF NULLIF(@consentListIDs,'') is null 
		-- get default consent list for site 	
		SELECT @consentListIDs = cast(defaultConsentListID as varchar(max))
		from dbo.sites
		where siteID = @siteID;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	INSERT INTO @optedOut (email, consentListName)
	SELECT DISTINCT ea.email, cl.consentListName
	FROM @emailAddresses ea
	INNER JOIN platformMail.dbo.email_consentListMembers clm ON clm.email = ea.email
	INNER JOIN platformMail.dbo.email_consentLists cl ON cl.consentListID = clm.consentListID
		AND cl.[status] = 'A'
	WHERE cl.consentListID = @globalOptOutListID
	OR EXISTS (
		SELECT 1
		FROM platformMail.dbo.email_consentListTypes AS clt
		INNER JOIN platformMail.dbo.email_consentLists AS cl2 ON clt.consentListTypeID = cl2.consentListTypeID
			AND clt.orgID = @orgID
			AND cl2.[status] = 'A'
		INNER JOIN platformMail.dbo.email_consentListModes AS clm2 ON clm2.consentListModeID = cl2.consentListModeID
			AND clm2.modeName = 'Opt-Out'
		INNER JOIN membercentral.dbo.email_emailBlastConsentLists AS bcl ON bcl.consentListID = cl.consentListID
			AND bcl.blastID = @blastID
		WHERE cl2.consentListID = cl.consentListID
	);

	-- Step 2: Build the output message from the temp table
	SELECT @optedOutEmails = STUFF((
		SELECT '; ' + email + ' (opted out from: ' + consentListName + ')'
		FROM @optedOut
		FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '');

	-- Step 3: Remove opted-out emails
	DELETE ea
	FROM @emailAddresses ea
	INNER JOIN @optedOut o ON o.email = ea.email;

	-- Set output message if any emails were opted out (but continue processing valid emails)
	IF LEN(@optedOutEmails) > 0 BEGIN
		SET @outputMessage = 'The following email addresses will not receive the test message: ' + @optedOutEmails;
		RETURN 0;
	END

	-- If no valid emails remain after opt-out filtering, exit
	IF NOT EXISTS (SELECT 1 FROM @emailAddresses) BEGIN
		SET @outputMessage = 'No valid email addresses remain after opt-out filtering.';
		RETURN 0;
	END

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');

	select @membernumber = membernumber
	from #tmpRecipients;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- if changes were made to the content, create a new inactive contentversion and use it for the email.
	if @messageHTML <> @rawContent BEGIN
		exec membercentral.dbo.cms_createContentVersion
			@contentLanguageID = @contentLanguageID,
			@rawContent = @messageHTML,
			@isActive = 0,
			@memberID = @orgSystemMemberID,
			@contentVersionID = @contentVersionID OUTPUT;
	END

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add email_message
	EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID,  @orgIdentityID=@orgIdentityID,
		@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=1, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='', 
		@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='', 
		@referenceType='EmailBlast', @referenceID=@blastID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, getdate(), fullname, mc_emailBlast_email, @messageStatusIDInserting, null, null, emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	SELECT @recipientIDList = 
	    ISNULL((
	      SELECT 
	        CASE 
	          WHEN COUNT(*) = 1 
	          THEN CAST(MIN(recipientID) AS VARCHAR(20))
	          ELSE STRING_AGG(CAST(recipientID AS VARCHAR(20)), ',') 
	        END
	      FROM platformMail.dbo.email_messageRecipientHistory
	      WHERE messageID = @messageID
	    ), '')

	-- add metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields;

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1;		
	
	-- add recipient metadata for all recipients
	set @ParamDefinition = N'@messageID int, @fieldID int';
	select @fieldID = min(fieldID) from @metadataFields;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, tr.memberID, fieldValue = ' + @fieldValueString + ', mrh.recipientID
				from #tmpRecipients tr
				inner join platformMail.dbo.email_messageRecipientHistory mrh on mrh.messageID = @messageID and mrh.memberID = tr.memberID and mrh.toEmail = tr.mc_emailBlast_email;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID;
	END	

	-- mark recipients as queued
	if @markRecipientAsReady = 1
		update mrh 
		set emailStatusID = @messageStatusIDQueued
		from platformMail.dbo.email_messages m
		inner join platformMail.dbo.email_messageRecipientHistory mrh on m.messageID = mrh.messageID
			and m.messageID = @messageID;


	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.pub_getEmailRecipients
@publicationID INT,
@mode TINYINT,
@emailTypeID INT,
@receiveFID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	CREATE TABLE #tmpMembers (memberID INT);
	CREATE TABLE #tmpRecipients (memberID INT, memberName VARCHAR(200), memberCompany VARCHAR(200), memberEmail VARCHAR(200), emailTypeID INT);
	CREATE TABLE #tmpFinalRecipients (memberID INT, memberName VARCHAR(200), memberCompany VARCHAR(200), memberEmail VARCHAR(200), emailTypeID INT);

	DECLARE @emailConsentListID INT, @emailConsentListMode VARCHAR(25), @orgID INT, @siteID INT, @globalOptOutListID INT;

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.pub_publications AS p
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE p.publicationID = @publicationID;

	SELECT @emailConsentListID = pub.emailConsentListID, @emailConsentListMode = m.modeName
	FROM dbo.pub_publications AS pub
	INNER JOIN platformMail.dbo.email_consentLists AS cl ON cl.consentListID = pub.emailConsentListID
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes AS m ON m.consentListModeID = cl.consentListModeID
	INNER JOIN platformMail.dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
	WHERE pub.publicationID = @publicationID;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	-- get members with ReceiveEditionEmails perms
	INSERT INTO #tmpMembers (memberID)
	SELECT DISTINCT m.activeMemberID
	FROM dbo.pub_publications AS p
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		and ai.applicationInstanceID = p.applicationInstanceID 
	INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'publications'
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID 
		and sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
		and srfrp.siteResourceID = sr.siteResourceID 
		AND srfrp.functionID = @receiveFID 
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
		and srfrp.rightPrintID = gprp.rightPrintID
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
		and m.groupPrintID = gprp.groupPrintID
	WHERE p.publicationID = @publicationID;

	-- get recipient member details
	INSERT INTO #tmpRecipients (memberID, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT DISTINCT m.memberID, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company, me.email, me.emailTypeID
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.memberID = tmp.memberID
		INNER JOIN dbo.pub_publicationEmailTagTypes as pet on pet.publicationID = @publicationID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
			AND metag.memberID = m.memberID 
			AND pet.emailTagTypeID = metag.emailTagTypeID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
			AND me.memberID = m.memberID
			AND metag.emailTypeID = me.emailTypeID
			AND me.email <> '';

	INSERT INTO #tmpFinalRecipients (memberID, memberName, memberCompany, memberEmail, emailTypeID)
	select memberID, memberName, memberCompany, memberEmail, emailTypeID
	from #tmpRecipients;
	
	IF @emailConsentListMode = 'Opt-Out' BEGIN
		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		INNER JOIN platformMail.dbo.email_consentListMembers AS clm
			ON clm.consentListID IN (@emailConsentListID,@globalOptOutListID)
			AND clm.email = tmp.memberEmail;
	END

	IF @emailConsentListMode = 'Opt-In' BEGIN
		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		LEFT OUTER JOIN platformMail.dbo.email_consentListMembers clm
			ON clm.consentListID = @emailConsentListID
			AND clm.email = tmp.memberEmail
		WHERE clm.consentListMemberID is null;
	END
	
	-- summary counts	 
	IF @mode = 0 BEGIN
		SELECT summaryRow, summaryItem, emailTypeID, summaryItemCount, summaryLinkText = 
			CASE 
			WHEN summaryRow IN (1,2,3,4) AND summaryItemCount = 0 then '0 members'
			WHEN summaryRow IN (1,2,3,4) AND summaryItemCount > 0 then 'view ' + cast(summaryItemCount AS VARCHAR(10)) + ' members'
			WHEN summaryRow IN (5) AND summaryItemCount = 0 then '0 recipients'
			WHEN summaryRow IN (5) AND summaryItemCount > 0 then 'view ' + cast(summaryItemCount AS VARCHAR(10)) + ' recipients'
			END
		FROM (
			SELECT 1 AS summaryRow, 'Members with Receive Email Editions permission' AS summaryItem, 0 AS emailTypeID, COUNT(memberID) AS summaryItemCount
			FROM #tmpMembers
				UNION ALL
			SELECT 2, 'Members using e-mail type: ' + met.emailType, met.emailTypeID, COUNT(tmp.memberid)
			FROM #tmpRecipients AS tmp
			INNER JOIN dbo.ams_memberEmailTypes AS met ON met.emailTypeID = tmp.emailTypeID
			GROUP BY met.emailType, met.emailTypeID
				UNION ALL
			SELECT 3, 'Members with no e-mail address', 0, COUNT(tmp.memberID)
			FROM #tmpMembers AS tmp
			LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
			WHERE tmpR.memberID IS NULL
				UNION ALL
			SELECT 4, 'Members within '+ @emailConsentListMode +' list', 0, COUNT(DISTINCT tmpR.memberID)
			FROM platformMail.dbo.email_consentListMembers AS clm
			INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
			WHERE clm.consentListID = @emailConsentListID OR (@emailConsentListMode = 'Opt-Out' AND clm.consentListID IN (@emailConsentListID,@globalOptOutListID))
				UNION ALL
			SELECT 5, 'Total messages to be sent', 0, COUNT(memberID)
			FROM #tmpFinalRecipients
		) AS outertmp
		ORDER BY summaryRow;

		GOTO on_done;
	END
	
	-- Members with Receive Email Editions permission
	IF @mode = 1 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
		ORDER BY memberName;

		GOTO on_done;
	END	
	
	-- Members using an Email-Type
	IF @mode = 2 BEGIN
		SELECT DISTINCT memberName, memberCompany, memberEmail
		FROM #tmpRecipients AS tmp
		WHERE emailTypeID = @emailTypeID
		ORDER BY memberName;

		GOTO on_done;
	END
	
	-- Members with no e-mail address
	IF @mode = 3 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tmpMembers AS tmp
		INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
		WHERE tmpR.memberID IS NULL
		ORDER BY memberName;

		GOTO on_done;
	END
	
	-- Members within Opt-In/Opt-Out list
	IF @mode = 4 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany, clm.email AS memberEmail
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.ams_members AS m on m.memberID = tmpR.memberID
		WHERE clm.consentListID = @emailConsentListID OR (@emailConsentListMode = 'Opt-Out' AND clm.consentListID IN (@emailConsentListID,@globalOptOutListID))
		ORDER BY memberName;

		GOTO on_done;
	END	
	
	-- Recipient Members
	IF @mode IN (5,6) BEGIN
		SELECT memberID, memberName, memberCompany, memberEmail, emailTypeID
		FROM #tmpFinalRecipients
		ORDER BY memberName, memberCompany, memberEmail;
		
		GOTO on_done;
	END

	on_done:

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.pub_sendEmailIssue
@siteID INT,
@issueID INT,
@emailSubject VARCHAR(200),
@rawContent VARCHAR(MAX),
@contentVersionID INT,
@overrideMemberID INT,
@overrideEmailAddress VARCHAR(MAX),
@recordedByMemberID INT,
@sendOnDate DATETIME,
@isTestMessage BIT = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @messageTypeID INT, @messageStatusIDInserting INT, @messageStatusIDQueued INT, @orgID INT, @defaultOrgIdentityID int, 
		@sendingSiteResourceID INT, @fieldID INT, @fieldName VARCHAR(300), @messageID INT, @vwSQL VARCHAR(MAX), 
		@ParamDefinition NVARCHAR(100), @fieldValueString VARCHAR(200), @mcSQL NVARCHAR(MAX), @colDataType VARCHAR(40), 
		@colList VARCHAR(MAX), @supportProviderEmail VARCHAR(100), @supportProviderName VARCHAR(100), 
		@pubResourceTypeID INT, @functionID INT, @overrideFunctionID INT, @numRecipients INT, @publicationID INT, @emailConsentListID INT,
		@emailConsentListModeName varchar(50), @emailEditionSenderName VARCHAR(200), @emailEditionReplyToAddress VARCHAR(400), @publicationSiteResourceID INT, 
		@itemGroupUID UNIQUEIDENTIFIER, @queueTypeID INT, @insertingQueueStatusID INT, @readyQueueStatusID INT, @globalOptOutListID INT, 
		@consentListIDs VARCHAR(MAX), @outputMessage varchar(max), @consentListName VARCHAR(100), @tempConsentListName VARCHAR(100);
	SET @outputMessage = '';

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpRecipientConsentLists') IS NOT NULL 
		DROP TABLE #tmpRecipientConsentLists;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpDeletedRecipients') IS NOT NULL
		DROP TABLE #tmpDeletedRecipients;
	IF OBJECT_ID('tempdb..#tmpOverrideEmails') IS NOT NULL
		DROP TABLE #tmpOverrideEmails;

	CREATE TABLE #tmpRecipientsMID (memberID int, recipientEmail varchar(255), recipientEmailTypeID int);
	CREATE TABLE #tmpRecipientDetails (recipientID INT, recipientMemberID INT, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName VARCHAR(300), fieldID INT, isExtMergeCode BIT, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpDeletedRecipients (recipientEmail varchar(255), consentListName varchar(100));
	CREATE TABLE #tmpOverrideEmails (emailAddress varchar(255));

	-- Parse multiple override email addresses if provided
	IF @overrideEmailAddress IS NOT NULL AND LEN(TRIM(@overrideEmailAddress)) > 0
		INSERT INTO #tmpOverrideEmails (emailAddress)
		SELECT listItem
		FROM dbo.fn_varcharListToTable(@overrideEmailAddress,';');

	SELECT @sendingSiteResourceID = st.siteResourceID, @orgID = o.orgID, @defaultOrgIdentityID = s.defaultOrgIdentityID
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	INNER JOIN dbo.admin_siteTools AS st ON st.siteID = s.siteID AND st.siteID = @siteID
	INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = st.toolTypeID AND tt.toolType = 'PublicationAdmin';

	SELECT TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	FROM dbo.networks AS net
	INNER JOIN dbo.networkSites AS ns ON net.networkID = ns.networkID
	INNER JOIN dbo.sites AS s ON s.siteID = ns.siteID
	WHERE s.siteID = @siteID 
	AND ns.isLoginNetwork = 1;

	IF @sendOnDate IS NULL OR @sendOnDate < GETDATE()
		SET @sendOnDate = GETDATE();

	SELECT @pubResourceTypeID = dbo.fn_getResourceTypeID('Publications');
	SELECT @functionID = dbo.fn_getResourceFunctionID('ReceiveEditionEmails', @pubResourceTypeID);
	SELECT @overrideFunctionID = dbo.fn_getResourceFunctionID('overrideReceiveEditionEmails', @pubResourceTypeID);

	SELECT @publicationID = p.publicationID, @emailConsentListID = p.emailConsentListID,
		@emailConsentListModeName = clm.modeName, @consentListName = cl.consentListName,
		@emailEditionSenderName = ISNULL(p.emailEditionSenderName, @supportProviderName), 
		@emailEditionReplyToAddress = ISNULL(p.emailEditionReplyToAddress, @supportProviderEmail),
		@publicationSiteResourceID = sr.siteResourceID
	FROM dbo.pub_volumes AS v
	INNER JOIN dbo.pub_issues AS i ON i.volumeID = v.volumeID
	INNER JOIN dbo.pub_publications AS p 
        ON p.publicationID = v.publicationID
        and i.issueID = @issueID
    INNER JOIN platformMail.dbo.email_consentLists cl
        on cl.consentListID = p.emailConsentListID
		AND cl.[status] = 'A'
    INNER JOIN platformMail.dbo.email_consentListModes clm
        on clm.consentListModeID = cl.consentListModeID
	INNER JOIN dbo.cms_applicationInstances AS ai 
        ON ai.applicationInstanceID = p.applicationInstanceID 
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
	INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
		AND srs.siteResourceStatusDesc = 'Active';

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	-- check for override emails to receive edition
	IF @overrideMemberID IS NULL AND EXISTS (
		SELECT 1
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @overrideFunctionID
			AND srfrp.rightPrintID = gprp.rightPrintID
		WHERE srfrp.siteID = @siteID
	) BEGIN
		INSERT INTO #tmpRecipientsMID (memberID, recipientEmail, recipientEmailTypeID)
		SELECT mActive.memberID, me.email, me.emailTypeID
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
			AND srfrp.rightPrintID = gprp.rightPrintID
			AND srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @overrideFunctionID
		INNER JOIN dbo.ams_members AS m ON m.groupPrintID = gprp.groupPrintID AND m.orgID in (1,@orgID)
		INNER JOIN dbo.ams_members AS mActive ON mActive.orgID in (1,@orgID) and mActive.memberID = m.activeMemberID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID IN (1,@orgID)
			AND me.memberID = mActive.memberID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID IN (1,@orgID)
			AND metag.memberID = me.memberID 
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.ams_memberEmailTagTypes AS mett ON mett.orgID IN (1,@orgID)
			AND mett.emailTagTypeID = metag.emailTagTypeID
			AND mett.emailTagType = 'Primary'
		WHERE srfrp.siteID = @siteID
		AND LEN(me.Email) > 0;
	END
	ELSE BEGIN
		INSERT INTO #tmpRecipientsMID (memberID, recipientEmail, recipientEmailTypeID)
		SELECT mActive.memberID, me.email, me.emailTypeID
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON srfrp.rightPrintID = gprp.rightPrintID
			and srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @functionID 
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.groupPrintID = gprp.groupPrintID
		INNER JOIN dbo.ams_members AS mActive on mActive.orgID = @orgID
			AND mActive.memberID = m.activeMemberID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
			AND me.memberID = mActive.memberID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
			AND metag.memberID = me.memberID 
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.pub_publicationEmailTagTypes as pet on pet.emailTagTypeID = metag.emailTagTypeID and pet.publicationID = @publicationID
		WHERE srfrp.siteID = @siteID
		AND LEN(me.Email) > 0
		GROUP BY mActive.memberID, me.email, me.emailTypeID;
	END

    IF @overrideMemberID IS NOT NULL
        DELETE FROM #tmpRecipientsMID
        WHERE memberID <> @overrideMemberID;

	IF @emailConsentListID IS NULL
		SELECT @emailConsentListID = cast(defaultConsentListID as varchar(max))
		FROM sites s 
		WHERE s.siteID = @siteID;

	SET @consentListIDs = NULL;
    IF @emailConsentListModeName = 'Opt-Out' BEGIN
		IF @isTestMessage = 0 BEGIN
			-- Normal processing: check actual recipient emails
			delete temp
			OUTPUT DELETED.recipientEmail, cl.consentListName INTO #tmpDeletedRecipients (recipientEmail, consentListName)
			from #tmpRecipientsMID temp
			inner join platformMail.dbo.email_consentListMembers clm
				on clm.consentListID IN (@emailConsentListID, @globalOptOutListID)
				and clm.email = temp.recipientEmail
			inner join platformMail.dbo.email_consentLists cl
				on cl.consentListID = clm.consentListID
				and cl.[status] = 'A';
		END
		ELSE BEGIN
			-- Test message: check override email addresses
			-- First, check if any override emails are in opt-out lists
			DECLARE @optOutEmails VARCHAR(MAX) = '';

			SELECT @optOutEmails = @optOutEmails + CASE WHEN @optOutEmails = '' THEN '' ELSE ', ' END + '"' + oe.emailAddress + '"'
			FROM #tmpOverrideEmails oe
			INNER JOIN platformMail.dbo.email_consentListMembers clm
				ON clm.consentListID IN (@emailConsentListID, @globalOptOutListID)
				AND clm.email = oe.emailAddress
			INNER JOIN platformMail.dbo.email_consentLists cl
				ON cl.consentListID = clm.consentListID
				AND cl.[status] = 'A';

			-- If any override emails are in opt-out lists, delete all recipients and set message
			IF @optOutEmails <> '' BEGIN
				DELETE FROM #tmpRecipientsMID;
				SET @outputMessage = 'The test message will not be sent as the following email(s) are in opt-out list(s): ' + @optOutEmails + ' (' + @consentListName + ').';
			END
		END

		SELECT @consentListIDs = cast(@emailConsentListID as varchar(10));
	END
    IF @emailConsentListModeName = 'Opt-In' BEGIN
		IF @isTestMessage = 0 BEGIN
			-- Normal processing: check actual recipient emails
			delete temp
			from #tmpRecipientsMID temp
			left outer join platformMail.dbo.email_consentListMembers clm
				on clm.consentListID = @emailConsentListID
				and clm.email = temp.recipientEmail
			where clm.consentListMemberID is null;
		END
		ELSE BEGIN
			-- Test message: check override email addresses
			-- Find override emails that are NOT in the opt-in list
			DECLARE @notInOptInEmails VARCHAR(MAX) = '';

			SELECT @notInOptInEmails = @notInOptInEmails + CASE WHEN @notInOptInEmails = '' THEN '' ELSE ', ' END + '"' + oe.emailAddress + '"'
			FROM #tmpOverrideEmails oe
			LEFT JOIN platformMail.dbo.email_consentListMembers clm
				ON clm.consentListID = @emailConsentListID
				AND clm.email = oe.emailAddress
			WHERE clm.consentListMemberID IS NULL;

			-- If any override emails are NOT in opt-in list, delete all recipients and set message
			IF @notInOptInEmails <> '' BEGIN
				DELETE FROM #tmpRecipientsMID;
				SET @outputMessage = 'The test message will not be sent as the following email(s) are NOT in the opt-in list "' + @consentListName + '": ' + @notInOptInEmails + '.';
			END
		END
    END

	SELECT @numRecipients = COUNT(*) FROM #tmpRecipientsMID;

	IF @numRecipients = 0 AND @isTestMessage = 0
		RAISERROR('No recipients for message.',16,1);

	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'PUBLICATIONS';
	SELECT @messageStatusIDInserting = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'I';
	SELECT @messageStatusIDQueued = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'Q';

	SET @itemGroupUID = NEWID();

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

	-- member consent list details
	select clt.consentListTypeID, clt.consentListTypeName as consentListType, cl.consentListID, cl.consentListName, 
		cl.consentListDesc, clm.modeName as consentListMode, oi.organizationName as consentListOrganization, oi.address1 as consentListAddress1, oi.address2 as consentListAddress2, oi.city as consentListCity, s.Name as consentListState,
		oi.postalCode as consentListPostalCode, c.country as consentListCountry,
		oi.phone as consentListPhone, oi.fax as consentListFax, oi.website as consentListWebsite, oi.XUserName as consentListXUsername, oi.email as consentListEmail
	INTO #tmpRecipientConsentLists
	from platformMail.dbo.email_consentListTypes as clt
	inner join platformMail.dbo.email_consentLists as cl 
		on cl.consentListTypeID = clt.consentListTypeID
		and cl.consentListID = @emailConsentListID
		and cl.[status] = 'A'
	inner join platformMail.dbo.email_consentListModes as clm on clm.consentListModeID = cl.consentListModeID
		inner join membercentral.dbo.orgIdentities oi
		on oi.orgIdentityID = cl.orgIdentityID
	inner join membercentral.dbo.ams_states as s on s.stateID = oi.stateID
	inner join membercentral.dbo.ams_countries as c 
		on c.countryID = s.countryID
	order by clt.orderNum, cl.orderNum;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@rawContent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    IF OBJECT_ID('tempdb..##tmpEmailPubIssues') IS NOT NULL 
        DROP TABLE ##tmpEmailPubIssues;

	IF @colList IS NULL
		SELECT memberID
		INTO ##tmpEmailPubIssues
		FROM #tmpRecipientsMID;
	ELSE BEGIN
		SET @vwSQL = 'SELECT m.memberid, ' + @colList + ' 
			INTO ##tmpEmailPubIssues 
			FROM #tmpRecipientsMID AS m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	SELECT m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, m.membernumber, m.firstname + ' ' + m.lastname AS fullname, 
		m.firstname + ISNULL(' ' + NULLIF(m.middlename,''),'') + ' ' + m.lastname + ISNULL(' ' + NULLIF(m.suffix,''),'') AS extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername,
		tmp.recipientEmail, tmp.recipientEmailTypeID, o.orgcode, cl.consentListName, cl.consentListType, cl.consentListMode, 
		cl.consentListOrganization, cl.consentListAddress1, cl.consentListAddress2, cl.consentListCity, cl.consentListState, cl.consentListPostalCode, 
		cl.consentListCountry, cl.consentListPhone, cl.consentListFax, cl.consentListEmail, cl.consentListWebsite, vw.*
	INTO #tmpRecipients
	FROM #tmpRecipientsMID AS tmp
	INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
	INNER JOIN dbo.organizations as o WITH(NOLOCK) on o.orgID = m.orgID
	INNER JOIN ##tmpEmailPubIssues AS vw ON vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID
	CROSS JOIN #tmpRecipientConsentLists AS cl;

	IF OBJECT_ID('tempdb..##tmpEmailPubIssues') IS NOT NULL 
		DROP TABLE ##tmpEmailPubIssues;

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	INSERT INTO #tmpRecipientsCols
	SELECT c.column_id, c.name, t.name
	FROM tempdb.sys.columns AS c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	WHERE c.object_id = OBJECT_ID('tempdb..#tmpRecipients');
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- find/insert merge code fields
	EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@issueID, @referenceType='publicationIssue',
		@messageToParse=@rawContent, @extraMergeCodeList='consentListManagementURL';

	-- add email_message
	EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=@isTestMessage, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@emailEditionSenderName, @fromEmail=@supportProviderEmail, @replyToEmail=@emailEditionReplyToAddress, 
		@senderEmail='', @subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper='', 
		@referenceType='publicationIssue', @referenceID=@issueID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	-- we do it this way because insert with OUTPUT INTO can only refer to columns of the inserted table
	IF @isTestMessage = 1 AND EXISTS(SELECT 1 FROM #tmpOverrideEmails) BEGIN
		-- For test messages with override emails, create recipients for each override email
		MERGE INTO platformMail.dbo.email_messageRecipientHistory AS t
		USING (
			SELECT tmp.memberID, tmp.fullname, tmp.recipientEmailTypeID, oe.emailAddress
			FROM #tmpRecipients AS tmp
			CROSS JOIN #tmpOverrideEmails AS oe
		) AS src ON 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID, queuePriority)
			VALUES (@messageID, src.memberID, GETDATE(), src.fullname, src.emailAddress, @messageStatusIDInserting, NULL, NULL, src.recipientEmailTypeID, @siteID, @initialQueuePriority)
			OUTPUT INSERTED.recipientID, INSERTED.memberID
			INTO #tmpRecipientDetails (recipientID, recipientMemberID);
	END
	ELSE BEGIN
		-- Normal processing: use actual recipient emails
		MERGE INTO platformMail.dbo.email_messageRecipientHistory AS t USING #tmpRecipients AS tmp ON 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
			VALUES (@messageID, memberID, GETDATE(), fullname, recipientEmail, @messageStatusIDInserting, NULL, NULL, recipientEmailTypeID, @siteID,@initialQueuePriority)
			OUTPUT INSERTED.recipientID, INSERTED.memberID
			INTO #tmpRecipientDetails (recipientID, recipientMemberID);
	END

	
	-- add recipient metadata
	SET @ParamDefinition = N'@messageID int, @fieldID int';
	SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 0;
	WHILE @fieldID IS NOT NULL BEGIN
		SELECT @fieldName = fieldName FROM #tmpEmailMetaDataFields WHERE fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (SELECT ORDINAL_POSITION FROM #tmpRecipientsCols WHERE column_name = @fieldName) BEGIN
			SET @fieldValueString = 'ISNULL(CAST(tmp.[' + @fieldName + '] AS VARCHAR(MAX)),'''')';

			SET @mcSQL = 'INSERT INTO platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				SELECT @messageID, @fieldID, rcp.recipientMemberID, fieldValue = ' + @fieldValueString + ', rcp.recipientID
				FROM #tmpRecipientDetails AS rcp
				INNER JOIN #tmpRecipients AS tmp ON rcp.recipientMemberID = tmp.memberID;';
			EXEC sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 0 AND fieldID > @fieldID;
	END

	-- has extended merge codes
	IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where isExtMergeCode = 1) BEGIN
		-- skip queue for test message
		IF @overrideMemberID IS NOT NULL
			GOTO on_done;

		-- queue recipient details with extended merge codes
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		select itemUID, @insertingQueueStatusID
		from #tmpRecipientDetails;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
		from #tmpRecipientDetails as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCRecipientID'
			union
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
		from #tmpRecipientDetails as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCMessageID';

		-- consentListEmail
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueString)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmpR.consentListEmail
		from #tmpRecipientDetails as tmp
		inner join #tmpRecipients AS tmpR ON tmpR.memberID = tmp.recipientMemberID
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCConsentListEmail';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
		from #tmpEmailMetaDataFields as mdf
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
		cross join #tmpRecipientDetails as tmp
		where mdf.isExtMergeCode = 1;

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = GETDATE()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpRecipientDetails as tmp on tmp.itemUID = qi.itemUID;
	END
	-- mark recipients as queued
	ELSE BEGIN
		UPDATE mrh 
		SET emailStatusID = @messageStatusIDQueued
		FROM platformMail.dbo.email_messages m
		INNER JOIN platformMail.dbo.email_messageRecipientHistory mrh ON m.messageID = mrh.messageID
		WHERE m.messageID = @messageID;
	END

	on_done:
	-- return recipients
	IF @isTestMessage = 1
	BEGIN
		IF @numRecipients = 0
			SELECT @outputMessage as outputMessage, @numRecipients as numRecipients
		ELSE 
			SELECT rcp.recipientID, rcp.recipientMemberID, tmp.recipientEmail, tmp.memberNumber, tmp.consentListEmail, @issueID, @messageID as messageID, @outputMessage as outputMessage, @numRecipients as numRecipients
			FROM #tmpRecipientDetails AS rcp
			INNER JOIN #tmpRecipients AS tmp ON tmp.memberID = rcp.recipientMemberID;
	END
	ELSE
		SELECT rcp.recipientID, rcp.recipientMemberID, tmp.recipientEmail, tmp.memberNumber, tmp.consentListEmail, @issueID, @messageID as messageID, @outputMessage as outputMessage, @numRecipients as numRecipients
		FROM #tmpRecipientDetails AS rcp
		INNER JOIN #tmpRecipients AS tmp ON tmp.memberID = rcp.recipientMemberID;


	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpRecipientConsentLists') IS NOT NULL 
		DROP TABLE #tmpRecipientConsentLists;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpDeletedRecipients') IS NOT NULL 
		DROP TABLE #tmpDeletedRecipients;
	IF OBJECT_ID('tempdb..#tmpOverrideEmails') IS NOT NULL
		DROP TABLE #tmpOverrideEmails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN -1;
END CATCH
GO

USE platformMail
GO

ALTER PROC dbo.email_addConsentList
@siteID int,
@consentListTypeID int,
@consentListName varchar(100),
@consentListDesc varchar(250),
@consentListModeID int,
@orgIdentityID int,
@isHidden bit,
@enteredByMemberID int,
@consentListID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @appCreatedContentResourceTypeID int, @resourceTypeID int, @defaultLanguageID int, 
		@footerContentID int, @intakeFormNonQualContentID int, @siteResourceID int, @trashID int;

	SET @consentListID = null;
	SELECT @appCreatedContentResourceTypeID = memberCentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SELECT @resourceTypeID  = memberCentral.dbo.fn_getResourceTypeId('EmailConsentList');
	SELECT @defaultLanguageID = defaultLanguageID from memberCentral.dbo.sites where siteID = @siteID;

	IF EXISTS (select top 1 consentListID from dbo.email_consentLists where consentListTypeID = @consentListTypeID and consentListName = @consentListName and [status] = 'A')
		RAISERROR('Consent List Name already in use', 16, 1);

	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1,
				@siteID=@siteID, @isVisible=1, @parentSiteResourceID=NULL, @siteResourceID=@siteResourceID OUTPUT;

		EXEC memberCentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID, 
			@contentID=@footerContentID OUTPUT, @siteResourceID=@trashID OUTPUT;

		INSERT INTO dbo.email_consentLists (consentListTypeID, consentListName, consentListDesc, consentListModeID, orgIdentityID, footerContentID, siteResourceID, orderNum, isHidden, [status])
		VALUES (@consentListTypeID, @consentListName, @consentListDesc, @consentListModeID, @orgIdentityID, @footerContentID, @siteResourceID, 9999, @isHidden, 'A');
			SET @consentListID = SCOPE_IDENTITY();

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_deleteConsentList
@orgID int,
@consentListID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @consentListTypeID int, @footerContentID int;

	IF EXISTS (
		SELECT 1
		FROM dbo.email_consentLists AS l
		INNER JOIN dbo.email_consentListTypes AS t ON t.consentListTypeID = l.consentListTypeID
			AND t.orgID = @orgID
			AND t.isSystemType = 1
		WHERE l.consentListID = @consentListID
	)
		RAISERROR('system type', 16, 1);

	IF EXISTS (select top 1 consentListID from dbo.email_consentListMembers where consentListID = @consentListID)
		RAISERROR('Consent List has members assigned to it', 16, 1);

	SELECT @consentListTypeID = l.consentListTypeID, @footerContentID = l.footerContentID
	FROM dbo.email_consentLists AS l
	INNER JOIN dbo.email_consentListTypes AS t ON t.consentListTypeID = l.consentListTypeID
		AND t.orgID = @orgID
	WHERE l.consentListID = @consentListID
	AND l.[status] = 'A';
	
	IF @consentListTypeID IS NULL
		RAISERROR('invalid consent list',16,1);

	BEGIN TRAN;
		UPDATE dbo.email_consentLists
		SET [status] = 'D'
		WHERE consentListID = @consentListID;

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_importConsentListMembers
@orgID int,
@consentListIDList varchar(max), 
@enteredByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportConsentListMemberErrors') IS NOT NULL 
		DROP TABLE #tblImportConsentListMemberErrors;
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols;
	CREATE TABLE #tblImportConsentListMemberErrors (rowid int IDENTITY(1,1), msg varchar(600));
	CREATE TABLE #tblImportCols (columnName varchar(200));
	
	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#mc_consentListMembersImport') IS NULL BEGIN
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to locate the imported data for processing.');
		GOTO on_done;
	END

	-- ensure all required columns exist
	BEGIN TRY
		INSERT INTO #tblImportCols (columnName) 
		VALUES ('rowID'), ('Email'), ('DateEntered');

		IF NOT EXISTS (select 1 from tempdb.sys.columns where object_id = object_id('tempdb..#mc_consentListMembersImport') and [name] = 'Email') BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) 
			VALUES ('The required column "Email" was not provided.');

			GOTO on_done;
		END
		
		IF EXISTS (select 1 from tempdb.sys.columns where object_id = object_id('tempdb..#mc_consentListMembersImport') and [name] not in (select columnName from #tblImportCols)) BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) 
			select 'The invalid column "' + cast([name] as varchar(300)) + '" should be removed.'
			from tempdb.sys.columns 
			where object_id = object_id('tempdb..#mc_consentListMembersImport') 
			and [name] not in (select columnName from #tblImportCols);

			GOTO on_done;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to validate if the file contains all required columns.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- validate columns
	BEGIN TRY
		-- no blank email
		update #mc_consentListMembersImport set Email = '' where Email is null;

		INSERT INTO #tblImportConsentListMemberErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Email.'
		FROM #mc_consentListMembersImport
		WHERE Email = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;
		
		-- email must be valid
		INSERT INTO #tblImportConsentListMemberErrors (msg)
		SELECT distinct 'Email "' + Email + '" is invalid.'
		FROM #mc_consentListMembersImport
		WHERE memberCentral.dbo.fn_RegExReplace(Email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') <> ''
		ORDER BY 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- validate DateEntered
		BEGIN TRY
			ALTER TABLE #mc_consentListMembersImport ALTER COLUMN DateEntered varchar(max) null;
			UPDATE #mc_consentListMembersImport SET DateEntered = null WHERE DateEntered = '';
			ALTER TABLE #mc_consentListMembersImport ALTER COLUMN DateEntered datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('DateEntered column contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- update DateEntered
		UPDATE #mc_consentListMembersImport SET DateEntered = GETDATE() WHERE DateEntered IS NULL;

		IF ISNULL(@consentListIDList,'') = '' BEGIN
			INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('No Consent Lists selected.');
			GOTO on_done;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to validate data in required columns.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- import data
	BEGIN TRY
		DECLARE @changeSetUID uniqueidentifier, @nowDate datetime, @addActionID int;
		SET @changeSetUID = NEWID();
		SET @nowDate = GETDATE();
		SELECT @addActionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Add';

		DECLARE @tblemail_consentListMemberHistory TABLE (changeSetUID uniqueidentifier, email varchar(200), consentListID int,
			actionID int, updateDate datetime, enteredByMemberID int);
		
		BEGIN TRAN;
			INSERT INTO dbo.email_consentListMembers (consentListID, email, dateCreated)
				OUTPUT @changeSetUID, inserted.email, inserted.consentListID, @addActionID, @nowDate, @enteredByMemberID
				INTO @tblemail_consentListMemberHistory (changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
			SELECT cl.consentListID, imp.email, MIN(imp.dateEntered)
			FROM #mc_consentListMembersImport as imp
			CROSS APPLY memberCentral.dbo.fn_intListToTable(@consentListIDList,',') as tmp
			INNER JOIN dbo.email_consentLists as cl on cl.consentListID = tmp.listitem
				AND cl.[status] = 'A'
			INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
			LEFT OUTER JOIN dbo.email_consentListMembers as clm on clm.consentListID = cl.consentListID
				AND clm.email = imp.email
			WHERE clt.orgID = @orgID
			AND clm.consentListMemberID IS NULL
			GROUP BY cl.consentListID, imp.email;

			INSERT INTO dbo.email_consentListMemberHistory (changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
			SELECT changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID
			FROM @tblemail_consentListMemberHistory;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES ('Unable to import data.');
		INSERT INTO #tblImportConsentListMemberErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT memberCentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportConsentListMemberErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportConsentListMemberErrors') IS NOT NULL
		DROP TABLE #tblImportConsentListMemberErrors;
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_insertMessage
@messageTypeID int,
@siteID int,
@orgIdentityID int = NULL,
@sendingSiteResourceID int,
@isTestMessage bit = 0,
@sendOnDate datetime,
@recordedByMemberID int,
@fromName varchar(200),
@fromEmail varchar(200),
@replyToEmail varchar(200),
@senderEmail varchar(200),
@subject varchar(400),
@contentVersionID int,
@messageWrapper varchar(max),
@referenceType varchar(20), 
@referenceID int,
@consentListIDs varchar(max) = NULL,
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	set @messageID = null;

	-- validate fromEmail for DMARC compliance and determine if sender value is needed
	DECLARE @messageContent varchar(max),@cleanedContent varchar(max), @contentLanguageID int, @orgID int, @orgSystemMemberID int, 
		@allowedDomainsRegex varchar(1050), @emailRegex varchar(100), @senderValue varchar(200)='', @defaultSendingHostname varchar(100),
		@mailStreamID int, @environmentName varchar(12), @environmentID int;
	SET @emailRegex = '[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}';

	IF @orgIdentityID IS NULL
		SELECT @orgIdentityID = defaultOrgIdentityID FROM memberCentral.dbo.sites WHERE siteID = @siteID;

	IF membercentral.dbo.fn_RegexMatch (@fromName,@emailRegex) = 1 BEGIN
		SELECT @fromName = siteName, @orgID = orgID FROM memberCentral.dbo.sites WHERE siteID = @siteID;
	END

	SELECT @environmentName = tier 
	FROM membercentral.dbo.fn_getServerSettings();

	SELECT @environmentID = environmentID 
	FROM membercentral.dbo.platform_environments 
	WHERE environmentName = @environmentName;

	SELECT @mailStreamID = mailStreamID
	FROM dbo.email_messageTypes
	WHERE messageTypeID = @messageTypeID;

	SELECT @allowedDomainsRegex = '[.@](' + replace(STRING_AGG(suds.sendingHostname,'|'),'.','\.') + ')$', 
		@defaultSendingHostname = (SELECT sendingHostname FROM dbo.sendgrid_subuserDomains WHERE subuserDomainID = su.activeSubuserDomainID)
	FROM dbo.sendgrid_subusers su
	INNER JOIN dbo.sendgrid_subuserDomains suds
		ON suds.subuserID = su.subuserID
		AND su.siteID = @siteID 
	INNER JOIN dbo.sendgrid_subuserMailstreams sums
		ON sums.subuserID = su.subuserID 
	INNER JOIN dbo.email_mailstreams ms
		ON sums.mailstreamID = ms.mailStreamID
		AND ms.mailStreamID = @mailstreamID
	GROUP BY su.activeSubuserDomainID;

	-- from default subuser for the environment/mailstream combination
	IF @allowedDomainsRegex IS NULL
		SELECT @allowedDomainsRegex = '[.@](' + replace(STRING_AGG(suds.sendingHostname,'|'),'.','\.') + ')$', 
			@defaultSendingHostname = (SELECT sendingHostname FROM dbo.sendgrid_subuserDomains WHERE subuserDomainID = su.activeSubuserDomainID)
		FROM dbo.sendgrid_defaultSubusers dsu
		INNER JOIN dbo.sendgrid_subusers su
			ON su.subuserID = dsu.subuserID
			AND dsu.mailStreamID = @mailStreamID
			AND dsu.environmentID = @environmentID
		INNER JOIN dbo.sendgrid_subuserDomains suds
			ON suds.subuserID = su.subuserID
		GROUP BY su.activeSubuserDomainID;

	IF membercentral.dbo.fn_RegexMatch (@fromEmail,@allowedDomainsRegex) = 0 BEGIN
		SET @fromEmail = 'noreply@'+@defaultSendingHostname;

		IF len(@senderEmail) > 0  AND membercentral.dbo.fn_RegexMatch (@senderEmail,@allowedDomainsRegex) = 1 
			SET @senderValue = @senderEmail;
	END

	
	set @messageWrapper=rtrim(ltrim(@messageWrapper))
	-- clear wrapper if it's simply @@rawcontent@@
	if @messageWrapper='@@rawcontent@@' 
		set @messageWrapper=''

	-- cleanup HTML, cleanup wrapper if it's defined ... otherwise cleanup contentVersion
	if len(@messageWrapper) > 0 BEGIN

		exec dbo.email_cleanupHTML
			@html = @messageWrapper,
			@doctype = DEFAULT, 
			@headtext = DEFAULT,
			@cleanHTML =  @cleanedContent OUTPUT
		set @messageWrapper = @cleanedContent;

	END ELSE BEGIN
		select @messageContent = cv.rawContent, @contentLanguageID=cv.contentLanguageID
		from membercentral.dbo.cms_contentVersions cv
		where cv.contentVersionID = @contentVersionID

		exec dbo.email_cleanupHTML
			@html = @messageContent,
			@doctype = DEFAULT, 
			@headtext = DEFAULT,
			@cleanHTML =  @cleanedContent OUTPUT

		-- if changes were made to the content, create a new inactive contentversion and use it for the email.
		if @messageContent <> @cleanedContent BEGIN
			set @orgSystemMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID)

			exec membercentral.dbo.cms_createContentVersion
				@contentLanguageID = @contentLanguageID,
				@rawContent = @cleanedContent,
				@isActive = 0,
				@memberID = @orgSystemMemberID,
				@contentVersionID = @contentVersionID OUTPUT

		END
	END

	insert into dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, recordedByMemberID, 
		fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, messageWrapper, [uid], 
		sendOnDate, referenceType, referenceID, orgIdentityID, isTestMessage)
	values (@messageTypeID, @siteID, @sendingSiteResourceID, getdate(), @recordedByMemberID, @fromName, @fromEmail, 
		@replyToEmail, @senderValue, @subject, @contentVersionID, @messageWrapper, NEWID(), @sendOnDate, 
		@referenceType, @referenceID, @orgIdentityID, @isTestMessage);

	SELECT @messageID = SCOPE_IDENTITY();

	IF LEN(@consentListIDs) > 0 
		BEGIN 
			IF OBJECT_ID('tempdb..#consentListIDs') IS NOT NULL 
				DROP TABLE #consentListIDs;
			CREATE TABLE #consentListIDs (autoid INT IDENTITY(1,1) NOT NULL ,consentListID int);
		
			INSERT INTO #consentListIDs (consentListID)
			SELECT ft.listitem
			FROM  membercentral.dbo.fn_intListToTable(@consentListIDs,',') as ft
			INNER JOIN email_consentLists ec ON ec.consentListID = ft.listitem
				AND ec.[status] = 'A'
			INNER JOIN membercentral.dbo.cms_siteresources sr ON sr.siteResourceID = ec.siteResourceID
			AND sr.siteID = @siteID

			IF EXISTS (select top 1 * FROM #consentListIDs)
				BEGIN
					INSERT INTO dbo.email_messageConsentLists (siteID, messageID, consentListID, isPrimary)
					SELECT @siteID, @messageID, consentListID,CASE WHEN autoid = 1 THEN 1 ELSE 0 END
					FROM #consentListIDs
				END	

			IF OBJECT_ID('tempdb..#consentListIDs') IS NOT NULL 
			DROP TABLE #consentListIDs;

		END
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_markRecipientBatch
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @batchSizeMultiplier int = 12, @realbatchSize int, @batchStartDate datetime, @processingStatusID int, 
		@recipientID int, @scheduledStatusID int, @optoutStatusID int, @suppressedStatusID int, 
		@scheduledJobsFound bit=0, @futureDatedQueuedMessagesFound bit=0, @optOutConsentListModeID int, 
		@globalOptOutConsentListModeID int, @tier varchar(12), @regexMergeCode varchar(40), 
		@defaultMarketingSubUserID int, @defaultTransactionalSubUserID int, @environmentID int;

	SELECT @tier = tier, @regexMergeCode = regexMergeCode from membercentral.dbo.fn_getServerSettings();
	select @optOutConsentListModeID = consentListModeID from  dbo.email_consentListModes where modeName = 'Opt-Out'
	select @globalOptOutConsentListModeID = consentListModeID from  dbo.email_consentListModes where modeName = 'GlobalOptOut'

	select @defaultMarketingSubUserID=defaultMarketingSubUserID, @defaultTransactionalSubUserID=defaultTransactionalSubUserID, 
		@environmentID=environmentID
	from membercentral.dbo.platform_environments
	where environmentName = @tier;

	declare @tblMessages table(
		messageID int NOT NULL,
		messageTypeID int NOT NULL,
		mailStreamID int,
		replyToEmail varchar(200) NOT NULL, 
		subject varchar(400) NOT NULL,
		siteID int NOT NULL, 
		siteCode varchar(10) NOT NULL, 
		siteName varchar(60) NOT NULL,
		fromName varchar(200) NOT NULL,
		fromEmail varchar(200) NOT NULL,
		senderEmail varchar(200) NOT NULL, 
		messageContent varchar(max) NOT NULL,
		username varchar(100));

	declare @tblMessageOptOutList table(
		messageID int NOT NULL, 
		consentListID int NOT NULL,
		isPrimary bit,
		INDEX ix_tblMessageOptOutList_messageID_consentListID (messageID,consentListID)
	);

	declare @tblRecipients table(
		recipientID int PRIMARY KEY, 
		siteID int NOT NULL,
		messageID int NOT NULL, 
		memberID int NOT NULL,
		toName varchar(200) NOT NULL,
		toEmail varchar(200) NOT NULL,
		INDEX ix_messageRecipients NONCLUSTERED (messageID, recipientID)

	);

	declare @tblActiveSubusers TABLE (
		siteID int NOT NULL,
		mailStreamID int NOT NULL,
		subuserID int,
		username varchar(100),
		apikey varchar(300),
		poolName varchar(100), 
		outboundProviderCode varchar(25),
		smtphost varchar(200),
		smtpport int,
		defaultFromUsername varchar(100),
		subuserDomainID int,
		defaultSendingHostname varchar(200),
		validSendingHostnames varchar(8000),
		warmupPlanID int,
		warmupPlanStepID int,
		phaseout_subuserID int,
		phaseout_username varchar(100),
		phaseout_apikey varchar(300),
		phaseout_poolName varchar(100), 
		phaseout_outboundProviderCode varchar(25),
		phaseout_smtphost varchar(200),
		phaseout_smtpport int,
		phaseout_defaultFromUsername varchar(100),
		phaseout_subuserDomainID int,
		phaseout_defaultSendingHostname varchar(100),
		phaseout_validSendingHostnames varchar(8000),
		stepRatio numeric(3,2),
		stepPhaseOutMessagesSent int, 
		stepPhaseInMessagesSent int,
		stepMaxPhaseInMessagesAllowed int,
		enableHalonSending bit NOT NULL DEFAULT(0)
	)

	declare @tblRecipientsRemoved table(
		recipientID int PRIMARY KEY, 
		messageID int NOT NULL
	);


    declare @smtpAsync bit = 0, @threadCount int = 10, @recipientsPerMiniBatch int = 250, @limitWarmupToInternalAddresses bit=0, @javaSMTP bit = 0; 
    declare @sendingMethod varchar(5) = 'smtp'; -- expected values: smtp or api

	declare @taskParams TABLE (threadCount int, sendingMethod varchar(5), smtpAsync bit, recipientsPerMiniBatch int, requestedBatchSize int, batchSizeMultiplier int, limitWarmupToInternalAddresses bit, javaSMTP bit)
	insert into @taskParams (threadCount, sendingMethod,smtpAsync,recipientsPerMiniBatch, requestedBatchSize, batchSizeMultiplier, limitWarmupToInternalAddresses, javaSMTP) values (@threadCount,@sendingMethod, @smtpAsync, @recipientsPerMiniBatch, @batchSize, @batchSizeMultiplier, @limitWarmupToInternalAddresses, @javaSMTP)

	set @workerUUID = newID();
	set @batchStartDate = getdate();
	select @processingStatusID = statusID from dbo.email_statuses where statusCode = 'G';
	select @scheduledStatusID = statusID from dbo.email_statuses where statusCode = 'scheduled';
	select @optoutStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'optout';
	select @suppressedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'suppressed';


	set @realbatchSize = @batchSize * @batchSizeMultiplier;


    -- any mail to send? Skip if we're sending a specific message
    -- hardcoded @queueStatusID to use filtered index
    IF @restrictToMessageID IS NULL BEGIN
        SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
        select top 1 @recipientID = mrh.recipientID 
        from dbo.email_messageRecipientHistory as mrh
        inner join dbo.email_messages as m on mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @batchStartDate
        	and m.messageID = mrh.messageID
        where mrh.emailStatusID = 2 
        and mrh.batchID is null;

        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

        if @recipientID is null 
            GOTO on_done;
    END
	-- mark recipients
	-- hardcoded @queueStatusID to use filtered index
	update r 
	set r.batchID = @workerUUID,
		r.batchStartDate = @batchStartDate,
		r.emailStatusID = @processingStatusID
		output inserted.recipientID, inserted.siteID, inserted.messageID, inserted.memberID, inserted.toName, inserted.toEmail
		into @tblRecipients
	from dbo.email_messageRecipientHistory as r
	inner join (
		select top (@realbatchSize) recipientID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m on mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @batchStartDate
			and m.messageID = mrh.messageID
		where mrh.emailStatusID = 2 
		and mrh.batchID is null
		and mrh.messageID = isnull(@restrictToMessageID,mrh.messageID)
		order by queuePriority
	) as temp on temp.recipientID = r.recipientID;


	-- get messages
	insert into @tblMessages (messageID, messageTypeID, replyToEmail, subject, siteID, siteCode, siteName, fromName, fromEmail, senderEmail, messageContent)
	select m.messageID, m.messageTypeID, m.replyToEmail, m.subject, s.siteID, s.siteCode, s.siteName,
		m.fromName, m.fromEmail,
		m.senderEmail, messageContent = case when nullif(ltrim(rtrim(m.messagewrapper)),'') is null then cv.rawContent else replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) end
	from (select distinct siteID, messageID from @tblRecipients) as tmpM
	inner join dbo.email_messages as m on tmpM.siteID = m.siteID
		and m.status = 'A'
		and m.messageID = tmpM.messageID 
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
	inner join membercentral.dbo.sites as s on s.siteID = m.siteID;

	-- get mailstreamIDs
	update tm set
		mailStreamID = mt.mailStreamID
	from @tblMessages tm
	inner join email_messageTypes mt
		on mt.messageTypeID = tm.messageTypeID;

	-- get activeSubusers
	insert into @tblActiveSubusers (siteID, mailStreamID, subuserID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname,enableHalonSending)
	select distinct tm.siteID, ms.mailStreamID, su.subuserID, su.username, su.apikey, ippools.poolName, mp.outboundProviderCode, mpe.smtphost, mpe.smtpport, sums.defaultFromUsername, sud.sendingHostname,su.enableHalonSending
	from @tblMessages tm 
	inner join email_mailstreams ms
		on tm.mailStreamID = ms.mailStreamID
	inner join [dbo].[sendgrid_subusers] su
		on su.siteID = tm.siteID
		and su.environmentID = @environmentID
	left outer join sendgrid_defaultSubusers dsu 
		on dsu.subuserID = su.subuserID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = su.activeSubuserDomainID
	inner join [dbo].[sendgrid_subuserStatuses] sus
		on sus.subuserStatusID = su.statusID
		and sus.subuserStatusID = sud.statusID
		and sus.[status] = 'Active'
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.subuserID = su.subuserID
		and sums.mailstreamID = tm.mailStreamID
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = sums.ipPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID
	where dsu.defaultSubuserID is null;


	-- associate default mailstream subuser with mailstream messages that don't have site specific subuser ready
	insert into @tblActiveSubusers (siteID, mailStreamID, subuserID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname,enableHalonSending)
	select distinct  missingSubusers.siteID, ms.mailStreamID, su.subuserID, su.username, su.apikey, ippools.poolName, mp.outboundProviderCode, mpe.smtphost, mpe.smtpport, sums.defaultFromUsername, sud.sendingHostname, su.enableHalonSending
	from (
		select siteID, mailStreamID
		from @tblMessages
		except
		select asu.siteID, asu.mailStreamID
		from @tblActiveSubusers asu
		inner join @tblMessages tm on tm.siteID = asu.siteID
	) as missingSubusers
	inner join sendgrid_defaultSubusers dsu
		on dsu.mailStreamID = missingSubusers.mailStreamID
		and dsu.environmentID = @environmentID
	inner join [dbo].[sendgrid_subusers] su
		on su.subuserID = dsu.subuserID
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.subuserID = su.subuserID
		and sums.mailstreamID = missingSubusers.mailStreamID
	inner join dbo.email_mailstreams ms 
		on ms.mailStreamID = sums.mailstreamID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = su.activeSubuserDomainID
	inner join [dbo].[sendgrid_subuserStatuses] sus
		on sus.subuserStatusID = su.statusID
		and sus.subuserStatusID = sud.statusID
		and sus.[status] = 'Active'
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = sums.ipPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID;


	-- get all validSendingHostnames per subuser
	update asu set 
		validSendingHostnames = temp.validSendingHostnames		
	from @tblActiveSubusers asu
	inner join (
		select subuserID, STRING_AGG(sendingHostname,',') as validSendingHostnames
		from (
		select distinct su.subuserID, sud.sendingHostname 
		from @tblActiveSubusers su
		inner join sendgrid_subuserDomains sud 
			on su.subuserID = sud.subuserID
			and sud.statusID=2) as tmp

		group by subuserID
	) as temp on asu.subuserID = temp.subuserID

	
	-- get subuser warmup information

	update asu SET
		warmupPlanID= wp.warmupPlanID, 
		warmupPlanStepID= wps.warmupPlanStepID, 
		phaseout_subuserID = wp.phaseout_subuserID,
		stepRatio= wps.stepRatio,
		stepPhaseOutMessagesSent = wps.stepPhaseOutMessagesSent, 
		stepPhaseInMessagesSent = wps.stepPhaseInMessagesSent,
		stepMaxPhaseInMessagesAllowed= wps.stepMaxPhaseInMessagesAllowed,
		phaseout_username=su.username, 
		phaseout_apikey = su.apikey,
		phaseout_poolName = ippools.poolName,
		phaseout_defaultSendingHostname = sud.sendingHostname,
		phaseout_outboundProviderCode = mp.outboundProviderCode,
		phaseout_smtphost = mpe.smtphost,
		phaseout_smtpport = mpe.smtpport
	from @tblActiveSubusers asu
	inner join sendgrid_subuserMailstreamWarmupPlans wp
		on wp.siteID = asu.siteID
		and wp.mailStreamID = asu.mailStreamID
		and wp.phasein_SubUserID = asu.subuserID
		and wp.status = 'A'
	inner join sendgrid_subuserMailstreamWarmupPlanSteps wps
		on wp.warmupPlanID = wps.warmupPlanID
		and wps.status = 'A'
	inner join [dbo].[sendgrid_subusers] su
		on su.subuserID = wp.phaseout_SubuserID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = wp.phaseout_SubuserDomainID
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = wp.phaseout_IPPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID;

	-- get all validSendingHostnames per phaseout subuser
	update asu set 
		phaseout_validSendingHostnames = temp.validSendingHostnames,
		phaseout_defaultFromUsername = sums.defaultFromUsername
	from @tblActiveSubusers asu
	inner join (
		select phaseout_subuserID, STRING_AGG(sendingHostname,',') as validSendingHostnames
		from (
		select distinct su.phaseout_subuserID, sud.sendingHostname
		from @tblActiveSubusers su
		inner join sendgrid_subuserDomains sud 
			on su.phaseout_subuserID = sud.subuserID
			and sud.statusID=2) as tmp
		group by phaseout_subuserID
	) as temp on asu.phaseout_subuserID = temp.phaseout_subuserID
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.mailstreamID = asu.mailStreamID
		and sums.subuserID = asu.phaseout_subuserID


	-- update sendgrid username
	update m set 
		username = sub.username
	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID;

	-- enforce opt-outs
	insert into @tblMessageOptOutList (messageID, consentListID, isPrimary)
	select mcl.messageID, mcl.consentListID, mcl.isPrimary
	from @tblMessages m
	inner join email_messageConsentLists mcl 
		on mcl.messageID = m.messageID
	inner join dbo.email_consentLists cl
		on cl.consentListID = mcl.consentListID
		and cl.[status] = 'A'
		and cl.consentListModeID in (@optOutConsentListModeID,@globalOptOutConsentListModeID)
	group by mcl.messageID, mcl.consentListID, mcl.isPrimary;
		
	--get Global Optout for all messages that had an opt-out consentList
	insert into @tblMessageOptOutList (messageID, consentListID, isPrimary)
	select mo.messageID, global_cl.consentListID, 0 as isPrimary
	from @tblMessageOptOutList mo 
	inner join dbo.email_consentLists cl
		on cl.consentListID = mo.consentListID
		and cl.[status] = 'A'
		and cl.consentListModeID = @optOutConsentListModeID
	inner join dbo.email_consentListTypes clt
		on clt.consentListTypeID = cl.consentListTypeID
	inner join dbo.email_consentListTypes global_clt
		on global_clt.orgID = clt.orgID
		and global_clt.consentListTypeName = 'Global Lists'
	inner join dbo.email_consentLists global_cl
		on global_cl.consentListTypeID = global_clt.consentListTypeID
		and global_cl.consentListModeID = @globalOptOutConsentListModeID
		and global_cl.[status] = 'A'
	left outer join @tblMessageOptOutList as tmp on tmp.messageID = mo.messageID
		and tmp.consentListID = global_cl.consentListID
	where tmp.messageID is null
	group by mo.messageID, global_cl.consentListID;

	IF EXISTS (SELECT 1 FROM @tblMessageOptOutList) BEGIN

		update r set 
			emailStatusID = @optoutStatusID
		output inserted.recipientID, inserted.messageID
		into @tblRecipientsRemoved
		from @tblMessageOptOutList mo
		inner join @tblRecipients tmp_r 
			on tmp_r.messageID = mo.messageID
		inner join dbo.email_consentListMembers AS clm
			on clm.consentListID = mo.consentListID
			and clm.email = tmp_r.toEmail 
		inner join dbo.email_messageRecipientHistory as r
			on r.messageID = tmp_r.messageID
			and r.recipientID = tmp_r.recipientID
			and r.emailStatusID = @processingStatusID;

	END

	-- enforce suppressions

	update r set 
		emailStatusID = @suppressedStatusID
	output inserted.recipientID, inserted.messageID
	into @tblRecipientsRemoved
	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID
	inner join @tblRecipients tmp_r 
		on tmp_r.messageID = m.messageID
	inner join dbo.sendgrid_suppressions suppress
		on suppress.subuserID = sub.subuserID
		and suppress.emailAddress = tmp_r.toEmail
	inner join dbo.email_messageRecipientHistory as r
		on r.messageID = tmp_r.messageID
		and r.recipientID = tmp_r.recipientID
		and r.emailStatusID = @processingStatusID;

	-- remove any suppressed or opted out recipients from temp table so they are not returned to the worker
	IF EXISTS (SELECT 1 FROM @tblRecipientsRemoved) BEGIN
		delete tmp_r
		from @tblRecipientsRemoved removed 
		inner join @tblRecipients tmp_r 
			on removed.recipientID = tmp_r.recipientID
	END



	-- qryMessages
	select m.*, mt.messageTypeCode, mt.messageType, sub.defaultFromUsername, sub.defaultSendingHostname, sub.poolName, sub.warmupPlanID, siteID_mailstreamID = cast(sub.siteID as varchar(10)) + '_' + cast(sub.mailstreamID as varchar(10)),
		consentListIDs = (
			SELECT STUFF((SELECT ',' + cast(consentListID AS VARCHAR(10))
			FROM @tblMessageOptOutList moptouts
			where moptouts.messageID = m.messageID
			order by isPrimary desc, consentListID
			FOR XML PATH('')),1,1,'')
		)

	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID
	order by messageID;
	
	-- qrySubjectMetadata
	select distinct m.messageID, left(reg.Text,300) as fieldName1
	from @tblMessages as m
	cross apply membercentral.dbo.fn_RegexMatches(m.subject,@regexMergeCode) as reg
	order by 1, 2;	

	-- qryMessageMetadata
	select distinct m.messageID, f.fieldName
	from @tblMessages as m
	inner join dbo.email_messageMetadataFields as mf on mf.messageID = m.messageID
	inner join dbo.email_metadataFields as f on f.fieldID = mf.fieldID;
	
	-- qryRecipients
	select r.recipientID, r.messageID, r.memberID, r.toName, r.toEmail, m.username, siteID_mailstreamID = cast(m.siteID as varchar(10)) + '_' + cast(m.mailstreamID as varchar(10))
	from @tblRecipients r
	inner join @tblMessages m
		on m.messageID = r.messageID;

	-- qryRecipientMetadata
	select r.recipientID, f.fieldID, f.fieldName, r.messageid, r.memberid, mf.fieldValue, mf.fieldTextToReplace
	from @tblRecipients as r
	inner join dbo.email_messageMetadataFields as mf
		on r.messageID = mf.messageID
		and r.recipientID = mf.recipientID
	inner join dbo.email_metadataFields as f
		on mf.fieldID = f.fieldID
		and f.isMergeField = 1;


	-- qryRecipientAttachments
	select r.recipientID, a.attachmentID, a.fileName, a.localDirectory
	from dbo.email_messageRecipientAttachments as ra
	inner join dbo.email_attachments as a on a.attachmentID = ra.attachmentID
	inner join @tblRecipients as r on r.recipientID = ra.recipientID;

	-- qryTaskParams
	select threadCount, sendingMethod, smtpAsync, recipientsPerMiniBatch, requestedBatchSize, batchSizeMultiplier, limitWarmupToInternalAddresses, javaSMTP
	from @taskParams tp;

	-- qrySubusers
	select siteID_mailstreamID = cast(siteID as varchar(10)) + '_' + cast(mailstreamID as varchar(10)), subuserID, siteID, mailstreamID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname, validSendingHostnames,
		warmupPlanID, warmupPlanStepID, stepRatio, stepPhaseOutMessagesSent, stepPhaseInMessagesSent, stepMaxPhaseInMessagesAllowed, phaseout_subuserID,phaseout_username, phaseout_apikey, phaseout_poolName, phaseout_outboundProviderCode, phaseout_smtphost, phaseout_smtpport, phaseout_defaultFromUsername, phaseout_defaultSendingHostname, phaseout_validSendingHostnames,
		deliveryMode = 'postfix', sub.enableHalonSending 
	from @tblActiveSubusers sub
	group by subuserID, siteID, mailstreamID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname, validSendingHostnames,
		warmupPlanID, warmupPlanStepID, stepRatio, stepPhaseOutMessagesSent, stepPhaseInMessagesSent, stepMaxPhaseInMessagesAllowed, phaseout_subuserID, phaseout_username, phaseout_apikey, phaseout_poolName, phaseout_outboundProviderCode, phaseout_smtphost, phaseout_smtpport, phaseout_defaultFromUsername, phaseout_defaultSendingHostname, phaseout_validSendingHostnames, sub.enableHalonSending


	-- subuserDomain hostnames: sending, returnPath, linkbrand
	select siteID_mailstreamID = cast(su.siteID as varchar(10)) + '_' + cast(su.mailstreamID as varchar(10)), sud.subuserDomainID, su.subuserID, mp.outboundProviderCode, sud.sendingHostname,sud.linkBrandHostname,sud.returnPathHostname, sud.dkimSelectorActive
	from @tblActiveSubusers su
	inner join dbo.sendgrid_subuserDomains sud 
		on sud.subuserID in (su.subuserID,su.phaseout_subuserID)
		and sud.statusID=2
	inner join dbo.email_outboundProviders mp 
		on mp.outboundProviderID = sud.outboundProviderID
		and mp.outboundProviderCode in (su.outboundProviderCode,su.phaseout_outboundProviderCode)
	inner join dbo.email_outboundProviderEnvironments mpe
		on mpe.outboundProviderID = mp.outboundProviderID
		and mpe.environmentID = @environmentID
	group by su.siteID, su.mailstreamID, sud.subuserDomainID, su.subuserID, mp.outboundProviderCode, sud.sendingHostname,sud.linkBrandHostname,sud.returnPathHostname, sud.dkimSelectorActive
	

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_moveConsentList
@consentListID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orderNum int, @consentListTypeID int;
	
	SELECT @orderNum = orderNum, @consentListTypeID = consentListTypeID
	FROM dbo.email_consentLists
	WHERE consentListID = @consentListID
	AND [status] = 'A';

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum + 1
			WHERE consentListTypeID = @consentListTypeID
			AND orderNum >= @orderNum - 1
			AND [status] = 'A';
		
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum - 2
			WHERE consentListID = @consentListID
			AND [status] = 'A';
		END
		ELSE BEGIN
			UPDATE dbo.email_consentLists
			SET orderNum = orderNum - 1
			WHERE consentListTypeID = @consentListTypeID
			AND orderNum <= @orderNum + 1
			AND [status] = 'A';

			UPDATE dbo.email_consentLists
			SET orderNum = orderNum + 2
			WHERE consentListID = @consentListID
			AND [status] = 'A';
		END

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.email_reorderConsentLists
@consentListTypeID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (newOrderNum int NOT NULL, consentListID int NOT NULL, orderNum int NOT NULL);

	INSERT INTO @tmp (consentListID, orderNum, newOrderNum)
	SELECT consentListID, orderNum, ROW_NUMBER() OVER(ORDER BY orderNum) as newOrderNum
	FROM dbo.email_consentLists
	WHERE consentListTypeID = @consentListTypeID
	AND [status] = 'A';

	UPDATE rg
	SET rg.orderNum = t.newOrderNum
	FROM dbo.email_consentLists as rg 
	INNER JOIN @tmp as t on rg.consentListID = t.consentListID
	WHERE rg.[status] = 'A';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

USE trialsmith
GO

ALTER PROC dbo.ts_approveDepoConnectInquiry
@inquiryID int,
@emailSubject varchar(250),
@emailContent varchar(max),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @conversationID int, @messageTypeID int, @adminToolSiteResourceID int, @openStatusID int, @newContentID int,
		@newResourceID int, @resourceTypeID int, @contentVersionID int, @sendOnDate datetime = getdate(), @messageID int, @conversationSubject varchar(400),
		@recipientID int, @languageID int, @fromName varchar(200), @fromFirm varchar(200), @fromNameAndFirm varchar(200), @tier varchar(12), @defaultMarketingSubUserID int, @fromEmail varchar(200),
		@replyToEmail varchar(200), @emailTypeID int, @respondentDepoMemberDataID int, @respondentName varchar(200), @respondentFirm varchar(200), @respondentEmail varchar(200),
		@recipientMemberID int, @fieldID int, @inquiryOptOutListID int, @globalOptOutListID int, @orgSysMemberID int, @consentListIDs VARCHAR(MAX);

	IF OBJECT_ID('tempdb..#tmpConversations') IS NOT NULL
		DROP TABLE #tmpConversations;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL
		DROP TABLE #tmpRecipientDetails;
	CREATE TABLE #tmpConversations (conversationID int, requestorDepoMemberDataID int, fromName varchar(200), fromFirm varchar(200), replyToEmail varchar(200),
		respondentDepoMemberDataID int, respondentName varchar(200), respondentFirm varchar(200), respondentEmail varchar(200));
	CREATE TABLE #tmpRecipientDetails (recipientID INT, recipientMemberID INT, recipientEmail varchar(255), messageID int, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));

	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @languageID = membercentral.dbo.fn_getLanguageID('en');
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = 'TS';
	SELECT @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SELECT @openStatusID = statusID FROM dbo.expertConnectInquiryStatuses WHERE statusCode = 'Open';
	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EXPERTINQUIRY';
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @defaultMarketingSubUserID = defaultMarketingSubUserID FROM membercentral.dbo.platform_environments WHERE environmentName = @tier;
	SELECT @fromEmail = 'noreply@' + sendingHostName FROM platformMail.dbo.sendgrid_subuserDomains WHERE subuserID = @defaultMarketingSubUserID;
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @adminToolSiteResourceID = ast.siteResourceID
	FROM membercentral.dbo.admin_siteTools ast
	INNER JOIN membercentral.dbo.admin_toolTypes att ON att.tooltypeID = ast.toolTypeID
 		AND att.toolType = 'TrialSmithTools'
	WHERE ast.siteID = @siteID;

	SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
	FROM dbo.expertConnectInquirySettings;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND clt.consentListTypeName = 'Global Lists'
		AND cl.[status] = 'A'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
		AND modeName = 'GlobalOptOut';

	INSERT INTO #tmpConversations (conversationID, requestorDepoMemberDataID, fromName, fromFirm, replyToEmail, respondentDepoMemberDataID,
		respondentName, respondentFirm, respondentEmail)
	SELECT c.conversationID, req.depoMemberDataID, req.firstName + ' ' + req.lastName, d1.BillingFirm,
		i.emailAddressSlug + '_' + CAST(i.inquiryID as varchar(10)) + '_' + CAST(req.participantID as varchar(10)) + '@depoconnect.trialsmith.com',
		resp.depomemberdataID, resp.firstName + ' ' + resp.lastName, d2.BillingFirm, resp.email
	FROM dbo.expertConnectInquiryConversations c
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS req
		INNER JOIN dbo.expertConnectInquiryRoles AS r1 ON r1.roleID = req.roleID
			AND r1.roleCode = 'Requestor'
		INNER JOIN dbo.depomemberdata AS d1 ON d1.depoMemberDataID = req.depoMemberDataID
		ON req.conversationID = c.conversationID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS resp
		INNER JOIN dbo.expertConnectInquiryRoles AS r2 ON r2.roleID = resp.roleID
			AND r2.roleCode = 'Respondent'
		INNER JOIN dbo.depomemberdata AS d2 ON d2.depoMemberDataID = resp.depoMemberDataID
		ON resp.conversationID = c.conversationID
	WHERE c.inquiryID = @inquiryID;

	DELETE tmp
	FROM #tmpConversations AS tmp
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
		AND clm.email = tmp.respondentEmail;

	UPDATE dbo.expertConnectInquiries
	SET statusID = @openStatusID
	WHERE inquiryID = @inquiryID;

	EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID,
		@parentSiteResourceID=@adminToolSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
		@languageID=@languageID, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', @rawContent=@emailContent,
		@memberID=@recordedByMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

	SELECT TOP 1 @contentVersionID = cv.contentVersionID
	FROM membercentral.dbo.cms_content AS c 
	INNER JOIN membercentral.dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID and cl.languageID = @languageID
	INNER JOIN membercentral.dbo.cms_contentVersions AS cv ON cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	WHERE c.contentID = @newContentID;

	EXEC platformMail.dbo.email_insertMetadataField @fieldName='emailOptOutURL', @isMergeField=1, @fieldID=@fieldID OUTPUT;

	SELECT @conversationID = MIN(conversationID) FROM #tmpConversations;
	WHILE @conversationID IS NOT NULL BEGIN
		BEGIN TRAN;
			SELECT @fromName = fromName, @fromFirm = fromFirm, @replyToEmail = replyToEmail, @respondentName = respondentName, @respondentFirm = respondentFirm,
				@respondentEmail = respondentEmail, @respondentDepoMemberDataID = respondentDepoMemberDataID
			FROM #tmpConversations
			WHERE conversationID = @conversationID;

			SET @fromNameAndFirm = @fromName + ' - ' + @fromFirm;
			SET @conversationSubject = @emailSubject + ' (' + @fromName + ' and ' + @respondentName + ')'

			EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode='TS', @depomemberdataid=@respondentDepoMemberDataID,
				@memberID=@recipientMemberID OUTPUT;

			IF @recipientMemberID > 0 BEGIN
				
				-- get consentListIDS
				SELECT @consentListIDs = cast(NULLIF(@inquiryOptOutListID,0) as varchar(10));

				-- add email_message
				EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=NULL,
					@sendingSiteResourceID=@adminToolSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID,
					@fromName=@fromNameAndFirm, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',  @subject=@conversationSubject,
					@contentVersionID=@contentVersionID, @messageWrapper='', @referenceType='EXPERTINQUIRY_START',
					@referenceID=@inquiryID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;
		
				-- add recipient
				EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@recipientMemberID, 
					@toName=@respondentName, @toEmail=@respondentEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID,
					@recipientID=@recipientID OUTPUT;

				INSERT INTO #tmpRecipientDetails (recipientID, recipientMemberID, recipientEmail, messageID)
				SELECT @recipientID, @recipientMemberID, @respondentEmail, @messageID;

				-- add cc recipient
				EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@orgSysMemberID, 
					@toName='DepoConnect Message Logs', @toEmail='<EMAIL>', @emailTypeID=@emailTypeID,
					@statusCode='I', @siteID=@siteID, @recipientID=@recipientID OUTPUT;

				INSERT INTO #tmpRecipientDetails (recipientID, recipientMemberID, recipientEmail, messageID)
				SELECT @recipientID, @orgSysMemberID, '<EMAIL>', @messageID;
			END
		COMMIT TRAN;
		SELECT @conversationID = MIN(conversationID) FROM #tmpConversations WHERE conversationID > @conversationID;
	END

	SELECT tmp.recipientID, tmp.recipientMemberID, tmp.recipientEmail, m.memberNumber, tmp.messageID
	FROM #tmpRecipientDetails AS tmp
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = tmp.recipientMemberID;

	IF OBJECT_ID('tempdb..#tmpConversations') IS NOT NULL
		DROP TABLE #tmpConversations;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL
		DROP TABLE #tmpRecipientDetails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_forwardIncomingDepoConnectMessage
@receiverParticipantID int,
@senderParticipantID int,
@emailContent varchar(max),
@messageID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @messageTypeID int, @adminToolSiteResourceID int,@newContentID int, @newResourceID int,
		@resourceTypeID int, @nowDate datetime = getdate(), @contentTitle varchar(250), @contentVersionID int,
		@conversationSubject varchar(400), @languageID int, @fromEmail varchar(400), @sentOnBehalfOfName varchar(200), @fromFirm varchar(200),
		@fromNameAndFirm varchar(200), @messageStatusIDInserting int, @tier varchar(12),
		@defaultMarketingSubUserID int, @replyToEmail varchar(200), @emailTypeID int, @fieldID int,
		@conversationID int, @receiverRoleCode varchar(20), @approvedByDepoMemberDataID int, @senderRoleCode varchar(20),
		@sentOnBehalfOfParticipantID int, @onBehalfOfReceiverDepoMemberID int, @onBehalfOfReceiverMemberID int,
		@delegateName varchar(200), @requestorName varchar(200), @respondentName varchar(200), @orgSysMemberID int,
		@itemGroupUID uniqueidentifier, @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
		@inquiryOptOutListID int, @globalOptOutListID int, @messageLogEmail varchar(100) = '<EMAIL>', @consentListIDs VARCHAR(MAX);

	DECLARE @tblRecipientRoles TABLE (roleCode varchar(20));

	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;
	CREATE TABLE #tmpRecipientEmails (email varchar(200), recipientName varchar(200));
	CREATE TABLE #tmpRecipientsAdded (recipientID INT, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));

	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @languageID = membercentral.dbo.fn_getLanguageID('en');
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = 'TS';
	SELECT @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'EXPERTINQUIRY';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	SELECT @defaultMarketingSubUserID = defaultMarketingSubUserID FROM membercentral.dbo.platform_environments WHERE environmentName = @tier;
	SELECT @fromEmail = 'noreply@' + sendingHostName FROM platformMail.dbo.sendgrid_subuserDomains WHERE subuserID = @defaultMarketingSubUserID;
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTypes WHERE orgID = @orgID AND emailTypeOrder = 1;

	SELECT @adminToolSiteResourceID = ast.siteResourceID
	FROM membercentral.dbo.admin_siteTools ast
	INNER JOIN membercentral.dbo.admin_toolTypes att ON att.tooltypeID = ast.toolTypeID
 		AND att.toolType = 'TrialSmithTools'
	WHERE ast.siteID = @siteID;

	SELECT @inquiryOptOutListID = ISNULL(optOutListID,0)
	FROM dbo.expertConnectInquirySettings;

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
		AND clt.orgID = @orgID
		AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID
		AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	DECLARE @initialQueuePriority int, @expectedRecipientCount int;
	SELECT @expectedRecipientCount = COUNT(*) FROM #tmpRecipientEmails;
	SELECT @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	SELECT @conversationID =  p.conversationID, @receiverRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @receiverParticipantID;

	IF @receiverRoleCode IN ('Requestor','RequestorDelegate')
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Requestor'), ('RequestorDelegate');
	ELSE
		INSERT INTO @tblRecipientRoles (roleCode)
		VALUES('Respondent'), ('RespondentDelegate');
	
	-- get recipient emails
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	SELECT email, LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,'')))
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN @tblRecipientRoles AS tmp ON tmp.roleCode = r.roleCode
	WHERE p.conversationID = @conversationID;

	-- add cc recipient
	INSERT INTO #tmpRecipientEmails (email, recipientName)
	VALUES(@messageLogEmail, 'DepoConnect Message Logs');

	DELETE tmp
	FROM #tmpRecipientEmails AS tmp
	INNER JOIN platformMail.dbo.email_consentListMembers AS clm ON clm.consentListID IN (@inquiryOptOutListID, @globalOptOutListID)
		AND clm.email = tmp.email;

	IF NOT EXISTS (SELECT 1 FROM #tmpRecipientEmails)
		GOTO on_done;

	-- get memberID for recipients
	IF @receiverRoleCode IN ('Requestor','Respondent')
		SELECT @onBehalfOfReceiverDepoMemberID = depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants
		WHERE participantID = @receiverParticipantID;
	ELSE BEGIN
		SELECT @onBehalfOfReceiverDepoMemberID = p.depoMemberDataID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @receiverRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode='TS',
		@depomemberdataid=@onBehalfOfReceiverDepoMemberID, @memberID=@onBehalfOfReceiverMemberID OUTPUT;

	IF @onBehalfOfReceiverMemberID = 0
		SET @onBehalfOfReceiverMemberID = @orgSysMemberID;
	
	-- prep contentTitle, conversationSubject, fromName & replyToEmail
	SELECT @senderRoleCode = r.roleCode
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	WHERE p.participantID = @senderParticipantID;

	IF @senderRoleCode IN ('Requestor','Respondent')
		SET @sentOnBehalfOfParticipantID = @senderParticipantID;
	ELSE BEGIN
		SELECT @sentOnBehalfOfParticipantID = p.participantID
		FROM dbo.expertConnectInquiryConversationParticipants AS p
		INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
			AND r.roleCode = CASE
				WHEN @senderRoleCode = 'RequestorDelegate' THEN 'Requestor'
				ELSE 'Respondent'
			END
		WHERE p.conversationID = @conversationID;
	END

	SELECT @contentTitle = 'Re: ' + LTRIM(RTRIM(ISNULL(i.expertFirstName,'') + ' ' + ISNULL(i.expertLastName,''))) + ' - ' + i.topic,
		@delegateName = CASE
				WHEN @sentOnBehalfOfParticipantID <> @senderParticipantID
					THEN (
						SELECT LTRIM(RTRIM(ISNULL(p2.firstName,'') + ' ' + ISNULL(p2.lastName,'')))
						FROM dbo.expertConnectInquiryConversationParticipants AS p2
						WHERE p2.participantID = @senderParticipantID
					)
				ELSE ''
			END,
		@sentOnBehalfOfName = LTRIM(RTRIM(ISNULL(p.firstName,'') + ' ' + ISNULL(p.lastName,''))),
		@fromFirm = CASE WHEN r.roleCode = 'Requestor' THEN i.companyName ELSE d.BillingFirm END
	FROM dbo.expertConnectInquiryConversationParticipants AS p
	INNER JOIN dbo.expertConnectInquiryRoles AS r ON r.roleID = p.roleID
	INNER JOIN dbo.depomemberdata AS d ON d.depoMemberDataID = p.depoMemberDataID
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = p.inquiryID
	WHERE p.participantID = @sentOnBehalfOfParticipantID;

	SET @fromNameAndFirm = @sentOnBehalfOfName + ' - ' + @fromFirm;
	IF LEN(@delegateName) > 0
		SET @fromNameAndFirm = @delegateName + ' on behalf of ' + @fromNameAndFirm;

	SELECT @requestorName = req.firstName + ' ' + req.lastName,
		@respondentName = resp.firstName + ' ' + resp.lastName,
		@replyToEmail = i.emailAddressSlug + '_' + CAST(i.inquiryID as varchar(10)) + '_' +
			CASE 
				WHEN @senderRoleCode IN ('Requestor','RequestorDelegate') THEN CAST(req.participantID as varchar(10))
				ELSE CAST(resp.participantID as varchar(10))
			END + '@depoconnect.trialsmith.com'
	FROM dbo.expertConnectInquiryConversations c
	INNER JOIN dbo.expertConnectInquiries AS i ON i.inquiryID = c.inquiryID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS req
		INNER JOIN dbo.expertConnectInquiryRoles AS r1 ON r1.roleID = req.roleID
			AND r1.roleCode = 'Requestor'
		INNER JOIN dbo.depomemberdata AS d1 ON d1.depoMemberDataID = req.depoMemberDataID
		ON req.conversationID = c.conversationID
	INNER JOIN dbo.expertConnectInquiryConversationParticipants AS resp
		INNER JOIN dbo.expertConnectInquiryRoles AS r2 ON r2.roleID = resp.roleID
			AND r2.roleCode = 'Respondent'
		INNER JOIN dbo.depomemberdata AS d2 ON d2.depoMemberDataID = resp.depoMemberDataID
		ON resp.conversationID = c.conversationID
	WHERE c.conversationID = @conversationID;

	SET @emailContent = REPLACE(@emailContent,'[[replyToEmailAddress]]',@replyToEmail);

	SET @conversationSubject = @contentTitle + ' (' + @requestorName + ' and ' + @respondentName + ')';

	BEGIN TRAN;

		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID,
			@parentSiteResourceID=@adminToolSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=@languageID, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@emailContent,
			@memberID=@orgSysMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

		SELECT TOP 1 @contentVersionID = cv.contentVersionID
		FROM membercentral.dbo.cms_content AS c 
		INNER JOIN membercentral.dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID
			AND cl.languageID = @languageID
		INNER JOIN membercentral.dbo.cms_contentVersions AS cv ON cv.contentLanguageID = cl.contentLanguageID
			AND cv.isActive = 1
		WHERE c.contentID = @newContentID;

		-- get consentListIDS
		SELECT @consentListIDs = cast(NULLIF(@inquiryOptOutListID,0) as varchar(10));

		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, @orgIdentityID=NULL,
			@sendingSiteResourceID=@adminToolSiteResourceID, @isTestMessage=0, @sendOnDate=@nowDate, @recordedByMemberID=@orgSysMemberID,
			@fromName=@fromNameAndFirm, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',  @subject=@conversationSubject,
			@contentVersionID=@contentVersionID, @messageWrapper='', @referenceType='EXPERTINQUIRY_REPLY',
			@referenceID=@conversationID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

		-- add recipients
		INSERT INTO platformMail.dbo.email_messageRecipientHistory(messageID, memberID, dateLastUpdated,
			toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
			OUTPUT INSERTED.recipientID
			INTO #tmpRecipientsAdded (recipientID)
		SELECT @messageID, CASE WHEN email = @messageLogEmail THEN @orgSysMemberID ELSE @onBehalfOfReceiverMemberID END,
			@nowDate, recipientName, email,
			@messageStatusIDInserting, NULL, NULL, @emailTypeID, @siteID, @initialQueuePriority
		FROM #tmpRecipientEmails;

		EXEC platformMail.dbo.email_insertMetadataField @fieldName='emailOptOutURL', @isMergeField=1, @fieldID=@fieldID OUTPUT;

		SET @itemGroupUID = NEWID();
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

		-- queue recipient details with extended merge code [[emailOptOutURL]]
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		SELECT itemUID, @insertingQueueStatusID
		FROM #tmpRecipientsAdded;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, tmp.recipientID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCRecipientID'
			UNION
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, @messageID
		FROM #tmpRecipientsAdded AS tmp
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCMessageID';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
		SELECT @itemGroupUID, tmp.itemUID, @orgSysMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID
		FROM platformMail.dbo.email_metadataFields AS mdf
		INNER JOIN platformQueue.dbo.tblQueueTypeDataColumns AS dc ON dc.queueTypeID = @queueTypeID
			AND dc.columnname = 'MCExtMergeCodeFieldID'
		CROSS JOIN #tmpRecipientsAdded AS tmp
		WHERE mdf.fieldName = 'emailOptOutURL';

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = GETDATE()
		FROM platformQueue.dbo.tblQueueItems AS qi
		INNER JOIN #tmpRecipientsAdded AS tmp ON tmp.itemUID = qi.itemUID;

	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpRecipientEmails') IS NOT NULL
		DROP TABLE #tmpRecipientEmails;
	IF OBJECT_ID('tempdb..#tmpRecipientsAdded') IS NOT NULL
		DROP TABLE #tmpRecipientsAdded;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO