ALTER FUNCTION dbo.ams_getVirtualGroupConditionVerbose_cl_entry (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	declare @verbose varchar(max);
	
	select top 1 @verbose = 'Has entry in '+
		case when len(isnull(consentListModeID.val,'')) > 0 then consentListModeID.val + ' ' else '' end +
		'Consent Lists' +
		case when len(isnull(emailTypeID.val,'')) > 0 then '; email types of ' + emailTypeID.val else '' end +
		case when len(isnull(emailTagTypeID.val,'')) > 0 then '; email tag types of ' + emailTagTypeID.val else '' end +
		
		case when len(isnull(consentListTypeID.val,'')) > 0 then '; consent list types of ' + consentListTypeID.val else '' end +
		case when len(isnull(consentListID.val,'')) > 0 then '; consent lists of ' + consentListID.val else '' end +
		case 
			when len(isnull(clDateAddedLower.val,'')) > 0 and len(isnull(clDateAddedUpper.val,'')) > 0 then '; date added between ' + clDateAddedLower.val + ' and ' + clDateAddedUpper.val
			when len(isnull(clDateAddedLower.val,'')) > 0 then '; date added after ' + clDateAddedLower.val
			when len(isnull(clDateAddedUpper.val,'')) > 0 then '; date added before ' + clDateAddedUpper.val
		else ''
		end
	from dbo.ams_virtualGroupConditions as c
	OUTER APPLY (
		SELECT STRING_AGG(met.emailType,' OR ') WITHIN GROUP (ORDER BY met.emailType ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'emailTypeID'
		INNER JOIN dbo.ams_memberEmailTypes as met on met.emailTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as emailTypeID(val)
	OUTER APPLY (
		SELECT STRING_AGG(met.emailTagType,' OR ') WITHIN GROUP (ORDER BY met.emailTagType ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'emailTagTypeID'
		INNER JOIN dbo.ams_memberEmailTagTypes as met on met.emailTagTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as emailTagTypeID(val)
	OUTER APPLY (
		SELECT clm.modeName
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListModeID'
		INNER JOIN platformMail.dbo.email_consentListModes as clm on clm.consentListModeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListModeID(val)
	OUTER APPLY (
		SELECT STRING_AGG(clt.consentListTypeName,' OR ') WITHIN GROUP (ORDER BY clt.consentListTypeName ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListTypeID'
		INNER JOIN platformMail.dbo.email_consentListTypes as clt on clt.consentListTypeID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListTypeID(val)
	OUTER APPLY (
		-- don't limit to active consent lists here
		SELECT STRING_AGG(cl.consentListName,' OR ') WITHIN GROUP (ORDER BY cl.consentListName ASC) as conditionValue
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'consentListID'
		INNER JOIN platformMail.dbo.email_consentLists as cl on cl.consentListID = cast(cv.conditionValue as int)
		WHERE cv.conditionID = c.conditionID
	) as consentListID(val)
	OUTER APPLY (
		SELECT cv.conditionValue + case when af.AFID is not null then ' (' + af.afName + ')' else '' end
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'clDateAddedLower'
		LEFT OUTER JOIN dbo.af_advanceFormulas as af on af.AFID = cv.AFID
		WHERE cv.conditionID = c.conditionID
	) as clDateAddedLower(val)
	OUTER APPLY (
		SELECT cv.conditionValue + case when af.AFID is not null then ' (' + af.afName + ')' else '' end
		FROM dbo.ams_virtualGroupConditionValues as cv
		INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'clDateAddedUpper'
		LEFT OUTER JOIN dbo.af_advanceFormulas as af on af.AFID = cv.AFID
		WHERE cv.conditionID = c.conditionID
	) as clDateAddedUpper(val)
	WHERE c.conditionID = @conditionID;

	RETURN @verbose;

END
GO
