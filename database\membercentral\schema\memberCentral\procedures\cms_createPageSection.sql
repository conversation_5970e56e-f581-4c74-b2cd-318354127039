ALTER PROC dbo.cms_createPageSection
@siteID int,
@sectionResourceTypeID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(50),
@sectionBreadcrumb varchar(200),
@inheritPlacements bit,
@sectionID int OUTPUT

AS


SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @sectionResourceID int, @rc int, @parentSiteResourceID int = null;

	SET @sectionID = null;

	-- get parentSiteResourceID of parentSection
	IF @parentSectionID IS NOT NULL
		select @parentSiteResourceID=sr.parentSiteResourceID 
		from dbo.cms_pageSections ps 
		inner join cms_siteResources sr 
			on ps.siteResourceID = sr.siteResourceID 
			and ps.siteID=@siteID
			and sr.siteID=@siteID
			and ps.sectionID=@parentSectionID;
	
	IF @parentSectionID IS NULL AND @sectionCode = 'Root'
		SELECT @parentSiteResourceID = siteResourceID
		FROM dbo.sites
		WHERE siteID = @siteID;

	-- sectionCode must be A-Z 0-9 only and be unique in site
	select @sectionCode = dbo.fn_regExReplace(isnull(@sectionCode,''),'[^A-Z0-9\-_]+','');
	IF EXISTS (select sectionID from dbo.cms_pageSections ps inner join cms_siteResources sr on ps.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ps.siteID = @siteID and ps.sectionCode = @sectionCode and ps.sectioncode <> '')
		RAISERROR('SectionCode must be unique',16,1);

	BEGIN TRAN;
		exec dbo.cms_createSiteResource @resourceTypeID = @sectionResourceTypeID, @siteResourceStatusID = 1,
			@siteID = @siteid, @isVisible = 1, @parentSiteResourceID = @parentSiteResourceID, @siteResourceID   = @sectionResourceID OUTPUT;

		INSERT INTO dbo.cms_pageSections (siteID, ovTemplateID, ovTemplateIDMobile, ovModeID, parentSectionID, sectionName, sectionCode,sectionBreadcrumb, inheritPlacements, siteResourceID)
		VALUES (@siteID, @ovTemplateID, @ovTemplateIDMobile, @ovModeID, @parentSectionID, @sectionName, nullif(@sectionCode,''),nullif(@sectionBreadcrumb,''), @inheritPlacements, @sectionResourceID);
	
		select @sectionID = SCOPE_IDENTITY();

		exec dbo.cache_cms_updateRecursivePageSections @siteID = @siteID, @restrictToSectionID = @sectionID;

		exec dbo.cache_cms_updateDerivedPageSectionSettings @restrictToSiteID = @siteID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
