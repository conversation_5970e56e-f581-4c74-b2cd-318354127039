<cfcomponent>
	
	<cffunction name="getSettings" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryOrg = "">

		<cfquery name="qryOrg" datasource="#application.dsn.memberCentral.dsn#">
			SELECT o.orgcode, oi.organizationName as orgName, oi.organizationShortName as orgShortName, oi.website as orgURL, o.emailImportResults,  
				o.hasPrefix, o.usePrefixList, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix, 
				o.memNumPrefixGuest, o.memNumPrefixUser, o.cache_perms_status, o.allowedAdminCount, 
				o.memberNumberOptionID, o.autoGenMemNumMax, o.profLicenseStatusLabel,
				o.profLicenseNumberLabel, o.profLicenseDateLabel, o.defaultOrgIdentityID,
				s.siteCode, s.siteName,
				isnull(fsCounts.m_prefix,0) as fsprefix,
				isnull(fsCounts.m_middlename,0) as fsmiddlename,
				isnull(fsCounts.m_suffix,0) as fssuffix,
				isnull(fsCounts.m_professionalSuffix,0) as fsprofessionalsuffix,
				isnull(vgcCounts.m_prefix,0) as vgcprefix,
				isnull(vgcCounts.m_middlename,0) as vgcmiddlename,
				isnull(vgcCounts.m_suffix,0) as vgcsuffix,
				isnull(vgcCounts.m_professionalSuffix,0) as vgcprofessionalsuffix,
				o.defaultRecordTypeID
			FROM dbo.organizations as o
			INNER JOIN dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			LEFT OUTER JOIN dbo.sites as s on s.siteID = o.defaultSiteID
			OUTER apply (
				select m_prefix, m_middlename, m_suffix, m_professionalSuffix
				from (
					select mf.fieldcode, count(distinct mfs.fieldSetID) as fsCount
					from dbo.ams_memberFieldSets as mfs
					inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = o.orgID
					inner join dbo.ams_memberFields as mf on mf.fieldSetID = mfs.fieldSetID 
						and mf.fieldCode in ('m_prefix','m_middlename','m_suffix','m_professionalSuffix')
					group by mf.fieldcode
				) tmp
				PIVOT (min(fsCount) for fieldcode in ([m_prefix],[m_middlename],[m_suffix],[m_professionalSuffix])) as pvt
			) as fsCounts
			OUTER apply (
				select m_prefix, m_middlename, m_suffix, m_professionalSuffix
				from (
					select fieldCode, count(distinct conditionID) as vgcCount
					FROM dbo.ams_virtualGroupConditions 
					WHERE orgID = o.orgID
					AND fieldCode in ('m_prefix','m_middlename','m_suffix','m_professionalSuffix')
					group by fieldCode
				) tmp
				PIVOT (min(vgcCount) for fieldcode in ([m_prefix],[m_middlename],[m_suffix],[m_professionalSuffix])) as pvt
			) as vgcCounts
			WHERE o.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
		</cfquery>

		<cfreturn qryOrg>
	</cffunction>
	
	<cffunction name="getMemberNumberOptions" access="public" output="false" returntype="query">
		<cfset var qryMemberNumberOptions = "">

		<cfquery name="qryMemberNumberOptions" datasource="#application.dsn.memberCentral.dsn#">
			SELECT optionID, optionName 
			FROM dbo.ams_orgMemberNumberOptions
		</cfquery>

		<cfreturn qryMemberNumberOptions>
	</cffunction>

	<cffunction name="getRecordTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arrrecordtypes":[] }>

		<cftry>
			<cfset local.returnStruct["arrrecordtypes"] = getRecordTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRecordTypesArray" access="public" output="false" returntype="array">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.arrRecordTypes = []>

		<cfset local.qryRecordTypes = application.objOrgInfo.getOrgRecordTypes(orgID=arguments.orgID)>

		<cfloop query="local.qryRecordTypes">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['rtid'] = local.qryRecordTypes.recordTypeID>
			<cfset local.tmp['rtname'] = local.qryRecordTypes.recordTypeName>
			<cfset local.tmp['rtcode'] = local.qryRecordTypes.recordTypeCode>
			<cfset local.tmp['rtdesc'] = local.qryRecordTypes.recordTypeDesc>
			<cfset local.tmp['isperson'] = local.qryRecordTypes.isPerson>
			<cfset local.tmp['isdefault'] = local.qryRecordTypes.isDefault>
			<cfset arrayAppend(local.arrRecordTypes,local.tmp)>
		</cfloop>

		<cfreturn local.arrRecordTypes>
	</cffunction>

	<cffunction name="updateRecordType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="recordTypeID" type="numeric" required="true">
		<cfargument name="recordType" type="string" required="true">
		<cfargument name="recordTypeCode" type="string" required="true">
		<cfargument name="recordTypeDesc" type="string" required="true">
		<cfargument name="isPerson" type="string" required="true">
		<cfargument name="isDefault" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg':'', 'arrrecordtypesrelationshiptypes': [], 'arrrecordtypes': [] }>

		<cftry>
			<cfquery name="local.qryRecordTypeUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.ams_OrgSettingsUpdateRecordType
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
						@RecordTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.recordTypeID#">,
						@RecordTypeCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordTypeCode)#">,
						@RecordTypeName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordType)#">,
						@RecordTypeDesc=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordTypeDesc)#">,
						@isPerson=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPerson is 1#">,
						@isOrganization=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPerson is not 1#">,
						@isDefault=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isDefault is 1#">;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['arrrecordtypesrelationshiptypes'] = getRecordTypesRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.returnStruct['arrrecordtypes'] = getRecordTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Changes were not saved.#cfcatch.message#">
			<cfif FindNoCase("Record type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That record type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertRecordType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="recordType" type="string" required="true">
		<cfargument name="recordTypeCode" type="string" required="true">
		<cfargument name="recordTypeDesc" type="string" required="true">
		<cfargument name="isPerson" type="string" required="true">
		<cfargument name="isDefault" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg':'', 'rtid':0, 'arrrecordtypes':[] }>

		<cftry>
			<cfquery name="local.qryRecordTypeInsert" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @recordTypeID int;

					EXEC dbo.ams_createRecordType
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
						@RecordTypeCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordTypeCode)#">,
						@RecordTypeName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordType)#">,
						@RecordTypeDesc=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.recordTypeDesc)#">,
						@isPerson=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPerson is 1#">,
						@isOrganization=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isPerson is not 1#">,
						@isDefault=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isDefault is 1#">,
						@recordTypeID=@recordTypeID OUTPUT;

					SELECT @recordTypeID AS recordTypeID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['rtid'] = val(local.qryRecordTypeInsert.recordTypeID)>
			<cfset local.returnStruct['arrrecordtypes'] = getRecordTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to add entry.#cfcatch.message#">
			<cfif FindNoCase("Record type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That record type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeRecordType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="recordTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'arrrecordtypesrelationshiptypes': [], 'arrrecordtypes':[] }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeRecordType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			
			<cfset local.data['success'] = true>
			<cfset local.data["arrrecordtypesrelationshiptypes"] = getRecordTypesRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.data['arrrecordtypes'] = getRecordTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.data['success'] = false>
			<cfset local.data['err'] = cfcatch.detail>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRelationshipTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arrrelationshiptypes":[] }>

		<cftry>
			<cfset local.returnStruct["arrrelationshiptypes"] = getRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRelationshipTypesArray" access="public" output="false" returntype="array">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.arrRelationshipTypes = []>
		<cfset local.qryRelationshipTypes = application.objOrgInfo.getOrgRecordRelationshipTypes(orgID=arguments.orgID)>

		<cfloop query="local.qryRelationshipTypes">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['rrtid'] = local.qryRelationshipTypes.relationshipTypeID>
			<cfset local.tmp['rrtname'] = local.qryRelationshipTypes.relationshipTypeName>
			<cfset local.tmp['rrtcode'] = local.qryRelationshipTypes.relationshipTypeCode>
			<cfset arrayAppend(local.arrRelationshipTypes,local.tmp)>
		</cfloop>

		<cfreturn local.arrRelationshipTypes>
	</cffunction>

	<cffunction name="updateRecordRelationshipType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="relationshipTypeID" type="numeric" required="true">
		<cfargument name="relationshipType" type="string" required="true">
		<cfargument name="relationshipTypeCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg':'', 'arrrecordtypesrelationshiptypes': [], 'arrrelationshiptypes':[] }>

		<cftry>
			<cfquery name="local.qryRelationshipTypeUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.ams_OrgSettingsUpdateRecordRelationshipType
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
						@RecordRelationshipTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.relationshipTypeID#">,
						@RecordRelationshipTypeCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.relationshipTypeCode)#">,
						@RecordRelationshipTypeName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.relationshipType)#">;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['arrrecordtypesrelationshiptypes'] = getRecordTypesRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.returnStruct['arrrelationshiptypes'] = getRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Changes were not saved.#cfcatch.message#">
			<cfif FindNoCase("Relationship type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That relationship type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertRecordRelationshipType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="relationshipType" type="string" required="true">
		<cfargument name="relationshipTypeCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg':'', 'rrtid':0, 'arrrelationshiptypes':[] }>

		<cftry>
			<cfquery name="local.qryRelationshipTypeInsert" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @relationshipTypeID int;

					EXEC dbo.ams_createRecordRelationshipType
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">, 
						@relationshipTypeCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.relationshipTypeCode)#">,
						@relationshipTypeName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.relationshipType)#">,
						@relationshipTypeID=@relationshipTypeID OUTPUT;

					SELECT @relationshipTypeID AS relationshipTypeID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['rrtid'] = val(local.qryRelationshipTypeInsert.relationshipTypeID)>
			<cfset local.returnStruct['arrrelationshiptypes'] = getRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to add entry.#cfcatch.message#">
			<cfif FindNoCase("Relationship type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That relationship type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeRecordRelationshipType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="recordRelationshipTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'arrrecordtypesrelationshiptypes': [], 'arrrelationshiptypes':[] }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeRecordRelationshipType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordRelationshipTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data['success'] = true>
			<cfset local.data['arrrecordtypesrelationshiptypes'] = getRecordTypesRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.data['arrrelationshiptypes'] = getRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.data['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRecordTypesRelationshipTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arrrecordtypesrelationshiptypes":[] }>

		<cftry>
			<cfset local.returnStruct["arrrecordtypesrelationshiptypes"] = getRecordTypesRelationshipTypesArray(orgID=arguments.mcproxy_orgID)>
			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRecordTypesRelationshipTypesArray" access="public" output="false" returntype="array">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.arrRecordTypesRelationshipTypes = []>
		<cfset local.qryRecordTypesRelationshipTypes = application.objOrgInfo.getOrgRecordTypeRelationshipTypes(orgID=arguments.orgID)>

		<cfloop query="local.qryRecordTypesRelationshipTypes">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['rtrtid'] = local.qryRecordTypesRelationshipTypes.recordTypeRelationshipTypeID>
			<cfset local.tmp['masterrtname'] = local.qryRecordTypesRelationshipTypes.masterRecordTypeName>
			<cfset local.tmp['childrtname'] = local.qryRecordTypesRelationshipTypes.childRecordTypeName>
			<cfset local.tmp['rrtname'] = local.qryRecordTypesRelationshipTypes.relationshipTypeName>
			<cfset arrayAppend(local.arrRecordTypesRelationshipTypes,local.tmp)>
		</cfloop>

		<cfreturn local.arrRecordTypesRelationshipTypes>
	</cffunction>

	<cffunction name="insertRecordTypeRelationshipType" access="public" output="false" returntype="struct">
		<cfargument name="relationshipTypeID" type="numeric" required="true">
		<cfargument name="masterRecordTypeID" type="numeric" required="true">
		<cfargument name="childRecordTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'rtrtid':0 }>

		<cftry>
			<cfquery name="local.qryRecordTypeRelationshipTypeInsert" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @recordTypeRelationshipTypeID int;

					EXEC dbo.ams_createRecordTypesRelationshipTypes
						@relationshipTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.relationshipTypeID#">,
						@masterRecordTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.masterRecordTypeID#">,
						@childRecordTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.childRecordTypeID#">,
						@isActive=1,
						@recordTypeRelationshipTypeID=@recordTypeRelationshipTypeID OUTPUT;

					SELECT @recordTypeRelationshipTypeID AS recordTypeRelationshipTypeID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['rtrtid'] = val(local.qryRecordTypeRelationshipTypeInsert.recordTypeRelationshipTypeID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to add entry.#cfcatch.message#">
			<cfif FindNoCase("Active Record Type / Relationship Type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That record type / relationship type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeRecordTypeRelationshipType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="recordTypeRelationshipTypeID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeRecordTypeRelationshipType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordTypeRelationshipTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data['success'] = true>
		<cfcatch type="Any">
			<cfset local.data['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveMemFields" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="hasPrefix" type="boolean" required="true">
		<cfargument name="usePrefixList" type="boolean" required="true">
		<cfargument name="hasMiddleName" type="boolean" required="true">
		<cfargument name="hasSuffix" type="boolean" required="true">
		<cfargument name="hasProfessionalSuffix" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_OrgSettingsUpdateMemberFields">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasPrefix#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.usePrefixList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasMiddleName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasSuffix#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hasProfessionalSuffix#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getPrefixTypesArray" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { 'success': false, 'arrprefixes': [] }>
		<cfset local.qryPrefixTypes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.mcproxy_orgID)>

		<cfloop query="local.qryPrefixTypes">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['ptid'] = local.qryPrefixTypes.prefixTypeID>
			<cfset local.tmp['prefix'] = local.qryPrefixTypes.prefix>
			<cfset local.tmp['membercount'] = local.qryPrefixTypes.memberCount>
			<cfset local.tmp['encodedprefix'] = encodeForJavaScript(local.qryPrefixTypes.prefix)>
			<cfset arrayAppend(local.data['arrprefixes'],local.tmp)>
		</cfloop>

		<cfset local.data['success'] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertPrefixType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="prefixType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg':'', 'ptid':0 }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberPrefixType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.prefixType#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.prefixTypeID">
			</cfstoredproc>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['ptid'] = val(local.prefixTypeID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to add entry.#cfcatch.message#">
			<cfif FindNoCase("Prefix type already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That prefix type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removePrefixType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="prefixTypeID" type="numeric" required="true">
		<cfargument name="newPrefixTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberPrefixType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.prefixTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.newPrefixTypeID#">
			</cfstoredproc>

			<cfset local.data['success'] = true>
		<cfcatch type="Any">
			<cfset local.data['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAddressTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arraddresstypes":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="address")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arraddresstypes"] = local.strAPIResponse.data.address>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateAddressType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="addressType" type="string" required="true">
		<cfargument name="addressTypeDesc" type="string" required="true">
		<cfargument name="addressTypeUID" type="string" required="true">
		<cfargument name="hasAttn" type="boolean" required="true">
		<cfargument name="hasAddress2" type="boolean" required="true">
		<cfargument name="hasAddress3" type="boolean" required="true">
		<cfargument name="hasCounty" type="boolean" required="true">
		<cfargument name="districtMatching" type="boolean" required="true">
		<cfargument name="standardizeAddress" type="boolean" required="true">
		<cfargument name="standardizeCity" type="boolean" required="true">
		<cfargument name="standardizeState" type="boolean" required="true">
		<cfargument name="standardizePostalCode" type="boolean" required="true">
		<cfargument name="standardizeCounty" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = {
				type:arguments.addressType, description:arguments.addressTypeDesc, useattn:arguments.hasAttn, useaddress2:arguments.hasAddress2,useaddress3:arguments.hasAddress3,
				usecounty:arguments.hasCounty, enabledistrictmatching:arguments.districtMatching, standardizeaddress:arguments.standardizeAddress, standardizecity:arguments.standardizeCity,
				standardizestate:arguments.standardizeState, standardizepostalcode:arguments.standardizePostalCode, standardizecounty:arguments.standardizeCounty
			}>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.addressTypeUID))>
				<cfset local.payLoadData.api_id = trim(arguments.addressTypeUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="address/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.addressTypeUID)) and trim(arguments.addressTypeUID) neq arguments.uid>
			<cfset local.returnStruct['straddress'] = local.strUpdateResponse.data.address>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertAddressType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="addressType" type="string" required="true">
		<cfargument name="addressTypeDesc" type="string" required="true">
		<cfargument name="hasAttn" type="boolean" required="true">
		<cfargument name="hasAddress2" type="boolean" required="true">
		<cfargument name="hasAddress3" type="boolean" required="true">
		<cfargument name="hasCounty" type="boolean" required="true">
		<cfargument name="districtMatching" type="boolean" required="true">
		<cfargument name="standardizeAddress" type="boolean" required="true">
		<cfargument name="standardizeCity" type="boolean" required="true">
		<cfargument name="standardizeState" type="boolean" required="true">
		<cfargument name="standardizePostalCode" type="boolean" required="true">
		<cfargument name="standardizeCounty" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = {
				type:arguments.addressType, description:arguments.addressTypeDesc, useattn:arguments.hasAttn, useaddress2:arguments.hasAddress2,useaddress3:arguments.hasAddress3,
				usecounty:arguments.hasCounty, enabledistrictmatching:arguments.districtMatching, standardizeaddress:arguments.standardizeAddress, standardizecity:arguments.standardizeCity,
				standardizestate:arguments.standardizeState, standardizepostalcode:arguments.standardizePostalCode, standardizecounty:arguments.standardizeCounty
			}>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="address", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['straddress'] = local.strInsertResponse.data.address>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="refreshAddressDistrictsByTypeorTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="strFrom" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfquery name="local.qryAddress" datasource="#application.dsn.platformQueue.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				declare @newQueueEntries TABLE (addressid int PRIMARY KEY, searchStringSHA1 varchar(40));
				declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">;

				insert into @newQueueEntries (addressID, searchStringSHA1)
				select addressID, 
					CONVERT(VARCHAR(40), HASHBYTES('SHA1',lower(
						cast(@orgID as varchar(10)) + '|' + isnull(address,'') + '|' + ltrim(rtrim(isnull(city,''))) + '|' + ltrim(rtrim(isnull(postalCode,''))) + '|' + ltrim(rtrim(isnull(stateCode,''))) + '|on_or_after_today'
					)),2)
				from (
					<cfif arguments.strFrom EQ "type">
						select distinct ma.addressTypeID, ma.addressID, ma.memberID, 
							ltrim(rtrim(ma.address1 + 
								case when mat.hasAddress2 = 1 then isnull(' ' + nullif(ma.address2,''),'') else '' end +  
								case when mat.hasAddress3 = 1 then isnull(' ' + nullif(ma.address3,''),'') else '' end)) as address, 
							ma.city, ma.postalCode, ma.stateCode
						from membercentral.dbo.ams_memberAddresses as ma 
						inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID
							and mat.addressTypeID = ma.addressTypeID
							and mat.uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.uid#">
							and mat.districtMatching = 1
						inner join membercentral.dbo.ams_members as m on m.orgID = @orgID 
							and m.memberID = ma.memberID
							and m.status = 'A'
						inner join membercentral.dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.groupPrintID = m.groupPrintID
						inner join membercentral.dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.rightPrintID = gprp.rightPrintID 
							and srfrp.siteID = gprp.siteID
						inner join membercentral.dbo.cms_siteResourceFunctions AS srf ON srf.functionID = srfrp.functionID 
							and srf.functionname = 'updateDistrictingInformation'
						inner join membercentral.dbo.sites as s on s.orgID = @orgID
							and s.siteResourceID = srfrp.siteResourceID 
						where ma.orgID = @orgID
						and ma.address1 is not null
						and ma.address1 <> ''
					<cfelse>
						select distinct ma.addressTypeID, ma.addressID, ma.memberID, 
							ltrim(rtrim(ma.address1 + 
								case when mat.hasAddress2 = 1 then isnull(' ' + nullif(ma.address2,''),'') else '' end +  
								case when mat.hasAddress3 = 1 then isnull(' ' + nullif(ma.address3,''),'') else '' end)) as address, 
							ma.city, ma.postalCode, ma.stateCode
						from membercentral.dbo.ams_memberAddresses as ma 
						inner join membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID
							and mat.addressTypeID = ma.addressTypeID
						inner join membercentral.dbo.ams_members as m on m.orgID = @orgID 
							and m.memberID = ma.memberID
							and m.status = 'A'
						inner join membercentral.dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
							AND matag.memberID = m.memberID
							and matag.addressTypeID = ma.addressTypeID
						inner join membercentral.dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
							and matagt.addressTagTypeID = matag.addressTagTypeID
							and matagt.uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.uid#">
							and matagt.districtMatching = 1
						inner join membercentral.dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.groupPrintID = m.groupPrintID
						inner join membercentral.dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.rightPrintID = gprp.rightPrintID 
							and srfrp.siteID = gprp.siteID
						inner join membercentral.dbo.cms_siteResourceFunctions AS srf ON srf.functionID = srfrp.functionID 
							and srf.functionname = 'updateDistrictingInformation'
						inner join membercentral.dbo.sites as s on s.orgID = @orgID
							and s.siteResourceID = srfrp.siteResourceID 
						where ma.orgID = @orgID
						and ma.address1 is not null
						and ma.address1 <> ''
					</cfif>
				) as tmp;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				delete new
				from platformQueue.dbo.queue_districtMatching dm
				inner join @newQueueEntries new on new.addressID = dm.addressID;

				insert into platformQueue.dbo.queue_districtMatching (addressID, useStoredXMLIfCurrent, dateAdded, searchStringSHA1)
				select addressID, 0, getdate(), searchStringSHA1
				from @newQueueEntries;
			</cfquery>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeAddressType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="address/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderAddressType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="address/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAddressTags" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arraddresstags":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="address/tag")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arraddresstags"] = local.strAPIResponse.data.addresstag>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateAddressTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="addressTagType" type="string" required="true">
		<cfargument name="addressTagTypeUID" type="string" required="true">
		<cfargument name="allowMemberUpdate" type="boolean" required="true">
		<cfargument name="districtMatching" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.addressTagType, allowmemberupdate:arguments.allowMemberUpdate, enabledistrictmatching:arguments.districtMatching }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.addressTagTypeUID))>
				<cfset local.payLoadData.api_id = trim(arguments.addressTagTypeUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="address/tag/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.addressTagTypeUID)) and trim(arguments.addressTagTypeUID) neq arguments.uid>
			<cfset local.returnStruct['straddresstag'] = local.strUpdateResponse.data.addresstag>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertAddressTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="addressTagType" type="string" required="true">
		<cfargument name="allowMemberUpdate" type="boolean" required="true">
		<cfargument name="districtMatching" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.addressTagType, allowmemberupdate:arguments.allowMemberUpdate, enabledistrictmatching:arguments.districtMatching }>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="address/tag", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['straddresstag'] = local.strInsertResponse.data.addresstag>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeAddressTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="address/tag/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderAddressTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="address/tag/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getPhoneTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arrphonetypes":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="phone")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arrphonetypes"] = local.strAPIResponse.data.phone>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePhoneType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="phoneType" type="string" required="true">
		<cfargument name="phoneTypeUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.phoneType }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.phoneTypeUID))>
				<cfset local.payLoadData.api_id = trim(arguments.phoneTypeUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="phone/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.phoneTypeUID)) and trim(arguments.phoneTypeUID) neq arguments.uid>
			<cfset local.returnStruct['strphone'] = local.strUpdateResponse.data.phone>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertPhoneType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="phoneType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.phoneType }>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="phone", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['strphone'] = local.strInsertResponse.data.phone>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removePhoneType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="phone/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderPhoneType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="phone/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEmailTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arremailtypes":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="email")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arremailtypes"] = local.strAPIResponse.data.email>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateEmailType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="emailType" type="string" required="true">
		<cfargument name="emailTypeDesc" type="string" required="true">
		<cfargument name="emailTypeUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.emailType, description:arguments.emailTypeDesc }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.emailTypeUID))>
				<cfset local.payLoadData.api_id = trim(arguments.emailTypeUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="email/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.emailTypeUID)) and trim(arguments.emailTypeUID) neq arguments.uid>
			<cfset local.returnStruct['stremail'] = local.strUpdateResponse.data.email>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertEmailType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="emailType" type="string" required="true">
		<cfargument name="emailTypeDesc" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.emailType, description:arguments.emailTypeDesc }>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="email", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['stremail'] = local.strInsertResponse.data.email>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeEmailType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="email/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderEmailType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="email/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEmailTags" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arremailtags":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="email/tag")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arremailtags"] = local.strAPIResponse.data.emailtag>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateEmailTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="emailTag" type="string" required="true">
		<cfargument name="emailTagUID" type="string" required="true">
		<cfargument name="allowMemberUpdate" type="boolean" required="true">
		<cfargument name="warnWhenEmpty" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.emailTag, allowmemberupdate:arguments.allowMemberUpdate, warnwhenempty:arguments.warnWhenEmpty }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.emailTagUID))>
				<cfset local.payLoadData.api_id = trim(arguments.emailTagUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="email/tag/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.emailTagUID)) and trim(arguments.emailTagUID) neq arguments.uid>
			<cfset local.returnStruct['stremailtag'] = local.strUpdateResponse.data.emailtag>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertEmailTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="emailTag" type="string" required="true">
		<cfargument name="allowMemberUpdate" type="boolean" required="true">
		<cfargument name="warnWhenEmpty" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.emailTag, allowmemberupdate:arguments.allowMemberUpdate, warnwhenempty:arguments.warnWhenEmpty }>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="email/tag", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['stremailtag'] = local.strInsertResponse.data.emailtag>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeEmailTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="email/tag/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderEmailTag" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="email/tag/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getWebsiteTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errmsg":'', "arrwebsitetypes":[] }>

		<cftry>
			<cfset local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="GET", endpoint="website")>
			<cfif not local.strAPIResponse.error>
				<cfset local.returnStruct["success"] = true>
				<cfset local.returnStruct["arrwebsitetypes"] = local.strAPIResponse.data.website>
			<cfelse>
				<cfset local.returnStruct["errmsg"] = arrayToList(local.strAPIResponse.messages)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateWebsiteType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">
		<cfargument name="websiteType" type="string" required="true">
		<cfargument name="websiteTypeDesc" type="string" required="true">
		<cfargument name="websiteTypeUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.websiteType, description:arguments.websiteTypeDesc }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.websiteTypeUID))>
				<cfset local.payLoadData.api_id = trim(arguments.websiteTypeUID)>
			</cfif>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="website/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['uidchanged'] = len(trim(arguments.websiteTypeUID)) and trim(arguments.websiteTypeUID) neq arguments.uid>
			<cfset local.returnStruct['strwebsite'] = local.strUpdateResponse.data.website>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertWebsiteType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="websiteType" type="string" required="true">
		<cfargument name="websiteTypeDesc" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { type:arguments.websiteType, description:arguments.websiteTypeDesc }>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="website", payload=serializeJSON(local.payloadData))>
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['strwebsite'] = local.strInsertResponse.data.website>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeWebsiteType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="website/#arguments.uid#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reorderWebsiteType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="pos" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfset local.payLoadData = { position = arguments.pos }>

		<cftry>
			<cfset local.strReorderResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="website/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strReorderResponse.error>
				<cfthrow message="#arrayToList(local.strReorderResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getProLicenseTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true }>

		<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct['arrlicensetypes'] = arrayNew(1)>
		<cfloop query="local.qryOrgProLicenses">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['pltypeid'] = local.qryOrgProLicenses.PLTypeID>
			<cfset local.tmp['plname'] = local.qryOrgProLicenses.PLName>
			<cfset local.tmp['alertduplicates'] = local.qryOrgProLicenses.alertDuplicates>
			<cfset arrayAppend(local.returnStruct['arrlicensetypes'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateProLicenseType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseTypeID" type="numeric" required="true">
		<cfargument name="licenseType" type="string" required="true">
		<cfargument name="alertDuplicates" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfquery name="local.qryLicenseTypeUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.ams_OrgSettingsUpdateLicense
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
						@PLTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.licenseTypeID#">,
						@PLName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.licenseType)#">,
						@alertDuplicates=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.alertDuplicates#">;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['earliestlicensedatecalcfields'] = getEarliestLicenseDateCalcFields(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Changes were not saved.#cfcatch.message#">
			<cfif FindNoCase("professional license type is invalid or already in use",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That professional license type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateProLicenseTypeDuplicate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseTypeID" type="numeric" required="true">
		<cfargument name="alertDuplicates" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfquery name="local.qryLicenseTypeUpdate" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.ams_memberProfessionalLicenseTypes 
				SET alertDuplicates = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.alertDuplicates#">
				WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
				AND PLTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.licenseTypeID#">;
			</cfquery>
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertProLicenseType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'pltypeid':0 }>

		<cftry>
			<cfquery name="local.qryLicenseTypeInsert" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @PLTypeID int;

					EXEC dbo.ams_createMemberProfessionalLicenseType
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">, 
						@PLName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.licenseType)#">,
						@alertDuplicates=0,
						@PLTypeID=@PLTypeID OUTPUT;

					SELECT @PLTypeID AS PLTypeID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['pltypeid'] = val(local.qryLicenseTypeInsert.PLTypeID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="moveProLicenseType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseTypeID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveMemberProfessionalLicenseType">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.licenseTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeProLicenseType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberProfessionalLicenseType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.licenseTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getProLicenseStatuses" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true }>

		<cfset local.qryOrgProLicensesStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=arguments.mcproxy_orgID)>

		<cfset local.returnStruct['arrlicensestatuses'] = arrayNew(1)>
		<cfloop query="local.qryOrgProLicensesStatuses">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['plstatusid'] = local.qryOrgProLicensesStatuses.PLStatusID>
			<cfset local.tmp['statusname'] = local.qryOrgProLicensesStatuses.statusName>
			<cfset arrayAppend(local.returnStruct['arrlicensestatuses'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateLicenseStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseStatusID" type="numeric" required="true">
		<cfargument name="licenseStatus" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfquery name="local.qryLicenseStatusUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					EXEC dbo.ams_OrgSettingsUpdateLicenseStatus
						@orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">,
						@PLStatusID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.licenseStatusID#">,
						@StatusName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.licenseStatus)#">;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['earliestlicensedatecalcfields'] = getEarliestLicenseDateCalcFields(orgID=arguments.mcproxy_orgID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Changes were not saved.#cfcatch.message#">
			<cfif FindNoCase("Professional License status already exists",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That professional license status is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertLicenseStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseStatus" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'plstatusid':0 }>

		<cftry>
			<cfquery name="local.qryLicenseStatusInsert" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @PLStatusID int;

					EXEC dbo.ams_createMemberProfessionalLicenseStatus @orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">, 
						@StatusName=<cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.licenseStatus)#">,
						@PLStatusID=@PLStatusID OUTPUT;

					SELECT @PLStatusID AS PLStatusID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['plstatusid'] = val(local.qryLicenseStatusInsert.PLStatusID)>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="moveProLicenseStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseStatusID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveMemberProfessionalLicenseStatus">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.licenseStatusID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeProLicenseStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="licenseStatusID" type="numeric" required="true">
		<cfargument name="newLicenseStatusID" type="numeric" required="true">

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberProfessionalLicenseStatus">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.licenseStatusID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.newLicenseStatusID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>	

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEarliestLicenseDateCalcFields" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct['arrlicensetypes'] = getProLicenseTypes(mcproxy_orgID=arguments.orgID).arrlicensetypes>
		<cfset local.returnStruct['arrlicensestatuses'] = getProLicenseStatuses(mcproxy_orgID=arguments.orgID).arrlicensestatuses>

		<cfquery name="local.qryOrgEarlyLicenseTypesAndStatuses" datasource="#application.dsn.membercentral.dsn#">
			SELECT PLTypeID, PLStatusID 
			FROM dbo.ams_memberProfessionalLicenseEarliestDateTypes 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			ORDER BY PLTypeID, PLStatusID
		</cfquery>
		<cfquery name="local.earlyProfLicenseTypes" dbtype="query">
			SELECT DISTINCT PLTypeID
			FROM [local].qryOrgEarlyLicenseTypesAndStatuses
			WHERE PLTypeID IS NOT NULL
		</cfquery>
		<cfquery name="local.earlyProfLicenseStatuses" dbtype="query">
			SELECT DISTINCT PLStatusID
			FROM [local].qryOrgEarlyLicenseTypesAndStatuses
			WHERE PLStatusID IS NOT NULL
		</cfquery>

		<cfset local.returnStruct['selectedearlyproflicensetypes'] = valueList(local.earlyProfLicenseTypes.PLTypeID)>
		<cfset local.returnStruct['selectedearlyproflicensestatuses'] = valueList(local.earlyProfLicenseStatuses.PLStatusID)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveEarliestLicenseDateCalcFields" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="earlyMemberCustomField" type="numeric" required="true">
		<cfargument name="earlyLicenseTypes" type="string" required="true">
		<cfargument name="earlyLicenseStatuses" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cfsetting requesttimeout="600">

		<cftry>
			<cfquery name="local.qryEarliestLicenseDateCalcFieldsSave" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.ams_orgSettingsSetEarliestLicenseDate 
					@orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">, 
					<cfif val(arguments.earlyMemberCustomField) gt 0>
						@columnID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.earlyMemberCustomField#">,
					<cfelse>
						@columnID = null,
					</cfif>
					<cfif len(arguments.earlyLicenseTypes)>
						@earlyLicenseTypes = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.earlyLicenseTypes#">,
					<cfelse>
						@earlyLicenseTypes = '',
					</cfif>
					<cfif len(arguments.earlyLicenseStatuses)>
						@earlyLicenseStatuses = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.earlyLicenseStatuses#">,
					<cfelse>
						@earlyLicenseStatuses = '',
					</cfif>
					@recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;
			</cfquery>
			
			<cfquery name="local.qryOrgEarlyLicenseTypesAndStatuses" datasource="#application.dsn.membercentral.dsn#">
				SELECT PLTypeID, PLStatusID 
				FROM dbo.ams_memberProfessionalLicenseEarliestDateTypes 
				WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
				ORDER BY PLTypeID, PLStatusID
			</cfquery>
			<cfquery name="local.earlyProfLicenseTypes" dbtype="query">
				SELECT DISTINCT PLTypeID
				FROM [local].qryOrgEarlyLicenseTypesAndStatuses
				WHERE PLTypeID IS NOT NULL
			</cfquery>
			<cfquery name="local.earlyProfLicenseStatuses" dbtype="query">
				SELECT DISTINCT PLStatusID
				FROM [local].qryOrgEarlyLicenseTypesAndStatuses
				WHERE PLStatusID IS NOT NULL
			</cfquery>

			<cfset local.returnStruct['selectedearlyproflicensetypes'] = valueList(local.earlyProfLicenseTypes.PLTypeID)>
			<cfset local.returnStruct['selectedearlyproflicensestatuses'] = valueList(local.earlyProfLicenseStatuses.PLStatusID)>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">	
			<cfset local.returnStruct['success'] = false>	
		</cfcatch>	
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveLicenseTypeLabels" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="profLicenseStatusLabel" type="string" required="true">
		<cfargument name="profLicenseNumberLabel" type="string" required="true">
		<cfargument name="profLicenseDateLabel" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfquery name="local.qryLicenseLabelsSave" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">;

					BEGIN TRAN;
						UPDATE dbo.organizations
						SET profLicenseStatusLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.profLicenseStatusLabel)#">,
							profLicenseNumberLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.profLicenseNumberLabel)#">,
							profLicenseDateLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.profLicenseDateLabel)#">
						WHERE orgID = @orgID;

						-- update verbose of org prof. license conditions
						UPDATE vgc
						SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
						FROM dbo.ams_memberProfessionalLicenseTypes AS mplt 
						INNER JOIN dbo.ams_virtualGroupConditions AS vgc ON vgc.orgID = @orgID 
							AND vgc.fieldCode like 'mpl\_' + cast(mplt.PLTypeID as varchar(10)) + '\_%' escape ('\')
						WHERE mplt.orgID = @orgID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addUSStatesAsLicenseTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false, 'errmsg': '' }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createAllStateProfessionalLicenseTypes">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			</cfstoredproc>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getDistrictTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "arrdistricttypes":[] }>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.arrdistricttypes" returntype="array">
			SELECT dt.districtTypeID as dtid, dt.vendorTypeID as vtid, dt.districtType as type,
				dt.districtTypeDesc as description, vt.vendorType + ' (' + vt.vendorDesc + ')' as vendortypedisplayname
			FROM dbo.ams_memberDistrictTypes dt
			INNER JOIN dbo.ams_memberDistrictVendorTypes vt ON vt.vendorTypeID = dt.vendorTypeID
			WHERE dt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			ORDER BY dt.districtTypeOrder;
		</cfquery>
		
		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getDistrictVendorTypes" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "arrvendortypes":[] }>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.arrvendortypes" returntype="array">
			SELECT vt.vendorTypeID as vtid, vt.vendorType + ' (' + vt.vendorDesc + ')' as displayname
			FROM dbo.ams_memberDistrictVendorTypes vt
			LEFT JOIN dbo.ams_memberDistrictTypes dt ON dt.vendorTypeID = vt.vendorTypeID
				AND dt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			WHERE dt.districtTypeID IS NULL
			ORDER BY vt.vendorTypeOrder;
		</cfquery>
		
		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateDistrictType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">
		<cfargument name="districtTypeID" type="numeric" required="true">
		<cfargument name="districtType" type="string" required="true">
		<cfargument name="districtTypeDesc" type="string" required="true">
		<cfargument name="vendorTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_OrgSettingsUpdateDistrict">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.districtTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.vendorTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.districtType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.districtTypeDesc#">
			</cfstoredproc>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to update entry.#cfcatch.message#">
			<cfif FindNoCase("District type already exists",cfcatch.detail) OR FindNoCase("Member view column name",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That district type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertDistrictType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">
		<cfargument name="districtType" type="string" required="true">
		<cfargument name="districtTypeDesc" type="string" required="true">
		<cfargument name="vendorTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberDistrictType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.vendorTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.districtType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.districtTypeDesc#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.districtTypeID">
			</cfstoredproc>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createVWMemberData">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
			</cfstoredproc>

			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['strdistricttype'] = {
				"dtid" = local.districtTypeID,
				"vtid" = arguments.vendorTypeID,
				"type" = arguments.districtType,
				"description" = arguments.districtTypeDesc
			}>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = "Unable to add entry.#cfcatch.message#">
			<cfif FindNoCase("district type is invalid or already in use",cfcatch.detail) OR FindNoCase("Member view column name",cfcatch.detail)>
				<cfset local.returnStruct['errmsg'] = local.returnStruct['errmsg'] & " That district type is invalid or already in use.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeDistrictType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">
		<cfargument name="districtTypeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberDistrictType">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.districtTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="reorderDistrictType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="string" required="true">
		<cfargument name="districtTypeID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="yes">

		<cfset var local = structNew()>

		<cfif arguments.dir eq "up">
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveMemberDistrictTypeUp">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.districtTypeID#">
			</cfstoredproc>
		<cfelseif arguments.dir eq "down">
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveMemberDistrictTypeDown">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.districtTypeID#">
			</cfstoredproc>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="checkOrgIdentityName" access="public" output=false returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">
		<cfargument name="organizationName" type="string" required="true">
		
		<cfset var qryCheckOrgIdentityName = "">

		<cfquery name="qryCheckOrgIdentityName" datasource="#application.dsn.membercentral.dsn#">
			select orgIdentityID
			from dbo.orgIdentities
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			AND organizationName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.organizationName#">
			<cfif arguments.orgIdentityID neq 0>
				AND orgIdentityID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
			</cfif>
		</cfquery>
		
		<cfreturn { "success":true, "orgidentitynameinuse":qryCheckOrgIdentityName.recordCount GT 0 }>
	</cffunction>

	<cffunction name="saveOrgIdentity" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">
		<cfargument name="organizationName" type="string" required="true">
		<cfargument name="organizationShortName" type="string" required="true">
		<cfargument name="address1" type="string" required="true">
		<cfargument name="address2" type="string" required="true">
		<cfargument name="city" type="string" required="true">
		<cfargument name="stateID" type="numeric" required="true">
		<cfargument name="postalCode" type="string" required="true">
		<cfargument name="phone" type="string" required="true">
		<cfargument name="fax" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="website" type="string" required="true">
		<cfargument name="XUserName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "newOrgIdentityID":0 }>
		
		<cftry>
			<cfif arguments.orgIdentityID GT 0>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_OrgSettingsUpdateIdentity">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.organizationName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.organizationShortName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.address1#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.address2#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.city#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.stateID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.postalCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.phone#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fax#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.email#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.website#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.XUserName#">
				</cfstoredproc>
			<cfelse>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createOrgIdentity">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.organizationName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.organizationShortName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.address1#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.address2#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.city#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.stateID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.postalCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.phone#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fax#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.email#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.website#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.XUserName#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.returnStruct.newOrgIdentityID">
				</cfstoredproc>
			</cfif>
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success  = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getorgIdentityDetailsStruct" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">

		<cfset var strOrgIdentity = structNew()>

		<cfquery name="strOrgIdentity" datasource="#application.dsn.platformMail.dsn#" returntype="struct" columnkey="orgIdentityID">
			SELECT oi.orgIdentityID, oi.organizationName AS orgname, oi.address1, oi.address2, oi.city, ISNULL(s.Name,'') AS state,
				oi.postalCode AS postalcode, ISNULL(c.country,'') country, oi.phone, oi.fax, oi.website, oi.email
			FROM membercentral.dbo.orgIdentities AS oi
			LEFT JOIN membercentral.dbo.ams_states AS s ON s.stateID = oi.stateID
			LEFT JOIN membercentral.dbo.ams_countries AS c ON c.countryID = s.countryID
			WHERE oi.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			AND oi.orgIdentityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">;
		</cfquery>

		<cfif structKeyExists(strOrgIdentity,arguments.orgIdentityID)>
			<cfset StructDelete(strOrgIdentity[arguments.orgIdentityID], "orgIdentityID")>
			<cfreturn strOrgIdentity[arguments.orgIdentityID]>
		<cfelse>
			<cfreturn structNew()>
		</cfif>
	</cffunction>

	<cffunction name="removeOrgIdentity" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeOrgIdentity">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.data>	
	</cffunction>

	<cffunction name="getOrgIdentityUsages" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">

		<cfset var qryOrgIdentityUsages = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getOrgIdentityUsage">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
			<cfprocresult name="qryOrgIdentityUsages">
		</cfstoredproc>

		<cfreturn qryOrgIdentityUsages>
	</cffunction>

</cfcomponent>