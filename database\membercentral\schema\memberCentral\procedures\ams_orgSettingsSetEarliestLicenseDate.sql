ALTER PROC dbo.ams_orgSettingsSetEarliestLicenseDate
@orgID int,
@columnID int,
@earlyLicenseTypes varchar(max),
@earlyLicenseStatuses varchar(max),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @runEarliestLicenseDateRule bit = 0;

	BEGIN TRAN;
		delete from dbo.ams_memberProfessionalLicenseEarliestDateTypes
		where orgID = @orgID;

		delete from dbo.ams_memberProfessionalLicenseEarliestDate
		where orgID = @orgID;

		IF @columnID is not null BEGIN
			insert into dbo.ams_memberProfessionalLicenseEarliestDate (orgID, columnID)
			values (@orgID, @columnID);

			IF ISNULL(@earlyLicenseTypes,'') <> '' OR ISNULL(@earlyLicenseStatuses,'') <> '' BEGIN
				-- to bypass empty varchar list
				IF ISNULL(@earlyLicenseTypes,'') = ''
					set @earlyLicenseTypes = '0';

				insert into dbo.ams_memberProfessionalLicenseEarliestDateTypes (orgID, PLTypeID, PLStatusID)
				select distinct @orgID as orgID, nullif(elt.listitem,0) as PLTypeID, nullif(els.listitem,0) as PLStatusID
				from dbo.fn_varcharListToTable(@earlyLicenseTypes,',') as elt
				outer apply dbo.fn_varcharListToTable(@earlyLicenseStatuses,',') as els;
			END

			SET @runEarliestLicenseDateRule = 1;
		END
	COMMIT TRAN;

	IF @runEarliestLicenseDateRule = 1
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=NULL, @recordedByMemberID=@recordedByMemberID, @bypassQueue=0;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
