<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// set links to different functions
			this.link.list 						= buildCurrentLink(arguments.event,"list");
			this.link.editCustomField			= buildCurrentLink(arguments.event,"editCustomField") & "&mode=direct";
			this.link.saveCustomField			= buildCurrentLink(arguments.event,"saveCustomField") & "&mode=direct";

			this.link.addCustomFieldValueMulti	= buildCurrentLink(arguments.event,"addCustomFieldValueMulti") & "&mode=stream";
			this.link.insertCustomFieldValueMulti	= buildCurrentLink(arguments.event,"insertCustomFieldValueMulti") & "&mode=stream";
			this.link.addCustomFieldValue		= buildCurrentLink(arguments.event,"addCustomFieldValue") & "&mode=stream";
			this.link.insertCustomFieldValue	= buildCurrentLink(arguments.event,"insertCustomFieldValue") & "&mode=stream";
			this.link.editCustomFieldValue		= buildCurrentLink(arguments.event,"editCustomFieldValue") & "&mode=direct";
			this.link.updateCustomFieldValue	= buildCurrentLink(arguments.event,"updateCustomFieldValue") & "&mode=stream";

			this.link.exportStructureZIP 	= buildCurrentLink(arguments.event,"exportStructureZIP") & "&mode=stream";
			this.link.doImportCustomFields	= buildCurrentLink(arguments.event,"doImportCustomFields") & "&mode=stream";
			this.link.message 				= buildCurrentLink(arguments.event,"message");

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct" hint="List Page">
		<cfargument name="Event" type="any">
		<cfargument name="impExData" type="string" required="false">
		<cfscript>
			var local = structNew();
			// Security --------------------------------------------------------------------------------- ::
			local.security.canView = checkRights(arguments.event,'ManageCustomFields');

			if (local.security.canView is not 1)
				application.objCommon.redirect('#this.link.message#&message=1');			

			// Setup Default Form Params ---------------------------------------------------------------- ::
			arguments.event.paramValue('cID',0);
			arguments.event.paramValue('cfvID',0);

			// BUILD URL STRING ------------------------------------------------------------------------- ::
			local.customFieldList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=customJSON&meth=getCustomFieldLists&mode=stream";
			local.qryDisplayTypes = getDataColumnDisplayTypes();
		</cfscript>

		<cfif arguments.event.getValue('tab','') eq 'ex'>
			<cfif arguments.event.getValue('importFileName','') neq ''>
				<cfset local.prepResult = prepareCustomFieldsImport(event=arguments.event)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_customFields.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>		

	<cffunction name="editCustomField" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objCustomField = CreateObject("component","customField");
			local.rc = arguments.event.getCollection();

			// Security --------------------------------------------------------------------------------- ::
			local.security.canView = checkRights(arguments.event,'ManageCustomFields');

			if (local.security.canView is not 1)
				application.objCommon.redirect('#this.link.message#&message=1');			

			local.qryData = local.objCustomField.getColumnData(val(arguments.event.getValue('cID',0)));
			local.columnID = arguments.event.getValue('cID',val(local.qryData.columnID));
			
			local.qryColumnDataTypes = getColumnDataTypes();
			local.qryDisplayTypes = getColumnDisplayTypes();
			local.qryDateCustomFields = local.objCustomField.getDateCustomFields(orgID=arguments.event.getValue('mc_siteinfo.orgid'));
			local.qryAdvanceFormulas = local.objCustomField.getAdvanceFormulas(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
						
			arguments.event.setValue('columnName',local.qryData.columnName);
			arguments.event.setValue('columnDesc',local.qryData.columnDesc);
			arguments.event.setValue('uid',local.qryData.uid);
			arguments.event.setValue('allowMultiple',val(local.qryData.allowMultiple));
			if (local.columnID gt 0) {
				arguments.event.setValue('allowNull',val(local.qryData.allowNull));
				arguments.event.setValue('allowNewValuesOnImport',val(local.qryData.allowNewValuesOnImport));
			} else {
				arguments.event.setValue('allowNull',1);
				arguments.event.setValue('allowNewValuesOnImport',1);
			}
			arguments.event.setValue('defaultValue',local.qryData.defaultValue);
			arguments.event.setValue('isReadOnly',val(local.qryData.isReadOnly));
			arguments.event.setValue('minChars',local.qryData.minChars);
			arguments.event.setValue('maxChars',local.qryData.maxChars);
			arguments.event.setValue('minSelected',local.qryData.minSelected);
			arguments.event.setValue('maxSelected',local.qryData.maxSelected);
			arguments.event.setValue('minValueInt',local.qryData.minValueInt);
			arguments.event.setValue('maxValueInt',local.qryData.maxValueInt);
			arguments.event.setValue('minValueDecimal2',local.qryData.minValueDecimal2);
			arguments.event.setValue('maxValueDecimal2',local.qryData.maxValueDecimal2);
			if (len(local.qryData.minValueDate))
				arguments.event.setValue('minValueDate',dateformat(local.qryData.minValueDate,'m/d/yyyy'));
			else
				arguments.event.setValue('minValueDate',local.qryData.minValueDate);
			if (len(local.qryData.maxValueDate))
				arguments.event.setValue('maxValueDate',dateformat(local.qryData.maxValueDate,'m/d/yyyy'));
			else
				arguments.event.setValue('maxValueDate',local.qryData.maxValueDate);
		</cfscript>
		<cfif local.columnID GT 0>
			<cfset local.displayTypeCode = local.qryData.displayTypeCode>
			<cfset local.customFieldValueList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=customJSON&meth=getCustomFieldValueLists&columnID=#local.columnID#&mode=stream">
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editCustomField.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCustomField" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.columnID = int(val(arguments.event.getValue('columnID',0)));

		// Security --------------------------------------------------------------------------------- ::
		local.security.canView = checkRights(arguments.event,'ManageCustomFields');

		if (local.security.canView is not 1)
			application.objCommon.redirect('#this.link.message#&message=1&mode=direct');			
		
		local.objCustomField = CreateObject("component","customField");
		
		// rules: only A-Z, 0-9, some special characters
		local.cleanColumnName = ReReplaceNoCase(arguments.event.getValue('columnName',''),'[^A-Z0-9 \&\_\:\-\/]','','ALL');

		local.strArgs = { 
			siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			columnName=local.cleanColumnName,
			columnDesc=arguments.event.getTrimValue('columnDesc',''),
			allowMultiple=arguments.event.getValue('allowMultiple',0),
			allowNull=arguments.event.getValue('allowNull',0),
			defaultValue=arguments.event.getTrimValue('defaultValue',''),
			allowNewValuesOnImport=arguments.event.getValue('allowNewValuesOnImport',0),
			dataTypeCode=arguments.event.getValue('dataType'),
			displayTypeCode=arguments.event.getValue('displayType'),
			isReadOnly=arguments.event.getValue('isReadOnly',0) };
		if (local.columnID AND application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('mduid','')))
			local.strArgs.uid = arguments.event.getTrimValue('mduid');
		if (val(arguments.event.getValue('minChars',0)) gt 0)
			local.strArgs.minChars = val(arguments.event.getValue('minChars'));
		if (val(arguments.event.getValue('maxChars',0)) gt 0)
			local.strArgs.maxChars = val(arguments.event.getValue('maxChars'));
		if (val(arguments.event.getValue('minSelected',0)) gt 0)
			local.strArgs.minSelected = val(arguments.event.getValue('minSelected'));
		if (val(arguments.event.getValue('maxSelected',0)) gt 0)
			local.strArgs.maxSelected = val(arguments.event.getValue('maxSelected'));
		if (len(arguments.event.getValue('minValueInt')))
			local.strArgs.minValueInt = val(arguments.event.getValue('minValueInt'));
		if (len(arguments.event.getValue('maxValueInt',0)))
			local.strArgs.maxValueInt = val(arguments.event.getValue('maxValueInt'));
		if (len(arguments.event.getValue('minValueDecimal2')))
			local.strArgs.minValueDecimal2 = val(arguments.event.getValue('minValueDecimal2'));
		if (len(arguments.event.getValue('maxValueDecimal2')))
			local.strArgs.maxValueDecimal2 = val(arguments.event.getValue('maxValueDecimal2'));
		if (len(arguments.event.getValue('minValueDate','')))
			local.strArgs.minValueDate = arguments.event.getValue('minValueDate');
		if (len(arguments.event.getValue('maxValueDate','')))
			local.strArgs.maxValueDate = arguments.event.getValue('maxValueDate');

		if (arguments.event.getValue('displayType') EQ 'TEXTBOX' AND arguments.event.getValue('dataType') EQ 'INTEGER' AND val(arguments.event.getValue('linkedDateColumnID',0))
				AND len(arguments.event.getTrimValue('linkedDateCompareDate','')) AND val(arguments.event.getValue('linkedDateCompareDateAFID',0))
				AND len(arguments.event.getTrimValue('linkedDateAdvanceDate','')) AND val(arguments.event.getValue('linkedDateAdvanceAFID',0))) {
			structAppend(local.strArgs, { "linkedDateColumnID"=arguments.event.getValue('linkedDateColumnID'), "linkedDateCompareDate"=arguments.event.getValue('linkedDateCompareDate'), 
				"linkedDateCompareDateAFID"=arguments.event.getValue('linkedDateCompareDateAFID'), "linkedDateAdvanceDate"=arguments.event.getValue('linkedDateAdvanceDate'), 
				"linkedDateAdvanceAFID"=arguments.event.getValue('linkedDateAdvanceAFID')});
		} else {
			structAppend(local.strArgs, { "linkedDateColumnID"=0, "linkedDateCompareDate"="", "linkedDateCompareDateAFID"=0, "linkedDateAdvanceDate"="", "linkedDateAdvanceAFID"=0});
		}

		if (local.columnID) {
			local.strArgs.columnID = local.columnID;
			local.strUpdate = local.objCustomField.updateColumn(argumentcollection=local.strArgs);
			local.result = { "success": len(local.strUpdate.errMsg) ? false : true, "errmsg": local.strUpdate.errMsg };
		} else {
			local.newColumnID = local.objCustomField.insertColumn(argumentcollection=local.strArgs);
			local.result = { "success": local.newColumnID GT 0 ? true : false, "errmsg": local.newColumnID EQ 0 ? 'We were unable to add this custom field. Try again.' : '' };
		}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					<cfif local.result.success>
						top.reloadcustomFieldsTable();
						<cfif local.columnID>
							top.MCModalUtils.hideModal();
						<cfelse>
							top.MCModalUtils.setTitle('Edit Custom Field');
							self.location.href="#this.link.editCustomField#&cID=#local.newColumnID#";
						</cfif>
					<cfelse>
						alert('#JSStringFormat(local.result.errMsg)#');
						top.MCModalUtils.hideModal();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addCustomFieldValueMulti" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Security --------------------------------------------------------------------------------- ::
			local.security.canView = checkRights(arguments.event,'ManageCustomFields');

			if (local.security.canView is not 1)
				application.objCommon.redirect('#this.link.message#&message=1&mode=direct');	

			local.qryColumnData = CreateObject("component","model.admin.customFields.customField").getColumnData(arguments.event.getValue('cID'));

			local.valueID = 0;
			local.formSubmit = this.link.insertCustomFieldValueMulti;
			local.columnValue = '';
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_editCustomFieldValueMulti.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addCustomFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			// Security --------------------------------------------------------------------------------- ::
			local.security.canView = checkRights(arguments.event,'ManageCustomFields');

			if (local.security.canView is not 1)
				application.objCommon.redirect('#this.link.message#&message=1&mode=direct');	

			local.qryColumnData = CreateObject("component","model.admin.customFields.customField").getColumnData(arguments.event.getValue('cID'));

			local.valueID = 0;
			local.formSubmit = this.link.insertCustomFieldValue;
			local.columnValue = '';
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_editCustomFieldValue.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="editCustomFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Security --------------------------------------------------------------------------------- ::
			local.security.canView = checkRights(arguments.event,'ManageCustomFields');

			if (local.security.canView is not 1)
				application.objCommon.redirect('#this.link.message#&message=1&mode=direct');				

			local.objCustomField = CreateObject("component","model.admin.customFields.customField");
			local.qryColumnData = local.objCustomField.getColumnData(arguments.event.getValue('cID'));
			local.qryValueData = local.objCustomField.getFieldValueData(arguments.event.getValue('cfvID'));

			local.columnID = arguments.event.getValue('cID');
			local.valueID = local.qryValueData.valueID;
			local.columnValue = local.qryValueData.columnValue;
			local.formSubmit = this.link.updateCustomFieldValue;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Edit Custom Field Value</h4>
				<cfinclude template="frm_editCustomFieldValue.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="insertCustomFieldValueMulti" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- Security --------------------------------------------------------------------------------- --->
		<cfset local.security.canView = checkRights(arguments.event,'ManageCustomFields')>
		<cfif (local.security.canView is not 1)>
			<cflocation url="#this.link.message#&message=1&mode=direct"  addtoken="false">
		</cfif>

		<cfset local.crlf = "#Chr(13)##Chr(10)#">
		<cfset local.objCF = CreateObject("component","model.admin.customFields.customField")>
		
		<cfset local.arrValues = listToArray(arguments.event.getTrimValue('columnValueList',''),local.crlf)>

		<cfloop array="#local.arrValues#" index="local.thisValue">
			<cfset local.objCF.insertColumnValue(columnID=arguments.event.getValue('columnID'),columnValue=trim(local.thisValue))>
		</cfloop>
		
		<cfreturn returnAppStruct("Successfully Saved.","echo")>	
	</cffunction>
	
	<cffunction name="insertCustomFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfscript>
		// Security --------------------------------------------------------------------------------- ::
		local.security.canView = checkRights(arguments.event,'ManageCustomFields');

		if (local.security.canView is not 1)
			application.objCommon.redirect('#this.link.message#&message=1&mode=direct');	

		local.newColumnID = CreateObject("component","model.admin.customFields.customField").insertColumnValue(
			columnID=arguments.event.getValue('columnID'),
			columnValue=arguments.event.getTrimValue('columnValue')
			);
		</cfscript>
		
		<cfreturn returnAppStruct("Successfully Saved.","echo")>	
	</cffunction>

	<cffunction name="updateCustomFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfscript>
		// Security --------------------------------------------------------------------------------- ::
		local.security.canView = checkRights(arguments.event,'ManageCustomFields');

		if (local.security.canView is not 1)
			application.objCommon.redirect('#this.link.message#&message=1&mode=direct');
						
		CreateObject("component","model.admin.customFields.customField").updateColumnValue(
			valueID=arguments.event.getValue('valueID'),
			columnValue=arguments.event.getTrimValue('columnValue')
			);
		</cfscript>
		
		
		
		<cfreturn returnAppStruct("Successfully Saved.","echo")>	
	</cffunction>

	<cffunction name="getColumnDataTypes" access="public" output="false" returntype="query">
		<cfset var qryData = "">		
		<cfquery name="qryData" datasource="#application.dsn.memberCentral.dsn#">
			SELECT dataTypeID, dataType, dataTypeCode
			FROM dbo.ams_memberDataColumnDataTypes	
			WHERE dataTypeCode <> 'IMAGEOBJ'		
			ORDER BY displayOrder
		</cfquery>
		<cfreturn qryData>		
	</cffunction>
	
	<cffunction name="getColumnDisplayTypes" access="public" output="false" returntype="query">
		<cfset var qryData = "">		
		<cfquery name="qryData" datasource="#application.dsn.memberCentral.dsn#">
			SELECT datty.dataTypeCode, 
				(select '''' + disty.displayTypeCode+'|'+disty.displayType + ''','
				FROM dbo.ams_memberDataColumnDisplayTypes as disty
				INNER JOIN dbo.ams_memberDataColumnDataTypeDisplayType as dtdt on dtdt.displayTypeID = disty.displayTypeID
				AND dtdt.dataTypeID = datty.dataTypeID
				ORDER BY disty.displayOrder
				FOR XML PATH('')) as displayTypeCodes
			FROM dbo.ams_memberDataColumnDataTypes as datty
			WHERE datty.dataTypeCode <> 'IMAGEOBJ'
			ORDER BY datty.dataTypeCode
		</cfquery>
		<cfreturn qryData>		
	</cffunction>

	<cffunction name="getDataColumnDisplayTypes" access="public" output="false" returntype="query">
		<cfset var qryData = "">		

		<cfquery name="qryData" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select displayTypeID, displayTypeCode 
			from dbo.ams_memberDataColumnDisplayTypes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryData>		
	</cffunction>

	<!--- export/import --->
	<cffunction name="exportStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "CustomFields.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_exportMemberDataColumnStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="prepareCustomFieldsImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">
	
		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/CustomFields.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/CustomFields.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/CustomFields.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_ams_memberDataColumns.bcp','sync_ams_memberDataColumnValues.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 2>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain two.">
				<cfelseif local.qryFilesCheck.theCount neq 2>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/CustomFields.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/CustomFields.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>
  		<!--- prepare import --->
  		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Custom Fields Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			<cfset local.CustomFieldImportStruct = application.mcCacheManager.sessionGetValue(keyname='CustomFieldImportStruct', defaultValue={})>
			<cfset local.CustomFieldImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue(keyname='CustomFieldImportStruct', value=local.CustomFieldImportStruct)>
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isCustomFieldImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.ams_prepareMemberDataColumnImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showCustomFieldsImportCompareResults(orgID=arguments.paramStruct.orgID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/CustomFieldImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="doImportCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="500">

		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.CustomFieldImportStruct = application.mcCacheManager.sessionGetValue(keyname='CustomFieldImportStruct', defaultValue={})>
		
		<cfif NOT isStruct(local.CustomFieldImportStruct) OR NOT structKeyExists(local.CustomFieldImportStruct,local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Custom Fields. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="ams_importMemberDataColumns" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Custom Fields file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from session --->
			<cfset StructDelete(local.CustomFieldImportStruct, local.threadID)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='CustomFieldImportStruct', value=local.CustomFieldImportStruct)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showCustomFieldsImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>

		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewFields = XMLSearch(arguments.strResult.importResultXML,"/import/newfields/field")>
			<cfset local.strImportResult.arrUpdateFields = XMLSearch(arguments.strResult.importResultXML,"/import/updatefields/field")>
			<cfset local.strImportResult.arrRemoveFields = XMLSearch(arguments.strResult.importResultXML,"/import/removefields/field")>
			<cfset local.strImportResult.arrRemoveValues = XMLSearch(arguments.strResult.importResultXML,"/import/removevalues/value")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importReport = generateCustomFieldsImportResultsReport(orgID=arguments.orgID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateCustomFieldImportErrorReport(orgID=arguments.orgID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Custom Fields Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelCustomFieldsImport(orgID=arguments.orgID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Custom Fields Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
				</cfif>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Custom Fields Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateCustomFieldsImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateFields)>
			<cfquery name="local.qryImportFileUpdateFields" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				SELECT DISTINCT smdc.[uid], smdc.columnID, smdc.columnName, smdc.columnDesc, smdc.dataTypeCode, smdc.displayTypeCode, mdata.dataType, mdisp.displayType, 
					smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, 
					smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, smdc.minValueDate, smdc.maxValueDate, smdc.defaultValue,
					mdc.columnName AS linkedDateColumnName, smdc.linkedDateCompareDate, af_c.afName AS linkedDateCompareDateAFName,
					smdc.linkedDateAdvanceDate, af_adv.afName AS linkedDateAdvanceDateAFName
				FROM dbo.sync_ams_memberDataColumns as smdc
				INNER JOIN membercentral.dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.dataTypeUID
				INNER JOIN membercentral.dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.[uid] = smdc.displayTypeUID
				LEFT OUTER JOIN membercentral.dbo.ams_memberDataColumns AS mdc ON mdc.orgID = @orgID
					AND mdc.[uid] = smdc.linkedDateColumnUID
				LEFT OUTER JOIN membercentral.dbo.af_advanceFormulas AS af_c ON af_c.[uid] = smdc.linkedDateCompareDateAFUID
				LEFT OUTER JOIN membercentral.dbo.af_advanceFormulas AS af_adv ON af_adv.[uid] = smdc.linkedDateAdvanceAFUID
				WHERE smdc.orgID = @orgID
				AND smdc.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateFields" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select f.columnID, f.uid, f.columnName, f.columnDesc, mdata.dataTypeCode, mdisp.displayTypeCode, mdata.dataType, mdisp.displayType, f.allowNewValuesOnImport, 
					f.allowNull, f.isReadOnly, f.allowMultiple, f.minChars, f.maxChars, f.minSelected, f.maxSelected, f.minValueInt, f.maxValueInt, f.minValueDecimal2, f.maxValueDecimal2, 
					f.minValueDate, f.maxValueDate, f.defaultValueID, 
					case when f.defaultValueID is not null then
						case mdata.dataTypeCode
							when 'STRING' then mdcv.columnValueString
							when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
							when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
							when 'DATE' then convert(varchar(10), mdcv.columnValueDate, 101)
							when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
							else null
							end
					else null 
					end as defaultValue,
					ldc.columnName AS linkedDateColumnName, f.linkedDateCompareDate, af_c.afName AS linkedDateCompareDateAFName,
					f.linkedDateAdvanceDate, af_adv.afName AS linkedDateAdvanceDateAFName
				from dbo.ams_memberDataColumns as f
				inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = f.dataTypeID
				inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
				left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = f.defaultValueID and mdcv.columnID = f.columnID
				left outer join dbo.ams_memberDataColumns as ldc on ldc.orgID = @orgID and ldc.columnID = f.linkedDateColumnID
				left outer join dbo.af_advanceFormulas as af_c on af_c.afID = f.linkedDateCompareDateAFID
				left outer join dbo.af_advanceFormulas as af_adv on af_adv.afID = f.linkedDateAdvanceAFID
				where f.orgID = @orgID
				and f.uid in (#listQualify(valueList(local.qryImportFileUpdateFields.uid), "'")#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateFieldValues" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select fv.valueID, fv.columnID, fv.columnValueString, fv.columnValueDecimal2 , fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit
				from dbo.ams_memberDataColumns as f
				inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
				inner join dbo.ams_memberDataColumnValues as fv on fv.columnID = f.columnID and mdisp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
				where f.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and f.columnID in (0#valueList(local.qryOrgUpdateFields.columnID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryAddNewFieldValuesForUpdateField" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				SELECT smdc.[uid], smdc.columnName, smdc.dataTypeCode, smdc.fieldValue
				FROM dbo.sync_ams_memberDataColumnValues as smdcv
				INNER JOIN dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
				WHERE smdcv.orgID = @orgID
				AND smdc.finalAction = 'C'
				AND smdcv.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateCustomFieldImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cancelCustomFieldsImport" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_ams_memberDataColumnValues WHERE orgID = @orgID;
					DELETE FROM dbo.sync_ams_memberDataColumns WHERE orgID = @orgID;					
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		
		<cftry>
			<cfset local.CustomFieldImportStruct = application.mcCacheManager.sessionGetValue(keyname='CustomFieldImportStruct', defaultValue={})>
			<cfif isStruct(local.CustomFieldImportStruct) and structKeyExists(local.CustomFieldImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.CustomFieldImportStruct[arguments.reportuid].folderPath & "/CustomFieldImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<!--- ajax Functions --->
	<cffunction name="removeColumn" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="columnID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasManageCustomFieldsRight(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberDataColumn">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.columnID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeFieldValue" access="public" output="false" returntype="struct" hint="Remove field value.">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasManageCustomFieldsRight(siteID=arguments.mcproxy_siteID)>
				<cfthrow message="invalid request">
			</cfif>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_removeMemberDataColumnValue">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfif findNoCase("data validation range",cfcatch.detail)>
				<cfset local.data.err = replaceNoCase(cfcatch.detail, "*** [ams_removeMemberDataColumnValue], 137. Errno 50000: ", "We were unable to remove this field value. ", "ALL")>
			<cfelse>
				<cfset local.data.err = "We were unable to remove this field value.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasManageCustomFieldsRight" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='CustomFieldAdmin', siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn local.tmpRights.ManageCustomFields is not 1 ? false : true>
	</cffunction>

	<cffunction name="precheckNameAndValidations" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="columnName" type="string" required="true">
		<cfargument name="columnID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryPreCheckColumn" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int, @columnID int, @columnName varchar(128), @oldColumnName varchar(128), @errCode tinyint, @trash bit,
					@checkColumnID int, @checkMemberID int;
				set @orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
				set @columnID = <cfqueryparam value="#arguments.columnID#" cfsqltype="CF_SQL_INTEGER">;
				set @columnName = <cfqueryparam value="#arguments.columnName#" cfsqltype="CF_SQL_VARCHAR">;
			
				<cfif arguments.columnID is 0>
					IF (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1
						set @errCode = 1;
				<cfelse>
					select @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID=@columnID;
					IF (@columnName <> @oldColumnName AND (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1)
						set @errCode = 1;

					IF @errCode is null BEGIN
						<cfif isDefined("arguments.datatype") and isDefined("arguments.minChars") and isDefined("arguments.maxChars")>
							<cfset arguments.minChars = val(arguments.minChars)>
							<cfset arguments.maxChars = val(arguments.maxChars)>
							<cfif arguments.minChars gt 0 and arguments.maxChars gt 0>
								<cfif arguments.datatype eq "STRING">
									IF EXISTS(
										select top 1 valueID
										from dbo.ams_memberdatacolumnValues
										where columnID = @columnID
										and len(columnValueString) > 0
										and len(columnValueString) not between #arguments.minChars# and #arguments.maxChars#
									)
									set @errCode = 2;
								<cfelseif arguments.datatype eq "CONTENTOBJ">
									IF EXISTS(
										select top 1 mdcv.valueID
										from dbo.ams_memberdatacolumnValues as mdcv
										inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
										inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
										inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
										where mdcv.columnID = @columnID
										and len(cv.rawContent) > 0
										and len(cv.rawContent) not between #arguments.minChars# and #arguments.maxChars#
									)
									set @errCode = 2;
								</cfif>
							</cfif>
						<cfelseif isDefined("arguments.minSelected") and isDefined("arguments.maxSelected")>
							<cfset arguments.minSelected = val(arguments.minSelected)>
							<cfset arguments.maxSelected = val(arguments.maxSelected)>
							<cfif arguments.minSelected gt 0 and arguments.maxSelected gt 0>
								SELECT @checkColumnID = columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1;

								SELECT @checkMemberID = md.memberid
								FROM dbo.ams_memberData as md
								INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.columnID = @columnID
								INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID and mdcv.valueID = md.valueID
								GROUP BY md.memberid
								HAVING count(*) not between #arguments.minSelected# and #arguments.maxSelected#;

								IF @checkColumnID is not null AND @checkMemberID is not null
									SET @errCode = 3;
							</cfif>
						<cfelseif isDefined("arguments.minValueInt") and isDefined("arguments.maxValueInt") and len(arguments.minValueInt) and len(arguments.maxValueInt)>
							IF EXISTS(
								select top 1 valueID
								from dbo.ams_memberdatacolumnValues
								where columnID = @columnID
								and columnValueInteger is not null
								and columnValueInteger not between #val(arguments.minValueInt)# and #val(arguments.maxValueInt)#
							)
							set @errCode = 2;
						<cfelseif isDefined("arguments.minValueDecimal2") and isDefined("arguments.maxValueDecimal2") and len(arguments.minValueDecimal2) and len(arguments.maxValueDecimal2)>
							IF EXISTS(
								select top 1 valueID
								from dbo.ams_memberdatacolumnValues
								where columnID = @columnID
								and columnValueDecimal2 is not null
								and columnValueDecimal2 not between #val(arguments.minValueDecimal2)# and #val(arguments.maxValueDecimal2)#
							)
							set @errCode = 2;
						<cfelseif isDefined("arguments.minValueDate") and isDefined("arguments.maxValueDate") and len(arguments.minValueDate) and len(arguments.maxValueDate)>
							IF EXISTS(
								select top 1 valueID
								from dbo.ams_memberdatacolumnValues
								where columnID = @columnID
								and columnValueDate is not null
								and columnValueDate not between '#arguments.minValueDate#' and '#arguments.maxValueDate#'
							)
							set @errCode = 2;
						</cfif>

						set @trash = 1;
					END
				</cfif>

				select isnull(@errCode,0) as errCode;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.data.errCode = local.qryPreCheckColumn.errCode>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.errCode = 4>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
			
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>That custom field was not found.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

</cfcomponent>