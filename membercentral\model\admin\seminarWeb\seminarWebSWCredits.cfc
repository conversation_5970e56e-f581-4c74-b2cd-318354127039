<cfcomponent output="false">

	<cffunction name="addSeminarCredit" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="CSALinkID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
			select s.lockSettings, p.orgcode as publisherOrgCode
			from dbo.tblSeminars as s
			inner join dbo.tblParticipants as p on p.participantID = s.participantID
			where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			and s.isDeleted = 0
		</cfquery>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryCreditSponsorOrgCode">
			SELECT cs.orgCode
			FROM dbo.tblCreditSponsorsAndAuthorities AS csa
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
			INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
			WHERE csa.CSALinkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.CSALinkID#">
		</cfquery>

		<cfset local.isPublisher = arguments.mcproxy_siteCode EQ local.qrySeminar.publisherOrgCode>

		<cfif NOT local.qrySeminar.lockSettings AND (
				(application.objUser.isSuperUser(cfcuser=session.cfcuser) AND local.isPublisher)
				OR arguments.mcproxy_siteCode EQ local.qryCreditSponsorOrgCode.orgCode
			)>
			<cfstoredproc procedure="sw_addSeminarAndCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.CSALinkID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addSeminarAllPublisherCredit" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="CSALinkID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.creditAdded  = addSeminarCredit(mcproxy_siteCode, seminarID, CSALinkID)>

		<cfif local.creditAdded.success>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryClearCreditDates">
				UPDATE dbo.tblSeminarsAndCredit
				SET creditOfferedStartDate = NULL,
					creditOfferedEndDate = NULL,
					creditCompleteByDate = NULL
				WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				AND CSALinkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.CSALinkID#">
			</cfquery>
			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addSeminarCreditExpress" access="public" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfstoredproc procedure="sw_addSeminarAndCreditExpress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveCreditForSWSeminar" access="public" returntype="struct" output="no">
		<cfargument name="seminarCreditID" type="numeric" required="yes">
		<cfargument name="statusID" type="numeric" required="yes">
		<cfargument name="approval" type="string" required="yes">
		<cfargument name="isCreditRequired" type="boolean" required="no" default="0">
		<cfargument name="isIDRequired" type="boolean" required="no" default="0">
		<cfargument name="isCreditDefaulted" type="boolean" required="no" default="0">
		<cfargument name="creditOfferedStartDate" type="string" required="yes">
		<cfargument name="creditOfferedEndDate" type="string" required="yes">
		<cfargument name="creditCompleteByDate" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = { "success":true, "errmsg":"" };

			local.creditsAvail = ArrayNew(1);
			for (local.key in arguments) {
				// only save credit if value is gt 0
				if (findNoCase("xcredit_",local.key) AND len(arguments[local.key])) {
					if (isValid("numeric",arguments[local.key])) {
						local.newEl = Arraylen(local.creditsAvail) + 1;
						local.creditsAvail[local.newEl] = StructNew();
						local.creditsAvail[local.newEl]["fieldname"] = getToken(local.key,2,"_");
						local.creditsAvail[local.newEl]["value"] = arguments[local.key];
					} else {
						local.data.success = false;
						local.data.errmsg = "Enter valid numeric values for Credits offered.";
						break;
					}
				}
			}

			if (NOT local.data.success AND len(local.data.errmsg))
				return local.data;
		</cfscript>

		<cfwddx action="CFML2WDDX" input="#local.creditsAvail#" output="local.wddxCredits">
		<cfset local.creditsAvailable = ToString(local.wddxCredits)>

		<cftry>
			<cfstoredproc procedure="sw_updateSeminarCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarCreditID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statusID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.approval#">
				<cfif len(arguments.creditOfferedStartDate) and isDate(arguments.creditOfferedStartDate)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.creditOfferedStartDate#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>
				<cfif len(arguments.creditOfferedEndDate) and isDate(arguments.creditOfferedEndDate)>
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.creditOfferedEndDate# 23:59:59">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="Yes">
				</cfif>
				<cfif len(arguments.creditCompleteByDate) and isDate(arguments.creditCompleteByDate)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.creditCompleteByDate# 23:59:59">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.creditsAvailable#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isCreditRequired#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isIDRequired#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isCreditDefaulted#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteSeminarCreditForm" access="public" output="false" returntype="struct" hint="Delete video">
		<cfargument name="seminarCreditID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfquery name="local.qryRemoveSeminarCreditForm" datasource="#application.dsn.platformQueue.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @seminarCreditID int, @objectkey VARCHAR(50), @s3DeleteReadyStatusID int, @orgID int, @siteID int, @msgjson varchar(max),
						@nowdate datetime = getdate(), @recordedByMemberID int, @hasForm1 bit;

					SET @seminarCreditID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarCreditID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SELECT @hasForm1 = hasForm1	FROM  seminarWeb.dbo.tblSeminarsAndCredit WHERE seminarCreditID = @seminarCreditID;

					IF (@hasForm1 = 1)
					BEGIN
						EXEC dbo.queue_getStatusIDbyType @queueType='s3Delete', @queueStatus='readyToProcess', @queueStatusID=@s3DeleteReadyStatusID OUTPUT;

						SET @objectkey = 'swodprograms/form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf';

						BEGIN TRAN;
							INSERT INTO dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
							VALUES (@s3DeleteReadyStatusID, 'seminarweb', @objectkey, @nowdate, @nowdate);

							UPDATE seminarWeb.dbo.tblSeminarsAndCredit
							SET hasForm1 = 0
							WHERE seminarCreditID = @seminarCreditID;
						COMMIT TRAN;

						SELECT @orgID = mcs.orgID, @siteID = mcs.siteID,
							@msgjson = 'Form1_'+ cast(@seminarCreditID as varchar(10)) +'.pdf removed from Credit Authority [' + ca.code + ' - ' + ca.authorityName + ' (' + cs.sponsorName + ')] associated to '
								+ CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END + + '-' + cast(sac.seminarID as varchar(10))
						FROM seminarWeb.dbo.tblSeminarsAndCredit AS sac
						INNER JOIN seminarWeb.dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
						INNER JOIN seminarWeb.dbo.tblParticipants AS p ON p.participantID = s.participantID
						INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
						INNER JOIN seminarWeb.dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID
						INNER JOIN seminarWeb.dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID
						INNER JOIN seminarWeb.dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
						LEFT OUTER JOIN seminarWeb.dbo.tblSeminarsSWOD AS swod ON swod.seminarID = sac.seminarID
						LEFT OUTER JOIN seminarWeb.dbo.tblSeminarsSWLive AS swl ON swl.seminarID = sac.seminarID
						WHERE sac.seminarCreditID = @seminarCreditID;
						
						INSERT INTO dbo.queue_mongo (msgjson)
						VALUES('{ "c":"auditLog", "d": {
							"AUDITCODE":"SW",
							"ORGID":' + CAST(@orgID AS varchar(10)) + ',
							"SITEID":' + CAST(@siteID AS varchar(10)) + ',
							"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
							"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
					END
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.form1FilePath = "#application.paths.SharedTempNoWeb.path#/swForm1/Form1_#arguments.seminarCreditID#.pdf">
			<cfif FileExists(local.form1FilePath)>
				<cffile action = "delete" file = "#local.form1FilePath#">
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeCreditFromSWSeminar" access="public" returntype="struct" output="no">
		<cfargument name="seminarCreditID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="sw_removeSeminarCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarCreditID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCreditAuthoritiesForGrid" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"authorityName #arguments.event.getValue('orderDir')#, code #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"jurisdiction #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>		

		<cfquery name="local.qryCreditAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpCreditAuthorities') IS NOT NULL
					DROP TABLE ##tmpCreditAuthorities;
				CREATE TABLE ##tmpCreditAuthorities (authorityID INT, jurisdiction VARCHAR(80), code VARCHAR(30), authorityName VARCHAR(200), row INT);

				DECLARE @totalCount INT, @posStart INT, @posStartAndCount INT,@searchValue varchar(300);
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(arguments.event.getTrimValue('searchValue',''))>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('searchValue','')#%">;
				</cfif>
				INSERT INTO ##tmpCreditAuthorities (authorityID, jurisdiction, code, authorityName, row)				
				SELECT authorityID, jurisdiction, code, authorityName, ROW_NUMBER() OVER (ORDER BY #local.orderby#)
				FROM dbo.tblCreditAuthorities
				WHERE 1 = 1
				<cfif len(arguments.event.getTrimValue('searchValue',''))>
					AND (authorityName LIKE @searchValue OR code LIKE @searchValue OR jurisdiction LIKE @searchValue)
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT authorityID, jurisdiction, code, authorityName, @totalCount AS totalCount
				FROM ##tmpCreditAuthorities
				WHERE row > @posStart 
				AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpCreditAuthorities') IS NOT NULL
					DROP TABLE ##tmpCreditAuthorities;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryCreditAuthorities>
	</cffunction>

	<cffunction name="getStates" access="public" returntype="query" output="false">
		<cfset var qryStates = "">
		
		<cfquery name="qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			SELECT code, name
			FROM dbo.states
			ORDER BY orderpref, name;
		</cfquery>
	
		<cfreturn qryStates>
	</cffunction>

	<cffunction name="isLinkedCreditRemoved" access="public" returntype="boolean">
		<cfargument name="creditAuthorityID" type="numeric" required="true">
		<cfargument name="arrCredits" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.result = false>

		<!--- prevent removing credit types if they are linked to seminars --->
		<cfloop from="1" to="#arrayLen(arguments.arrCredits)#" index="local.thisEl">
			<cfif NOT len(arguments.arrCredits[local.thisEl].fieldname) and len(arguments.arrCredits[local.thisEl].oldfieldname)>
				<cfquery name="local.qryCheckType" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SELECT sac.seminarID
					FROM dbo.tblSeminarsAndCredit AS sac
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID
					CROSS APPLY wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS S(authtype)
					WHERE csa.authorityID = <cfqueryparam value="#val(arguments.creditAuthorityID)#" cfsqltype="CF_SQL_INTEGER">
					AND S.authtype.value('(var[@name=''fieldname'']/string)[1]','varchar(300)') = '#arguments.arrCredits[local.thisEl].oldfieldname#'
				</cfquery>
				<cfif local.qryCheckType.recordcount>
					<cfreturn true>
				</cfif>
			</cfif>
		</cfloop>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="updateCreditAuthorityDetails" access="public" returntype="void">
		<cfargument name="creditAuthorityID" type="numeric" required="true">
		<cfargument name="authorityName" type="string" required="true">
		<cfargument name="code" type="string" required="true">
		<cfargument name="jurisdiction" type="string" required="true">
		<cfargument name="contact" type="string" required="true">
		<cfargument name="address" type="string" required="true">
		<cfargument name="city" type="string" required="true">
		<cfargument name="state" type="string" required="true">
		<cfargument name="zip" type="string" required="true">
		<cfargument name="phone" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="website" type="string" required="true">
		<cfargument name="creditIDText" type="string" required="true">
		<cfargument name="wddxCreditTypes" type="string" required="true">
		<cfargument name="arrCredits" type="array" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateAuth">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @authorityID INT, @authorityName varchar(200), @wddxCreditTypes xml, @authorityNameChange bit = 0;
				DECLARE @tblProgramReferences TABLE (itemID INT IDENTITY(1,1), programType VARCHAR(4), programID INT);
				DECLARE @itemID INT, @programType VARCHAR(4), @programID INT;
				SET @authorityID = <cfqueryparam value="#val(arguments.creditAuthorityID)#" cfsqltype="CF_SQL_INTEGER">;
				SET @authorityName = <cfqueryparam value="#trim(arguments.authorityName)#" cfsqltype="CF_SQL_VARCHAR">;
				SET @wddxCreditTypes = <cfqueryparam value="#trim(arguments.wddxCreditTypes)#" cfsqltype="CF_SQL_LONGVARCHAR">;

				IF NOT EXISTS (SELECT 1 FROM dbo.tblCreditAuthorities WHERE authorityID = @authorityID AND authorityName = @authorityName)
					SET @authorityNameChange = 1;

				IF @authorityNameChange = 1 BEGIN
					INSERT INTO @tblProgramReferences (programID)
					SELECT DISTINCT sac.seminarID
					FROM dbo.tblSeminarsAndCredit AS sac 
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID
					INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID
						AND ca.authorityID = @authorityID;
				END
				
				BEGIN TRAN;
					UPDATE dbo.tblCreditAuthorities 
					SET Code = <cfqueryparam value="#trim(arguments.code)#" cfsqltype="CF_SQL_VARCHAR">,
						Jurisdiction = <cfqueryparam value="#trim(arguments.jurisdiction)#" cfsqltype="CF_SQL_VARCHAR">,
						authorityName = @authorityName,
						Contact = <cfqueryparam value="#trim(arguments.contact)#" cfsqltype="CF_SQL_VARCHAR">,
						Address = <cfqueryparam value="#trim(arguments.address)#" cfsqltype="CF_SQL_VARCHAR">,
						City = <cfqueryparam value="#trim(arguments.city)#" cfsqltype="CF_SQL_VARCHAR">,
						State = <cfqueryparam value="#trim(arguments.state)#" cfsqltype="CF_SQL_VARCHAR">,
						ZIP = <cfqueryparam value="#trim(arguments.zip)#" cfsqltype="CF_SQL_VARCHAR">,
						Phone = <cfqueryparam value="#trim(arguments.phone)#" cfsqltype="CF_SQL_VARCHAR">,
						Email = <cfqueryparam value="#trim(arguments.email)#" cfsqltype="CF_SQL_VARCHAR">,
						Website = <cfqueryparam value="#trim(arguments.website)#" cfsqltype="CF_SQL_VARCHAR">,
						wddxCreditTypes = @wddxCreditTypes,
						creditIDText = <cfqueryparam value="#trim(arguments.creditIDText)#" cfsqltype="CF_SQL_VARCHAR">
					WHERE authorityID = @authorityID

					<cfloop from="1" to="#arrayLen(arguments.arrCredits)#" index="local.thisEl">
						<cfif len(arguments.arrCredits[local.thisEl].fieldname) and arguments.arrCredits[local.thisEl].oldFieldName neq arguments.arrCredits[local.thisEl].fieldname>
							UPDATE sac
							SET sac.wddxCreditsAvailable = replace(CAST(wddxCreditsAvailable AS VARCHAR(MAX)),'<string>#arguments.arrCredits[local.thisEl].oldFieldName#</string>','<string>#arguments.arrCredits[local.thisEl].fieldname#</string>')
							FROM dbo.tblSeminarsAndCredit AS sac
							INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID
							WHERE csa.authorityID = @authorityID;

							IF @authorityNameChange = 0 BEGIN
								INSERT INTO @tblProgramReferences (programID)
								SELECT DISTINCT sac.seminarID
								FROM dbo.tblSeminarsAndCredit AS sac
								INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID
								INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID
									AND ca.authorityID = @authorityID
								CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS CRDA(credittype)
								CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
								WHERE CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
								AND CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = <cfqueryparam value="#arguments.arrCredits[local.thisEl].fieldname#" cfsqltype="CF_SQL_VARCHAR">
									EXCEPT
								SELECT programID
								FROM @tblProgramReferences;
							END
						</cfif>
					</cfloop>
				COMMIT TRAN;

				-- outside of tran is ok
				IF EXISTS (SELECT 1 FROM @tblProgramReferences) BEGIN
					UPDATE tmp
					SET tmp.programType = CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END
					FROM @tblProgramReferences AS tmp
					LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = tmp.programID
					LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = tmp.programID;

					SELECT @itemID = MIN(itemID) FROM @tblProgramReferences;
					WHILE @itemID IS NOT NULL BEGIN
						SELECT @programType=programType, @programID=programID FROM @tblProgramReferences WHERE itemID = @itemID;

						IF @programType = 'SWL'
							EXEC dbo.swl_populateSearchText @seminarID = @programID;
						IF @programType = 'SWOD'
							EXEC dbo.swod_populateSearchText @seminarID = @programID;

						SELECT @itemID = MIN(itemID) FROM @tblProgramReferences WHERE itemID > @itemID;
					END
				END
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="insertCreditAuthority" access="public" returntype="numeric">
		<cfargument name="authorityName" type="string" required="true">
		<cfargument name="code" type="string" required="true">
		<cfargument name="jurisdiction" type="string" required="true">
		<cfargument name="contact" type="string" required="true">
		<cfargument name="address" type="string" required="true">
		<cfargument name="city" type="string" required="true">
		<cfargument name="state" type="string" required="true">
		<cfargument name="zip" type="string" required="true">
		<cfargument name="phone" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="website" type="string" required="true">
		<cfargument name="creditIDText" type="string" required="true">
		<cfargument name="wddxCreditTypes" type="string" required="true">

		<cfset var local = structNew()>		

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryAddAuth">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @authorityID INT;
								
				BEGIN TRAN;
					INSERT INTO dbo.tblCreditAuthorities (Code, Jurisdiction, authorityName, Contact, Address, City, State, ZIP, Phone, Email, Website, wddxCreditTypes, creditIDText)
					VALUES (
						<cfqueryparam value="#trim(arguments.code)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.jurisdiction)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.authorityName)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.contact)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.address)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.city)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.state)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.zip)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.phone)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.email)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.website)#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#trim(arguments.wddxCreditTypes)#" cfsqltype="CF_SQL_LONGVARCHAR">,
						<cfqueryparam value="#trim(arguments.creditIDText)#" cfsqltype="CF_SQL_VARCHAR">
					);
					SELECT @authorityID = SCOPE_IDENTITY();
					
					-- insert holder settings
					INSERT INTO dbo.tblCreditAuthoritiesSWLive (authorityID, promptInterval, promptTypeID, mustAttend, mustAttendMinutes)
					VALUES (@authorityID, 0, NULL, 1, 0);

					INSERT INTO dbo.tblCreditAuthoritiesSWOD (authorityID, promptInterval, promptTypeID, examRequired, evaluationRequired, mediaRequiredPct, daysToComplete, mustAttendMinutes)
					VALUES (@authorityID, 0, NULL, 0, 0, 95, 0, 0);

					SELECT @authorityID AS authorityID;
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryAddAuth.authorityID>
	</cffunction>

	<cffunction name="saveCreditAuthoritySWL" access="public" returntype="void">
		<cfargument name="creditAuthorityID" type="numeric" required="true">
		<cfargument name="swlPromptTypeID" type="string" required="true">
		<cfargument name="swlPromptInterval" type="numeric" required="true">
		<cfargument name="swlMustAttend" type="boolean" required="true">
		<cfargument name="swlMustAttendMinutes" type="numeric" required="true">
		<cfargument name="preExamRequired" type="boolean" required="true">
		<cfargument name="examRequired" type="boolean" required="true">
		<cfargument name="evaluationRequired" type="boolean" required="true">
		<cfargument name="daysToCompleteExam" type="numeric" required="true">
		<cfargument name="daysToCompleteEvaluation" type="numeric" required="true">

		<cfset var local = structNew()>		

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySaveAuthSWL">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @authorityID INT;
				SET @authorityID = <cfqueryparam value="#val(arguments.creditAuthorityID)#" cfsqltype="CF_SQL_INTEGER">;

				UPDATE dbo.tblCreditAuthoritiesSWLive 
				SET promptInterval = <cfqueryparam value="#trim(arguments.swlPromptInterval)#" cfsqltype="CF_SQL_INTEGER">,
					promptTypeID = 
						<cfif len(arguments.swlPromptTypeID) is 0>
							<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
						<cfelse>
							<cfqueryparam value="#trim(arguments.swlPromptTypeID)#" cfsqltype="CF_SQL_INTEGER">,
						</cfif>
					mustAttend = <cfqueryparam value="#trim(arguments.swlMustAttend)#" cfsqltype="CF_SQL_BIT">,
					mustAttendMinutes = <cfqueryparam value="#trim(arguments.swlMustAttendMinutes)#" cfsqltype="CF_SQL_INTEGER">,
					preExamRequired = <cfqueryparam value="#trim(arguments.preExamRequired)#" cfsqltype="CF_SQL_BIT">,
					examRequired = <cfqueryparam value="#trim(arguments.examRequired)#" cfsqltype="CF_SQL_BIT">,
					evaluationRequired = <cfqueryparam value="#trim(arguments.evaluationRequired)#" cfsqltype="CF_SQL_BIT">,
					daysToCompleteExam = <cfqueryparam value="#val(trim(arguments.daysToCompleteExam))#" cfsqltype="CF_SQL_INTEGER">,
					daysToCompleteEvaluation = <cfqueryparam value="#val(trim(arguments.daysToCompleteEvaluation))#" cfsqltype="CF_SQL_INTEGER">
				WHERE authorityID = @authorityID;				
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="saveCreditAuthoritySWOD" access="public" returntype="void">
		<cfargument name="creditAuthorityID" type="numeric" required="true">
		<cfargument name="swodPromptTypeID" type="string" required="true">
		<cfargument name="swodPromptInterval" type="numeric" required="true">
		<cfargument name="preExamRequired" type="boolean" required="true">
		<cfargument name="examRequired" type="boolean" required="true">
		<cfargument name="evaluationRequired" type="boolean" required="true">
		<cfargument name="mediaRequiredPct" type="string" required="true">
		<cfargument name="daysToComplete" type="numeric" required="true">
		<cfargument name="mustAttendMinutes" type="numeric" required="true">

		<cfset var local = structNew()>		

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySaveAuthSWOD">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @authorityID INT;
				SET @authorityID = <cfqueryparam value="#val(arguments.creditAuthorityID)#" cfsqltype="CF_SQL_INTEGER">;

				UPDATE dbo.tblCreditAuthoritiesSWOD 
				SET promptInterval = <cfqueryparam value="#val(arguments.swodPromptInterval)#" cfsqltype="CF_SQL_INTEGER">,
					promptTypeID = 
					<cfif len(arguments.swodPromptTypeID) is 0>
						<cfqueryparam null="yes" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						<cfqueryparam value="#trim(arguments.swodPromptTypeID)#" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					preExamRequired = <cfqueryparam value="#trim(arguments.preExamRequired)#" cfsqltype="CF_SQL_BIT">,
					examRequired = <cfqueryparam value="#trim(arguments.examRequired)#" cfsqltype="CF_SQL_BIT">,
					evaluationRequired = <cfqueryparam value="#trim(arguments.evaluationRequired)#" cfsqltype="CF_SQL_BIT">,
					mediaRequiredPct = <cfqueryparam value="#trim(arguments.mediaRequiredPct)#" cfsqltype="CF_SQL_INTEGER">,
					daysToComplete = <cfqueryparam value="#trim(arguments.daysToComplete)#" cfsqltype="CF_SQL_INTEGER">,
					mustAttendMinutes = <cfqueryparam value="#trim(arguments.mustAttendMinutes)#" cfsqltype="CF_SQL_INTEGER">
				WHERE authorityID = @authorityID;
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="saveCreditAuthorityLinkedSponsors" access="public" output="false" returntype="void">
		<cfargument name="arrSponsorInfo" type="array" required="true">

		<cfset var qrySaveData = "">

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="qrySaveData">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @CSALinkID int;

				BEGIN TRAN;
					<cfloop array="#arguments.arrSponsorInfo#" item="local.thisEl">
						SET @CSALinkID = <cfqueryparam value="#local.thisEl.linkID#" cfsqltype="CF_SQL_INTEGER">;

						UPDATE dbo.tblCreditSponsorsAndAuthorities
						SET certificateMessage = <cfqueryparam value="#local.thisEl.certificateMessage#" cfsqltype="CF_SQL_LONGVARCHAR">,
							creditMessage = <cfqueryparam value="#local.thisEl.creditMessage#" cfsqltype="CF_SQL_LONGVARCHAR">,
							swlGrantedCreditCert = NULLIF(<cfqueryparam value="#local.thisEl.swlGrantedCreditCert#" cfsqltype="CF_SQL_VARCHAR">, ''),
							swlDeniedCreditCert = NULLIF(<cfqueryparam value="#local.thisEl.swlDeniedCreditCert#" cfsqltype="CF_SQL_VARCHAR">, ''),
							swodGrantedCreditCert = NULLIF(<cfqueryparam value="#local.thisEl.swodGrantedCreditCert#" cfsqltype="CF_SQL_VARCHAR">, ''),
							swodDeniedCreditCert = NULLIF(<cfqueryparam value="#local.thisEl.swodDeniedCreditCert#" cfsqltype="CF_SQL_VARCHAR">, '')
						WHERE CSALinkID = @CSALinkID;

						DELETE FROM dbo.tblNationalProgramCSALinks
						WHERE CSALinkID = @CSALinkID;

						<cfif listLen(local.thisEl.programIDList)>
							<cfloop list="#local.thisEl.programIDList#" index="local.programID">
								INSERT INTO dbo.tblNationalProgramCSALinks (programID, CSALinkID)
								VALUES(<cfqueryparam value="#local.programID#" cfsqltype="CF_SQL_INTEGER">, @CSALinkID);
							</cfloop>
						</cfif>
					</cfloop>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="addAuthoritySponsorLink" access="public" returntype="struct">
		<cfargument name="creditAuthorityID" type="numeric" required="yes">
		<cfargument name="sponsorID" type="numeric" required="yes">

		<cfset var local = structNew()>		

		<cfstoredproc procedure="sw_addAuthoritySponsorLink" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.creditAuthorityID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
		</cfstoredproc>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getLinkedSponsorParticipants" access="public" output="false" returntype="string">
		<cfset var qryParticipantsLinked = "">
		
		<cfquery name="qryParticipantsLinked" datasource="#application.dsn.tlasites_seminarWeb.dsn#">
			select distinct orgcode
			from dbo.tblCreditSponsors
			where orgcode is not null
		</cfquery>

		<cfreturn valueList(qryParticipantsLinked.orgcode)>
	</cffunction>

	<cffunction name="insertSponsorDetails" access="public" output="false" returntype="numeric">
		<cfargument name="sponsorName" type="string" required="true">
		<cfargument name="linkedOrgCode" type="string" required="true">
		<cfargument name="contact" type="string" required="true">
		<cfargument name="contactTitle" type="string" required="true">
		<cfargument name="address" type="string" required="true">
		<cfargument name="sponsorCity" type="string" required="true">
		<cfargument name="sponsorState" type="string" required="true">
		<cfargument name="sponsorZIP" type="string" required="true">
		<cfargument name="sponsorPhone" type="string" required="true">
		<cfargument name="sponsorContactEmail" type="string" required="true">
		<cfargument name="sponsorWebsite" type="string" required="true">
		<cfargument name="statementAppProvider" type="string" required="true">
		<cfargument name="statementAppProgram" type="string" required="true">
		<cfargument name="statementPendProgram" type="string" required="true">

		<cfset var qryInsertSponsor = "">

		<cfquery name="qryInsertSponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			
			INSERT INTO dbo.tblCreditSponsors (sponsorName, Contact, title, Address, City, State, ZIP, Phone, Email, 
				Website, statementAppProvider, statementAppProgram, statementPendProgram, orgcode)
			VALUES (
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorName#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.contact#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.contactTitle#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.address#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorCity#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorState#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorZIP#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorPhone#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorContactEmail#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorWebsite#">,
				<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementAppProvider#">,
				<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementAppProgram#">,
				<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementPendProgram#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkedOrgCode#">
			);
			
			SELECT SCOPE_IDENTITY() AS sponsorID;
		</cfquery>

		<cfreturn qryInsertSponsor.sponsorID>
	</cffunction>

	<cffunction name="updateSponsorDetails" access="public" output="false" returntype="void">
		<cfargument name="sponsorid" type="numeric" required="true">
		<cfargument name="sponsorName" type="string" required="true">
		<cfargument name="linkedOrgCode" type="string" required="true">
		<cfargument name="contact" type="string" required="true">
		<cfargument name="contactTitle" type="string" required="true">
		<cfargument name="address" type="string" required="true">
		<cfargument name="sponsorCity" type="string" required="true">
		<cfargument name="sponsorState" type="string" required="true">
		<cfargument name="sponsorZIP" type="string" required="true">
		<cfargument name="sponsorPhone" type="string" required="true">
		<cfargument name="sponsorContactEmail" type="string" required="true">
		<cfargument name="sponsorWebsite" type="string" required="true">
		<cfargument name="statementAppProvider" type="string" required="true">
		<cfargument name="statementAppProgram" type="string" required="true">
		<cfargument name="statementPendProgram" type="string" required="true">

		<cfset var qryUpdateSponsor = "">

		<cfquery name="qryUpdateSponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			UPDATE dbo.tblCreditSponsors 
			SET sponsorName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorName#">,
				Contact = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.contact#">, 
				title = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.contactTitle#">, 
				Address = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.address#">, 
				City = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorCity#">, 
				State = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorState#">, 
				ZIP = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorZIP#">, 
				Phone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorPhone#">, 
				Email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorContactEmail#">, 
				Website = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorWebsite#">, 
				statementAppProvider = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementAppProvider#">, 
				statementAppProgram = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementAppProgram#">, 
				statementPendProgram = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.statementPendProgram#">, 
				orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkedOrgCode#">
			WHERE sponsorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">;
		</cfquery>
	</cffunction>

	<cffunction name="saveSponsorSWLSettings" access="public" output="false" returntype="struct">
		<cfargument name="sponsorID" type="numeric" required="yes">
		<cfargument name="sendEvaluations" type="boolean" required="yes">
		<cfargument name="sendCertificates" type="boolean" required="yes">
		<cfargument name="sendEvaluationsEmail" type="string" required="yes">
		<cfargument name="sendCertificatesEmail" type="string" required="yes">

		<cfset var local = structnew()>

		<cfif arguments.sendEvaluations is 0>
			<cfset arguments.sendEvaluationsEmail = "">
		</cfif>
		<cfif arguments.sendCertificates is 0>
			<cfset arguments.sendCertificatesEmail = "">
		</cfif>
		<cftry>
			<cfquery name="local.qryAddSponSWL" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @sponsorID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">;

				IF NOT EXISTS (select swlid from dbo.tblCreditSponsorsSWLive where sponsorID = @sponsorID)
					INSERT INTO dbo.tblCreditSponsorsSWLive (sponsorID, sendCertificates, sendCertificatesEmail, sendEvaluations, sendEvaluationsEmail)
					VALUES (@sponsorID,
						<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.sendCertificates#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.sendCertificatesEmail)#">,
						<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.sendEvaluations#">,
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.sendEvaluationsEmail)#">);
				ELSE 
					UPDATE dbo.tblCreditSponsorsSWLive 
					SET sendCertificates = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.sendCertificates#">,
						sendCertificatesEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.sendCertificatesEmail)#">,
						sendEvaluations = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.sendEvaluations#">,
						sendEvaluationsEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.sendEvaluationsEmail)#">
					WHERE sponsorID = @sponsorID;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
</cfcomponent>