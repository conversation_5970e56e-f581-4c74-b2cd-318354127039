<cfcomponent extends="model.admin.admin" output="no">

	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.viewNotes = buildCurrentLink(arguments.event,"viewNotes");

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getFilterLabelStructure" access="public" output="false" returntype="struct">
		<cfargument name="sinceDate" type="date" required="true">
		<cfargument name="label" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = {}>

		<cfsetting requesttimeout="500">

		<!--- pull the issues --->
		<cfset local.issuesStartDateISO = dateformat(arguments.sinceDate,"yyyy-mm-dd") & "T00:00:00Z">
		<cfset local.apiBaseURL = "https://api.github.com/issues?state=closed&filter=all&direction=asc&since=#local.issuesStartDateISO#" & (len(arguments.label) ? "&labels=RN: " & arguments.label : "")>
		<cfset local.pageNum = 1>
		<cfset local.isDone = 0>
		<cfset local.arrRawIssues = []>
		<cfloop condition="local.isDone is 0">
			<cfhttp url="#local.apiBaseURL#&page=#local.pageNum#" method="get" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
				<cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
			</cfhttp>

			<cfset local.arrRawIssues.append(deserializeJSON(local.apiResult.fileContent),true)>

			<cfif isDefined("local.apiResult.responseheader.Link") and findNoCase('rel="next"',local.apiResult.responseheader.Link)>
				<cfset local.pageNum = local.pageNum + 1>
			<cfelse>
				<cfset local.isDone = 1>
			</cfif>
		</cfloop>

		<!--- Remove issues created by dependabot --->
		<!--- Remove closed PRs that were not merged. If pull_request is not present, it is an Issue and should always be included. --->
		<cfset local.arrRawIssues = local.arrRawIssues.filter(function(el) { 
			if (arguments.el.user.login eq "dependabot[bot]")
				return false;
			else if (NOT structKeyExists(arguments.el,"pull_request"))
				return true; 
			else if (structKeyExists(arguments.el,"pull_request") and structKeyExists(arguments.el.pull_request,"merged_at") and len(arguments.el.pull_request.merged_at))
				return true; 
			else 
				return false;
		})>

		<cfset var labelFilter = arguments.label>
		<cfset local.arrRawIssues = local.arrRawIssues.map(function(el) { 
			// Issues with no labels need to be unclassified
			if (structKeyExists(arguments.el,"labels")) {
				// Issues with a HRN label should be marked as highlighted
				var arrHighlighted = arrayFilter(arguments.el.labels, function(item){ return left(arguments.item.name,4) eq 'HRN:'; });
				arguments.el['mc_ishighlighted'] = arrayLen(local.arrHighlighted) ? 1 : 0;

				// Remove all non RN: labels
				arguments.el['labels'] = arrayFilter(arguments.el.labels, function(item){ return left(arguments.item.name,3) eq 'RN:'; });

				if (!arrayLen(arguments.el.labels))
					arguments.el['labels'] = [ { "name": "Uncategorized" } ];
				else {
					// Strip label names of "RN:"
					arguments.el['labels'] = arguments.el.labels.map(function(item){ 
						arguments.item.name = trim(replace(arguments.item.name,'RN:',''));
						return arguments.item;
					});

					// if we are filtering on label, only include that label
					if (labelFilter neq "")
						arguments.el['labels'] = arrayFilter(arguments.el.labels, function(item){ return arguments.item.name eq labelFilter; });
				}
			} else {
				arguments.el['mc_ishighlighted'] = 0;
				arguments.el['labels'] = [ { "name": "Uncategorized" } ];
			}

			// Clean issue body
			arguments.el['body'] = trim((arguments.el.body?:'').replaceNoCase(arguments.el.title, "").reReplaceNoCase("[\r\n]+-{3,}[\r\n]{1,2}[\w\W]*$",""));

			return arguments.el;
		})>

		<!--- Setup final structs of issues --->
		<cfset var strFinalIssues = structNew("ordered")>
		<cfset strFinalIssues.insert("Uncategorized", [], true)>
		<cfset local.arrFilterLabels = getLabels()>
		<cfset local.arrFilterLabels.each(function(el) {
			strFinalIssues.insert(arguments.el, [], true);
		})>	
		<cfset var strFinalHighlightedIssues = duplicate(strFinalIssues)>

		<!--- put issues into label keys --->
		<cfset local.arrRawIssues.each(function(el) {
			var strIssue = { 
				"title": arguments.el.title,
				"body": arguments.el.body,
				"repo": arguments.el.repository.name,
				"pr": arguments.el.number,
				"closed_at": arguments.el.closed_at
			};
			arguments.el.labels.each(function(thisLabel) {
				strFinalIssues[arguments.thisLabel.name].append(strIssue);
			});
			if (arguments.el.mc_ishighlighted == 1)
				strFinalHighlightedIssues[arguments.el.labels[1].name].append(strIssue);
		})>

		<cfset local.strReturn = {
			finalPRCount=arrayLen(local.arrRawIssues),
			arrFilterLabels=local.arrFilterLabels,
			strFinalHighlightedIssues=strFinalHighlightedIssues,
			strFinalIssues=strFinalIssues
		}>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="viewNotes" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.mostRecentSunday = dateformat(dateAdd("d",-(dayOfWeek(now())-1),now()),"m/d/yyyy")>
		<cfset local.sinceDateFilter = arguments.event.getValue('sinceDate',local.mostRecentSunday)>
		<cfset local.labelFilter = arguments.event.getTrimValue('label','')>

		<cfif arguments.event.getValue('sr',0) is 1>
			<cfset local.strNotes = getFilterLabelStructure(sinceDate=local.sinceDateFilter, label=local.labelFilter)>
		<cfelse>
			<cfset local.strNotes = { finalPRCount=0, strFinalHighlightedIssues={}, arrFilterLabels=getLabels() }>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_viewNotes.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getLabels" access="private" output="false" returntype="array">
		<cfset var local = structNew()>
		<cfset local.pageNum = 1>
		<cfset local.isDone = 0>
		<cfset local.apiBaseURL = "https://api.github.com/repos/TrialSmith/MC/labels">
		<cfset local.arrApiResult = arrayNew(1)>

		<cfloop condition="local.isDone is 0">
			<cfhttp url="#local.apiBaseURL#?page=#local.pageNum#" method="get" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
				<cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
			</cfhttp>
			
			<cfset local.arrApiLabels = deserializeJSON(local.apiResult.fileContent)>
			
			<cfloop array="#local.arrApiLabels#" index="local.thisLabel">
				<cfset arrayAppend(local.arrApiResult,local.thisLabel)>
			</cfloop>

			<cfif isDefined("local.apiResult.responseheader.Link") and findNoCase('rel="next"',local.apiResult.responseheader.Link)>
				<cfset local.pageNum = local.pageNum + 1>
			<cfelse>
				<cfset local.isDone = 1>
			</cfif>
		</cfloop>

		<cfset local.arrLabels = local.arrApiResult.filter(function(item) { 
			return left(arguments.item.name,3) eq 'RN:'; 
		}).map(function(item) {
			return trim(replace(arguments.item.name,'RN:',''));
		}).sort("textnocase","asc")>

		<cfreturn local.arrLabels>
	</cffunction>
</cfcomponent>