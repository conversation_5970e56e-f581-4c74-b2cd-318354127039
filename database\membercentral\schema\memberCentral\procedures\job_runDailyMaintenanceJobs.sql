ALTER PROC dbo.job_runDailyMaintenanceJobs
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tier varchar(12), @jobRunOrder tinyint, @procName varchar(100), @todayDOM tinyint, 
		@todayDOW tinyint, @start datetime, @totalMS int;
	DECLARE @tblJobs TABLE (runorder tinyint IDENTITY(1,1) PRIMARY KEY, procName varchar(100), prodOnly bit, domOnly tinyint, dowOnly tinyint);

	SELECT @tier=tier from dbo.fn_getServerSettings();
	set @todayDOM = datepart(d,getdate());
	set @todayDOW = datepart(dw,getdate());

	/* setup jobs */
	INSERT INTO @tblJobs (procName, prodOnly, domOnly, dowOnly)
	VALUES 

	-- highpriority, needs to run as close to midnight as possible
	('ams_advanceMemberDataCompareDates', 0, null, null),
	('sub_advanceRates', 0, null, null),
	('cp_advanceProgramDates', 0, null, null),
	('ams_advanceRollingDateConditionValues', 0, null, null),
	('rpt_advanceRollingDates', 0, null, null),
	('tasks_advanceRollingDates', 0, null, null),
	('cle_advanceCreditDates', 0, null, null),
	('ams_recalcVirtualGroupsBasedOnDateConditions', 0, null, null),
	('ams_memberAddressData_reprocessConditionsBasedOnDateChanges', 0, null, null),
	('sub_job_statusChangeAcceptedToActive', 0, null, null),
	('sub_job_statusChangeActiveInactiveToExpired', 0, null, null),
	('sub_job_statusChangeOfferedToOfferExpired', 0, null, null),
	('tr_autoCloseSystemBatches', 0, null, null),
	('cp_convertDueInstallmentsToSale', 0, null, null),
	('cp_setContributionAsDelinquent', 0, null, null),
	('cp_setContributionAsCancelled', 0, null, null),
	('cp_setContributionAsCurrent', 0, null, null),
	('cp_setContributionAsFulfilled', 0, null, null),
	('ams_populateTrackGroupMembershipHistoryQueue', 0, null, null),
	('ams_autoRunLinkedDateCustomFieldRule', 0, null, null),

	-- tasks that can run a little later
	('tr_autoMarkClosedInvoicesAsPaid', 0, null, null),
	('sub_closePendingInvoices', 0, null, null),
	('tr_autoCloseEmptyOpenInvoices', 0, null, null),
	('tr_makeInvoicesDelinquent', 0, null, null),
	('tr_advanceInvoiceExpectedPayDate', 0, null, null),
	('tr_autoRecognizeDeferred', 0, null, null), 
	('tr_autoPayInvoices', 1, null, null), 
	('tr_populatecache_tr_ARByDayInvoiceProfileChanges', 0, null, null);

	select @jobRunOrder = min(runorder) 
	from @tblJobs
	where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'))
	and (domOnly is null OR (domOnly = @todayDOM))
	and (dowOnly is null OR (dowOnly = @todayDOW));

	while @jobRunOrder is not null begin
		select @procName = procName from @tblJobs where runOrder = @jobRunOrder;

		BEGIN TRY
			SET XACT_ABORT OFF;	

			SET @start = getdate();
			EXEC @procName;
			SET @totalMS = datediff(ms,@start,getdate());

			INSERT INTO platformStatsMC.dbo.job_runtimeLog (procname, timeMS) 
			VALUES (@procName, @totalMS);	

			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH

		-- delay inside the loop to address apparent parallel issue
		WAITFOR DELAY '00:00:01';

		select @jobRunOrder = min(runorder) 
		from @tblJobs
		where (prodOnly = 0 OR (prodOnly = 1 AND @tier = 'Production'))
		and (domOnly is null OR (domOnly = @todayDOM))
		and (dowOnly is null OR (dowOnly = @todayDOW))
		and runOrder > @jobRunOrder;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
