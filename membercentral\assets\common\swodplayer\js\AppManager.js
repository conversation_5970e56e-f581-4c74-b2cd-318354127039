(function(){
	
	'use strict';
	
	angular
		.module('module_1')
		.factory('AppManager', AppManager);
	
	AppManager.$inject = ['$timeout', 'TimerService','$injector', 'APIResource', 'APIRequestService','SeminarDataService','imagePreloader','$rootScope','$window','PromptTimerService'];
	
	function AppManager($timeout, TimerService, $injector, APIResource, APIRequestService, SeminarDataService,imagePreloader,$rootScope,$window,PromptTimerService) {
		
		var service = {

			orgCode : 'BWC',
			enrollmentID : 0,
			logAccessID : 0,
			seminarDetailsObj : null,
			seminarCompletionCheckObj : null,
			activityLogArray : [],
			debugLogArray : [],
			activityLogIndexSavePoint : 0,
			debugLogIndexSavePoint : 0,
			reportToServerInProgress : false,
			seminarLayout :"",
			isFastForwardPermitted : 0,
			semninarHasPreTest : 0,
			semninarHasPostTest : 0,
			semninarHasEvaluation : 0,
			allEvaluationCompleted : 0,
			allPreTestCompleted : 0,
			allPostTestCompleted : 0,
			isCompleted : 0,
			isEvaluationRequired : 0,
			isQAAsked : 0,
			offerQA : 0,
			blankOnInactivity :0,
			previousTotalTimeSpent  : 0,
			totalTimeSpent : 0,
			totalTimeSpentString : "",
			isSeminarFinalCompleted : 0,
			userMustReportTime : false,
			userReportedTimeSpent : 0,
			enrollmentCreditObj : null,
			supportEmail: "",
			supportPhone: "",
			sponsor      :"",
			email     : "",
			seminarName      :"",
			saveAndExitString : "",
			deadlineDate : "",
			promptUseActivity : false,
			promptMessage : "",
			currentVideoIndex : 0,
			arrVideoSync : [],
			evaluationAllObj : {},
			currentPaperIndex : 0,
			instanceStartedAt : '',
			mediaRequiredPct : 0,
			isMediaRequiredPctCompleted : false,
			isGuidedTourOpened : false,
			materialPairs : [],
			downloadArrays : [],
			linksArray : [],
			currentTab : "Program",
			noteText : "",
			signuporgcode : "",      
			connectedToServer : false,
			testInProgress : false,
			currentBrowser : '',
			isPaused : 0,
			completByPass : 0,
			mobileLoader : 0,    
			currentTime : '',    
			currentTimeInterval : '',    
			currentTimeZoneFromServer : '',    
			reviewMediaOnStart : 0, 
			isFirstLoad: false,
			completeProcessing: 0,
			learnTabInitialised: '',
			mediaListRendered: 0,
			evaluationFormStateStarted: 0,
			evaluationFormStatePage: 0,
			evaluationFormStateSection: 0,
			examCompletionTriggered: 0,
			activeEvaluationFormID: 0,
			init : function(){
		if (navigator.userAgent.indexOf('iPad') != -1) {
			this.currentBrowser = 'ipad';
		}else if(navigator.userAgent.indexOf('iPhone') != -1){
			this.currentBrowser = 'iphone';
		}else if(navigator.userAgent.indexOf('iPhone') != -1){
			this.currentBrowser = 'iphone';
		}else if (navigator.userAgent.indexOf('MSIE') != -1 || navigator.appVersion.indexOf('Trident/') > 0) {
			this.currentBrowser = 'ie';   
		}else if(navigator.userAgent.match(/Android/i) != null){
			this.currentBrowser = 'android';
		}else if(navigator.userAgent.match('Firefox') != null){
			this.currentBrowser = 'firefox';
		}else{
			var isIPad = /Macintosh/.test(navigator.userAgent) && 'ontouchend' in document;
			if(isIPad){
				this.currentBrowser = 'ipad';
				$rootScope.onMobile = 'ipad';
			}
		}


					// Loading fonts.
					WebFont.load({
						custom: {
							families: ['bold','bold-italic','bold-extra','italic','light','light-italic','regular','bold-semi','bold-semi-italic']
						},fontloading: function(familyName, fvd) {},
						active:function(){},
						inactive:function(){}
					});

					// Preload the images; then, update display when returned.
					imagePreloader.preloadImages(imageLocations ).then(
						function handleResolve( imageLocations ) {
							/* console.log('Loading was successful.'); */
								service.loadSeminarDetails();
						},

						function handleReject( imageLocation ) {},
						
						function handleNotify( event ) {
							angular.element('#spinner #percent').html(event.percent+"%");  
						}
					);
			},

			loadSeminarDetails : function(){

				/* #### enrollmentID, seminarID and orgcode comming from querystring ### */
					// only comment for QC release 
					var self = this;
					this.enrollmentID = enrollmentID;
					this.seminarID = seminarID;
					this.orgcode =  orgCode;

					APIResource.enrollmentID  = enrollmentID
					APIResource.seminarID     = seminarID
					APIResource.orgCode       = orgCode

				/* ##################################################################### */

					// Request for getting seminarData here.  
					var promise = APIRequestService.getSeminarforPlayer();
					promise.then(function (response) 
					{

					if(seminarType != 'swl'){
						var userLoggedIn = response.userLoggedIn;
					}else{
						var userLoggedIn = 1;
						meetingUrlSWL = response.returnData.meetingUrl;
					}
					if (userLoggedIn == 1){
						self.connectedToServer = true;
						service.handleSeminarContent(response);
						try{
							PromptTimerService.stop();
							PromptTimerService.start();
						}catch(e){}         
						setTimeout(function(){
							angular.element(window).trigger('resize');
						},100); 
					} else {
							service.disconnectedFromServer();
							angular.element('#activity').scope().preloaded = true;
							angular.element('#preload').addClass('displayHide');
					}

				});
				
			},

			handleSeminarContent : function(response){
	
				if(seminarType != 'swl'){
					var userLoggedIn = response.userLoggedIn;
				}else{
					var userLoggedIn = 1;
				}
				if (userLoggedIn != 1){

						this.disconnectedFromServer();
				} else {
					if(response.returnData.seminarID != undefined){
						this.seminarDetailsObj = response.returnData;
						this.seminarCompletionCheckObj = response.returnData.completionCheckObj.returnData;
						
						SeminarDataService.getMaterialList(this.seminarDetailsObj)

						// enrollmentID, seminarID and orgCode will come from query string.
						this.logAccessID = this.seminarDetailsObj.logAccessID;
						this.seminarLayout = this.seminarDetailsObj.seminarLayout;
						this.isFastForwardPermitted = this.seminarDetailsObj.isFastForwardPermitted;
						this.semninarHasPreTest = this.seminarDetailsObj.seminarLoadPointsObj.semninarHasPreTest;
						this.semninarHasPostTest = this.seminarDetailsObj.seminarLoadPointsObj.semninarHasPostTest;
						this.semninarHasEvaluation = this.seminarDetailsObj.seminarLoadPointsObj.semninarHasEvaluation;
						if(seminarType != 'swl'){
							this.allEvaluationCompleted = (this.seminarDetailsObj.completionCheckObj.returnData.allEvaluationCompleted == 1);
							this.allPreTestCompleted = (this.seminarDetailsObj.completionCheckObj.returnData.allPreTestCompleted == 1);
							this.allPostTestCompleted = (this.seminarDetailsObj.completionCheckObj.returnData.allPostTestCompleted == 1);
						}else{
							this.allEvaluationCompleted = (this.seminarDetailsObj.allEvaluationCompleted == 1);
							this.allPreTestCompleted = (this.seminarDetailsObj.allPreTestCompleted == 1);
							this.allPostTestCompleted = (this.seminarDetailsObj.allPostTestCompleted == 1);
							if(this.seminarDetailsObj.currentLoadPoint == "postTest"){
								this.allPreTestCompleted = true;
							}
							if(this.seminarDetailsObj.currentLoadPoint == "evaluation"){
								this.allPreTestCompleted = true;
								this.allPostTestCompleted = true;
							}
							if(this.seminarDetailsObj.currentLoadPoint == ""){
								this.allPreTestCompleted = true;
								
								if(this.seminarDetailsObj.completionCheckObj.returnData.allPreTestCompleted == 1){
									this.seminarDetailsObj.currentLoadPoint = 'preTest';
								}
								if(this.seminarDetailsObj.completionCheckObj.returnData.allPostTestCompleted == 1){
									this.seminarDetailsObj.currentLoadPoint = 'postTest';
								}
								if(this.seminarDetailsObj.completionCheckObj.returnData.allEvaluationCompleted == 1){
									this.seminarDetailsObj.currentLoadPoint = 'evaluation';
								}
							}
							
						} 
						this.mediaRequiredPct =  this.seminarDetailsObj.completionCheckObj.returnData.mediaRequiredPct;
						this.isSeminarFinalCompleted = this.seminarDetailsObj.isCompleted;
						this.isCompleted = this.seminarDetailsObj.completionCheckObj.returnData.isCompleted;
						this.offerQA = this.seminarDetailsObj.offerQA;
						this.isEvaluationRequired = this.seminarDetailsObj.isEvaluationRequired;
						this.isQAAsked = this.seminarDetailsObj.completionCheckObj.returnData.askedQA;
						this.blankOnInactivity = this.seminarDetailsObj.blankOnInactivity;
						this.dspNotes = 1;

						
						this.supportEmail = this.seminarDetailsObj.supportEmail;
						this.supportPhone = this.seminarDetailsObj.supportPhone;
						this.sponsor      = this.seminarDetailsObj.sponsor;
						this.email      = this.seminarDetailsObj.email;
						this.seminarName      = this.seminarDetailsObj.seminarName;

						this.previousTotalTimeSpent = this.seminarDetailsObj.completionCheckObj.returnData.totalTimeSpent;
						this.totalTimeSpent = this.seminarDetailsObj.completionCheckObj.returnData.totalTimeSpent;
						this.totalTimeSpentString = this.generateTimeString(this.totalTimeSpent);

						if(this.seminarDetailsObj.enrollmentCreditObj.length){
							for (var i = 0; i < this.seminarDetailsObj.enrollmentCreditObj.length; i++) {								
								if(this.seminarDetailsObj.enrollmentCreditObj[i].COMPLETEBYDATEPASSED == 0 || this.isSeminarFinalCompleted){
									this.completByPass = 1;
								}
							}
								
							this.enrollmentCreditObj = this.seminarDetailsObj.enrollmentCreditObj[0];

							this.deadlineDate = this.enrollmentCreditObj.LASTDATETOCOMPLETE;
							
							if (!this.userMustReportTime){
								this.userReportedTimeSpent = -1;
							}
							this.saveAndExitString = "You may save your work, exit, and resume the audio or video presentations later at the exact point you left the program. This program must be completed by " + this.deadlineDate;

						}else{
							this.completByPass = 1;
							this.saveAndExitString = "You may save your work, exit, and resume the audio or video presentations later at the exact point you left the program.";
						}

						this.promptFrequency = this.seminarDetailsObj.promptFrequency;    
						if (this.seminarDetailsObj.promptUseActivity == 1){
							this.promptMessage = "According to the credit selections you chose, we must verify that you are at your computer after every " + this.promptFrequency + " minute(s) of inactivity.  Please press OK to continue this seminar.";
							this.promptUseActivity = true;
						}
						else {
							this.promptMessage = "According to the credit selections you chose, we must verify that you are at your computer every " + this.promptFrequency + " minute(s).  Please press OK to continue this seminar.";       
							this.promptUseActivity = false;
						}

							this.checkLearnCTRLLoad();
							// Call main-controller init() from here.
							var mainScope = angular.element(document.getElementById("activity")).scope();
							mainScope.init();

						if(seminarType != 'swl'){
							var sideBarInitInterval = setInterval(function(){	
								var sideBarScope = angular.element('.sidebar-controller').scope();						
								if(sideBarScope != undefined){
									if($rootScope.alreadyInitialized == 0){
										sideBarScope.init();	
									}
									clearInterval(sideBarInitInterval);
								}
							}, 1000);

							var noteScopeInitInterval = setInterval(function(){	
								var noteScope = angular.element(document.getElementById("note-panel")).scope();							
								if(noteScope != undefined){
									noteScope.init();
									clearInterval(noteScopeInitInterval);
								}
							}, 1000);

						}else{
							var userLoggedIn = 1;
							var sideBarInitInterval = setInterval(function(){	
								var swlCompletecope = angular.element('#swlCompleteWrap .seminarCompleteProcess').scope();						
								if(swlCompletecope != undefined){
									swlCompletecope.setSWLExam();
									clearInterval(sideBarInitInterval);
								}
							}, 1000);
						}
					}else{
						angular.element("#preload #spinner").addClass('displayHide');
						angular.element("#preload #invalidSeminarMsg").removeClass('displayHide');
						setTimeout(() => {
							var hostname = window.location.hostname;
							if(response.returnData.mk != undefined && response.returnData.mk != '' ){
								window.location.href = "//" + hostname + '/?pg=semwebCatalog&panel=My&mk='+response.returnData.mk;
							}else{
								window.location.href = "//" + hostname + '/?pg=semwebCatalog&panel=My';
							}
						}, 3000);						
					}
			
				}
			},
			checkLearnCTRLLoad : function(){
				var self = this;
				var repeatRecursion
					$timeout(function() {
						//console.log('checking element load!!');
						var width = parseInt(angular.element("#learn-controller").width()); 

						if (seminarType == 'swl') {
							 width = 10; 
						}

						if (width) {
							// if width is computed, the element is ready to be manipulated
							// so manipulate the element here
							angular.element('#activity').scope().preloaded = true;
							angular.element('#preload').addClass('displayHide');
							$rootScope.$broadcast('seminarDataLoaded');
							$timeout(function() {
								angular.element(window).trigger('resize');
							});  
							//console.log('element loaded!!');
							// ... and more directive logic ...
						} else {
								// otherwise, the element is not ready, so wait a bit and try again:
								self.checkLearnCTRLLoad();
								 //console.log('element not loaded!!');
						}
					}, 500);
			},
			/* Calling from timer service */
			reportProgresstoServer : function(){
				// reset data    
				
				this.saveProgressFromPlayer();
				 //TimerService.stop();
			},

			saveNotes : function(){
				if(this.dspNotes){
					var noteText = $(".jqte_editor").html();
					noteText = noteText.substring(0, 8000);
					var promise = APIRequestService.saveNote(noteText)
					promise.then(function (data) {
						console.log(data);
						if(data.returndata){
							if(angular.element('#noteSave_feedback').length)
								angular.element('#noteSave_feedback').show();
							angular.element('.saveNoteText').html('Save');
							if(angular.element('#noteSave_feedback').length){
								setTimeout(function(){
									angular.element('#noteSave_feedback').hide('fade');
								},5000);
							}
							
						}
						
					});            
				}
			},
			saveAndDownloadNotes : function(){
				if(this.dspNotes){
					var noteText = $(".jqte_editor").html();
					noteText = noteText.substring(0, 8000);
					var promise = APIRequestService.saveAndDownloadNotes(noteText,service.seminarName)
					promise.then(function (data) {
						if(data.returndata){
							angular.element('.downloadNoteCell').removeClass('displayHide');
							angular.element('.downloadNoteCellWait').addClass('displayHide');
							var downloadLink = data.pdfPath;
							let link = document.createElement('a');
							link.href = downloadLink;
							link.download = service.seminarName+'_notes.pdf';
							link.click();
							link.remove();
						}else{
							angular.element('.downloadNoteCell').removeClass('displayHide');
							angular.element('.downloadNoteCellWait').addClass('displayHide');
						}						
					});            
				}
			},

			saveProgressFromPlayer : function(){

				this.activityLogIndexSavePoint = this.activityLogArray.length;
				this.debugLogIndexSavePoint = this.debugLogArray.length;
				this.addActivityLogEntry("Save Seminar Progress Called",true);
				
				var reportObj = this.buildProgressReport();
				var sendReminderEmail = "No";
				if ( angular.isDefined( $rootScope.sendExitEmail ) ) {
					sendReminderEmail=$rootScope.sendExitEmail;
				}
				var objParams = { json: JSON.stringify({sendReminderEmailFlag:sendReminderEmail, includeLoginDetails:"No", orgcode:this.orgcode, logAccessID:this.logAccessID, progressObj:reportObj})}
		
				var promise = APIRequestService.saveProgressFromPlayer(objParams);
		
				promise.then( function( data ) {
					if( data.userLoggedIn == 1 ) {
						if( data.returnData == 1 ) {
							service.addActivityLogEntry( "Save Seminar Result Received: Success", true );
							service.activityLogArray = [];
							service.debugLogArray =[];
						} else {
							service.addActivityLogEntry( "Save Seminar Result Received: Failed", true );
						}
						if ( angular.isDefined( $rootScope.sendExitEmail ) && $rootScope.sendExitEmail == "Yes" ) {
							var isMobile = navigator.userAgent.match(/(iPad)|(iPhone)|(iPod)|(android)|(webOS)/i);
							if(!isMobile) {
								var url = $(location).attr('protocol') + '//' + $(location).attr('host') + '?pg=semwebCatalog&panel=My';    
								$(location).attr('href',url);
							} else {
								if (/iP(hone|od|ad)/.test(navigator.platform)) {
									// supports iOS 2.0 and later: <http://bit.ly/TJjs1V>
									var v = (navigator.appVersion).match(/OS (\d+)_(\d+)_?(\d+)?/);
									var ver = [parseInt(v[1], 10), parseInt(v[2], 10), parseInt(v[3] || 0, 10)];
									if (ver[0] >= 10) {
										open(location, '_self').close();
										return;
									}
								}
								var url = $(location).attr('protocol') + '//' + $(location).attr('host') + '?pg=semwebCatalog&panel=My';    
								$(location).attr('href',url);
							}
						}
					} else {
						$rootScope.$broadcast( 'serverOffline' );
						service.disconnectedFromServer();
					}
				}, function(reason) {
				$rootScope.$broadcast( 'serverOffline' );
				service.disconnectedFromServer();
				}
			);
			},

			buildProgressReport : function(){
				
				var reportObj = {}
				var additionalDebugEntries = [];
				
				reportObj.activityLogArray = this.activityLogArray;
				reportObj.debugLogArray = this.debugLogArray;
				reportObj.enrollmentID = this.enrollmentID;
				reportObj.fileArray =null;
				reportObj.fileArray =[];
				reportObj.logAccessID = this.logAccessID;
				reportObj.timespent = Math.round(TimerService.timespent);;

				var SeminarDataService = $injector.get('SeminarDataService');
				var materialLen = SeminarDataService.materialPairs.length;
				var materialMessagePrefix;
				var materialMessageSuffix;
				var materialMessage;
				var lasttimecode;
				var timespent;
				for (var i=0; i<materialLen; i++) {
						var materialObj = SeminarDataService.materialPairs[i];
						lasttimecode = (materialObj.lastTimeCode != null && materialObj.lastTimeCode != '' && materialObj.lastTimeCode != undefined)? materialObj.lastTimeCode : 0;
						timespent = (materialObj.timespent != null && materialObj.timespent != '' && materialObj.timespent != undefined)? Math.round(materialObj.timespent) : 0;
						reportObj.fileArray.push({accessDetails: SeminarDataService.localPairs[i].accessDetails.join(""), fileLogAccessID: materialObj.fileLogAccessID, lasttimecode: lasttimecode,timespent:timespent });

					if (materialObj.hasOwnProperty('mediaSrcHashes')) {

						materialMessagePrefix = materialObj.fileTitle + "(" + materialObj.fileURL + ") ";

						for (var thisVideoUrlHash in materialObj.mediaSrcHashes) {
							let data = materialObj.mediaSrcHashes[thisVideoUrlHash];
							materialMessageSuffix = " - SRC: " + data.src;
							materialMessage = "";

							if (data.hasOwnProperty('downloadedSegments')) {
								materialMessage = materialMessage + " Downloaded: " + data.downloadedPercentage + " Downloaded Segments: " + data.downloadedSegments;
							}
							if (data.hasOwnProperty('playedSegments')) {
								materialMessage = materialMessage + " Played: " + data.playedPercentage + " Played Segments: " + data.playedSegments;
							}
							if (materialMessage.length)
								additionalDebugEntries.push({ dateentered: '', message: materialMessagePrefix + materialMessage + materialMessageSuffix });
						};
					}
				}

				if (additionalDebugEntries.length) {
					reportObj.debugLogArray = reportObj.debugLogArray.concat(additionalDebugEntries);
				}

				return reportObj;
			},

		addActivityLogEntry : function(newEntryDescription,countAsActivity){
			var resetPrompt = false;
			if(!countAsActivity){
				resetPrompt = true;
			}
			//countAsActivity = !countAsActivity || true;
			if (this.promptUseActivity && resetPrompt){
				//trace("restarting timer");
				PromptTimerService.stop();
				PromptTimerService.start();
			}
			if(service.currentTime == '' || service.currentTime == undefined){
				APIRequestService.getCurrentTime().then(function(data){
					var newEntry = {dateentered: data , message: newEntryDescription};
					service.activityLogArray.push(newEntry)
				});
				this.getCurrentTime();
			}else{
				var newEntry = {dateentered: this.getCurrentTimeStr() , message: newEntryDescription};
				service.activityLogArray.push(newEntry);
			}
			this.addDebugLogEntry(newEntryDescription);
			
		},
		
		addDebugLogEntry : function(newEntryDescription){
			if(service.currentTime == '' || service.currentTime == undefined){
				APIRequestService.getCurrentTime().then(function(data){
					var newEntry = {dateentered: data , message: newEntryDescription};
					service.debugLogArray.push(newEntry);
				});
				this.getCurrentTime();
			}else{
				var newEntry = {dateentered: this.getCurrentTimeStr() , message: newEntryDescription};
				service.debugLogArray.push(newEntry);
			}
		},
		
		getCurrentTime : function(){
			if(service.currentTime == '' || service.currentTime == undefined){				
				APIRequestService.getCurrentTime().then(function(data){					
					var arrTime = data.split(' ');
					service.currentTimeZoneFromServer = arrTime[parseInt(arrTime.length)-1];
					arrTime.splice(parseInt(arrTime.length)-1);
					var strTime = arrTime.join(' ');	
					service.currentTime = new Date(strTime);					
					if(service.currentTimeInterval == '' || service.currentTimeInterval == undefined){	
						service.currentTimeInterval = setInterval(function(){								
							service.currentTime.setSeconds(service.currentTime.getSeconds() + 1); 
						}, 1000);	
					}						
				});	
			}				
		},
		getCurrentTimeStr : function(){
			var currentMonth = service.currentTime.toLocaleString('en-US', { month: 'long' });
			var currentDay = ("0" + service.currentTime.getDate()).slice(-2);
			var currentYear = service.currentTime.getFullYear();
			var currentHrs = ("0" + service.currentTime.getHours()).slice(-2);
			var currentMin = ("0" + service.currentTime.getMinutes()).slice(-2);
			var currentSeconds = ("0" + service.currentTime.getSeconds()).slice(-2);
			var currentTz = service.currentTimeZoneFromServer;
			
			var strTime = currentMonth+", "+currentDay+" "+currentYear+" "+currentHrs+":"+currentMin+":"+currentSeconds+" "+currentTz;
			
			return strTime;
		},
		checkCompletionForSeminar : function(){
			var reportObj = this.buildProgressReport();
			 
			if(Object.keys(reportObj).length){
				var promise = APIRequestService.checkSeminarforCompletion(AppManager.logAccessID, reportObj);
				promise.then(function (data) {
					 //console.log("checkSeminarforCompletion:  " + JSON.stringify(data))
						if(data.userLoggedIn == 1){
							service.isCompleted = data.returnData.isCompleted;
							service.allPostTestCompleted = data.returnData.allPostTestCompleted;
							if(userType && $rootScope.hasPaper == 1 ){
								angular.element('.audioVideoSyncBtn').removeClass('hide');
							}					 
						}
						/*else{
							 service.disconnectedFromServer();
						}*/
						});
			}	
		},
		checkCompletionForSeminaronCompleteTab : function(){
				var reportObj = this.buildProgressReport();
			 
		if(Object.keys(reportObj).length){
						var promise = APIRequestService.checkSeminarforCompletion(AppManager.logAccessID, reportObj);
						promise.then(function (data) {
							 //console.log("checkSeminarforCompletion:  " + JSON.stringify(data))
				if(data.userLoggedIn == 1){
					service.isCompleted = data.returnData.isCompleted;
					service.allPostTestCompleted = data.returnData.allPostTestCompleted;
					if(userType && $rootScope.hasPaper == 1 ){
						angular.element('.audioVideoSyncBtn').removeClass('hide');
					}					 
				}
				var scope = angular.element(document.getElementById("complete-controller")).scope();
				scope.init();	
				});
		}else{
			var scope = angular.element(document.getElementById("complete-controller")).scope();
			scope.init();	
			
		}
			},

			showEvaluationScreen : function(){
				angular.element('#right .screen.cmplt .elements').displayHideShow('.evaluationScreen');
			},

			finalCheckSeminarforCompletion : function(userReportedTimeSpent){
				var promise = APIRequestService.finalCheckSeminarforCompletion(userReportedTimeSpent);
				promise.then(function (data) {
					if(data.userLoggedIn == 1){
						if(data.returnData == 1){
							service.isSeminarFinalCompleted = 1;
							if( service.completeProcessing == 1){
								angular.element('#activity > #overlay-box').displayHide();
								angular.element('#activity .seminarCompleted.elementsSem').addClass('displayHide');
           						angular.element('#activity .seminarCompleted.elementsSem').addClass('hide');								
								var sideBarScope = angular.element(document.getElementsByClassName('sidebar-controller')[0]).scope();
								sideBarScope.init();
							}
							if( service.examCompletionTriggered == 1){
								if (service.seminarDetailsObj.offerCertificate == 1) {
									var scopeCmplt = angular.element('.seminarCompleteProcess').scope();
									scopeCmplt.showCertificate();
								}
								service.examCompletionTriggered = 0;
							}else{
								if(service.isSeminarFinalCompleted == 1){
									if (service.seminarDetailsObj.offerCertificate == 1) {
										var scopeCmplt = angular.element('.seminarCompleteProcess').scope();
										scopeCmplt.showCertificate();
									}
								}
							}
						}else{
							service.isSeminarFinalCompleted = 0;
							service.completeProcessing = 0;
						}
					}
					/* else{
							service.disconnectedFromServer();
					}*/
				});
			},

			disconnectedFromServer : function(){
		$rootScope.isUserLoggedIn=false;
		$rootScope.showAlert = false;
				if(!angular.element('#activity').hasClass('sizeLarge')){
					var height = Math.max(document.body.scrollHeight, document.body.offsetHeight,document.getElementById('wrapper_parent').clientHeight, document.getElementById('wrapper_parent').scrollHeight, document.getElementById('wrapper_parent').offsetHeight);
					angular.element("#background-color-patch").height(height);
				}else{
					angular.element("#background-color-patch").height('100%');
				}
				angular.element('#activity > #overlay-box').displayShow();
				angular.element('#activity > .patch .elements').addRemoveClassEle('displayHide', '.content-popup');
				try{
					TimerService.stop();
				}catch(e){}
			},

				generateTimeString : function(seconds) {

				var totalMinutes = Math.round(seconds / 60);
				var numHours = Math.floor(totalMinutes / 60);
				var numMinutes = totalMinutes % 60;

				var returnString = "";
				
				if (totalMinutes == 0)
					return "0 Minutes";

				if (numHours == 1)
					returnString = numHours.toString() + " Hour";
				else if (numHours > 1)
					returnString = numHours.toString() + " Hours";

				if ((numMinutes == 1) && (returnString.length > 0))
					returnString = returnString + ", " + numMinutes + " Minute";
				else if ((numMinutes > 1) && (returnString.length > 0))
					returnString = returnString + ", " + numMinutes + " Minutes";
				else if (numMinutes == 1)
					returnString = returnString + numMinutes + " Minute";
				else if (numMinutes > 1)
					returnString = returnString + numMinutes + " Minutes";


				return returnString;
		}

		};

		service.init();

		return service;
	}
		
})();

