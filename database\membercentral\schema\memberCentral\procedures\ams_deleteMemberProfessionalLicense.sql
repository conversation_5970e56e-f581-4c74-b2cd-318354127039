ALTER PROC dbo.ams_deleteMemberProfessionalLicense
@memberID int,
@PLID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @PLTypeID int, @orgID int;

	select @PLTypeID = PLTypeID from dbo.ams_memberProfessionalLicenses where plid = @PLID;
	select @orgID = orgID from dbo.ams_members where memberID = @memberID;

	BEGIN TRAN;
		delete from dbo.ams_memberProfessionalLicenses
		where plid = @PLID
		and memberID = @memberID;

		update dbo.ams_members
		set dateLastUpdated = getdate()
		where memberID = @memberID;
	COMMIT TRAN;
	
	-- check for earliest license date rule	
	IF EXISTS (SELECT 1 FROM dbo.ams_memberProfessionalLicenseEarliestDate WHERE orgID = @orgID)
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=@memberid, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	-- reprocess any applicable conditions
	IF @byPassQueue = 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, @memberID, vgc.conditionID
		from dbo.ams_virtualGroupConditions as vgc
		inner join dbo.ams_memberProfessionalLicenseTypes as plt on plt.orgID = vgc.orgID and plt.PLTypeID = @PLTypeID
		where vgc.orgID = @orgID
		and vgc.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\');

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
