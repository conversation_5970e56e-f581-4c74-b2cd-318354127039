<cfoutput>
<html>
<head>
<title>Official Certificate of Attendance</title>
<style>
body { background-image: url(#local.assetsURL#swlSwooshWatermark.png);background-position:center;background-repeat: no-repeat;margin:0;}
@font-face { font-family:<PERSON>hom<PERSON>;panose-1:2 11 6 4 3 5 4 4 2 4; }
@font-face { font-family:Verdana;panose-1:2 11 6 4 3 5 4 4 2 4; }
p.<PERSON>, td.<PERSON>Normal, li.<PERSON>, div.MsoNormal { margin:0in; margin-bottom:.0001pt;font-size:12.0pt;font-family:Verdana; }
@page {size: 9.5in 12in;margin: 0;}
div.Section1{ page:Section1; }
tr { vertical-align:top; }
div.<PERSON><PERSON>al, td.MsoNormal { font-size:8.0pt; font-family:Verdana; }
div.pdfWrapper {width: 7.5in;height: 10in;padding: 35px;box-sizing: border-box;}
table.mainTable {
  page-break-inside: auto;
  border-collapse: collapse;
}

table.mainTable > tr {
  page-break-inside: avoid;
  page-break-after: auto;
}

table.mainTable > tr > td {
  page-break-inside: avoid;
}
</style>
</head>
<body>
<div class="pdfWrapper">
<div class=Section1>
<div align=center><img src="#local.assetsURL#swlogoBW300.png" width="332" height="63"></div>
<br/>
<p class=MsoNormal align=center><b><span style='font-size:14.0pt;'>Official Certificate of Attendance</span></b></p>
<p class=MsoNormal align=center><b><span style='font-size:8.0pt;line-height:13pt;'>Please keep this copy for your records.</span></b></p>
<br/>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:8.6pt;'><b>Registrar Verification of Program Activity:</b> 
	This is an Official Record of #UCASE(arguments.enrollmentInfo.fullname)#'s attendance in this program. 
	To determine #UCASE(arguments.enrollmentInfo.fullname)#'s actual participation in this program, please 
	refer to the "SeminarWeb Verification of Activity" below to verify participation. Questions 
	for the Registrar should be directed to SeminarWeb.
</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;'>&nbsp;</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:8.5pt;'><b>How to Obtain Credit:</b> 
	#UCASE(arguments.enrollmentInfo.fullname)# may submit this Registrar Verification of Program 
	Activity to the appropriate accrediting authority and may apply to obtain credit; SeminarWeb 
	makes no warranty or guarantee that this Registrar Verification will be accepted by an 
	accrediting authority.
</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;'>&nbsp;</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:9pt;'><b>Program Status by Continuing Education Accrediting Authority: (as of #dateformat(now(),"mmmm d, yyyy")#)</b><br/>
	#UCASE(arguments.enrollmentInfo.fullname)# may or may not qualify for credit with these authorities:<br/>
	Credit Approved: <cfif arguments.strSAC.qryApproved.recordcount><cfloop query="arguments.strSAC.qryApproved"><nobr>#arguments.strSAC.qryApproved.authoritycode#<cfif len(arguments.strSAC.qryApproved.courseApproval)> - #arguments.strSAC.qryApproved.courseApproval#</cfif><cfif len(arguments.strSAC.qryApproved.credits)> - #arguments.strSAC.qryApproved.credits#</cfif></nobr><cfif arguments.strSAC.qryApproved.currentrow neq arguments.strSAC.qryApproved.recordcount>; </cfif></cfloop><cfelse>(none)</cfif><br/>
	Credit Pending: <cfif arguments.strSAC.qryPending.recordcount><cfloop query="arguments.strSAC.qryPending">#arguments.strSAC.qryPending.authoritycode#<cfif arguments.strSAC.qryPending.currentrow neq arguments.strSAC.qryPending.recordcount>, </cfif></cfloop><cfelse>(none)</cfif><br/>
</span></p>

<table width="100%" cellpadding="6" cellspacing="0" class="mainTable">
<tr><td class=MsoNormal><b>Program Title:</b></td>
	<td class=MsoNormal><b>#arguments.enrollmentInfo.contentName#</b></td></tr>
<cfif len(trim(arguments.enrollmentInfo.contentSubTitle))>
	<tr><td class=MsoNormal><b>Program&nbsp;Subtitle:</b></td>
		<td class=MsoNormal>#arguments.enrollmentInfo.contentSubTitle#</td></tr>
</cfif>	
<tr><td class=MsoNormal><b>Sponsor:</b></td>
	<td class=MsoNormal>#arguments.enrollmentInfo.signUpOrgDescription#</td></tr>
<cfif arraylen(arguments.arrSpeakers)>
	<tr><td class=MsoNormal><b>Author(s):</b></td>
		<td class=MsoNormal>#arrayToList(arguments.arrSpeakers,"; ")#</td></tr>
</cfif>
<tr><td class=MsoNormal><b>Format:</b></td>
	<td class=MsoNormal>This was a self-paced, online, distance learning program. The participant was able to ask 
		questions of the moderators in the program. Materials were provided to the participant. The program was 
		completely web-based; there was NOT a live component to this self-paced program.
		<cfset local.arrcomponents = arrayNew(1)>
		<cfloop query="arguments.qrySeminarFiles">
			<cfif arguments.qrySeminarFiles.fileType eq "video">
				<cfset ArrayAppend(local.arrcomponents,"video")>
			<cfelseif arguments.qrySeminarFiles.fileType eq "audio">
				<cfset ArrayAppend(local.arrcomponents,"audio")>
			<cfelseif arguments.qrySeminarFiles.fileType eq "paper">
				<cfset ArrayAppend(local.arrcomponents,"papers and reading materials")>
			</cfif>
		</cfloop>
		The program included 
		<cfloop from="1" to="#arrayLen(local.arrcomponents)#" index="local.i">
			#local.arrcomponents[local.i]#<cfif arrayLen(local.arrcomponents) gt 2 and local.i lt arrayLen(local.arrcomponents)>,</cfif><cfif local.i is arrayLen(local.arrcomponents)-1> and </cfif>
		</cfloop>. 

		<cfset local.arrcontrols = arrayNew(1)>
		<cfif arguments.qrySeminarSettings.mustAttendMinutes gt 0>
			<cfset ArrayAppend(local.arrcontrols,"a minimum time requirement for successful completion")>
		</cfif>
		<cfif arguments.qrySeminarSettings.promptInterval gt 0>
			<cfset ArrayAppend(local.arrcontrols,"automated polling requiring the participant to be present during the program")>
		</cfif>
		<cfif arrayLen(local.arrcontrols) gt 0>
			The program included these auditing controls: 
			<cfloop from="1" to="#arrayLen(local.arrcontrols)#" index="local.i">
				#local.arrcontrols[local.i]#<cfif arrayLen(local.arrcontrols) gt 2 and local.i lt arrayLen(local.arrcontrols)>;</cfif><cfif local.i is arrayLen(local.arrcontrols)-1> and </cfif>
			</cfloop>. 
		</cfif>
		<cfif arguments.qrySeminarSettings.examRequired>
			The program included successful completion of an online exam. 
		</cfif>
		<cfif arguments.qrySeminarSettings.evaluationRequired>
			The program included completion of a survey.
		</cfif>
	</td></tr>
<tr><td class=MsoNormal><b>SeminarWeb Verification of Activity:</b></td>
	<td class=MsoNormal>Attendance at this program is certified by SeminarWeb as follows:
		<br/><br/>
		Participant Identifier: #arguments.enrollmentInfo.sourceID#<br/>
		#UCASE(arguments.enrollmentInfo.fullname)#<br/>
		<cfif len(arguments.enrollmentInfo.billingfirm)>#UCASE(arguments.enrollmentInfo.billingfirm)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress)>#UCASE(arguments.enrollmentInfo.billingaddress)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress2)>#UCASE(arguments.enrollmentInfo.billingaddress2)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress3)>#UCASE(arguments.enrollmentInfo.billingaddress3)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingcity)>#UCASE(arguments.enrollmentInfo.billingcity)#, </cfif>
		<cfif len(arguments.enrollmentInfo.billingstate)>#UCASE(arguments.enrollmentInfo.billingstate)# </cfif>
		<cfif len(arguments.enrollmentInfo.billingzip)>#UCASE(arguments.enrollmentInfo.billingzip)#</cfif>
		<br/><br/>
		<table cellpadding="3" cellspacing="0">
		<tr><td class=MsoNormal>Participant started program at</td>
			<td class=MsoNormal align="right">#DateFormat(arguments.enrollmentInfo.dateEnrolled,"m/d/yyyy")# #TimeFormat(arguments.enrollmentInfo.dateEnrolled,"h:mm TT")# CENTRAL</td>
		</tr>
		<tr><td class=MsoNormal>Participant exited program at</td>
			<td class=MsoNormal align="right">#DateFormat(arguments.enrollmentInfo.dateCompleted,"m/d/yyyy")# #TimeFormat(arguments.enrollmentInfo.dateCompleted,"h:mm TT")# CENTRAL</td>
		</tr>
		<tr><td class=MsoNormal>Total time spent in program:</td>
			<td class=MsoNormal align="right">#val(arguments.enrollmentInfo.calcTimeSpent)# minutes</td>
		</tr>
		</table>
		<div style="margin-top:0;position:relative;z-index:9">#local.strSWRegistrar.swRegistrarSignature#</div>
		<br/>
		<br/>
		<div style="width:70%; border-top:1px solid ##000;padding-top:4px;margin-top:-25px;position:relative;z-index:10">
		#local.strSWRegistrar.swRegistrarLine#
		</div>
	</td></tr>
<tr><td class=MsoNormal><br/><b>Participant Verification:</b></td>
	<td class=MsoNormal><br/>
		I, #UCASE(arguments.enrollmentInfo.fullname)#, certify that I completed this program in its entirety.<br/>
		Seminar: #arguments.enrollmentInfo.contentName#<br/>
		<div style="width:70%;text-align:right;margin-top:.2in;z-index:50;">&nbsp;</div>
		<div style="width:70%;border-top:1px solid ##000;padding-top:4px;z-index:51;">
			<div style="display:inline;float:right;text-align:right;">DATE#repeatString("&nbsp; ",15)#</div>
			<div style="display:inline;text-align:left">#UCASE(arguments.enrollmentInfo.fullname)#</div>
		</div>
	</td></tr>
<tr><td class="MsoNormal"><br/><b>Important Instructions:</b></td>
	<td class="MsoNormal"><br/>
		This is your official attendance record. Utilizing this form, you may qualify for continuing 
		education credit, but you must follow the rules and filing guidelines of the accrediting authority.
	</td></tr>
</table>
</div>
</div>
</body>
</html>
</cfoutput>