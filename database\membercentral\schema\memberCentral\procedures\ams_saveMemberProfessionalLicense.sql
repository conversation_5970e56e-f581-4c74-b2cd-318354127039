ALTER PROC dbo.ams_saveMemberProfessionalLicense
@memberID int,
@PLTypeID int,
@licenseNumber varchar(200),
@activeDate datetime,
@PLStatusID int,
@recordedByMemberID int,
@byPassQueue bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @PLID int;
	select @orgID = orgID from dbo.ams_memberProfessionalLicenseTypes where PLTypeID = @PLTypeID;
	select @PLID = PLID from dbo.ams_memberProfessionalLicenses where memberID = @memberID and PLTypeID = @PLTypeID

	IF @PLID IS NOT NULL
		IF @licenseNumber IS NULL AND @activeDate IS NULL AND @PLStatusID IS NULL
			EXEC dbo.ams_deleteMemberProfessionalLicense @memberID=@memberID, @PLID=@PLID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		ELSE
			update dbo.ams_memberProfessionalLicenses
			set licenseNumber = nullif(@licenseNumber,''),
				activeDate = @activeDate,
				PLStatusID = @PLStatusID
			where memberID = @memberID 
			and PLTypeID = @PLTypeID;
	ELSE
		insert into dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, licenseNumber, activeDate, PLStatusID)
		values (@memberID, @PLTypeID, nullif(@licenseNumber,''), @activeDate, @PLStatusID);

	-- set member as updated
	update dbo.ams_members
	set dateLastUpdated = getdate()
	where memberID = @memberID;

	-- check for earliest license date rule
	IF EXISTS (SELECT 1 FROM dbo.ams_memberProfessionalLicenseEarliestDate where orgID = @orgID)
		EXEC dbo.ams_runEarliestLicenseDateRule @orgID=@orgID, @memberID=@memberid, @recordedByMemberID=@recordedByMemberID, @byPassQueue=@byPassQueue;

	-- reprocess any applicable conditions
	IF @byPassQueue = 0 BEGIN
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, @memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\');

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
	END ELSE BEGIN
		IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
			select @orgid=orgID from dbo.ams_memberProfessionalLicenseTypes where PLTypeID = @PLTypeID;

			INSERT INTO #tmpMCGConditions (conditionID)
			SELECT distinct c.conditionID
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape ('\')
				except 
			select conditionID from #tmpMCGConditions;
		END
	END


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
