ALTER PROC dbo.email_markRecipientBatch
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @batchSizeMultiplier int = 12, @realbatchSize int, @batchStartDate datetime, @processingStatusID int, 
		@recipientID int, @scheduledStatusID int, @optoutStatusID int, @suppressedStatusID int, 
		@scheduledJobsFound bit=0, @futureDatedQueuedMessagesFound bit=0, @optOutConsentListModeID int, 
		@globalOptOutConsentListModeID int, @tier varchar(12), @regexMergeCode varchar(40), 
		@defaultMarketingSubUserID int, @defaultTransactionalSubUserID int, @environmentID int;

	SELECT @tier = tier, @regexMergeCode = regexMergeCode from membercentral.dbo.fn_getServerSettings();
	select @optOutConsentListModeID = consentListModeID from  dbo.email_consentListModes where modeName = 'Opt-Out'
	select @globalOptOutConsentListModeID = consentListModeID from  dbo.email_consentListModes where modeName = 'GlobalOptOut'

	select @defaultMarketingSubUserID=defaultMarketingSubUserID, @defaultTransactionalSubUserID=defaultTransactionalSubUserID, 
		@environmentID=environmentID
	from membercentral.dbo.platform_environments
	where environmentName = @tier;

	declare @tblMessages table(
		messageID int NOT NULL,
		messageTypeID int NOT NULL,
		mailStreamID int,
		replyToEmail varchar(200) NOT NULL, 
		subject varchar(400) NOT NULL,
		siteID int NOT NULL, 
		siteCode varchar(10) NOT NULL, 
		siteName varchar(60) NOT NULL,
		fromName varchar(200) NOT NULL,
		fromEmail varchar(200) NOT NULL,
		senderEmail varchar(200) NOT NULL, 
		messageContent varchar(max) NOT NULL,
		username varchar(100));

	declare @tblMessageOptOutList table(
		messageID int NOT NULL, 
		consentListID int NOT NULL,
		isPrimary bit,
		INDEX ix_tblMessageOptOutList_messageID_consentListID (messageID,consentListID)
	);

	declare @tblRecipients table(
		recipientID int PRIMARY KEY, 
		siteID int NOT NULL,
		messageID int NOT NULL, 
		memberID int NOT NULL,
		toName varchar(200) NOT NULL,
		toEmail varchar(200) NOT NULL,
		INDEX ix_messageRecipients NONCLUSTERED (messageID, recipientID)

	);

	declare @tblActiveSubusers TABLE (
		siteID int NOT NULL,
		mailStreamID int NOT NULL,
		subuserID int,
		username varchar(100),
		apikey varchar(300),
		poolName varchar(100), 
		outboundProviderCode varchar(25),
		smtphost varchar(200),
		smtpport int,
		defaultFromUsername varchar(100),
		subuserDomainID int,
		defaultSendingHostname varchar(200),
		validSendingHostnames varchar(8000),
		warmupPlanID int,
		warmupPlanStepID int,
		phaseout_subuserID int,
		phaseout_username varchar(100),
		phaseout_apikey varchar(300),
		phaseout_poolName varchar(100), 
		phaseout_outboundProviderCode varchar(25),
		phaseout_smtphost varchar(200),
		phaseout_smtpport int,
		phaseout_defaultFromUsername varchar(100),
		phaseout_subuserDomainID int,
		phaseout_defaultSendingHostname varchar(100),
		phaseout_validSendingHostnames varchar(8000),
		stepRatio numeric(3,2),
		stepPhaseOutMessagesSent int, 
		stepPhaseInMessagesSent int,
		stepMaxPhaseInMessagesAllowed int,
		enableHalonSending bit NOT NULL DEFAULT(0)
	)

	declare @tblRecipientsRemoved table(
		recipientID int PRIMARY KEY, 
		messageID int NOT NULL
	);


    declare @smtpAsync bit = 0, @threadCount int = 10, @recipientsPerMiniBatch int = 250, @limitWarmupToInternalAddresses bit=0, @javaSMTP bit = 0; 
    declare @sendingMethod varchar(5) = 'smtp'; -- expected values: smtp or api

	declare @taskParams TABLE (threadCount int, sendingMethod varchar(5), smtpAsync bit, recipientsPerMiniBatch int, requestedBatchSize int, batchSizeMultiplier int, limitWarmupToInternalAddresses bit, javaSMTP bit)
	insert into @taskParams (threadCount, sendingMethod,smtpAsync,recipientsPerMiniBatch, requestedBatchSize, batchSizeMultiplier, limitWarmupToInternalAddresses, javaSMTP) values (@threadCount,@sendingMethod, @smtpAsync, @recipientsPerMiniBatch, @batchSize, @batchSizeMultiplier, @limitWarmupToInternalAddresses, @javaSMTP)

	set @workerUUID = newID();
	set @batchStartDate = getdate();
	select @processingStatusID = statusID from dbo.email_statuses where statusCode = 'G';
	select @scheduledStatusID = statusID from dbo.email_statuses where statusCode = 'scheduled';
	select @optoutStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'optout';
	select @suppressedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'suppressed';


	set @realbatchSize = @batchSize * @batchSizeMultiplier;


    -- any mail to send? Skip if we're sending a specific message
    -- hardcoded @queueStatusID to use filtered index
    IF @restrictToMessageID IS NULL BEGIN
        SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
        select top 1 @recipientID = mrh.recipientID 
        from dbo.email_messageRecipientHistory as mrh
        inner join dbo.email_messages as m on mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @batchStartDate
        	and m.messageID = mrh.messageID
        where mrh.emailStatusID = 2 
        and mrh.batchID is null;

        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

        if @recipientID is null 
            GOTO on_done;
    END
	-- mark recipients
	-- hardcoded @queueStatusID to use filtered index
	update r 
	set r.batchID = @workerUUID,
		r.batchStartDate = @batchStartDate,
		r.emailStatusID = @processingStatusID
		output inserted.recipientID, inserted.siteID, inserted.messageID, inserted.memberID, inserted.toName, inserted.toEmail
		into @tblRecipients
	from dbo.email_messageRecipientHistory as r
	inner join (
		select top (@realbatchSize) recipientID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m on mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @batchStartDate
			and m.messageID = mrh.messageID
		where mrh.emailStatusID = 2 
		and mrh.batchID is null
		and mrh.messageID = isnull(@restrictToMessageID,mrh.messageID)
		order by queuePriority
	) as temp on temp.recipientID = r.recipientID;


	-- get messages
	insert into @tblMessages (messageID, messageTypeID, replyToEmail, subject, siteID, siteCode, siteName, fromName, fromEmail, senderEmail, messageContent)
	select m.messageID, m.messageTypeID, m.replyToEmail, m.subject, s.siteID, s.siteCode, s.siteName,
		m.fromName, m.fromEmail,
		m.senderEmail, messageContent = case when nullif(ltrim(rtrim(m.messagewrapper)),'') is null then cv.rawContent else replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) end
	from (select distinct siteID, messageID from @tblRecipients) as tmpM
	inner join dbo.email_messages as m on tmpM.siteID = m.siteID
		and m.status = 'A'
		and m.messageID = tmpM.messageID 
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
	inner join membercentral.dbo.sites as s on s.siteID = m.siteID;

	-- get mailstreamIDs
	update tm set
		mailStreamID = mt.mailStreamID
	from @tblMessages tm
	inner join email_messageTypes mt
		on mt.messageTypeID = tm.messageTypeID;

	-- get activeSubusers
	insert into @tblActiveSubusers (siteID, mailStreamID, subuserID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname,enableHalonSending)
	select distinct tm.siteID, ms.mailStreamID, su.subuserID, su.username, su.apikey, ippools.poolName, mp.outboundProviderCode, mpe.smtphost, mpe.smtpport, sums.defaultFromUsername, sud.sendingHostname,su.enableHalonSending
	from @tblMessages tm 
	inner join email_mailstreams ms
		on tm.mailStreamID = ms.mailStreamID
	inner join [dbo].[sendgrid_subusers] su
		on su.siteID = tm.siteID
		and su.environmentID = @environmentID
	left outer join sendgrid_defaultSubusers dsu 
		on dsu.subuserID = su.subuserID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = su.activeSubuserDomainID
	inner join [dbo].[sendgrid_subuserStatuses] sus
		on sus.subuserStatusID = su.statusID
		and sus.subuserStatusID = sud.statusID
		and sus.[status] = 'Active'
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.subuserID = su.subuserID
		and sums.mailstreamID = tm.mailStreamID
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = sums.ipPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID
	where dsu.defaultSubuserID is null;


	-- associate default mailstream subuser with mailstream messages that don't have site specific subuser ready
	insert into @tblActiveSubusers (siteID, mailStreamID, subuserID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname,enableHalonSending)
	select distinct  missingSubusers.siteID, ms.mailStreamID, su.subuserID, su.username, su.apikey, ippools.poolName, mp.outboundProviderCode, mpe.smtphost, mpe.smtpport, sums.defaultFromUsername, sud.sendingHostname, su.enableHalonSending
	from (
		select siteID, mailStreamID
		from @tblMessages
		except
		select asu.siteID, asu.mailStreamID
		from @tblActiveSubusers asu
		inner join @tblMessages tm on tm.siteID = asu.siteID
	) as missingSubusers
	inner join sendgrid_defaultSubusers dsu
		on dsu.mailStreamID = missingSubusers.mailStreamID
		and dsu.environmentID = @environmentID
	inner join [dbo].[sendgrid_subusers] su
		on su.subuserID = dsu.subuserID
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.subuserID = su.subuserID
		and sums.mailstreamID = missingSubusers.mailStreamID
	inner join dbo.email_mailstreams ms 
		on ms.mailStreamID = sums.mailstreamID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = su.activeSubuserDomainID
	inner join [dbo].[sendgrid_subuserStatuses] sus
		on sus.subuserStatusID = su.statusID
		and sus.subuserStatusID = sud.statusID
		and sus.[status] = 'Active'
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = sums.ipPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID;


	-- get all validSendingHostnames per subuser
	update asu set 
		validSendingHostnames = temp.validSendingHostnames		
	from @tblActiveSubusers asu
	inner join (
		select subuserID, STRING_AGG(sendingHostname,',') as validSendingHostnames
		from (
		select distinct su.subuserID, sud.sendingHostname 
		from @tblActiveSubusers su
		inner join sendgrid_subuserDomains sud 
			on su.subuserID = sud.subuserID
			and sud.statusID=2) as tmp

		group by subuserID
	) as temp on asu.subuserID = temp.subuserID

	
	-- get subuser warmup information

	update asu SET
		warmupPlanID= wp.warmupPlanID, 
		warmupPlanStepID= wps.warmupPlanStepID, 
		phaseout_subuserID = wp.phaseout_subuserID,
		stepRatio= wps.stepRatio,
		stepPhaseOutMessagesSent = wps.stepPhaseOutMessagesSent, 
		stepPhaseInMessagesSent = wps.stepPhaseInMessagesSent,
		stepMaxPhaseInMessagesAllowed= wps.stepMaxPhaseInMessagesAllowed,
		phaseout_username=su.username, 
		phaseout_apikey = su.apikey,
		phaseout_poolName = ippools.poolName,
		phaseout_defaultSendingHostname = sud.sendingHostname,
		phaseout_outboundProviderCode = mp.outboundProviderCode,
		phaseout_smtphost = mpe.smtphost,
		phaseout_smtpport = mpe.smtpport
	from @tblActiveSubusers asu
	inner join sendgrid_subuserMailstreamWarmupPlans wp
		on wp.siteID = asu.siteID
		and wp.mailStreamID = asu.mailStreamID
		and wp.phasein_SubUserID = asu.subuserID
		and wp.status = 'A'
	inner join sendgrid_subuserMailstreamWarmupPlanSteps wps
		on wp.warmupPlanID = wps.warmupPlanID
		and wps.status = 'A'
	inner join [dbo].[sendgrid_subusers] su
		on su.subuserID = wp.phaseout_SubuserID
	inner join [dbo].[sendgrid_subuserDomains] sud 
		on sud.subuserDomainID = wp.phaseout_SubuserDomainID
	inner join [dbo].[sendgrid_ipPools] ippools
		on ippools.ippoolID = wp.phaseout_IPPoolID
    inner join dbo.email_outboundProviders mp 
        on mp.outboundProviderID = ippools.outboundProviderID
    inner join dbo.email_outboundProviderEnvironments mpe
        on mpe.outboundProviderID = mp.outboundProviderID
        and mpe.environmentID = @environmentID;

	-- get all validSendingHostnames per phaseout subuser
	update asu set 
		phaseout_validSendingHostnames = temp.validSendingHostnames,
		phaseout_defaultFromUsername = sums.defaultFromUsername
	from @tblActiveSubusers asu
	inner join (
		select phaseout_subuserID, STRING_AGG(sendingHostname,',') as validSendingHostnames
		from (
		select distinct su.phaseout_subuserID, sud.sendingHostname
		from @tblActiveSubusers su
		inner join sendgrid_subuserDomains sud 
			on su.phaseout_subuserID = sud.subuserID
			and sud.statusID=2) as tmp
		group by phaseout_subuserID
	) as temp on asu.phaseout_subuserID = temp.phaseout_subuserID
	inner join [dbo].[sendgrid_subuserMailstreams] sums
		on sums.mailstreamID = asu.mailStreamID
		and sums.subuserID = asu.phaseout_subuserID


	-- update sendgrid username
	update m set 
		username = sub.username
	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID;

	-- enforce opt-outs
	insert into @tblMessageOptOutList (messageID, consentListID, isPrimary)
	select mcl.messageID, mcl.consentListID, mcl.isPrimary
	from @tblMessages m
	inner join email_messageConsentLists mcl 
		on mcl.messageID = m.messageID
	inner join dbo.email_consentLists cl
		on cl.consentListID = mcl.consentListID
		and cl.[status] = 'A'
		and cl.consentListModeID in (@optOutConsentListModeID,@globalOptOutConsentListModeID)
	group by mcl.messageID, mcl.consentListID, mcl.isPrimary;
		
	--get Global Optout for all messages that had an opt-out consentList
	insert into @tblMessageOptOutList (messageID, consentListID, isPrimary)
	select mo.messageID, global_cl.consentListID, 0 as isPrimary
	from @tblMessageOptOutList mo 
	inner join dbo.email_consentLists cl
		on cl.consentListID = mo.consentListID
		and cl.[status] = 'A'
		and cl.consentListModeID = @optOutConsentListModeID
	inner join dbo.email_consentListTypes clt
		on clt.consentListTypeID = cl.consentListTypeID
	inner join dbo.email_consentListTypes global_clt
		on global_clt.orgID = clt.orgID
		and global_clt.consentListTypeName = 'Global Lists'
	inner join dbo.email_consentLists global_cl
		on global_cl.consentListTypeID = global_clt.consentListTypeID
		and global_cl.consentListModeID = @globalOptOutConsentListModeID
		and global_cl.[status] = 'A'
	left outer join @tblMessageOptOutList as tmp on tmp.messageID = mo.messageID
		and tmp.consentListID = global_cl.consentListID
	where tmp.messageID is null
	group by mo.messageID, global_cl.consentListID;

	IF EXISTS (SELECT 1 FROM @tblMessageOptOutList) BEGIN

		update r set 
			emailStatusID = @optoutStatusID
		output inserted.recipientID, inserted.messageID
		into @tblRecipientsRemoved
		from @tblMessageOptOutList mo
		inner join @tblRecipients tmp_r 
			on tmp_r.messageID = mo.messageID
		inner join dbo.email_consentListMembers AS clm
			on clm.consentListID = mo.consentListID
			and clm.email = tmp_r.toEmail 
		inner join dbo.email_messageRecipientHistory as r
			on r.messageID = tmp_r.messageID
			and r.recipientID = tmp_r.recipientID
			and r.emailStatusID = @processingStatusID;

	END

	-- enforce suppressions

	update r set 
		emailStatusID = @suppressedStatusID
	output inserted.recipientID, inserted.messageID
	into @tblRecipientsRemoved
	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID
	inner join @tblRecipients tmp_r 
		on tmp_r.messageID = m.messageID
	inner join dbo.sendgrid_suppressions suppress
		on suppress.subuserID = sub.subuserID
		and suppress.emailAddress = tmp_r.toEmail
	inner join dbo.email_messageRecipientHistory as r
		on r.messageID = tmp_r.messageID
		and r.recipientID = tmp_r.recipientID
		and r.emailStatusID = @processingStatusID;

	-- remove any suppressed or opted out recipients from temp table so they are not returned to the worker
	IF EXISTS (SELECT 1 FROM @tblRecipientsRemoved) BEGIN
		delete tmp_r
		from @tblRecipientsRemoved removed 
		inner join @tblRecipients tmp_r 
			on removed.recipientID = tmp_r.recipientID
	END



	-- qryMessages
	select m.*, mt.messageTypeCode, mt.messageType, sub.defaultFromUsername, sub.defaultSendingHostname, sub.poolName, sub.warmupPlanID, siteID_mailstreamID = cast(sub.siteID as varchar(10)) + '_' + cast(sub.mailstreamID as varchar(10)),
		consentListIDs = (
			SELECT STUFF((SELECT ',' + cast(consentListID AS VARCHAR(10))
			FROM @tblMessageOptOutList moptouts
			where moptouts.messageID = m.messageID
			order by isPrimary desc, consentListID
			FOR XML PATH('')),1,1,'')
		)

	from @tblMessages m
	inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
	inner join @tblActiveSubusers sub
		on sub.siteID = m.siteID
		and sub.mailStreamID = m.mailStreamID
	order by messageID;
	
	-- qrySubjectMetadata
	select distinct m.messageID, left(reg.Text,300) as fieldName1
	from @tblMessages as m
	cross apply membercentral.dbo.fn_RegexMatches(m.subject,@regexMergeCode) as reg
	order by 1, 2;	

	-- qryMessageMetadata
	select distinct m.messageID, f.fieldName
	from @tblMessages as m
	inner join dbo.email_messageMetadataFields as mf on mf.messageID = m.messageID
	inner join dbo.email_metadataFields as f on f.fieldID = mf.fieldID;
	
	-- qryRecipients
	select r.recipientID, r.messageID, r.memberID, r.toName, r.toEmail, m.username, siteID_mailstreamID = cast(m.siteID as varchar(10)) + '_' + cast(m.mailstreamID as varchar(10))
	from @tblRecipients r
	inner join @tblMessages m
		on m.messageID = r.messageID;

	-- qryRecipientMetadata
	select r.recipientID, f.fieldID, f.fieldName, r.messageid, r.memberid, mf.fieldValue, mf.fieldTextToReplace
	from @tblRecipients as r
	inner join dbo.email_messageMetadataFields as mf
		on r.messageID = mf.messageID
		and r.recipientID = mf.recipientID
	inner join dbo.email_metadataFields as f
		on mf.fieldID = f.fieldID
		and f.isMergeField = 1;


	-- qryRecipientAttachments
	select r.recipientID, a.attachmentID, a.fileName, a.localDirectory
	from dbo.email_messageRecipientAttachments as ra
	inner join dbo.email_attachments as a on a.attachmentID = ra.attachmentID
	inner join @tblRecipients as r on r.recipientID = ra.recipientID;

	-- qryTaskParams
	select threadCount, sendingMethod, smtpAsync, recipientsPerMiniBatch, requestedBatchSize, batchSizeMultiplier, limitWarmupToInternalAddresses, javaSMTP
	from @taskParams tp;

	-- qrySubusers
	select siteID_mailstreamID = cast(siteID as varchar(10)) + '_' + cast(mailstreamID as varchar(10)), subuserID, siteID, mailstreamID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname, validSendingHostnames,
		warmupPlanID, warmupPlanStepID, stepRatio, stepPhaseOutMessagesSent, stepPhaseInMessagesSent, stepMaxPhaseInMessagesAllowed, phaseout_subuserID,phaseout_username, phaseout_apikey, phaseout_poolName, phaseout_outboundProviderCode, phaseout_smtphost, phaseout_smtpport, phaseout_defaultFromUsername, phaseout_defaultSendingHostname, phaseout_validSendingHostnames,
		deliveryMode = 'postfix', sub.enableHalonSending 
	from @tblActiveSubusers sub
	group by subuserID, siteID, mailstreamID, username, apikey, poolName, outboundProviderCode, smtphost, smtpport, defaultFromUsername, defaultSendingHostname, validSendingHostnames,
		warmupPlanID, warmupPlanStepID, stepRatio, stepPhaseOutMessagesSent, stepPhaseInMessagesSent, stepMaxPhaseInMessagesAllowed, phaseout_subuserID, phaseout_username, phaseout_apikey, phaseout_poolName, phaseout_outboundProviderCode, phaseout_smtphost, phaseout_smtpport, phaseout_defaultFromUsername, phaseout_defaultSendingHostname, phaseout_validSendingHostnames, sub.enableHalonSending


	-- subuserDomain hostnames: sending, returnPath, linkbrand
	select siteID_mailstreamID = cast(su.siteID as varchar(10)) + '_' + cast(su.mailstreamID as varchar(10)), sud.subuserDomainID, su.subuserID, mp.outboundProviderCode, sud.sendingHostname,sud.linkBrandHostname,sud.returnPathHostname, sud.dkimSelectorActive
	from @tblActiveSubusers su
	inner join dbo.sendgrid_subuserDomains sud 
		on sud.subuserID in (su.subuserID,su.phaseout_subuserID)
		and sud.statusID=2
	inner join dbo.email_outboundProviders mp 
		on mp.outboundProviderID = sud.outboundProviderID
		and mp.outboundProviderCode in (su.outboundProviderCode,su.phaseout_outboundProviderCode)
	inner join dbo.email_outboundProviderEnvironments mpe
		on mpe.outboundProviderID = mp.outboundProviderID
		and mpe.environmentID = @environmentID
	group by su.siteID, su.mailstreamID, sud.subuserDomainID, su.subuserID, mp.outboundProviderCode, sud.sendingHostname,sud.linkBrandHostname,sud.returnPathHostname, sud.dkimSelectorActive
	

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
