ALTER PROC dbo.ams_updateMemberDataColumn
@siteID int,
@columnID int,
@columnName varchar(128),
@columnDesc varchar(255),
@allowMultiple bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(14,2),
@maxValueDecimal2 decimal(14,2),
@minValueDate date,
@maxValueDate date,
@linkedDateColumnID int, 
@linkedDateCompareDate date, 
@linkedDateCompareDateAFID int,
@linkedDateAdvanceDate date, 
@linkedDateAdvanceAFID int,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	CREATE TABLE #tmpAuditLogData (
		[rowCode] varchar(20) PRIMARY KEY, [Name] varchar(128), [Data Stored As] varchar(max), [Display Field As] varchar(max), [Description] varchar(max),
		[New values can be added during Member Import] varchar(max), [Allow null values or no selection for this field] varchar(max),
		[Default Value] varchar(max), [Prevent editing field values] varchar(max), [Members can have multiple values for this field] varchar(max),
		[Min Characters] varchar(max), [Max Characters] varchar(max), [Min Selectable Options] varchar(max), [Max Selectable Options] varchar(max),
		[Min Value Integer] varchar(max), [Max Value Integer] varchar(max), [Min Value Decimal] varchar(max), [Max Value Decimal] varchar(max),
		[Min Value Date] varchar(max), [Max Value Date] varchar(max), [Linked Date Column] varchar(max), [Linked Date Compare Date] varchar(max),
		[Linked Date Compare Date Adv Formula] varchar(max), [Linked Date Adv Date] varchar(max), [Linked Date Adv Formula] varchar(max)
	);

	DECLARE @valueID int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int, 
		@oldColumnName varchar(128), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20), 
		@memberIDList varchar(max), @conditionIDList varchar(max), @msg varchar(max), @crlf varchar(2),
		@runLinkedDateCustomFieldRule bit = 0;

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	SET @crlf = char(13) + char(10);
	SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID;

	IF @columnID is null OR @columnName is null
		GOTO on_done;

	IF NOT EXISTS (SELECT 1 FROM dbo.ams_memberdataColumns where columnID = @columnID AND orgID = @orgID)
		RAISERROR('Invalid column.', 16, 1);

	-- new column name (if applicable) cannot be a reserved name
	IF (@columnName <> @oldColumnName AND (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1)
		RAISERROR('That column name is invalid or already in use.', 16, 1);

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
		SET @displayTypeCode = 'CHECKBOX';
	IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
		SET @displayTypeCode = 'RADIO';

	-- validations
	SELECT @olddataTypeCode = dt.dataTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
		and c.columnID = @columnID;
	SELECT @olddisplayTypeCode = dt.displayTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
		and c.columnID = @columnID;
	SELECT @displayTypeID = displayTypeID
		from dbo.ams_memberDataColumnDisplayTypes
		where displayTypeCode = @displayTypeCode;
	SELECT @dataTypeID = dataTypeID
		from dbo.ams_memberDataColumnDataTypes
		where dataTypeCode = @dataTypeCode;
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('CONTENTOBJ','DOCUMENTOBJ')
		SET @allowNull = 1;
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SET @allowNull = 1;
	IF @allowNull = 1
		SET @defaultValue = '';

	IF @linkedDateColumnID IS NOT NULL 
		AND EXISTS (SELECT 1 
					FROM dbo.ams_memberDataColumns 
					WHERE orgID = @orgID 
					AND columnID = @columnID 
					AND (ISNULL(linkedDateColumnID,0) <> @linkedDateColumnID 
						OR ISNULL(linkedDateCompareDate,'1/1/1900') <> @linkedDateCompareDate)
					)
		SET @runLinkedDateCustomFieldRule = 1;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	VALUES (
		'DATATYPECODE', 'STRING', 'STRING', 'STRING', 'CONTENTOBJ', 'BIT', 'BIT', 'STRING', 'BIT',
		'BIT', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'DECIMAL2',
		'DECIMAL2', 'DATE', 'DATE', 'STRING', 'DATE', 'STRING', 'DATE', 'STRING'
	);

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	SELECT 'OLDVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate,
		ldc.columnName,  c.linkedDateCompareDate, ldc_af.afName, c.linkedDateAdvanceDate, ldc_adv_af.afName
	FROM dbo.ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	LEFT OUTER JOIN dbo.ams_memberDataColumns AS ldc ON ldc.orgID = @orgID
		AND ldc.columnID = c.linkedDateColumnID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_af ON ldc_af.siteID = @siteID
		AND ldc_af.afID = c.linkedDateCompareDateAFID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_adv_af ON ldc_adv_af.siteID = @siteID
		AND ldc_adv_af.afID = c.linkedDateAdvanceAFID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	BEGIN TRAN;
		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT;
				IF @valueID = 0 SET @allowNull = 1;
		END 

		-- update column info
		UPDATE dbo.ams_memberDataColumns 
		SET columnName = @columnName,
			columnDesc = @columnDesc,
			allowNull = @allowNull,
			defaultValueID = nullif(@valueID,0),
			allowNewValuesOnImport = @allowNewValuesOnImport,
			isReadOnly = @isReadOnly,
			allowMultiple = @allowMultiple,
			minChars = @minChars,
			maxChars = @maxChars,
			minSelected = @minSelected,
			maxSelected = @maxSelected,
			minValueInt = @minValueInt,
			maxValueInt = @maxValueInt,
			minValueDecimal2 = @minValueDecimal2,
			maxValueDecimal2 = @maxValueDecimal2,
			minValueDate = @minValueDate,
			maxValueDate = @maxValueDate,
			linkedDateColumnID = @linkedDateColumnID, 
			linkedDateCompareDate = @linkedDateCompareDate, 
			linkedDateCompareDateAFID = @linkedDateCompareDateAFID,
			linkedDateAdvanceDate = @linkedDateAdvanceDate, 
			linkedDateAdvanceAFID = @linkedDateAdvanceAFID
		WHERE columnID = @columnID;

		IF @displayTypeCode <> @olddisplayTypeCode OR @dataTypeCode <> @olddataTypeCode 
			UPDATE vgc
			SET processValuesSection = 
						case
						when vge.expression in ('eq','neq') and @dataTypeCode in ('STRING','DECIMAL2','DATE') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') then 1
						when vge.expression in ('eq','neq','lt','lte','gt','gte') and @dataTypeCode = 'INTEGER' then 1
						when vge.expression in ('datepart','datediff') then 1
						when vge.expression in ('eq','neq') and @dataTypeCode = 'STRING' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 2
						when vge.expression in ('lt','lte','gt','gte','contains','contains_regex') and @dataTypeCode = 'STRING' then 2
						when vge.expression in ('eq','neq') and @dataTypeCode = 'BIT' then 3
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DECIMAL2' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 4
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DECIMAL2' then 4
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DATE' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 5
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DATE' then 5
						else null end
			FROM dbo.ams_virtualGroupConditions as vgc
			INNER JOIN dbo.ams_virtualGroupExpressions as vge on vge.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10));

		-- if changing the display type
		IF @displayTypeCode <> @olddisplayTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET displayTypeID = @displayTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
				
				-- err if any expressions are not 1,2,7,8 now that it will be a select
				IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
					RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1);

				-- create column values for those condition values that dont yet exist as column values
				declare @tblVals TABLE (newVal varchar(max));
				insert into @tblVals (newVal)
				select vgcv.conditionValue
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 1
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 2
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(14,2)))
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 3
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
					union
				select convert(varchar(10),vgcv.conditionValue,101)
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 4
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as date));

				DECLARE @newvalueID int, @minValue varchar(max);
				select @minValue = min(newVal) from @tblVals;
				while @minValue is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT;
					select @minValue = min(newVal) from @tblVals where newVal > @minValue;
				END

				-- get the valueID
				UPDATE vgcv
				SET vgcv.conditionValue = tmp.valueID
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN (
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 1 
					and vgcv.conditionValue = mdcv.columnvalueString
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 2 
					and cast(vgcv.conditionValue as decimal(14,2)) = mdcv.columnvalueDecimal2
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 3 
					and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 4 
					and cast(vgcv.conditionValue as date) = mdcv.columnvalueDate
				) as tmp on tmp.conditionID = vgc.conditionID
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			UPDATE dbo.ams_virtualGroupConditions
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END

		-- if changing the data type
		IF @dataTypeCode <> @olddataTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET dataTypeID = @dataTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- check ams_virtualGroupConditions for expression conflicts
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueString as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueString as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = cast(columnValueString as date)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueString as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueDecimal2 as varchar(255))
					where columnID = @columnID
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDecimal2 = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueInteger as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueInteger as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueInteger as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = convert(varchar(10),columnValueDate,101)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are date values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDate = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (11,12)
				) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1);
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueBit as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueString = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueBit as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueDecimal2 = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueBit as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END

			UPDATE dbo.ams_virtualGroupConditions
			set dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
			CREATE TABLE #tmpAffectedConditions (orgID int, conditionID int);

			UPDATE vgc
			SET vgc.subProc = CASE WHEN e.expression='datediff' then 'MD_DATEDIFF'
								WHEN e.expression='datepart' then 'MD_DATEPART'
								WHEN e.expression='contains' and @dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
								WHEN e.expression='contains_regex' and @dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
								WHEN e.expression='eq' and @dataTypeCode='STRING' then 'MD_EQ_STRING'
								WHEN e.expression='eq' and @dataTypeCode='BIT' then 'MD_EQ_BIT'
								WHEN e.expression='eq' and @dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
								WHEN e.expression='eq' and @dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
								WHEN e.expression='eq' and @dataTypeCode='DATE' then 'MD_EQ_DATE'
								WHEN e.expression='exists' and @dataTypeCode='STRING' then 'MD_EXISTS_STRING'
								WHEN e.expression='exists' and @dataTypeCode='BIT' then 'MD_EXISTS_BIT'
								WHEN e.expression='exists' and @dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
								WHEN e.expression='exists' and @dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
								WHEN e.expression='exists' and @dataTypeCode='DATE' then 'MD_EXISTS_DATE'
								WHEN e.expression='exists' and @dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
								WHEN e.expression='exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
								WHEN e.expression='gt' and @dataTypeCode='STRING' then 'MD_GT_STRING'
								WHEN e.expression='gt' and @dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
								WHEN e.expression='gt' and @dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
								WHEN e.expression='gt' and @dataTypeCode='DATE' then 'MD_GT_DATE'
								WHEN e.expression='gte' and @dataTypeCode='STRING' then 'MD_GTE_STRING'
								WHEN e.expression='gte' and @dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
								WHEN e.expression='gte' and @dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
								WHEN e.expression='gte' and @dataTypeCode='DATE' then 'MD_GTE_DATE'
								WHEN e.expression='lt' and @dataTypeCode='STRING' then 'MD_LT_STRING'
								WHEN e.expression='lt' and @dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
								WHEN e.expression='lt' and @dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
								WHEN e.expression='lt' and @dataTypeCode='DATE' then 'MD_LT_DATE'
								WHEN e.expression='lte' and @dataTypeCode='STRING' then 'MD_LTE_STRING'
								WHEN e.expression='lte' and @dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
								WHEN e.expression='lte' and @dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
								WHEN e.expression='lte' and @dataTypeCode='DATE' then 'MD_LTE_DATE'
								WHEN e.expression='neq' and @dataTypeCode='STRING' then 'MD_NEQ_STRING'
								WHEN e.expression='neq' and @dataTypeCode='BIT' then 'MD_NEQ_BIT'
								WHEN e.expression='neq' and @dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
								WHEN e.expression='neq' and @dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
								WHEN e.expression='neq' and @dataTypeCode='DATE' then 'MD_NEQ_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
								WHEN e.expression='not_exists' and @dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
								WHEN e.expression='not_exists' and @dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
								WHEN e.expression='not_exists' and @dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
								WHEN e.expression='not_exists' and @dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
								WHEN e.expression='not_exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
							END
				OUTPUT INSERTED.orgID, INSERTED.conditionID INTO #tmpAffectedConditions (orgID, conditionID)
			FROM dbo.ams_virtualGroupConditions AS vgc
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON e.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
			AND vgc.orgID = @orgID;

			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- reprocess conditions based on field
			IF EXISTS (SELECT 1 FROM #tmpAffectedConditions) BEGIN
				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT orgID, NULL, conditionID
				FROM #tmpAffectedConditions;

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				TRUNCATE TABLE #tblMCQRun;
			END

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
		END

		-- if valueID is not null, there is a def value. 
		-- Anyone who doesnt have a value for this column needs this value.
		IF nullif(@valueID,0) is not null BEGIN
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
			CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY);

			insert into #tblMDDEF (memberID)
			select distinct m.memberid
			from dbo.ams_members as m
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
				except
			select distinct md.memberID
			from dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID;

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF;

			UPDATE m
			SET m.dateLastUpdated = getdate()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID;


			-- reprocess conditions based on field
			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT distinct c.orgID, m.memberID, c.conditionID
			from dbo.ams_virtualGroupConditions as C
			cross join #tblMDDEF as m
			where c.orgID = @orgID
			and C.fieldcode = 'md_' + Cast(@columnID as varchar(10));

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			TRUNCATE TABLE #tblMCQRun;

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END

		
		-- check custom field validation ranges against existing data
		IF @minChars is not null and @maxChars is not null BEGIN
			IF @dataTypeCode = 'STRING' BEGIN
				IF EXISTS(
					select top 1 valueID
					from dbo.ams_memberdatacolumnValues
					where columnID = @columnID
					and len(columnValueString) > 0
					and len(columnValueString) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
			IF @dataTypeCode = 'CONTENTOBJ' BEGIN
				IF EXISTS(
					select top 1 mdcv.valueID
					from dbo.ams_memberdatacolumnValues as mdcv
					inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
					inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
					inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
					where mdcv.columnID = @columnID
					and len(cv.rawContent) > 0
					and len(cv.rawContent) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
		END
		IF @minSelected is not null and @maxSelected is not null BEGIN
			IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
			AND EXISTS(
				select top 1 md.memberid
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID
				group by md.memberid
				having count(*) not between @minSelected and @maxSelected
			)
			RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1);
		END
		IF @minValueInt is not null and @maxValueInt is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueInteger is not null
				and columnValueInteger not between @minValueInt and @maxValueInt
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDecimal2 is not null
				and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDate is not null and @maxValueDate is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDate is not null
				and columnValueDate not between @minValueDate and @maxValueDate
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END


		-- if there was a change in columnname
		IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
			-- update member fields
			UPDATE dbo.ams_memberFields
			SET dbField = @columnName
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- update saved query fields
			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedQueriesFields AS qf
			INNER JOIN dbo.ams_savedQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedLinkedRecordsQueriesFields AS qf
			INNER JOIN dbo.ams_savedLinkedRecordsQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));
		
			-- update virtual group conditions
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END
		
	COMMIT TRAN;

	-- new data
	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date], [Linked Date Column], [Linked Date Compare Date],
		[Linked Date Compare Date Adv Formula], [Linked Date Adv Date], [Linked Date Adv Formula]
	)
	SELECT 'NEWVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate,
		ldc.columnName,  c.linkedDateCompareDate, ldc_af.afName, c.linkedDateAdvanceDate, ldc_adv_af.afName
	FROM ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	LEFT OUTER JOIN dbo.ams_memberDataColumns AS ldc ON ldc.orgID = @orgID
		AND ldc.columnID = c.linkedDateColumnID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_af ON ldc_af.siteID = @siteID
		AND ldc_af.afID = c.linkedDateCompareDateAFID
	LEFT OUTER JOIN dbo.af_advanceFormulas AS ldc_adv_af ON ldc_adv_af.siteID = @siteID
		AND ldc_adv_af.afID = c.linkedDateAdvanceAFID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

	-- audit log
	IF ISNULL(@msg,'') <> '' BEGIN
		SET @msg = 'Custom Field ' + STRING_ESCAPE(QUOTENAME(@oldColumnName),'json') + ' has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"MEMCF",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + @msg + '" } }');
	END

	-- recreate the view and cache tables	
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- execute linked date custom field rules after creating org views
	IF @runLinkedDateCustomFieldRule = 1
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	-- if this is a date field that is changing its name, check for subscription member date rules we may need to update
	IF @oldColumnName <> @columnName AND @dataTypeCode = 'DATE' BEGIN
	
		declare @tblJD TABLE (udid int PRIMARY KEY);
		insert into @tblJD (udid)
		select distinct udid 
		from customapps.dbo.schedTask_memberJoinDates as jd
		inner join membercentral.dbo.sites as s on s.siteCode = jd.siteCode
		where s.orgID = @orgID
		and ( joinDateFieldName = @oldColumnName 
			or rejoinDateFieldName = @oldColumnName 
			or droppedDateFieldName = @oldColumnName 
			or paidThruDateFieldName = @oldColumnName
			or renewalDateFieldName = @oldColumnName )

		IF @@ROWCOUNT = 0 GOTO on_done;

		UPDATE jd
		SET jd.joinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.joinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.rejoinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.rejoinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.droppedDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.droppedDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.paidThruDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.paidThruDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.renewalDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.renewalDateFieldName = @oldColumnName;
	END

	on_done:
	
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
