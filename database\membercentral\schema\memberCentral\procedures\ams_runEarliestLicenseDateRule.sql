ALTER PROC dbo.ams_runEarliestLicenseDateRule
@orgID int,
@memberID int,
@recordedByMemberID int,
@byPassQueue bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpLicenseTypeIDs') IS NOT NULL
		DROP TABLE #tmpLicenseTypeIDs;
	IF OBJECT_ID('tempdb..#tmpLicenseStatusIDs') IS NOT NULL
		DROP TABLE #tmpLicenseStatusIDs;
	CREATE TABLE #tmpLicenseTypeIDs (PLTypeID int);
	CREATE TABLE #tmpLicenseStatusIDs (PLStatusID int);

	DECLARE @activeMemberID int, @columnID int, @columnName varchar(128), @earliestLicenseDate date, @existingBarDate date;
	SELECT @activeMemberID = dbo.fn_getActiveMemberID(@memberID);

	select @columnID = led.columnID, @columnName = mdc.columnName
	from dbo.ams_memberProfessionalLicenseEarliestDate led
	inner join ams_memberDataColumns mdc on mdc.columnId = led.columnID
	where led.orgID = @orgID;

	IF @columnID is null
		RAISERROR('Date column not specified.',16,1);

	INSERT INTO #tmpLicenseTypeIDs (PLTypeID)
	select PLTypeID
	from dbo.ams_memberProfessionalLicenseEarliestDateTypes
	where orgID = @orgID
	and PLTypeID is not null;

	IF @@ROWCOUNT = 0 
		INSERT INTO #tmpLicenseTypeIDs (PLTypeID)
		SELECT PLTypeID 
		FROM dbo.ams_memberProfessionalLicenseTypes 
		WHERE orgID = @orgID;

	INSERT INTO #tmpLicenseStatusIDs (PLStatusID)
	select PLStatusID
	from dbo.ams_memberProfessionalLicenseEarliestDateTypes
	where orgID = @orgID
	and PLStatusID is not null;

	IF @@ROWCOUNT = 0 
		INSERT INTO #tmpLicenseStatusIDs (PLStatusID)
		SELECT PLStatusID 
		FROM dbo.ams_memberProfessionalLicenseStatuses 
		WHERE orgID = @orgID;

	-- update member
	-- when run for a single MemberID, this MUST be called from the context of PMI (via the ams_importMemberFromQueue)
	IF @memberID IS NOT NULL BEGIN
		SELECT @earliestLicenseDate = MIN(mpl.activeDate)
		FROM dbo.ams_memberProfessionalLicenses as mpl
		INNER JOIN #tmpLicenseTypeIDs AS lt ON lt.PLTypeID = mpl.PLTypeID
		INNER JOIN #tmpLicenseStatusIDs AS ls ON ls.PLStatusID = mpl.PLStatusID
		WHERE mpl.memberID = @activeMemberID;

		SELECT @existingBarDate = mdcv.columnValueDate
		FROM dbo.ams_memberData as md 
		INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID 
			and mdc.columnID = @columnID
		WHERE md.memberID = @activeMemberID;

		IF ISNULL(@earliestLicenseDate,'1/1/1900') <> ISNULL(@existingBarDate,'1/1/1900')
			EXEC dbo.ams_setMemberData @memberID=@activeMemberID, @orgID=@orgID, @columnName=@columnName, 
				@columnValue=@earliestLicenseDate, @recordedByMemberID=@recordedByMemberID, @byPassQueue=1;
		
		-- reprocess any applicable conditions without rolling up error
		IF @byPassQueue = 0 BEGIN			
			BEGIN TRY
				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT @orgID, @memberID, c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
			END TRY
			BEGIN CATCH
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH
		END ELSE BEGIN
			IF OBJECT_ID('tempdb..#tmpMCGConditions') IS NOT NULL BEGIN
				INSERT INTO #tmpMCGConditions (conditionID)
				SELECT distinct c.conditionID
				from dbo.ams_virtualGroupConditions as c
				where c.orgID = @orgID
				and c.fieldcode = 'md_' + cast(@columnID as varchar(10));
			END
		END
	END
	-- use PMI to mass update members
	ELSE BEGIN
		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
		CREATE TABLE #mc_PartialMemImport (membernumber varchar(50));

		DECLARE @sql varchar(max), @importResult xml, @errCount int, @environmentName varchar(50);
		SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

		EXEC ('ALTER TABLE #mc_PartialMemImport ADD [' + @columnName + '] date, rowID int;');

		set @sql = 'select memberNumber, earliestLicenseDate, ROW_NUMBER() over (order by memberNumber)
			from (
				select m.memberNumber, min(mpl.activeDate) as earliestLicenseDate, mdcv.columnValueDate as existingBarDate
				from dbo.ams_members as m
				left outer join dbo.ams_memberProfessionalLicenses as mpl 
					inner join #tmpLicenseStatusIDs as ls on ls.PLStatusID = mpl.PLStatusID 
					inner join #tmpLicenseTypeIDs as lt on lt.PLTypeID = mpl.PLTypeID 
				on mpl.memberid = m.memberid 
				left outer join dbo.ams_memberData as md 
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.columnID = ' + cast(@columnID as varchar(10)) + '
					on md.memberid = m.memberID
				where m.orgID = ' + cast(@orgID as varchar(4)) + ' 
				and m.memberid = m.activememberID 
				and m.status in (''A'',''I'')
				group by m.memberNumber, mdcv.columnValueDate
			) as tmp
			where isnull(earliestLicenseDate,''1/1/1900'') <> isnull(ExistingBarDate,''1/1/1900'')';

		INSERT INTO #mc_PartialMemImport
		EXEC(@sql);

		IF EXISTS (select 1 from #mc_PartialMemImport) BEGIN
			EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Manual Partial Update', 
				@runByMemberID=@recordedByMemberID, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, 
				@bypassRO=1, @finalMSGHeader='MemberCentral automatically', @emailSubject='Earliest License Date Rule Run Report',
				@environmentName=@environmentName, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;

			IF @errCount > 0
				RAISERROR('Error running partial member update.',16,1);
		END

		IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
			DROP TABLE #mc_PartialMemImport;
	END

	IF OBJECT_ID('tempdb..#tmpLicenseTypeIDs') IS NOT NULL
		DROP TABLE #tmpLicenseTypeIDs;
	IF OBJECT_ID('tempdb..#tmpLicenseStatusIDs') IS NOT NULL
		DROP TABLE #tmpLicenseStatusIDs;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
