ALTER PROC dbo.ams_exportMemberDataColumnStructure
@orgID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgCode varchar(10), @cmd varchar(4000), @svrName varchar(40);

	SELECT @orgCode = orgCode FROM dbo.organizations WHERE orgID = @orgID;
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgcode = @orgcode;

	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumns (orgCode, orgID, columnID, columnName, columnDesc, dataTypeUID, dataTypeCode, displayTypeCode, displayTypeUID, 
		allowNewValuesOnImport, allowNull, defaultValue<PERSON>, isReadOnly, allowMultiple, [uid],  minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, 
		minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate,
		linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID)
	select @orgCode, @orgID, f.columnID, f.columnName, f.columnDesc, mdata.uid, mdata.dataTypeCode, mdisp.displayTypeCode, mdisp.uid, f.allowNewValuesOnImport, 
		f.allowNull, f.defaultValueID, f.isReadOnly, f.allowMultiple, f.uid, f.minChars, f.maxChars, f.minSelected, f.maxSelected, f.minValueInt, f.maxValueInt, f.minValueDecimal2, 
		f.maxValueDecimal2, f.minValueDate, f.maxValueDate, 
		case when f.defaultValueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then mdcv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
				when 'DATE' then convert(varchar(10), mdcv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
				else null
				end
		else null
		end, fv.valueID, 
		case when fv.valueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then fv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), fv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), fv.columnValueInteger)
				when 'DATE' then convert(varchar(10), fv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), fv.columnValueBit)
			else null
			end 
		else null
		end as fieldValue,
		ldc.[uid], f.linkedDateCompareDate, af_c.[uid], f.linkedDateAdvanceDate, af_adv.[uid]
	from dbo.ams_memberDataColumns as f
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = f.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = f.defaultValueID and mdcv.columnID = f.columnID
	left outer join dbo.ams_memberDataColumnValues as fv on fv.columnID = f.columnID and mdisp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	left outer join dbo.ams_memberDataColumns as ldc on ldc.orgID = @orgID and ldc.columnID = f.linkedDateColumnID
	left outer join dbo.af_advanceFormulas as af_c on af_c.afID = f.linkedDateCompareDateAFID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.afID = f.linkedDateAdvanceAFID
	where f.orgID = @orgID
	order by f.columnName, fieldValue;

	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumnValues (orgCode, orgID, valueID, columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit)
	select smdc.orgCode, smdc.orgID, fv.valueID, smdc.columnID, fv.columnValueString, fv.columnValueDecimal2 , fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = smdc.defaultValueID and fv.columnID = smdc.columnID
	where smdc.orgID = @orgID
		union
	select smdc.orgCode, smdc.orgID, fv.valueID, smdc.columnID, fv.columnValueString, fv.columnValueDecimal2, fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = smdc.valueID and fv.columnID = smdc.columnID and smdc.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	where smdc.orgID = @orgID;

	-- export to file
	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, columnID, columnName, columnDesc, dataTypeUID, dataTypeCode, displayTypeCode, displayTypeUID, allowNewValuesOnImport, allowNull, defaultValueID, isReadOnly, allowMultiple, [uid],  minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate, linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID, finalAction FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_ams_memberDataColumns.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, useValue, finalAction FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_ams_memberDataColumnValues.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear tables
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
