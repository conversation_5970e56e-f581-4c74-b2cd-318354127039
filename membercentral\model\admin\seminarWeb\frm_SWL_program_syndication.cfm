<cfsavecontent variable="local.syndicationJS">
	<cfoutput>
	<script language="JavaScript">
		function saveSWLRateSyndication(callback) {
			mca_hideAlert('err_syndication');
			if (isSWProgramLocked()) return false;

			var saveRateSyndResult = function(r) {
				$('##frmSWLProgramSynd,##divSWLSyndicationSaveLoading').toggle();
				if (r.success && r.success.toLowerCase() == 'true'){
					swl_showDownloadAllRegistrantsForSyndicated = $("##allowSyndication:checked").length;
					$(".syndPrice").each(function(){
						if($(this).val().trim().length==0){
							$(this).parents('tr').remove();   
						}
					});
					if($("table##syndRate tbody tr").length==0){
						$("table##syndRate tbody").append('<tr id="emptyRow"><td colspan="4" class="text-center">No Rates Found</td></tr>');
					}
					
					<cfif local.hasManageOptInRights>
						if($('input[name="allowSyndication"]:checked').length && $("##programPartner option:selected").length == 0){
							$("##syndicationWarning").removeClass("d-none");
						}else if(($('input[name="allowSyndication"]:checked').length && $("##programPartner option:selected").length) || !$('input[name="allowSyndication"]:checked').length){
							$("##syndicationWarning").addClass("d-none");
						}
					</cfif>
					
					if(!$("##program-syndication .card-header:first ##saveResponse").length)
						$("##program-syndication .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
					$('##program-syndication .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-syndication .card-footer .save-button').prop('disabled',false);
					if(callback){
						callback();
					}
				} else {
					var arrReq = [];
					arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save Syndication Options.');
					mca_showAlert('err_syndication', arrReq.join('<br/>'));
				}
			};
			
			var arrReq = [];
			var xmlsynd = '<syndication>';
			var allowSyndication = $('input[name="allowSyndication"]:checked').length;
			if (allowSyndication == 1) {
				var allowOptInRateChange = $('##allowOptInRateChange').is(':checked');
				var hasRates = $("table##syndRate tbody tr:not(##emptyRow)").length > 0; 

				if(!allowOptInRateChange){
					if (programAdded){
						if(!hasRates)
							arrReq.push('Please either allow partner associations to create their own pricing or add at least one rate.');

						<cfif local.hasManageOptInRights>
							if($("##programPartner option:selected").length == 0)
								arrReq.push('Select Partner Sites to Opt In to this program.');
						</cfif>
					}

					var validSyndRateCount = 0;
					for (var i=1; i<=$("table##syndRate tbody tr:not(##emptyRow)").length; i++){
						if($('##syndGroup' + i) && $('##syndGroup' + i).val().trim() != '' && $('##syndPrice' + i).val().trim() == ''){
							arrReq.push('Enter valid syndication prices.');
							break;
						} else if ($('##syndGroup' + i) && $('##syndGroup' + i).val().trim() == '' && formatCurrency($('##syndPrice' + i).val()) == '0.00') {
							$('##syndPrice' + i).val('');
						} else if ($('##syndGroup' + i) && $('##syndGroup' + i).val().trim() != '' && $('##syndPrice' + i).val().trim() != '') {
							xmlsynd = xmlsynd + '<pricegroup><group>' + $('##syndGroup' + i).val().trim() + '</group><price>' + formatCurrency($('##syndPrice' + i).val()).replace(/,/g,'') + '</price></pricegroup>';
							validSyndRateCount++;
						}
					}
					if(!arrReq.length && validSyndRateCount == 0 && programAdded){
						arrReq.push('Please either allow partner associations to create their own pricing or define at least one valid rate.');
					}
				}
			}
			xmlsynd = xmlsynd + '</syndication>';

			if (arrReq.length) {
				$('##err_syndication').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_syndication').offset().top-120},500);
				if(programAdded)
					$('##nextButton').prop('disabled',false);
				else
					$('##program-syndication .card-footer .save-button').prop('disabled',false);
				return false;
			}
			
			if($('input[name="allowOptInRateChange"]:checked').length == 1){
				$("table##syndRate .syndRateRemove").trigger("click");
			}
			
			$('##frmSWLProgramSynd,##divSWLSyndicationSaveLoading').toggle();			
			
			var objParams = { programID:getSWProgramID(), programType:sw_itemtype, xmlsynd:xmlsynd, allowSyndication:allowSyndication,
				pushDefaultPricingToOptIns:$('input[name="allowOptInRateChange"]:checked').length? 0:1,
				allowOptInRateChange:$('input[name="allowOptInRateChange"]:checked').length };
			TS_AJX('ADMINSWCOMMON','saveSWProgramRateSyndication',objParams,saveRateSyndResult,saveRateSyndResult,40000,saveRateSyndResult);
		}
		
		function showAllowSyndication(){
			<cfif local.hasManageOptInRights>
				getSWProgramPartners();
			</cfif>
			if ($('##allowSyndication').is(':checked')) {
				$('##allowSyndicationHolder').removeClass("d-none");
			} else {
				$('##allowSyndicationHolder').addClass("d-none");
			}
		}
		
		function showAllowOptInRateChange(){
			if ($('##allowOptInRateChange').is(':checked')) {
				$('##allowOptInRateChangeHolder').addClass("d-none");
			} else {
				$('##allowOptInRateChangeHolder').removeClass("d-none");				
			}
		}
		function initSWLSyndication(){
			<cfif local.hasManageOptInRights>
				mca_setupSelect2ByID('programPartner');
				var programPartnerSelect = $('##programPartner');
				var clearSelectionArray = []; 
				programPartnerSelect
				.on('select2:select', function (e) {
					changeProgramPartnetEnrollChoiceColor();
					addSWProgramPartner(e.params.data.id,'swlsyndtab');
				})
				.on('select2:unselect', function (e) {
					changeProgramPartnetEnrollChoiceColor();
					var index = clearSelectionArray.indexOf(e.params.data.id);
					if (index !== -1) {
						clearSelectionArray.splice(index, 1);
					}else if($("##programPartner option[value='"+e.params.data.id+"'][isenrollmentexist!=0]").length==0){
						deleteSWProgramPartner(e.params.data.id,'swlsyndtab');
					}
				})
				.on('select2:clear', function (e) {
					var idValues = e.params.data.map(function(item) {clearSelectionArray.push(item.id);return item.id;});
					deleteSWProgramPartner(idValues.join(','),'swlsyndtab');
				}).on("select2:unselecting", function (e) {
					if ($("##programPartner option[value='"+e.params.args.data.id+"'][isenrollmentexist!=0]").length==1 && clearSelectionArray.length==0) {
						e.preventDefault();
					}
				});
			</cfif>
			<cfif local.hasManageSWLRatesRights and local.hasManageOptInRights and local.isPublisher and NOT val(local.qrySeminar.preventSeminarFees)>
				getSWProgramPartners();
			</cfif>
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.syndicationJS#">
<cfoutput>

<div id="err_syndication" class="alert alert-danger mb-2 mt-2 d-none"></div>
<form name="frmSWLProgramSynd" id="frmSWLProgramSynd" onsubmit="saveSWLRateSyndication(); return false;">
	<div class="text-right mb-2">
		<span id="saveSyndicationResponse"></span>
		<button type="button" name="btnSaveSWProgramSyndication" class="btn btn-sm btn-primary d-none" onclick="saveSWLRateSyndication();">Save Changes</button>
	</div>
	<div class="form-group mt-2 mb-3">
		<div class="custom-control custom-switch">
			<input type="checkbox" name="allowSyndication" id="allowSyndication" class="custom-control-input" onclick="showAllowSyndication();" <cfif val(local.qrySeminar.allowSyndication) is 1> checked="checked"</cfif>>
			<label class="custom-control-label" for="allowSyndication">
				I want to syndicate this program to other partner associations.
			</label>
			<div class="form-group my-2 <cfif local.qrySeminar.allowSyndication is 0>d-none</cfif>" id="allowSyndicationHolder">
				<div class="custom-control custom-switch mb-2">
					<input type="checkbox" name="allowOptInRateChange" id="allowOptInRateChange" class="custom-control-input" onclick="showAllowOptInRateChange();" <cfif val(local.qrySeminar.allowOptInRateChange) is 1> checked="checked"</cfif>>
					<label class="custom-control-label" for="allowOptInRateChange">
						I want to allow partner associations to create their own pricing.
					</label>
					<div class="form-group my-2 <cfif local.qrySeminar.allowOptInRateChange is 1>d-none</cfif>" id="allowOptInRateChangeHolder">
						<div class="mt-2">
							<a href="javascript:void(0)" onClick="addSyndicatedRate();"><i class="fa-regular fa-circle-plus"></i> Add Syndicated Rate</a>
						</div>				
						<cfif len(local.qrySeminar.priceSyndication)>
							<cfset local.arrGroupNodes = XMLSearch(local.qrySeminar.priceSyndication,"//pricegroup")>
						<cfelse>
							<cfset local.arrGroupNodes = arrayNew(1)>
						</cfif>
						<table class="table table-sm table-borderless my-2" id="syndRate">
							<thead>
								<tr>
									<th width="1%"></th>
									<th>Rate Name</th>
									<th>Rate</th>
									<th width="1%"></th>
								</tr>
							</thead>
							<tbody>
							<cfif arrayLen(local.arrGroupNodes)>
								<cfloop from="1" to="#arrayLen(local.arrGroupNodes)#" index="local.thisNode">
									<tr>
										<td>#local.thisNode#</td>
										<td><input type="text" id="syndGroup#local.thisNode#" class="form-control form-control-sm" name="syndGroup#local.thisNode#" value="#local.arrGroupNodes[local.thisNode].group.XMLText#" size="24"></td>
										<td><input type="text" id="syndPrice#local.thisNode#" class="form-control form-control-sm syndPrice" name="syndPrice#local.thisNode#" value="#NumberFormat(ReReplace(local.arrGroupNodes[local.thisNode].price.XMLText,"[^0-9\.]","","ALL"),'9.99')#" size="6" onBlur="if (this.value != '') this.value=formatCurrency(this.value);"></td>
										<td><i class="fa-solid fa-circle-minus syndRateRemove" onClick="removeSWRateSyndication(this)"></i></td>
									</tr>
								</cfloop>
							<cfelse>
								<tr id="emptyRow"><td colspan="4" class="text-center">No Rates Found</td></tr>
							</cfif>
							</tbody>
						</table>
					</div>
				</div>
				<cfif local.hasManageOptInRights>
					<b>Share Program to these Partner Sites</b>
					<div class="d-flex mt-2">
						<a href="javascript:void(0)" onClick="selectAllSWProgramPartners('swlsyndtab');"><i class="fa-regular fa-circle-plus"></i> Select All Partner Sites</a>
						<div id="divProgramPartnerSaveProgress" class="ml-auto d-none">
							<div class="d-flex align-items-center text-primary">
								<span>Please wait while we save the partner sites selection</span>
								<div class="spinner-border spinner-border-sm ml-1" role="status">
									<span class="sr-only">Loading...</span>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group mt-2 mb-3">
						<div class="form-label-group">
							<div class="input-group">
								<select name="programPartner" id="programPartner" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="true" placeholder="No Program Partner(s)"></select>								
								<label for="programPartner">Choose Partner(s)</label>
							</div>
						</div>
					</div>
					<div class="alert alert-info">
					<strong>Note: </strong>This association cannot opt-out of a seminar if registrants have enrolled in that seminar through this website.
					Site Administrators of associations opted-in to this seminar may have the ability to assign a $0 Staff Comp Rate to this seminar.
					</div>
				</cfif>
			</div>
		</div>
		<div class="alert alert-info mt-3 d-none" id="noSyndicatePrograms">
			Your site is not currently setup to syndicate programs
		</div>
	</div>
</form>
<div id="divSWLSyndicationSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these syndication.</b>
		<br/>
	</div>
</div>
</cfoutput>