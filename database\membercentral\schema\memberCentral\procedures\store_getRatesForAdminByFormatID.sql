ALTER PROC dbo.store_getRatesForAdminByFormatID
@formatid int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @formatRecordCount int;

	select @formatRecordCount = count(Formatid)
	from dbo.store_rates
	where formatid = @formatid;

	IF @formatRecordCount > 0
		select r.rateid, r.siteresourceID, r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate,
			g.groupID, g.groupName, srrc.include, pf.Formatid, pf.Itemid, pf.Name as formatName,
			pf.GLAccountID as formatGLAccountID, pf.shippingGLAccountID as formatShippingGLAccountID,
			pf.status, pf.offerAffirmations, pf.isAffirmation, pf.quantity, pf.formatOrder, pf.inventory
		from dbo.store_rates as r
		right outer join dbo.store_productformats as pf on r.formatID = pf.formatID
		inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
		inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active'
		left outer join dbo.cms_siteResourceRightsCache as srrc
			inner join ams_groups g on g.groupID = srrc.groupID	on srrc.resourceid = r.siteresourceID
			and srrc.functionID = @qualifyFID
			and srrc.siteID = sr.siteID
		where r.formatid = @formatid
		and r.rate >= 0
		order by r.rateOrder;

	ELSE
		select 0 as rateid, 0 as siteresourceID, 0 as GLAccountID, null as rateName, 0 as rate, 
			null as startDate, null as endDate, 0 as groupID, null as groupName, 1 as include,
			pf.Formatid, pf.Itemid, pf.Name as formatName, 
			pf.GLAccountID as formatGLAccountID, pf.shippingGLAccountID as formatShippingGLAccountID,
			pf.status, pf.offerAffirmations, pf.isAffirmation, pf.quantity, pf.formatOrder, pf.inventory
		from dbo.store_productformats as pf
		where pf.formatID = @formatid;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
