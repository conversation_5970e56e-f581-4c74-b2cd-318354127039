ALTER PROC dbo.ams_prepareMemberDataColumnImport
@siteID int,
@pathToImport varchar(400),
@importResult XML OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpOrgAddMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgAddMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpNewColumnNames') IS NOT NULL
		DROP TABLE #tmpNewColumnNames;

	CREATE TABLE #tblImportErrors (rowID int IDENTITY(1,1), msg varchar(600), errorCode varchar(30));
	CREATE TABLE #tmpOrgMemberDataColumns (columnID int, [uid] uniqueidentifier, columnName varchar(128), columnDesc varchar(255), 
		dataTypeUID uniqueidentifier, displayTypeUID uniqueidentifier, dataTypeCode varchar(20), displayTypeCode varchar(20), 
		allowNewValuesOnImport bit, allowNull bit, isReadOnly bit, allowMultiple bit, minChars int, maxChars int, minSelected int, 
		maxSelected int, minValueInt int, maxValueInt int, minValueDecimal2 decimal(14,2), maxValueDecimal2 decimal(14,2), 
		minValueDate date, maxValueDate date, defaultValueID int, defaultValue varchar(255), valueID int, fieldValue varchar(255), 
		linkedDateColumnUID uniqueidentifier, linkedDateCompareDate date, linkedDateCompareDateAFUID uniqueidentifier, 
		linkedDateAdvanceDate date, linkedDateAdvanceAFUID uniqueidentifier);
	CREATE TABLE #tmpOrgMemberDataColumnValues (valueID int, columnID int, columnValueString varchar(255), 
		columnValueDecimal2 decimal(14,2), columnValueInteger int, columnvalueDate date, columnValueBit bit, columnValueSiteResourceID int);
	CREATE TABLE #tmpOrgAddMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgUpdateMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgDeleteMemberDataColumns ([uid] uniqueidentifier, columnName varchar(128));
	CREATE TABLE #tmpOrgDeleteMemberDataColumnValues (columnUID uniqueidentifier, columnName varchar(128), valueID int, 
		[value] varchar(255), useCount int);
	CREATE TABLE #tmpNewColumnNames (columnUID uniqueidentifier, columnName varchar(128), isNewColumn bit);

	DECLARE @orgID int, @orgCode varchar(10), @cmd varchar(400), @svrName varchar(40);
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	SELECT @orgID = o.orgID, @orgCode = o.orgCode
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	-- ensure file is present
	IF dbo.fn_FileExists(@pathToImport + 'sync_ams_memberDataColumns.bcp') = 0 OR dbo.fn_FileExists(@pathToImport + 'sync_ams_memberDataColumnValues.bcp') = 0 BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgcode = @orgcode;

	-- import data
	SET @cmd = 'bcp datatransfer.dbo.sync_ams_memberDataColumns in ' + @pathToImport + 'sync_ams_memberDataColumns.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_ams_memberDataColumnValues in ' + @pathToImport + 'sync_ams_memberDataColumnValues.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_ams_memberDataColumns SET orgID = @orgID WHERE orgCode = @orgCode;
	UPDATE datatransfer.dbo.sync_ams_memberDataColumnValues SET orgID = @orgID WHERE orgCode = @orgCode;

	-- existing org member data columns
	INSERT INTO #tmpOrgMemberDataColumns (columnID, [uid], columnName, columnDesc, dataTypeUID, displayTypeUID, dataTypeCode, displayTypeCode, allowNewValuesOnImport, allowNull, 
		isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, 
		defaultValueID, defaultValue, valueID, fieldValue, linkedDateColumnUID, linkedDateCompareDate,linkedDateCompareDateAFUID, linkedDateAdvanceDate, linkedDateAdvanceAFUID)
	select f.columnID, f.uid, f.columnName, f.columnDesc, mdata.uid, mdisp.uid, mdata.dataTypeCode, mdisp.displayTypeCode, f.allowNewValuesOnImport, f.allowNull, f.isReadOnly, 
		f.allowMultiple, f.minChars, f.maxChars, f.minSelected, f.maxSelected, f.minValueInt, f.maxValueInt, f.minValueDecimal2, f.maxValueDecimal2, f.minValueDate, f.maxValueDate, 
		f.defaultValueID, 
		case when f.defaultValueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then mdcv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
				when 'DATE' then convert(varchar(10), mdcv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
				else null
				end
		else null
		end, fv.valueID, 
		case when fv.valueID is not null then
			case mdata.dataTypeCode
				when 'STRING' then fv.columnValueString
				when 'DECIMAL2' then convert(varchar(255), fv.columnValueDecimal2)
				when 'INTEGER' then convert(varchar(255), fv.columnValueInteger)
				when 'DATE' then convert(varchar(10), fv.columnValueDate, 101)
				when 'BIT' then convert(varchar(255), fv.columnValueBit)
			else null
			end 
		else null
		end as fieldValue,
		ldc.[uid], f.linkedDateCompareDate, af_c.[uid], f.linkedDateAdvanceDate, af_adv.[uid]
	from dbo.ams_memberDataColumns as f
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = f.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = f.displayTypeID
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = f.defaultValueID and mdcv.columnID = f.columnID
	left outer join dbo.ams_memberDataColumnValues as fv on fv.columnID = f.columnID and mdisp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	left outer join dbo.ams_memberDataColumns as ldc on ldc.orgID = @orgID and ldc.columnID = f.linkedDateColumnID
	left outer join dbo.af_advanceFormulas as af_c on af_c.afID = f.linkedDateCompareDateAFID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.afID = f.linkedDateAdvanceAFID
	where f.orgID = @orgID
	order by f.columnName, fieldValue;

	INSERT INTO #tmpOrgMemberDataColumnValues (valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, columnValueSiteResourceID)
	select tmp.valueID, tmp.columnID, fv.columnValueString, fv.columnValueDecimal2 , fv.columnValueInteger, fv.columnvalueDate, fv.columnValueBit, fv.columnValueSiteResourceID
	from #tmpOrgMemberDataColumns as tmp
	inner join dbo.ams_memberDataColumnValues as fv on fv.valueID = tmp.valueID;

	/*** Process the Data for Comparisons ***/
	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column data type UID does not match an existing data type UID', 'INVALIDDATATYPE'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.dataTypeUID
	where smdc.orgID = @orgID
	and mdata.dataTypeID is null;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column display type UID does not match an existing display type UID', 'INVALIDDISPLAYTYPE'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.uid = smdc.displayTypeUID
	where smdc.orgID = @orgID
	and mdisp.displayTypeID is null;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct 'Advance Formula UID [' + CAST(smdc.linkedDateCompareDateAFUID AS varchar(36)) + '] does not match an existing Advance Formula UID', 'INVALIDAFUID'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.af_advanceFormulas as af on af.siteID = @siteID and af.[uid] = smdc.linkedDateCompareDateAFUID
	where smdc.orgID = @orgID
	and smdc.linkedDateCompareDateAFUID is not null
	and af.afID is null;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct 'Advance Formula UID [' + CAST(smdc.linkedDateAdvanceAFUID AS varchar(36)) + '] does not match an existing Advance Formula UID', 'INVALIDAFUID'
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join dbo.af_advanceFormulas as af on af.siteID = @siteID and af.[uid] = smdc.linkedDateAdvanceAFUID
	where smdc.orgID = @orgID
	and smdc.linkedDateAdvanceAFUID is not null
	and af.afID is null;

	IF EXISTS (SELECT 1 FROM #tblImportErrors)
		GOTO on_done;

	-- new fields
	INSERT INTO #tmpOrgAddMemberDataColumns ([uid], columnName)
	select distinct smdc.[uid], smdc.columnName
	from dataTransfer.dbo.sync_ams_memberDataColumns as smdc
	left outer join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = smdc.[uid]
	where smdc.orgID = @orgID
	and mdc.[uid] is null;

	-- find fields to be updated
	INSERT INTO #tmpOrgUpdateMemberDataColumns ([uid], columnName)
	SELECT DISTINCT [uid], columnName
	from (
		SELECT smdc.[uid], smdc.columnName, smdc.columnDesc, smdc.dataTypeCode, smdc.displayTypeCode, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
			smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
			smdc.minValueDate, smdc.maxValueDate, smdc.defaultValue, smdc.fieldValue, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate,
			smdc.linkedDateCompareDateAFUID, smdc.linkedDateAdvanceDate, smdc.linkedDateAdvanceAFUID
		FROM dataTransfer.dbo.sync_ams_memberDataColumns as smdc
		LEFT OUTER JOIN #tmpOrgAddMemberDataColumns as tmp on tmp.[uid] = smdc.[uid]
		WHERE smdc.orgID = @orgID
		AND tmp.[uid] IS NULL
			EXCEPT
		SELECT tmp.[uid], tmp.columnName, tmp.columnDesc, tmp.dataTypeCode, tmp.displayTypeCode, tmp.allowNewValuesOnImport, tmp.allowNull, tmp.isReadOnly, tmp.allowMultiple, tmp.minChars, tmp.maxChars, 
			tmp.minSelected, tmp.maxSelected, tmp.minValueInt, tmp.maxValueInt, tmp.minValueDecimal2, tmp.maxValueDecimal2, tmp.minValueDate, tmp.maxValueDate, tmp.defaultValue, tmp.fieldValue,
			tmp.linkedDateColumnUID, tmp.linkedDateCompareDate, tmp.linkedDateCompareDateAFUID, tmp.linkedDateAdvanceDate, tmp.linkedDateAdvanceAFUID
		FROM #tmpOrgMemberDataColumns as tmp
		INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
	) tmp;

	-- flip and test incase the org has more fields
	INSERT INTO #tmpOrgUpdateMemberDataColumns ([uid], columnName)
	SELECT DISTINCT [uid], columnName
	FROM (
		SELECT tmp.[uid], tmp.columnName, tmp.columnDesc, tmp.dataTypeCode, tmp.displayTypeCode, tmp.allowNewValuesOnImport, tmp.allowNull, tmp.isReadOnly, tmp.allowMultiple, tmp.minChars, tmp.maxChars, 
			tmp.minSelected, tmp.maxSelected, tmp.minValueInt, tmp.maxValueInt, tmp.minValueDecimal2, tmp.maxValueDecimal2, tmp.minValueDate, tmp.maxValueDate, tmp.defaultValue, tmp.fieldValue,
			tmp.linkedDateColumnUID, tmp.linkedDateCompareDate, tmp.linkedDateCompareDateAFUID, tmp.linkedDateAdvanceDate, tmp.linkedDateAdvanceAFUID
		FROM #tmpOrgMemberDataColumns as tmp
		INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			EXCEPT
		SELECT smdc.[uid], smdc.columnName, smdc.columnDesc, smdc.dataTypeCode, smdc.displayTypeCode, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
			smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
			smdc.minValueDate, smdc.maxValueDate, smdc.defaultValue, smdc.fieldValue, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate,
			smdc.linkedDateCompareDateAFUID, smdc.linkedDateAdvanceDate, smdc.linkedDateAdvanceAFUID
		FROM dataTransfer.dbo.sync_ams_memberDataColumns as smdc
		LEFT OUTER JOIN #tmpOrgAddMemberDataColumns as tmp on tmp.[uid] = smdc.[uid]
		WHERE smdc.orgID = @orgID
		AND tmp.[uid] IS NULL
	) tmp
	WHERE NOT EXISTS (SELECT 1 FROM #tmpOrgUpdateMemberDataColumns WHERE [uid] = tmp.[uid]);

	-- to be deleted fields
	INSERT INTO #tmpOrgDeleteMemberDataColumns ([uid], columnName)
	SELECT DISTINCT mdc.[uid], mdc.columnName
	FROM #tmpOrgMemberDataColumns AS mdc
	LEFT OUTER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID 
		AND smdc.[uid] = mdc.[uid]
	WHERE smdc.columnID IS NULL;

	-- validate new columnNames
	INSERT INTO #tmpNewColumnNames (columnUID, columnName, isNewColumn)
	select [uid], columnName, 1
	from #tmpOrgAddMemberDataColumns
		union all
	select [uid], columnName, 0
	from #tmpOrgUpdateMemberDataColumns;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select 'Duplicate ' + columnName + ' column found', 'DUPLICATECOLNAME'
	from #tmpNewColumnNames
	group by columnName	
	having count(columnName) > 1;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- remove to be deleted columns
	DELETE tmp
	FROM #tmpNewColumnNames as tmp
	INNER JOIN #tmpOrgDeleteMemberDataColumns as tmpD on tmpD.columnName = tmp.columnName;

	-- remove existing column names
	DELETE tmp
	FROM #tmpNewColumnNames as tmp
	INNER JOIN #tmpOrgMemberDataColumns as tmpE on tmpE.columnName = tmp.columnName;

	INSERT INTO #tblImportErrors (msg, errorCode)
	select columnName + ' column name is invalid.', 'INVALIDCOLUMNNAME'
	from #tmpNewColumnNames
	where dbo.fn_ams_isValidNewMemberViewColumn(@orgID,columnName) = 1;

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- if changing the display type
	-- err if any expressions are not 1,2,7,8 now that it will be a select
	INSERT INTO #tblImportErrors (msg, errorCode)
	select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the selected display type', 'INVALIDVGCDISPLAYTYPE'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and expressionID not in (1,2,7,8)
	where mdc.dataTypeCode <> 'BIT' 
	and smdc.dataTypeCode <> 'BIT'
	and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
	and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX');

	IF @@ROWCOUNT > 0
		GOTO on_done;

	-- if changing the data type
	IF EXISTS (select smdc.columnName 
				from #tmpOrgUpdateMemberDataColumns as tmp
				inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
				inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
				where mdc.dataTypeCode <> smdc.dataTypeCode) BEGIN
		
		-- string to decimal
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueString as decimal(14,2))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Decimal Number (2) data type.','INVALIDSTRINGTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- string to integer
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueInteger = cast(mdcv.columnValueString as int)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'INTEGER';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Whole Number data type.','INVALIDSTRINGTOINTEGER');
			GOTO on_done;
		END CATCH

		-- string to date
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDate = cast(mdcv.columnValueString as date)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'DATE';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Date data type.','INVALIDSTRINGTODATE');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the ' + mdata.dataType + ' data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.displayTypeUID
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (9,10)
		where mdc.dataTypeCode = 'STRING'
		and smdc.dataTypeCode IN ('INTEGER','DECIMAL2','DATE');

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- string to bit
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueBit = cast(mdcv.columnValueString as bit)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'STRING'
			AND smdc.dataTypeCode = 'BIT';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are string values not compatible with the Boolean data type.','INVALIDSTRINGTOBIT');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Boolean data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (3,4,5,6,9,10)
		where mdc.dataTypeCode = 'STRING'
		and smdc.dataTypeCode= 'BIT';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- decimal2 to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueDecimal2 as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'DECIMAL2'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are decimal values not compatible with the Text String data type.','INVALIDDECIMAL2TOSTRING');
			GOTO on_done;
		END CATCH

		-- integer to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Text String data type.','INVALIDINTEGERTOSTRING');
			GOTO on_done;
		END CATCH

		-- integer to decimal2
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Decimal Number (2) data type.','INVALIDINTEGERTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- integer to bit
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueBit = cast(mdcv.columnValueInteger as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'INTEGER'
			AND smdc.dataTypeCode = 'BIT';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are whole number values not compatible with the Boolean data type.','INVALIDINTEGERTOBIT');
			GOTO on_done;
		END CATCH
		

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Boolean data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (3,4,5,6)
		where mdc.dataTypeCode = 'INTEGER'
		and smdc.dataTypeCode= 'BIT';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- date to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = convert(varchar(10),mdcv.columnValueDate,101)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'DATE'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are date values not compatible with the Text String data type.','INVALIDDATETOSTRING');
			GOTO on_done;
		END CATCH

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column exists in group assignment conditions that are not compatible with the Text String data type', 'INVALIDVGCDATATYPE'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
			and expressionID in (11,12)
		where mdc.dataTypeCode = 'DATE'
		and smdc.dataTypeCode= 'STRING';

		IF @@ROWCOUNT > 0
			GOTO on_done;

		-- bit to string
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueString = cast(mdcv.columnValueBit as varchar(255))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'STRING';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Text String data type.','INVALIDBITTOSTRING');
			GOTO on_done;
		END CATCH

		-- bit to decimal2
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueBit as decimal(14,2))
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'DECIMAL2';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Text Decimal Number (2) data type.','INVALIDBITTODECIMAL2');
			GOTO on_done;
		END CATCH

		-- bit to integer
		BEGIN TRY
			UPDATE mdcv
			SET mdcv.columnValueInteger = cast(mdcv.columnValueBit as int)
			FROM #tmpOrgUpdateMemberDataColumns as tmp
			INNER JOIN #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
			INNER JOIN #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
			WHERE mdc.dataTypeCode = 'BIT'
			AND smdc.dataTypeCode = 'INTEGER';
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg, errorCode)
			VALUES ('There are boolean values not compatible with the Whole Number data type.','INVALIDBITTOINTEGER');
			GOTO on_done;
		END CATCH
	END

	-- check custom field validation ranges against existing data
	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minChars is not null and maxChars is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDSTRINGDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'STRING'
		and smdc.minChars is not null 
		and  smdc.maxChars is not null
		and len(mdcv.columnValueString) > 0
		and len(mdcv.columnValueString) not between smdc.minChars and smdc.maxChars;

		IF @@ROWCOUNT > 0
			GOTO on_done;

		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDSTRINGDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
		inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
		inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'CONTENTOBJ'
		and smdc.minChars is not null 
		and  smdc.maxChars is not null
		and len(cv.rawContent) > 0
		and len(cv.rawContent) not between smdc.minChars and smdc.maxChars;
		
		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minSelected is not null and maxSelected is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select columnName + ' column has existing members that have field options that are outside the data validation range.', 'INVALIDFIELDOPTS'
		from (
			select md.memberID, count(md.dataID) as optCount, smdc.columnName, smdc.minSelected, smdc.maxSelected
			from #tmpOrgUpdateMemberDataColumns as tmp
			inner join (
				select distinct [uid], columnID
				from #tmpOrgMemberDataColumns 
				where allowMultiple = 1 ) as mdc on mdc.[uid] = tmp.[uid] 
			inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
			inner join (
				select distinct [uid], columnName, minSelected, maxSelected
				from dataTransfer.dbo.sync_ams_memberDataColumns
				where orgID = @orgID 
				and minSelected is not null
				and maxSelected is not null ) as smdc on smdc.[uid] = tmp.[uid]
			group by md.memberID, smdc.columnName, smdc.minSelected, smdc.maxSelected
			having count(*) not between smdc.minSelected and smdc.maxSelected
		) as tmp;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END
	
	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueInt is not null and maxValueInt is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'INTEGER'
		and smdc.minValueInt is not null 
		and smdc.maxValueInt is not null
		and mdcv.columnValueInteger is not null
		and mdcv.columnValueInteger not between smdc.minValueInt and smdc.maxValueInt;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueDecimal2 is not null and maxValueDecimal2 is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'DECIMAL2'
		and smdc.minValueDecimal2 is not null 
		and smdc.maxValueDecimal2 is not null
		and mdcv.columnValueDecimal2 is not null
		and mdcv.columnValueDecimal2 not between smdc.minValueDecimal2 and smdc.maxValueDecimal2;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END

	IF EXISTS (select 1 from dataTransfer.dbo.sync_ams_memberDataColumns where orgID = @orgID and minValueDate is not null and maxValueDate is not null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode)
		select distinct smdc.columnName + ' column has existing values for this column that are outside the data validation range.', 'INVALIDINTEGERDATA'
		from #tmpOrgUpdateMemberDataColumns as tmp
		inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
		inner join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
		inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		where smdc.dataTypeCode= 'DECIMAL2'
		and smdc.minValueDate is not null 
		and smdc.maxValueDate is not null
		and mdcv.columnValueDate is not null
		and mdcv.columnValueDate not between smdc.minValueDate and smdc.maxValueDate;

		IF @@ROWCOUNT > 0
			GOTO on_done;
	END
	
	-- if changing the display type from non-options to options we need to create column values for those condition values that dont yet exist as column values
	INSERT INTO datatransfer.dbo.sync_ams_memberDataColumnValues (orgCode, orgID, valueID, columnID, columnValueString, columnValueDecimal2 , columnValueInteger, columnvalueDate, columnValueBit, finalAction)
	select @orgCode, @orgID, null as valueID, smdc.columnID, vgcv.conditionValue, null , null, null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 1
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueString = vgcv.conditionValue
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, cast(vgcv.conditionValue as decimal(14,2)), null, null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 2
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(14,2))
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, null, cast(vgcv.conditionValue as int), null, null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 3
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueInteger = cast(vgcv.conditionValue as int)
	where mdcv.valueID is null
		union
	select @orgCode, @orgID, null as valueID, smdc.columnID, null, null, null, cast(vgcv.conditionValue as date), null, 'A'
	from #tmpOrgUpdateMemberDataColumns as tmp
	inner join #tmpOrgMemberDataColumns as mdc on mdc.[uid] = tmp.[uid]
	inner join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = tmp.[uid]
		and mdc.dataTypeCode <> 'BIT' 
		and smdc.dataTypeCode <> 'BIT'
		and mdc.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') 
		and smdc.displayTypeCode in ('RADIO','SELECT','CHECKBOX')
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10)) 
		and vgc.dataTypeID = 4
		and vgc.expressionID in (1,2)
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	left outer join #tmpOrgMemberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID and mdcv.columnvalueDate = cast(vgcv.conditionValue as date)
	where mdcv.valueID is null;

	-- update finalAction
	-- new fields
	UPDATE smdc
	SET smdc.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumns AS smdc
	INNER JOIN #tmpOrgAddMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid]
	WHERE smdc.orgID = @orgID
	AND smdc.finalAction IS NULL;

	-- new field values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'A'
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL;

	-- new field default values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'A'
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL;

	-- update fields
	UPDATE smdc
	SET smdc.finalAction = 'C'
	FROM dataTransfer.dbo.sync_ams_memberDataColumns AS smdc
	INNER JOIN #tmpOrgUpdateMemberDataColumns AS tmp ON tmp.[uid] = smdc.[uid]
	WHERE smdc.orgID = @orgID
	AND smdc.finalAction IS NULL;

	-- update useValue for update field values
	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'STRING' 
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.columnValueString = mdcv.columnValueString;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'STRING' 
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.columnValueString = mdcv.columnValueString COLLATE Latin1_General_CS_AS;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'DECIMAL2' 
	AND smdcv.columnValueDecimal2 = mdcv.columnValueDecimal2;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'INTEGER' 
	AND smdcv.columnValueInteger = mdcv.columnValueInteger;
	
	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'DATE' 
	AND smdcv.columnValueDate = mdcv.columnValueDate;

	UPDATE smdcv
	SET smdcv.useValue = mdcv.valueID
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
		AND smdc.orgID = @orgID
		AND smdc.finalAction = 'C'
	INNER JOIN #tmpOrgMemberDataColumns AS mdc ON mdc.[uid] = smdc.[uid] AND ISNULL(mdc.fieldValue,'') = ISNULL(smdc.fieldValue,'')
	INNER JOIN #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID
	WHERE smdcv.useValue IS NULL
	AND smdc.dataTypeCode = 'BIT' 
	AND smdcv.columnValueBit = mdcv.columnValueBit;

	-- new field values for update fields
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'C'
	AND smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL
	AND smdcv.useValue IS NULL;

	-- update finalAction for update field default values
	UPDATE smdcv
	SET smdcv.finalAction = 'A'
	FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
	INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumns AS smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
	WHERE smdcv.orgID = @orgID
	AND smdc.finalAction = 'C'
	AND smdc.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
	AND smdcv.finalAction IS NULL
	AND smdcv.useValue IS NULL;

	-- delete field values
	INSERT INTO #tmpOrgDeleteMemberDataColumnValues (columnUID, columnName, valueID, value, useCount)
	select mdc.[uid], mdc.columnName, mdc.valueID, mdc.fieldValue, count(distinct md.dataID)
	from #tmpOrgMemberDataColumns AS mdc
	inner join #tmpOrgMemberDataColumnValues AS mdcv ON mdcv.valueID = mdc.valueID 
	inner join dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
		and smdc.finalAction = 'C'
	left outer join dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.columnID = smdc.columnID
		and smdcv.useValue = mdcv.valueID
	left outer join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	where smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	and smdc.dataTypeCode <> 'BIT'
	and smdcv.valueID is null
	group by mdc.[uid], mdc.columnName, mdc.valueID, mdc.fieldValue;

	on_done:
	-- return the xml results
	SELECT @importResult = (
		SELECT GETDATE() AS "@date",

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			FROM dataTransfer.dbo.sync_ams_memberDataColumns
			WHERE orgID = @orgID
			AND finalAction = 'A'
			FOR XML path('field'), ROOT('newfields'), TYPE),'<newfields/>'),

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			FROM dataTransfer.dbo.sync_ams_memberDataColumns
			WHERE orgID = @orgID
			AND finalAction = 'C'
			FOR XML path('field'), ROOT('updatefields'), TYPE),'<updatefields/>'),

			ISNULL((SELECT DISTINCT [uid] AS "@uid", columnName AS "@columnName"
			from #tmpOrgDeleteMemberDataColumns
			FOR XML path('field'), ROOT('removefields'), TYPE),'<removefields/>'),

			ISNULL((SELECT DISTINCT columnUID AS "@uid", columnName AS "@columnName", value AS "@value", useCount AS "@useCount"
			from #tmpOrgDeleteMemberDataColumnValues
			FOR XML path('value'), ROOT('removevalues'), TYPE),'<removevalues/>'),
			
			ISNULL((SELECT dbo.fn_RegExReplace(ISNULL(msg,''),'[^\x20-\x7E]','') AS "@msg", errorCode AS "@errorcode"
			FROM #tblImportErrors
			ORDER BY MSG
			FOR XML path('error'), ROOT('errors'), TYPE),'<errors/>')

		FOR XML PATH('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpOrgAddMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgAddMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteMemberDataColumnValues') IS NOT NULL
		DROP TABLE #tmpOrgDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#tmpNewColumnNames') IS NOT NULL
		DROP TABLE #tmpNewColumnNames;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
