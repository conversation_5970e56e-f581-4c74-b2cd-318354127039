<cfsavecontent variable="local.js">
	<cfoutput>
	<style type="text/css">
	div.validationDiv { margin-top:30px; }
	div##cboxoptDiv, div##trDefaultValue { margin-top:25px; }
	div##cboxoptDiv div, div##trDefaultValue > div { margin:6px 0 6px 20px; }	
	</style>

	<script language="javascript">
	var CFSFieldsTable;
	<!--- data types array --->
	objdatatypecode = [];
	<cfloop query="local.qryDisplayTypes">
		objdatatypecode['#local.qryDisplayTypes.dataTypeCode#'] = [#left(local.qryDisplayTypes.displayTypeCodes,len(local.qryDisplayTypes.displayTypeCodes)-1)#];
	</cfloop>

	function setDataTypeOptions() {
		$("##dataType").children().remove();
		var dtoptions = '';
		<cfloop query="local.qryColumnDataTypes">
			<cfif local.columnID is 0 
				OR (local.qryData.dataTypeCode eq "STRING" and listFindNoCase("STRING,DECIMAL2,INTEGER,DATE,BIT",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "DECIMAL2" and listFindNoCase("DECIMAL2,STRING",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "INTEGER" and listFindNoCase("INTEGER,STRING,DECIMAL2,BIT",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "DATE" and listFindNoCase("DATE,STRING",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "BIT" and listFindNoCase("BIT,STRING,DECIMAL2,INTEGER",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "CONTENTOBJ" and listFindNoCase("CONTENTOBJ",local.qryColumnDataTypes.datatypeCode))
				OR (local.qryData.dataTypeCode eq "DOCUMENTOBJ" and listFindNoCase("DOCUMENTOBJ",local.qryColumnDataTypes.datatypeCode))>
				dtoptions += '<option value="#local.qryColumnDataTypes.datatypeCode#">#local.qryColumnDataTypes.dataType#</option>'; 
			</cfif>
		</cfloop>
		$("##dataType").html(dtoptions);
	}
	function setDisplayTypeOptions(dt) {
		<!--- these fields dont support multiple values --->
		if (dt != 'STRING' && dt != 'DECIMAL2' && dt != 'INTEGER') {
			$('##allowMultiple').attr('checked',false);
			$('##trAllowMultiple').hide();
		}

		var disoptions = '';
		for (var i=0; i < objdatatypecode[dt].length; i++) {
			if ($('##allowMultiple').is(':checked')) {
				if (objdatatypecode[dt][i].split('|')[0] == 'RADIO') 
					disoptions += '<option value="CHECKBOX">Check Box</option>'; 
				else if (objdatatypecode[dt][i].split('|')[0] == 'SELECT') 
					disoptions += '<option value="SELECT">Multi-Select Box</option>'; 
			} else {
				disoptions += '<option value="' + objdatatypecode[dt][i].split('|')[0] + '">' + objdatatypecode[dt][i].split('|')[1] + '</option>'; 
			}
		}
		$('##displayType').children().remove();
		$('##displayType').html(disoptions);
	}
	
	function checkMultValue() {
		<cfif local.qryData.allowMultiple is 1 and local.qryData.allowMultipleOff is 0>
			showErr('There are members with multiple values for this field. This option cannot be turned off.');
			document.getElementById('allowMultiple').checked = true;
			return false;
		<cfelse>
			var seldisp_val = $('##displayType').val();
			setDisplayTypeOptions($('##dataType').val());
			if (seldisp_val == 'SELECT') $("##displayType").val(seldisp_val);
			checkDisplayType();
		</cfif>
	}
	
	function checkName() {
		var txt2 = $('##columnName').val().replace(/[^A-Za-z0-9 \&\_\:\-\/]/g, '');
		if (txt2 != $('##columnName').val()) {
			showErr('Custom fields may only contain:<br/>- letters A-Z<br/>- numbers 0-9<br/>- special characters \& \_ \: \- \/ (space)<br/>All other characters will be removed.');
			$('##columnName').val(txt2);
		}
	}

	function checkDisplayType() {
		mca_hideAlert('divErr');
		var seldata_val = $('##dataType').val();
		var seldisp_val = $('##displayType').val();

		<!--- these fields dont support multiple values --->
		if (seldata_val == 'STRING' || seldata_val == 'DECIMAL2' || seldata_val == 'INTEGER') {
			$('##trAllowMultiple').show();
		} else {
			$('##allowMultiple').attr('checked',false);
			$('##trAllowMultiple').hide();
		}

		<!--- combinations that support minChars, maxChars --->
		if ((seldata_val == 'STRING' && seldisp_val == 'TEXTBOX') || seldata_val == 'CONTENTOBJ') 
			$('##divMinMaxChar').show();
		else 
			$('##divMinMaxChar').hide();

		<!--- combinations that support minSelected, maxSelected --->
		if ((seldata_val == 'STRING' || seldata_val == 'INTEGER' || seldata_val == 'DECIMAL2') && seldisp_val == 'SELECT' && $('##allowMultiple').is(':checked')) 
			$('##divMinMaxSelect').show(); 
		else 
			$('##divMinMaxSelect').hide();

		<!--- combinations that support minValueInt, maxValueInt --->
		if (seldata_val == 'INTEGER' && seldisp_val == 'TEXTBOX') 
			$('##divMinMaxValueInt').show();
		else 
			$('##divMinMaxValueInt').hide();

		<!--- combinations that support minValueDecimal2, maxValueDecimal2 --->
		if (seldata_val == 'DECIMAL2' && seldisp_val == 'TEXTBOX') 
			$('##divMinMaxValueDecimal2').show();
		else 
			$('##divMinMaxValueDecimal2').hide();

		<!--- combinations that support minValueDate, maxValueDate --->
		if (seldata_val == 'DATE' && seldisp_val == 'DATE') {
			$('##divMinMaxValueDate').show();
		} else {
			$('##divMinMaxValueDate').hide();
		}

		<!--- these fields support allow new values --->
		if (seldisp_val == 'SELECT' || seldisp_val == 'RADIO' || seldisp_val == 'CHECKBOX') {
			$('##trAllowNew').show();
		}
		<!--- all other non-option fields except BIT cols allow new values --->
		else if (seldata_val != 'BIT') {
			$('##allowNewValuesOnImport').attr('checked',true);
			$('##trAllowNew').hide();
		}

		<!--- BIT should not support new values --->
		if (seldata_val == 'BIT') {
			$('##allowNewValuesOnImport').attr('checked',false);
			$('##trAllowNew').hide();
		}

		<!--- document, content dont show new values on import box and must allow null --->
		if (seldata_val == 'DOCUMENTOBJ' || seldata_val == 'CONTENTOBJ') {
			$('##allowNull').attr('checked',true);
		}
		
		<!--- hide default value if allowNull is checked --->
		if ($('##allowNull').is(':checked')) { 
			$('##trDefaultValue').hide();
			destroyDefaultDateControl();
		} else { 
			$('##trDefaultValue').show();
			if (seldisp_val == 'DATE') {
				$("##defaultValue").addClass('dateControl');
				mca_setupDatePickerField('defaultValue');
				mca_setupCalendarIcons('trDefaultValue');
				$("##defaultValue").trigger('blur');
				$(".defaultDateControl").show();
			} else {
				destroyDefaultDateControl();
			}
		}

		<!--- optional whole number custom field options --->
		if (seldata_val == 'INTEGER' && seldisp_val == 'TEXTBOX') {
			$('##customOptions_INTEGER_TEXTBOX').show();
			toggleLinkedDateColumnAFSettings();
		} else if ($('##customOptions_INTEGER_TEXTBOX').length) {
			$('##linkedDateColumnID').val(0);
			$('##customOptions_INTEGER_TEXTBOX,##linkedDateColumnAFSettings').hide();
		}
	}

	function destroyDefaultDateControl() {
		$("##defaultValue").datetimepicker('destroy');
		$("##defaultValue").removeAttr('readonly').removeClass('dateControl');
		$(".defaultDateControl").hide();
	}
	
	function checkDefaultValue() {	
		<!--- if nulls are allowed, no need to check default value. it is empty. --->
		if ($('##allowNull').is(':checked')) {
			$('##defaultValue').val('');
			return true;
		}
		
		<!--- otherwise, doesnt allow nulls so validate default value --->
		var txt_val = $('##defaultValue').val();
		if (txt_val.replace(/(^[\s\xA0]+|[\s\xA0]+$)/g,'').length == 0) {
			showErr('This column\'s default value cannot be blank.');
			return false;
		}
		switch ($('##dataType').val()) {
			case 'INTEGER':
				if (!(/^([0-9]\d*)$/.test(txt_val))) {
					showErr('Enter a valid whole number for this column\'s default value.');
					return false;
				}
				break;
			case 'DECIMAL2':
				if (!(/^ *[0-9]+(\.\d{0,2})?$/.test(txt_val))) {
					showErr('Enter a valid decimal number for this column\'s default value.');
					return false;
				}
				break;
			case 'BIT':
				if (!((txt_val == '0') || (txt_val == '1'))) {
					showErr('Enter 0 or 1 for this column\'s default value.');
					return false;
				}
				break;
			default:
		}
		
		return true;
	}
	
	function precheckNameAndValidations() {
		var isdivMinMaxChar = $('##divMinMaxChar').is(":visible");
		var isdivMinMaxSelect = $('##divMinMaxSelect').is(":visible");
		var isdivMinMaxValueInt = $('##divMinMaxValueInt').is(":visible");
		var isdivMinMaxValueDecimal2 = $('##divMinMaxValueDecimal2').is(":visible");
		var isdivMinMaxValueDate = $('##divMinMaxValueDate').is(":visible");
		$('##saveCFdiv, ##saveCFloadingDIV').toggle();

		var precheckNameAndValidationsResult = function(s) {
			if (s.success && s.success.toLowerCase() == 'true') {
				if (s.errcode && s.errcode == 1) {
					showErr('That column name is invalid or already in use. Enter a different column name.');
					$('##saveCFdiv, ##saveCFloadingDIV').toggle();
				} else if (s.errcode && s.errcode == 2) {
					showErr('We are unable to save changes. There are existing values for this column that are outside the data validation range.');
					$('##saveCFdiv, ##saveCFloadingDIV').toggle();
				} else if (s.errcode && s.errcode == 3) {
					showErr('We are unable to save changes. There are existing members that have field options that are outside the data validation range.');
					$('##saveCFdiv, ##saveCFloadingDIV').toggle();
				} else {
					top.$('##btnSaveCustomField').html('Please wait...').prop('disabled',true);
					document.forms["frmCustomField"].submit();
				}
			} else {
				precheckNameAndValidationsFailure(s);
			}
		};
		var precheckNameAndValidationsFailure = function(s) {
			if ($.trim($('##columnID').val()) == 0) {
				showErr('We were unable to add this column.');
				$('##saveCFdiv, ##saveCFloadingDIV').toggle();
			} else {
				showErr('We were unable to edit this column.');
				$('##saveCFdiv, ##saveCFloadingDIV').toggle();
			}
		};
		
		var objParams = { columnID:$.trim($('##columnID').val()), columnName:$.trim($('##columnName').val()) };
		if (isdivMinMaxChar) {
			objParams.datatype = $('##dataType').val();
			objParams.minChars = $.trim($('##minChars').val());
			objParams.maxChars = $.trim($('##maxChars').val());
		} else if (isdivMinMaxSelect) {
			objParams.minSelected = $.trim($('##minSelected').val());
			objParams.maxSelected = $.trim($('##maxSelected').val());
		} else if (isdivMinMaxValueInt) {
			objParams.minValueInt = $.trim($('##minValueInt').val());
			objParams.maxValueInt = $.trim($('##maxValueInt').val());
		} else if (isdivMinMaxValueDecimal2) {
			objParams.minValueDecimal2 = $.trim($('##minValueDecimal2').val());
			objParams.maxValueDecimal2 = $.trim($('##maxValueDecimal2').val());
		} else if (isdivMinMaxValueDate) {
			objParams.minValueDate = $.trim($('##minValueDate').val());
			objParams.maxValueDate = $.trim($('##maxValueDate').val());
		}
		TS_AJX('CUSTOMFIELDS','precheckNameAndValidations',objParams,precheckNameAndValidationsResult,precheckNameAndValidationsFailure,15000,precheckNameAndValidationsFailure);
	
		return false;
	}

	function validateMixMax() {
		var intRegex = /^([1-9]\d*)$/;
		var intRegexWithZero = /^([0-9]\d*)$/;
		var decRegex = /^[0-9]+(\.\d{0,2})?$/;
		if ($('##divMinMaxChar').is(":visible")) {
			var a = $.trim($('##minChars').val());
			var b = $.trim($('##maxChars').val());
			var pa = parseInt('0'+a);
			var pb = parseInt('0'+b);
			if (a.length > 0 && (!intRegex.test(a) || pa == 0)) {
				showErr('Enter a valid minimum number of characters (a number more than 0).');
				return false;
			}
			if (b.length > 0 && (!intRegex.test(b) || pb == 0)) {
				showErr('Enter a valid maximum number of characters (a number more than 0).');
				return false;
			}
			if ((pa > 0 && pb == 0) || (pa == 0 && pb > 0)) {
				showErr('Both the minimum and maximum number of characters must be entered if one is entered.');
				return false;
			}
			if (pa > pb) {
				showErr('The minimum number of characters is more than the maximum number of characters.');
				return false;
			}
			if ($('##trDefaultValue').is(":visible") && a.length > 0 && b.length > 0) {
				var dLen = $.trim($('##defaultValue').val()).length;
				if (!(dLen >= pa && dLen <= pb)) {
					showErr('The default value is outside the data validation range.');
					return false;
				}
			}
		} else if ($('##divMinMaxSelect').is(":visible")) {
			var a = $.trim($('##minSelected').val());
			var b = $.trim($('##maxSelected').val());
			var pa = parseInt('0'+a);
			var pb = parseInt('0'+b);
			if (a.length > 0 && (!intRegex.test(a) || pa == 0)) {
				showErr('Enter a valid minimum number of options (a number more than 0).');
				return false;
			}
			if (b.length > 0 && (!intRegex.test(b) || pb == 0)) {
				showErr('Enter a valid maximum number of options (a number more than 0).');
				return false;
			}
			if ((pa > 0 && pb == 0) || (pa == 0 && pb > 0)) {
				showErr('Both the minimum and maximum number of options must be entered if one is entered.');
				return false;
			}
			if (pa > pb) {
				showErr('The minimum number of options is more than the maximum number of options.');
				return false;
			}
		} else if ($('##divMinMaxValueInt').is(":visible")) {
			var a = $.trim($('##minValueInt').val());
			var b = $.trim($('##maxValueInt').val());
			var pa = parseInt('0'+a);
			var pb = parseInt('0'+b);
			if (a.length > 0 && !intRegexWithZero.test(a)) {
				showErr('Enter a valid minimum value allowed.');
				return false;
			}
			if (b.length > 0 && !intRegexWithZero.test(b)) {
				showErr('Enter a valid maximum value allowed.');
				return false;
			}
			if (a.length > 0 && b.length > 0 && pa == pb) {
				showErr('The minimum and maximum values allowed cannot be the same.');
				return false;
			}
			if ((a.length > 0 && b.length == 0) || (a.length == 0 && b.length > 0)) {
				showErr('Both the minimum and maximum values allowed must be entered if one is entered.');
				return false;
			}
			if (pa > pb) {
				showErr('The minimum value allowed is more than the maximum value allowed.');
				return false;
			}
			if ($('##trDefaultValue').is(":visible") && a.length > 0 && b.length > 0) {
				var d = $.trim($('##defaultValue').val());
				var pd = parseInt('0'+d);
				if (!(pd >= pa && pd <= pb)) {
					showErr('The default value is outside the data validation range.');
					return false;
				}
			}
		} else if ($('##divMinMaxValueDecimal2').is(":visible")) {
			var a = $.trim($('##minValueDecimal2').val());
			var b = $.trim($('##maxValueDecimal2').val());
			var pa = parseFloat('0'+a);
			var pb = parseFloat('0'+b);
			if (a.length > 0 && !decRegex.test(a)) {
				showErr('Enter a valid minimum value allowed.');
				return false;
			}
			if (b.length > 0 && !decRegex.test(b)) {
				showErr('Enter a valid maximum value allowed.');
				return false;
			}
			if (a.length > 0 && b.length > 0 && pa == pb) {
				showErr('The minimum and maximum values allowed cannot be the same.');
				return false;
			}
			if ((a.length > 0 && b.length == 0) || (a.length == 0 && b.length > 0)) {
				showErr('Both the minimum and maximum values allowed must be entered if one is entered.');
				return false;
			}
			if (pa > pb) {
				showErr('The minimum value allowed is more than the maximum value allowed.');
				return false;
			}
			if ($('##trDefaultValue').is(":visible") && a.length > 0 && b.length > 0) {
				var d = $.trim($('##defaultValue').val());
				var pd = parseFloat('0'+d);
				if (!(pd >= pa && pd <= pb)) {
					showErr('The default value is outside the data validation range.');
					return false;
				}
			}
		} else if ($('##divMinMaxValueDate').is(":visible")) {
			var a = $.trim($('##minValueDate').val());
			var b = $.trim($('##maxValueDate').val());
			if (a.length > 0 && b.length > 0 && a == b) {
				showErr('The minimum and maximum dates allowed cannot be the same.');
				return false;
			}
			if ((a.length > 0 && b.length == 0) || (a.length == 0 && b.length > 0)) {
				showErr('Both the minimum and maximum dates allowed must be selected if one is selected.');
				return false;
			}
			if ($('##trDefaultValue').is(":visible") && a.length > 0 && b.length > 0) {
				var d = $.trim($('##defaultValue').val());
				var pa = mca_getParsedDateTime(a);
				var pb = mca_getParsedDateTime(b);
				var pd = mca_getParsedDateTime(d);
				if (pd < pa || pd > pb) {
					showErr('The default value is outside the data validation range.');
					return false;
				}
			}
		}

		return true;
	}

	function chkCustomFieldForm() {
		if(validateForm()) {
			if (checkDefaultValue()) {
				if (validateMixMax()) {
					precheckNameAndValidations();
				}
			}
		}
		return false;
	}
	function validateForm(){
		let arrReq = [];
		if ($('##columnName').val().trim().length == 0) arrReq.push('Name of field');
		if ($('##mdUID').length && $('##mdUID').val().trim().length == 0) arrReq.push('UID');
		if ($('##linkedDateColumnID').length && $('##linkedDateColumnID').val() > 0) {
			if ($('##linkedDateCompareDate').val().trim() == '') arrReq.push('What date do you want to compare it to?');
			if ($('##linkedDateCompareDateAFID').val() == 0) arrReq.push('How should we advance this date?');
			if ($('##linkedDateAdvanceDate').val().trim() == '') arrReq.push('When should we next advance these dates?');
			if ($('##linkedDateAdvanceAFID').val() == 0) arrReq.push('How should we advance this date?');
		}

		if(arrReq.length) {
			showErr('The following fields are required:<br> - '+arrReq.join('<br/> - '));
			return false;
		}
		
		return true;		
	}

	function showErr(msg) { mca_showAlert('divErr', msg, true); }
	
	<cfif local.columnID gt 0>
		function initFieldsinFS() {
			var fsFields = document.querySelectorAll('.inputFieldLabel');
			
			for (var i = 0; i < fsFields.length; i++) {
				(function(fID, inputElement) {
					var originalValue = inputElement.value;
					var eventExecuted = false; // Flag to track if an event has been executed
					
					inputElement.addEventListener('blur', function(event) {
						if (!eventExecuted) {
							var currentValue = inputElement.value;
							if (originalValue !== currentValue) {
								onBlurFieldsinFS(fID, currentValue);
							}
							eventExecuted = true;
						}
					});

					inputElement.addEventListener('keyup', function(event) {
						if (!eventExecuted && event.keyCode === 13) { // Enter key
							var currentValue = inputElement.value;
							if (originalValue !== currentValue) {
								onBlurFieldsinFS(fID, currentValue);
							}
							eventExecuted = true;
						}
					});
				})(fsFields[i].attributes["data-fieldid"].value, fsFields[i]);
			}
		}
		function onBlurFieldsinFS(fID, fieldLabel) {
			if(fieldLabel != ''){
				mca_hideAlert('divCFFieldErrorArea');
				doSaveMemberFields(fID,fieldLabel);						
			} else {
				mca_showAlert('divCFFieldErrorArea', 'Field Label is required', false);
				return false;
			}			
		}
		function doSaveMemberFields(fID,fieldLabel){
			$("##divCFFormSubmitArea")
			.load('#this.link.updateCustomFieldValue#&columnID=#local.columnID#&valueID='+fID+'&columnValue='+encodeURIComponent(fieldLabel),
				function() { 
					CFSFieldsTable.draw(false);
				});
		}
		function initializeFieldSetTable() {

			CFSFieldsTable = $('##CFSFieldsTable').DataTable({
						"processing": true,
						"serverSide": true,
						"paging": false,
						"info": false,
						"ajax": { 
							"url": "#local.customFieldValueList#",
							"type": "post"
						},
						"autoWidth": false,
						"columns": [
							{ "data": null,
								"render": function ( data, type, row, meta ) {
									let renderData = '';
									if (type === 'display') {
										renderData += '<div class="d-flex"><input type="text" data-fieldID="'+data.valueID+'" id="fieldLabel_'+data.valueID+'" style="width: 287px;" class="form-control form-control-sm inputFieldLabel mr-2" value="'+data.columnValue+'">';									
										if(data.memCount){
										renderData += '<span> ('+data.memCount+') </span>';	
										}
										if(data.isDefault){
										renderData += '<span id="featuredBadgeContainer_'+data.valueID+'" class="ml-auto"><span class="badge badge-info">Default</span></span>';
										}
										renderData += '</div>';
									}
									return type === 'display' ? renderData : data;
								},
								"width": "60%",
								"orderable": false 
							},
							{ "data": null,
								"render": function ( data, type, row, meta ) {
									let renderData = '';
									if (type === 'display') {	
										if(data.isDefault == 0){
											renderData += '<a href="##" id="cffieldRemove_'+data.valueID+'" class="btn btn-sm btn-outline-danger px-2 m-1" title="Remove Value From Custom Field" onclick="removeFieldValue('+data.valueID+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';	
										}
										else{
											renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-can"></i></a>';
										}									
									}
									return type === 'display' ? renderData : data;
								},
								"width": "40%",
								"className": "text-center",
								"orderable": false 
							}
						],
						"searching": false,
						"ordering": false,
						"drawCallback": function() {
							initFieldsinFS();
						}
					});
		}	
		
		function cancelCFFields() {
				$('##divCFFormContainer').html('').addClass('d-none');
				$('##saveCFdiv').removeClass('d-none');
				top.$('##MCModalFooter').removeClass('d-none');
			}
		function addColumnValue() {
			$('##saveCFdiv').addClass('d-none');
			top.$('##MCModalFooter').addClass('d-none');
			$("##divCFFormSubmitArea").addClass('d-none');
			$('##divCFFormContainer')
				.removeClass('d-none')
				.html(mca_getLoadingHTML())
				.load('#this.link.addCustomFieldValue#&cID=#local.columnID#');
		}
		function addColumnValueMulti() {
			$('##saveCFdiv').addClass('d-none');
			top.$('##MCModalFooter').addClass('d-none');
			$("##divCFFormSubmitArea").addClass('d-none');
			$('##divCFFormContainer')
				.removeClass('d-none')
				.html(mca_getLoadingHTML())
				.load('#this.link.addCustomFieldValueMulti#&cID=#local.columnID#');
		}
		function removeFieldValue(valueID) {
			let removeValue = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') CFSFieldsTable.draw(false);
				else { 
					removeFSFieldElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('There was an error removing this field value.');
				}
			};

			let removeFSFieldElement = $('##cffieldRemove_'+valueID);
			mca_initConfirmButton(removeFSFieldElement, function(){
				var objParams = { valueID:valueID };
				TS_AJX('CUSTOMFIELDS','removeFieldValue',objParams,removeValue,removeValue,20000,removeValue);
			});
		}
		function showAddValueArea(){
			var seldisp_val = $('##displayType').val();
			if(seldisp_val == "RADIO" || seldisp_val == "SELECT" || seldisp_val == "CHECKBOX"){
				$('##divMCFFieldsContainer').removeClass('d-none');
			}
			else {
				$('##divMCFFieldsContainer').addClass('d-none');
			}
		}
	</cfif>
	
	function toggleLinkedDateColumnAFSettings() {
		let linkedDateColumnID = $('##linkedDateColumnID').val();
		if (linkedDateColumnID > 0) {
			$('##allowMultiple').prop('checked',false);
			$('##trAllowMultiple,##divMinMaxValueInt').hide();
			$('##minValueInt,##maxValueInt').val('');
			$('##isReadOnly').prop('checked',true);
		} else {
			$('##trAllowMultiple,##divMinMaxValueInt').show();
		}
		$('##linkedDateColumnAFSettings').toggle(linkedDateColumnID > 0);
		$('##linkedDateColumnAFSettings').find('.mccf_rollDate').off('change').on('change',function() {
			updateLinkedDateAFExample($(this).attr('name'),$(this).data('afid'),$(this).data('dt'));
		});
	}
	function clearLinkRollRunDate() {
		mca_clearDateRangeField('linkedDateCompareDate');
		updateLinkedDateAFExample('linkedDateCompareDate',$('##linkedDateCompareDate').data('afid'),$('##linkedDateCompareDate').data('dt'));
	}
	function clearLinkRollAdvDate() {
		mca_clearDateRangeField('linkedDateAdvanceDate');
		updateLinkedDateAFExample('linkedDateAdvanceDate',$('##linkedDateAdvanceDate').data('afid'),$('##linkedDateAdvanceDate').data('dt'));
	}
	function updateLinkedDateAFExample(dt,af,txt) {
		let jtxt = $('##'+txt);
		let dtVal = $('##'+dt).val();
		let afVal = $('##'+af).val();

		let chkDateExResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') jtxt.html(r.retdate);
			else jtxt.html('Unable to calculate date.');
		};

		if (afVal == '0' || dtVal == '') {
			jtxt.html('&lt;No Advance&gt;');
		} else {
			jtxt.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Calculating...');

			let objParams = { baseDate:dtVal, afid:afVal };
			TS_AJX('ADMADVFORM','getAdvanceFormulaDateforAFID',objParams,chkDateExResult,chkDateExResult,10000,chkDateExResult);
		}
	}

	$(function() {
		mca_setupDatePickerRangeFields('minValueDate','maxValueDate');
		mca_setupDatePickerField('linkedDateCompareDate');
		mca_setupDatePickerField('linkedDateAdvanceDate');
		mca_setupCalendarIcons('frmCustomField');
		setDataTypeOptions();
		<cfif local.columnID gt 0>
			initializeFieldSetTable();
			$("##dataType").val($('##olddataType').val());
		</cfif>
		$("##dataType").bind("change", function() { 
			setDisplayTypeOptions(this.value); 
			<cfif local.columnID gt 0>
				$("##displayType").val($('##olddisplayType').val());
			</cfif>
			checkDisplayType(); 
		} );
		$("##dataType").change();

		$("##allowNull").bind("click",
			function() {
				if ($("##allowNull").is(":checked")) { 
				} else {
					var msg = 'Setting this field to not allow Null values requires you to specify a default value for this field.\n\nIf you continue, all members without a value for this field will be assigned the default value.';
					if (!confirm(msg)) { $('##allowNull').attr('checked',true); checkDisplayType(); }
				}
			}
		);
		
		<cfif local.columnID GT 0 >
			showAddValueArea();
			$("##displayType").bind("change", function() { 
				showAddValueArea();
			} );

			<cfif val(local.qryData.linkedDateColumnID)>
				updateLinkedDateAFExample('linkedDateCompareDate','linkedDateCompareDateAFID','linkedDateCompareDateAFID_advDate');
				updateLinkedDateAFExample('linkedDateAdvanceDate','linkedDateAdvanceAFID','linkedDateAdvanceAFID_advDate');
			</cfif>
		</cfif>

		top.MCModalUtils.buildFooter({
			classlist: 'd-flex',
			showclose: true,
			buttons: [
				{
					class: "btn-sm btn-primary ml-auto",
					clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.chkCustomFieldForm',
					label: 'Save', 
					name: 'btnSaveCustomField',
					id: 'btnSaveCustomField'
				}
			]
		});
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.js#">

<cfoutput>
<div id="saveCFdiv" class="container-fluid">
	<form name="frmCustomField" id="frmCustomField" method="post" action="#this.link.saveCustomField#">
		<input type="hidden" name="columnID" id="columnID" value="#local.columnID#">		
		<input type="hidden" name="olddataType" id="olddataType" value="#local.qryData.dataTypeCode#">		
		<input type="hidden" name="olddisplayType" id="olddisplayType" value="#local.qryData.displayTypeCode#">
		<div id="divErr" class="alert alert-danger my-2 d-none"></div>
		<div class="form-group">
			<div class="form-label-group">
				<input  size="40" maxlength="255" type="text" name="columnName" id="columnName" value="#arguments.event.getValue('columnName')#" onBlur="checkName()" class="form-control">
				<label for="columnName">Name</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<textarea maxlength="255" name="columnDesc" id="columnDesc" style="height:100px;" class="form-control form-control-sm">#arguments.event.getValue('columnDesc')#</textarea>
				<label for="columnDesc">Description</label>
			</div>
		</div>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and local.columnID gt 0>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" autocomplete="off" size="38" maxlength="60" name="mdUID" id="mdUID" value="#arguments.event.getValue('uid')#" class="form-control">
					<label for="mdUID">API ID</label>
				</div>
			</div>
		<cfelseif local.columnID gt 0>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" autocomplete="off" size="38" maxlength="60" name="mdUIDRO" id="mdUIDRO" value="#arguments.event.getValue('uid')#" class="form-control" readOnly="true">
					<label for="mdUIDRO">API ID</label>
				</div>
			</div>
		</cfif>
		<div class="form-group">
			<div class="form-label-group">
				<select id="dataType" name="dataType" class="form-control">
					
				</select>
				<label for="dataType">Data stored as</label>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<select  name="displayType" id="displayType" onChange="checkDisplayType()" class="form-control">
					
				</select>
				<label for="dataType">Display field as</label>
			</div>
		</div>
		<div id="divMinMaxChar" class="validationDiv" style="display:none;">
			<b>Data Validation</b>
			<div class="form-group row">
				<label for="minChars" class="col-sm-4 col-xl-2 col-form-label">Values must have between</label>
				<div class="col-sm-8 col-xl-6 align-self-center">
					<div class="row">
						<div class="col-sm-6 pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="minChars" id="minChars" value="#arguments.event.getValue('minChars')#" size="4" maxlength="6" class="form-control">							
								&nbsp; and
							</div>
						</div>
						<div class="col-sm-6">
							<div class="input-group input-group-sm">
								<input  type="text" name="maxChars" id="maxChars" value="#arguments.event.getValue('maxChars')#" size="4" maxlength="6" class="form-control">							
								&nbsp; characters
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 text-center">
					<small class="form-text ">
						(leave blank if no restriction)
					</small>
				</div>
			</div>
		</div>
		<div id="divMinMaxSelect" class="validationDiv" style="display:none;">
			<b>Data Validation</b><br/>
			<div class="form-group row">
				<label for="minSelected" class="col-sm-4 col-xl-2 col-form-label">Members must select between</label>
				<div class="col-sm-8 col-xl-6 align-self-center">
					<div class="row">
						<div class="col-sm-6 pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="minSelected" id="minSelected" value="#arguments.event.getValue('minSelected')#" size="4" maxlength="6" class="form-control">							
								&nbsp; and
							</div>
						</div>
						<div class="col-sm-6">
							<div class="input-group input-group-sm">
								<input  type="text" name="maxSelected" id="maxSelected" value="#arguments.event.getValue('maxSelected')#" size="4" maxlength="6" class="form-control">							
								&nbsp; options
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 text-center">
					<small class="form-text ">
						(leave blank if no restriction)
					</small>
				</div>
			</div>
		</div>
		<div id="divMinMaxValueInt" class="validationDiv" style="display:none;">
			<b>Data Validation</b><br/>
			<div class="form-group row">
				<label for="minSelected" class="col-sm-4 col-xl-2 col-form-label">Values must be between</label>
				<div class="col-sm-8 col-xl-6 align-self-center">
					<div class="row">
						<div class="col-sm-6 pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="minValueInt" id="minValueInt" value="#arguments.event.getValue('minValueInt')#" size="10" maxlength="10" class="form-control">							
								&nbsp; and
							</div>
						</div>
						<div class="col-sm-6">
							<div class="input-group input-group-sm">
								<input  type="text" name="maxValueInt" id="maxValueInt" value="#arguments.event.getValue('maxValueInt')#" size="10" maxlength="10" class="form-control">							
								
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 text-center">
					<small class="form-text ">
						(leave blank if no restriction)
					</small>
				</div>
			</div>
		</div>	
		<div id="divMinMaxValueDecimal2" class="validationDiv" style="display:none;">
			<b>Data Validation</b><br/>
			<div class="form-group row">
				<label for="minValueDecimal2" class="col-sm-4 col-xl-2 col-form-label">Values must be between</label>
				<div class="col-sm-8 col-xl-6 align-self-center">
					<div class="row">
						<div class="col-sm-6 pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="minValueDecimal2" id="minValueDecimal2" value="#arguments.event.getValue('minValueDecimal2')#" size="10" maxlength="9" class="form-control">							
								&nbsp; and
							</div>
						</div>
						<div class="col-sm-6">
							<div class="input-group input-group-sm">
								<input type="text" name="maxValueDecimal2" id="maxValueDecimal2" value="#arguments.event.getValue('maxValueDecimal2')#" size="10" maxlength="9" class="form-control">							
								
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 text-center">
					<small class="form-text ">
						(leave blank if no restriction)
					</small>
				</div>
			</div>
		</div>	
		<div id="divMinMaxValueDate" class="validationDiv" style="display:none;">
			<b>Data Validation</b><br/>
			<div class="form-group row">
				<label for="minValueDate" class="col-sm-4 col-xl-2 col-form-label">Values must be between:</label>
				<div class="col-sm-8 col-xl-6 align-self-center">
					<div class="row">
						<div class="col-sm-6 pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="minValueDate" id="minValueDate" value="#dateFormat(arguments.event.getValue('minValueDate'),'m/d/yyyy')#" class="form-control form-control-sm dateControl p-1"  maxlength="12" >
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="minValueDate"><i class="fa-solid fa-calendar"></i></span>
								</div>
								&nbsp; and
							</div>
						</div>
						<div class="col-sm-6">
							<div class="input-group input-group-sm">
								<input type="text" name="maxValueDate" id="maxValueDate" value="#dateFormat(arguments.event.getValue('maxValueDate'),'m/d/yyyy')#" class="form-control form-control-sm dateControl p-1"  maxlength="12">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="maxValueDate"><i class="fa-solid fa-calendar"></i></span>
								</div>
								<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-3 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('minValueDate','maxValueDate');">clear</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 text-center">
					<small class="form-text ">
						(clear dates if no restriction)
					</small>
				</div>
			</div>
		</div>	
		<div id="cboxoptDiv">
			<div class="col-sm-12 pl-0 ml-0"><b>Field Settings</b></div>
			<div id="trAllowMultiple" class="form-group form-check">
				<input type="checkbox" id="allowMultiple" name="allowMultiple" value="1" <cfif val(arguments.event.getValue('allowMultiple',0))>checked</cfif> onClick="checkMultValue();" class="form-check-input">
				<label class="form-check-label" for="allowMultiple">Members can have multiple values for this field</label>
			</div>
			
			<div id="trAllowNew" class="form-group form-check">
				<input type="checkbox" id="allowNewValuesOnImport" name="allowNewValuesOnImport" value="1" <cfif val(arguments.event.getValue('allowNewValuesOnImport',0))>checked</cfif> class="form-check-input">
				<label class="form-check-label" for="allowNewValuesOnImport">New values can be added during Member Import.</label>
			</div>

			<div id="trAllowNull" class="form-group form-check">
				<input type="checkbox" name="allowNull" id="allowNull" value="1" <cfif val(arguments.event.getValue('allowNull',0))>checked</cfif>  onClick="checkDisplayType();" class="form-check-input">
				<label class="form-check-label" for="allowNull">Allow null values or no selection for this field.</label>
			</div>

			<div class="form-group form-check">
				<input type="checkbox" name="isReadOnly" id="isReadOnly" value="1"<cfif val(arguments.event.getValue('isReadOnly',0))>checked</cfif>   onClick="checkDisplayType();" class="form-check-input">
				<label class="form-check-label" for="isReadOnly">Prevent editing field values in Member Admin and editable field sets.</label>
			</div>
		</div>	
		<div id="trDefaultValue">
			<div class="col-sm-12 pl-0 ml-0">
				<b>Default Value</b>
			</div>
			<div class="form-group">
				<div class="col-sm-5 pl-0 pr-0">
					<div class="input-group input-group-sm">
						<input size="20" type="text" id="defaultValue" name="defaultValue" class="form-control form-control-sm"  value="#arguments.event.getValue('defaultValue')#" onBlur="checkDefaultValue();">
						<div class="input-group-append hide defaultDateControl">
							<span class="input-group-text cursor-pointer calendar-button" data-target="defaultValue"><i class="fa-solid fa-calendar"></i></span>
						</div>
						<button type="button" class="btn btn-pill btn-secondary btn-sm ml-3 py-0 defaultDateControl hide" name="btnClearDate" onclick="return mca_clearDateRangeField('defaultValue');">clear</button>
					</div>
				</div>
			</div>
		</div>

		<div id="customOptions_INTEGER_TEXTBOX" class="my-5" style="display:none;">
			<h6 class="font-weight-bold mb-0">Custom Settings</h6>
			<div class="mb-3 small text-dim">We can automatically update this member custom field with the number of years since the value of a specified date custom field. To do this, define the following:</div>
			<div class="form-label-group">
				<select name="linkedDateColumnID" id="linkedDateColumnID" class="custom-select" onchange="toggleLinkedDateColumnAFSettings();">
					<option value="0"></option>
					<cfloop query="local.qryDateCustomFields">
						<option value="#local.qryDateCustomFields.columnID#"<cfif val(local.qryData.linkedDateColumnID) eq local.qryDateCustomFields.columnID> selected</cfif>>#local.qryDateCustomFields.columnName#</option>
					</cfloop>
				</select>
				<label for="linkedDateColumnID">Select a Date Custom Field</label>
			</div>
			<div id="linkedDateColumnAFSettings" style="display:none;">
				<div class="form-row">
					<div class="col-auto">
						<div class="form-label-group" style="width:290px;">
							<div class="input-group dateFieldHolder">
								<input type="text" name="linkedDateCompareDate" id="linkedDateCompareDate" value="#dateFormat(local.qryData.linkedDateCompareDate,'m/d/yyyy')#" class="form-control dateControl mccf_rollDate" data-afid="linkedDateCompareDateAFID" data-dt="linkedDateCompareDateAFID_advDate">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="linkedDateCompareDate"><i class="fas fa-calendar"></i></span>
									<span class="input-group-text"><a href="javascript:clearLinkRollRunDate();"><i class="fas fa-times-circle"></i></a></span>
								</div>
								<label for="linkedDateCompareDate">What date do you want to compare it to?</label>
							</div>
						</div>
					</div>
					<div class="col">
						<div class="form-label-group">
							<div class="input-group">
								<select name="linkedDateCompareDateAFID" id="linkedDateCompareDateAFID" class="custom-select" onchange="updateLinkedDateAFExample('linkedDateCompareDate','linkedDateCompareDateAFID','linkedDateCompareDateAFID_advDate');">
									<option value="0"></option>
									<cfloop query="local.qryAdvanceFormulas">
										<option value="#local.qryAdvanceFormulas.AFID#"<cfif val(local.qryData.linkedDateCompareDateAFID) eq local.qryAdvanceFormulas.AFID> selected</cfif>>#local.qryAdvanceFormulas.afName#</option>
									</cfloop>
								</select>
								<div class="input-group-append">
									<span id="linkedDateCompareDateAFID_advDate" class="input-group-text px-1 font-size-sm justify-content-center" style="width:95px;">&lt;No Advance&gt;</span>
								</div>
								<label for="linkedDateCompareDateAFID">How should we advance this date?</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col-auto">
						<div class="form-label-group" style="width:290px;">
							<div class="input-group dateFieldHolder">
								<input type="text" name="linkedDateAdvanceDate" id="linkedDateAdvanceDate" value="#dateFormat(local.qryData.linkedDateAdvanceDate,'m/d/yyyy')#" class="form-control dateControl mccf_rollDate" data-afid="linkedDateAdvanceAFID" data-dt="linkedDateAdvanceAFID_advDate">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="linkedDateAdvanceDate"><i class="fas fa-calendar"></i></span>
									<span class="input-group-text"><a href="javascript:clearLinkRollAdvDate();"><i class="fas fa-times-circle"></i></a></span>
								</div>
								<label for="linkedDateAdvanceDate">When should we next advance these dates?</label>
							</div>
						</div>
					</div>
					<div class="col">
						<div class="form-label-group">
							<div class="input-group">
								<select name="linkedDateAdvanceAFID" id="linkedDateAdvanceAFID" class="custom-select" onchange="updateLinkedDateAFExample('linkedDateAdvanceDate','linkedDateAdvanceAFID','linkedDateAdvanceAFID_advDate');">
									<option value="0"></option>
									<cfloop query="local.qryAdvanceFormulas">
										<option value="#local.qryAdvanceFormulas.AFID#"<cfif val(local.qryData.linkedDateAdvanceAFID) eq local.qryAdvanceFormulas.AFID> selected</cfif>>#local.qryAdvanceFormulas.afName#</option>
									</cfloop>
								</select>
								<div class="input-group-append">
									<span id="linkedDateAdvanceAFID_advDate" class="input-group-text px-1 font-size-sm justify-content-center" style="width:95px;">&lt;No Advance&gt;</span>
								</div>
								<label for="linkedDateAdvanceAFID">How should we advance this date?</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!--- hidden submit triggered from parent --->
		<button type="button" name="btnSubmit" id="btnSubmit" class="d-none" onClick="chkCustomFieldForm();"></button>
	</form>
	
		<div id="divMCFFieldsContainer" class="mt-4 d-none">
			<div class="text-right">
				<button type="button" name="btnAddFieldValueMulti" id="btnAddFieldValueMulti" class="btn btn-sm btn-primary" onclick="addColumnValueMulti();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add Multiple Field Values">
					<span class="btn-wrapper--icon">
						<i class="fa-solid fa-layer-plus"></i>
					</span>
				</button>
				<button type="button" name="btnAddFieldValue" id="btnAddFieldValue" class="btn btn-sm btn-primary" onclick="addColumnValue();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add Field Value">
					<span class="btn-wrapper--icon">
						<i class="fa-regular fa-plus"></i>
					</span>
				</button>
			</div><br />
			<div id="divCFFieldErrorArea" class="alert alert-danger mb-2 d-none"></div>
			<table id="CFSFieldsTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Field Value</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>
		</div>
	
</div>
<div id="divCFFormContainer" class="p-3 d-none"></div>
<div id="saveCFloadingDIV" style="display:none;">
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
<div id="divCFFormSubmitArea" class="d-none"></div>
</cfoutput>