<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// for this tool, it needs to be the siteResourceID of the store, not the storeAdmin tool.
			local.storeSiteResourceID = arguments.event.getTrimValue('storeSRID',0);
			// set rights into event
			local.tmpRights = buildRightAssignments(
				siteResourceID=local.storeSiteResourceID, 
				memberID=session.cfcuser.memberdata.memberID, 
				siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getStreams" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"DateOfOrder")>
		<cfset arrayAppend(local.arrCols,"membername")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryStreams" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @totalCount int, @storeID int;
				SET @storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpOrderDetailsWithStreams') IS NOT NULL 
					DROP TABLE ##tmpOrderDetailsWithStreams;

				select o.orderID, od.orderDetailID, o.DateOfOrder, 
					m2.memberid, m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as membername, 
					m2.company,
					pc.contentTitle as productTitle, pf.name as formatTitle, 
					rc.creditAwarded, ca.affirmationID,
					count(distinct ods.orderDetailStreamID) as streamCount,
					sum(case when ods.dateCompleted is not null then 1 else 0 end) as completedCount
				INTO ##tmpOrderDetailsWithStreams
				FROM dbo.store_orders as o
				inner join dbo.store_orderDetails as od on od.orderID = o.orderID
				inner join dbo.store_orderDetailStreams as ods on ods.orderDetailID = od.orderDetailID
				inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
				inner join dbo.store_ProductFormatsStreamUsages as pfsu on pfsu.uid = ods.streamUsageUID
				inner join dbo.store_ProductFormats as pf on pf.formatID = pfsu.formatID
				inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
				INNER JOIN dbo.ams_members as m on m.memberid = ods.assignedToMemberID
				INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
				left outer join dbo.crd_affirmations ca
					inner join dbo.crd_requests rc on rc.affirmationID = ca.affirmationID	
					 on ca.orderid = o.orderID and od.formatID = ca.productFormatID
				WHERE o.storeID = @storeID
				<cfif len(arguments.event.getTrimValue('s_prdkey',''))>
					and p.productID + pc.contentTitle like <cfqueryparam value="%#arguments.event.getTrimValue('s_prdkey','')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(arguments.event.getTrimValue('s_pur',''))>
					and m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' like <cfqueryparam value="%#arguments.event.getTrimValue('s_pur')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(arguments.event.getTrimValue('s_ds',''))>
					and o.DateOfOrder >= <cfqueryparam value="#arguments.event.getValue('s_ds')#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getTrimValue('s_de',''))>
					and o.DateOfOrder <= <cfqueryparam value="#arguments.event.getValue('s_de')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY o.orderID, od.orderDetailID, o.DateOfOrder, m2.memberid, m2.lastname, m2.firstname, m2.middlename, m2.membernumber, m2.company,
					pc.contentTitle, pf.name, rc.creditAwarded, ca.affirmationID
				<cfif arguments.event.getTrimValue('s_st','') eq "NS">
					HAVING sum(case when ods.dateCompleted is not null then 1 else 0 end) = 0
				<cfelseif arguments.event.getTrimValue('s_st','') eq "IP">
					HAVING sum(case when ods.dateCompleted is not null then 1 else 0 end) > 0 AND sum(case when ods.dateCompleted is not null then 1 else 0 end) < count(distinct ods.orderDetailStreamID)
				<cfelseif arguments.event.getTrimValue('s_st','') eq "CP">
					HAVING sum(case when ods.dateCompleted is not null then 1 else 0 end) = count(distinct ods.orderDetailStreamID)
				</cfif>;

				select @totalCount = count(*) from ##tmpOrderDetailsWithStreams;

				select orderID, orderDetailID, DateOfOrder, memberid, membername, company, productTitle, formatTitle, streamCount,
					completedCount, creditAwarded, affirmationID, @totalCount as totalcount
				from (
					select orderID, orderDetailID, DateOfOrder, memberid, membername, company, productTitle, formatTitle, streamCount, completedCount, creditAwarded, affirmationID,
						ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
					from ##tmpOrderDetailsWithStreams
				) as tmp
				WHERE tmp.row > #arguments.event.getValue('posStart')# AND tmp.row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpOrderDetailsWithStreams') IS NOT NULL 
					DROP TABLE ##tmpOrderDetailsWithStreams;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryStreams">
			<cfset local.arrData.append({
				"orderid": local.qryStreams.orderID,
				"orderdetailid": local.qryStreams.orderDetailID,
				"dateoforder": "#dateFormat(local.qryStreams.DateOfOrder,"m/d/yy")#",
				"memberid": local.qryStreams.memberid,
				"membername": local.qryStreams.membername,
				"company": local.qryStreams.company,
				"producttitle": local.qryStreams.productTitle,
				"formattitle": local.qryStreams.formatTitle,
				"streamcount": local.qryStreams.streamCount,
				"completedcount": local.qryStreams.completedcount,
				"creditawarded": local.qryStreams.creditAwarded,
				"affirmationid": local.qryStreams.affirmationID,
				"displayordernumber": "#arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryStreams.orderID,"0000")#",
				"totalcount": local.qryStreams.totalcount,
				"DT_RowId": "row_#local.qryStreams.orderDetailID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryStreams.totalcount),
			"recordsFiltered": val(local.qryStreams.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getShippingList" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.canEdit = checkRights(arguments.event,'EditProducts') IS 1;
			local.canDelete = checkRights(arguments.event,'DeleteProducts') IS 1;
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sm.shippingName")>
		<cfset arrayAppend(local.arrCols,"sm.Visible")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryShippingMethods" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpShippingMethods') IS NOT NULL 
				DROP TABLE ##tmpShippingMethods;
			CREATE TABLE ##tmpShippingMethods (shippingID int PRIMARY KEY, shippingName varchar(200), visible bit, row int)

			DECLARE @siteID int, @storeID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID',0)#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
				
			INSERT INTO ##tmpShippingMethods (shippingID, shippingName, visible, row)
			SELECT sm.shippingID, sm.shippingName, sm.Visible, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
			FROM dbo.store_ShippingMethods AS sm
			INNER JOIN dbo.store AS s ON s.storeID = sm.storeID 
			WHERE s.storeID = @storeID
			AND s.siteID = @siteID
			<cfif len(local.searchValue)>
			 	AND sm.shippingName LIKE @searchValue
			</cfif>;

			SET @totalCount = @@rowcount;

			SELECT shippingID, shippingName, Visible, @totalCount AS totalCount
			FROM ##tmpShippingMethods
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpShippingMethods') IS NOT NULL 
				DROP TABLE ##tmpShippingMethods;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryShippingMethods">
			<cfset local.arrData.append({
				"shippingID": local.qryShippingMethods.shippingID,
				"shippingName": local.qryShippingMethods.shippingName,
				"shippingNameEnc": encodeForHTMLAttribute(local.qryShippingMethods.shippingName),
				"visible": YesNoFormat(local.qryShippingMethods.Visible),
				"canEdit": local.canEdit,
				"canDelete": local.canDelete,
				"totalcount": local.qryShippingMethods.totalcount,
				"DT_RowId": "shippingRow_#local.qryShippingMethods.shippingID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryShippingMethods.totalcount),
			"recordsFiltered": val(local.qryShippingMethods.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProductList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			arguments.event.paramValue('kw','');
			arguments.event.paramValue('cat','');
			arguments.event.paramValue('shd','');

			local.hasAddProductRights =  checkRights(arguments.event,'AddProducts');
			local.hasEditProductRights	= checkRights(arguments.event,'EditProducts');
			local.hasDeleteProductRights = checkRights(arguments.event,'DeleteProducts');
		</cfscript>
		<cfset local.arrCols = arrayNew(1)>
		<cfif arguments.event.getValue('listMode','products') EQ 'products'>
			<cfset arrayAppend(local.arrCols,"cl.contentTitle #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"p.productID #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"CASE WHEN p.productDate IS NULL THEN 1 ELSE 0 END, p.productDate #arguments.event.getValue('orderDir')#")>
		<cfelseif arguments.event.getValue('listMode','') EQ 'addOrderItem'>
			<cfset arrayAppend(local.arrCols,"cl.contentTitle #arguments.event.getValue('orderDir')#, p.productID #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"cl.contentTitle #arguments.event.getValue('orderDir')#, p.productID #arguments.event.getValue('orderDir')#")>
		</cfif>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryStoreProducts" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpStoreProducts') IS NOT NULL
				DROP TABLE ##tmpStoreProducts;
			CREATE TABLE ##tmpStoreProducts (itemID int, contentTitle varchar(200), productID varchar(50), productDate datetime,
				status char(1), row int);

			DECLARE @posStart int, @posStartAndCount int, @totalCount int, @kw varchar(300), @storeID int;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif arguments.event.getTrimValue('kw','') NEQ ''>
				SET @kw = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('kw')#%">;
			</cfif>
			SET @storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="CF_SQL_INTEGER">;

			INSERT INTO ##tmpStoreProducts(ItemID, status, productID, contentTitle, productDate, row)
			SELECT p.ItemID, p.status, p.productID, cl.contentTitle, p.productDate, 
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			FROM dbo.store_Products as p
			INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = p.productContentID and cl.languageID = 1
			WHERE p.storeID = @storeID
			<cfif len(arguments.event.getValue('kw'))>
				AND (cl.contentTitle like @kw or p.productID like @kw ) 
			</cfif>			
			<cfif len(arguments.event.getTrimValue('cat',''))>
				AND p.ItemID in (select ItemID from dbo.store_ProductCategoryLinks where categoryID = <cfqueryparam value="#arguments.event.getTrimValue('cat','')#" cfsqltype="CF_SQL_INTEGER">)
			</cfif>
			<cfif arguments.event.getTrimValue('shd',0) is 0>
				AND p.status <> 'D'
			</cfif>;

			SET @totalCount = @@rowcount;

			SELECT ItemID, status, productID, contentTitle, productDate, @totalCount AS totalCount
			FROM ##tmpStoreProducts
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpStoreProducts') IS NOT NULL 
				DROP TABLE ##tmpStoreProducts;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryStoreProducts">
			<cfset local.arrData.append({
				"ItemID": local.qryStoreProducts.ItemID,
				"status": local.qryStoreProducts.status,
				"productID": local.qryStoreProducts.productID,
				"contentTitle": local.qryStoreProducts.contentTitle,
				"contentTitleEnc": encodeForJavaScript(local.qryStoreProducts.contentTitle),
				"productDate": Dateformat(local.qryStoreProducts.productDate,"m/d/yyyy"),
				"canEdit": local.hasEditProductRights,
				"canDelete": local.hasDeleteProductRights,
				"canAdd": local.hasAddProductRights,
				"DT_RowId": "storeProductRow_#local.qryStoreProducts.ItemID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryStoreProducts.totalcount),
			"recordsFiltered": val(local.qryStoreProducts.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getStoreOrderList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			local.hasViewOrderRights = checkRights(arguments.event,'ViewOrders');

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('mid',int(val(arguments.event.getValue('mid',0))));
		</cfscript>
		
		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"o.orderID")>
		<cfset arrayAppend(local.arrCols,"s.statusName")>
		<cfset arrayAppend(local.arrCols,"o.DateOfOrder")>
		<cfif arguments.event.getTrimValue('mid') gt 0>
			<cfset arrayAppend(local.arrCols,"pc.contentTitle")>
		<cfelse>
			<cfset arrayAppend(local.arrCols,"m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')'")>
		</cfif>		
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryProductData" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpFilteredOrders') IS NOT NULL 
					DROP TABLE ##tmpFilteredOrders; 
				IF OBJECT_ID('tempdb..##tmpStoreOrderFees') IS NOT NULL 
					DROP TABLE ##tmpStoreOrderFees;
				IF OBJECT_ID('tempdb..##tblStoreOrderInvoices') IS NOT NULL 
					DROP TABLE ##tblStoreOrderInvoices;
				CREATE TABLE ##tmpFilteredOrders (orderID int PRIMARY KEY, rowID int);
				CREATE TABLE ##tmpStoreOrderFees (orderID int, totalFee decimal(18,2), remainingFee decimal(18,2));
				CREATE TABLE ##tblStoreOrderInvoices (orderID int, invoiceID int);

				DECLARE @orgID int, @storeID int, @totalCount int, @posStart int, @posStartPlusCount int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">;

				INSERT INTO ##tmpFilteredOrders (orderID, rowID)
				SELECT o.orderID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as rowID
				FROM dbo.store_orders as o
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = o.memberID
					AND o.storeID = @storeID
				INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activememberid
				LEFT OUTER JOIN dbo.store_OrderStatus as s on s.orderStatusID = o.orderStatusID
				<cfif len(arguments.event.getTrimValue('o_prdkey','')) OR arguments.event.getTrimValue('mid') gt 0>
					left outer join dbo.store_orderDetails as od 
						inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
						inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
					on od.orderID = o.orderID
				</cfif>
				WHERE o.orderCompleted = 1
				<cfif len(arguments.event.getTrimValue('o_prdkey',''))>
					and p.productID + pc.contentTitle like <cfqueryparam value="%#arguments.event.getTrimValue('o_prdkey','')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(arguments.event.getTrimValue('o_id',''))>
					and o.orderID like <cfqueryparam value="%#arguments.event.getTrimValue('o_id','')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(arguments.event.getTrimValue('o_pur',''))>
					and m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' like <cfqueryparam value="%#arguments.event.getTrimValue('o_pur')#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(arguments.event.getTrimValue('o_st',''))>
					and o.orderStatusID in (<cfqueryparam value="#arguments.event.getValue('o_st')#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
				<cfif len(arguments.event.getTrimValue('o_ds',''))>
					and o.DateOfOrder >= <cfqueryparam value="#arguments.event.getValue('o_ds')#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getTrimValue('o_de',''))>
					and o.DateOfOrder <= <cfqueryparam value="#arguments.event.getValue('o_de')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				<cfif arguments.event.getTrimValue('mid') gt 0>
					and m2.memberid = <cfqueryparam value="#arguments.event.getTrimValue('mid')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				group by o.orderID<cfif local.orderby neq 'o.orderID'>, #preserveSingleQuotes(local.orderby)#</cfif>;

				SET @totalCount = @@ROWCOUNT;

				DELETE FROM ##tmpFilteredOrders
				WHERE NOT (rowID > @posStart AND rowID <= @posStartPlusCount);

				INSERT INTO ##tmpStoreOrderFees (orderID, totalFee, remainingFee)
				select tmp.orderID, sum(ts.cache_amountAfterAdjustment) as totalFee, sum(ts.cache_amountAfterAdjustment)-sum(ts.cache_activePaymentAllocatedAmount) as remainingFee
				from ##tmpFilteredOrders as tmp
				cross apply dbo.fn_store_orderTransactions(@orgID,tmp.orderid) as ot
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = ot.transactionID
				group by tmp.orderID;

				INSERT INTO ##tblStoreOrderInvoices (orderID, invoiceID)
				select distinct tmp.orderID, it.invoiceID
				from ##tmpFilteredOrders as tmp
				cross apply dbo.fn_store_orderTransactions(@orgID,tmp.orderid) as ot
				inner join dbo.tr_invoiceTransactions it on it.orgID = ot.ownedByOrgID and it.transactionid = ot.transactionid
				where (it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0

				select distinct o.orderID, s.statusName, o.DateOfOrder, mActive.status, mActive.memberid, isnull(orderFees.totalFee,0) as totalFee, 
					isNull(orderFees.remainingFee,0) as amountDue, mActive.company, 
					mActive.lastname + ', ' + mActive.firstname + ' ' + isnull(mActive.middlename,'') + ' (' + mActive.membernumber + ')' as memberName, 
					<cfif arguments.event.getTrimValue('mid') gt 0>
						productNameList = (
							select replace(STRING_AGG( replace(contentTitle,'|',char(7)),'<br/>'),char(7),'|')
							from (
							select distinct pc.contentTitle
							from dbo.store_orderDetails as od
							inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
							inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
							where od.orderID = o.orderID) as tmp
						),
					</cfif>
					invoiceIDList = case 
										when orderFees.remainingFee > 0 
											then (select substring((
											  select ','+ cast(invoiceID as varchar(10)) AS [text()]
												from ##tblStoreOrderInvoices
												where orderID = tmp.orderID
												For XML PATH ('')
												), 2, 2000)) 
										else '' end, 
					tmp.rowID, @totalCount as totalCount
				from ##tmpFilteredOrders as tmp
				inner join dbo.store_orders as o on o.orderID = tmp.orderID 
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = o.memberID
				inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberid = m.activememberid
				left outer join dbo.store_OrderStatus as s on s.orderStatusID = o.orderStatusID
				left outer join ##tmpStoreOrderFees as orderFees on orderFees.orderID = tmp.orderID
				order by tmp.rowID;

				IF OBJECT_ID('tempdb..##tmpFilteredOrders') IS NOT NULL 
					DROP TABLE ##tmpFilteredOrders; 
				IF OBJECT_ID('tempdb..##tmpStoreOrderFees') IS NOT NULL 
					DROP TABLE ##tmpStoreOrderFees;
				IF OBJECT_ID('tempdb..##tblStoreOrderInvoices') IS NOT NULL 
					DROP TABLE ##tblStoreOrderInvoices;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryProductData">
			<cfset local.addPaymentEncString = "">
			<cfif local.qryProductData.amountDue gt 0 and local.hasViewOrderRights>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryProductData.memberID, t="Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryProductData.orderID,"0000")#", ta=local.qryProductData.amountDue, tmid=local.qryProductData.memberID, ad="v|#local.qryProductData.invoiceidList#")>
			</cfif>		

			<cfset local.arrData.append({
				"orderID": Numberformat(local.qryProductData.orderID,"0000"),
				"orderNumber": arguments.event.getValue('mc_siteinfo.sitecode') & Numberformat(local.qryProductData.orderID,"0000"),
				"status": local.qryProductData.status,
				"statusName": local.qryProductData.statusName,
				"DateOfOrder": dateformat(local.qryProductData.DateOfOrder,'m/d/yy') & ' ' & timeformat(local.qryProductData.DateOfOrder,'h:mm tt'),
				"productNameList": local.qryProductData.productNameList ?: "",
				"purchaserCompany": local.qryProductData.company,
				"purchaserName": local.qryProductData.memberName,
				"memberID": local.qryProductData.memberID,
				"totalFee": dollarFormat(local.qryProductData.totalFee),
				"amountDue": dollarFormat(local.qryProductData.amountDue),
				"payForOrder": local.qryProductData.amountDue GT 0 AND local.hasViewOrderRights,
				"addPaymentEncString" : local.addPaymentEncString,
				"hasViewOrderRights": local.hasViewOrderRights
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryProductData.totalcount),
			"recordsFiltered": val(local.qryProductData.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCategoryList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			local.security.edit = checkRights(arguments.event,'EditProducts');
			local.security.delete = checkRights(arguments.event,'DeleteProducts');

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.searchValue = form['search[value]'] ?: '';
		</cfscript>
		
		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tmp.thePath #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryCategoryData" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpCategoryList') IS NOT NULL
				DROP TABLE ##tmpCategoryList;
			CREATE TABLE ##tmpCategoryList (categoryID int, categoryName varchar(200), parentCategoryID int, visibility char(1), thePathExpanded varchar(max), 
				thePath varchar(max), numberLinkedProducts int, row int);

			DECLARE @storeID int, @posStart int, @posStartAndCount int, @totalCount int, @searchValue varchar(300);
			SET @storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpCategoryList (categoryID, categoryName, parentCategoryID, visibility, thePathExpanded, thePath, numberLinkedProducts, row)
			SELECT categoryID, categoryName, parentCategoryID, visibility, thePathExpanded, thePath, numberLinkedProducts, 
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
			FROM (
				SELECT c.categoryID, c.categoryName, c.parentCategoryID, c.visibility, c.thePathExpanded, c.thePath, count(p.ItemId) as numberLinkedProducts
				FROM dbo.fn_getRecursiveStoreCategories(@storeID, null, null) as c
				LEFT OUTER JOIN dbo.store_ProductCategoryLinks as p on p.categoryID = c.categoryID
				<cfif len(local.searchValue)>
					WHERE c.thePathExpanded LIKE @searchValue
				</cfif>
				GROUP BY c.categoryID, c.categoryName, c.parentCategoryID, c.visibility, c.thePathExpanded, c.thePath
			) as tmp;

			SET @totalCount = @@ROWCOUNT;

			SELECT categoryID, categoryName, parentCategoryID, visibility, thePathExpanded, thePath, numberLinkedProducts, @totalCount AS totalcount
			FROM ##tmpCategoryList
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;
			
			IF OBJECT_ID('tempdb..##tmpCategoryList') IS NOT NULL
				DROP TABLE ##tmpCategoryList;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryCategoryData">
			<cfset local.arrData.append({
				"categoryID": local.qryCategoryData.categoryID,
				"visibility": local.qryCategoryData.visibility,
				"categoryName": htmlEditFormat(local.qryCategoryData.categoryName),
				"thePathExpanded": htmlEditFormat(local.qryCategoryData.thePathExpanded),
				"numberLinkedProducts": local.qryCategoryData.numberLinkedProducts,
				"canEdit": local.security.edit,
				"canDelete": local.security.delete AND val(local.qryCategoryData.numberLinkedProducts) EQ 0
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryCategoryData.totalcount),
			"recordsFiltered": val(local.qryCategoryData.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getUnclaimedAffirmations" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.hasEditProductRights = checkRights(arguments.event,'EditProducts');
			local.hasDeleteProductRights = checkRights(arguments.event,'DeleteProducts');
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"ca.dateIssued #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"cl.contentTitle + ca.affirmationCode #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryUnclaimedAffirmations" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpUnclaimedAffirmations') IS NOT NULL
				DROP TABLE ##tmpUnclaimedAffirmations;
			CREATE TABLE ##tmpUnclaimedAffirmations (affirmationID int, itemID int, linkAffirmation bit, validFormatInd bit, dateIssued datetime, 
				status char(1), affirmationCode char(10), purchaserMemberID int, purchaserName varchar(250), purchaserCompany varchar(200), 
				productTitle varchar(200), row int);

			DECLARE @posStart int, @posStartAndCount int, @totalCount int, @searchValue varchar(300), @storeID int;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			SET @storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">;

			INSERT INTO ##tmpUnclaimedAffirmations(affirmationID, itemID, linkAffirmation, validFormatInd, dateIssued, status, affirmationCode, 
				purchaserMemberID, purchaserName, purchaserCompany, productTitle, row)
			select ca.affirmationID, spf.itemid, 
				linkAffirmation = cast(
					case 
					when ca.status = 'A' 
						and co.completeByDate >= getDate() 
						and exists (
							select distinct innerpf.Formatid
							from dbo.store_ProductFormats as innerpf
							inner join dbo.crd_offerings as innerec on innerec.productFormatID = innerpf.Formatid
							where innerpf.itemid = spf.itemid
							and innerpf.status = 'A'
						) then 1 
					else 0 
					end as bit),
				validFormatInd = cast(0 AS bit), ca.dateIssued,	ca.status, ca.affirmationCode, m2.activememberID,
				m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')',
				isNull(m2.company, ''), cl.contentTitle,
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			from dbo.crd_affirmations as ca
			inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = ca.affirmationTypeID  and cat.affirmationType = 'paper'
			inner join dbo.store_productFormats as spf on spf.formatID = ca.productFormatID
			inner join dbo.store_products as sp on sp.storeID = @storeID and sp.itemid = spf.itemid 
			inner join dbo.cms_contentLanguages as cl on cl.contentid = sp.productContentID and cl.languageID = 1
			inner join dbo.crd_offerings as co on co.offeringid = ca.offeringid
			inner join dbo.ams_members as m on m.memberid = ca.issuedByMemberID
			inner join dbo.ams_members as m2 on m2.memberid = m.activememberid	
			where ca.assignToMemberID is null
			<cfif len(local.searchValue)>
				and (ca.affirmationCode like @searchValue or cl.contentTitle like @searchValue)
			</cfif>;

			SET @totalCount = @@rowcount;

			SELECT affirmationid, itemID, linkAffirmation, validFormatInd, dateIssued, status, affirmationCode, 
				purchaserMemberID, purchaserName, purchaserCompany, productTitle, @totalCount AS totalCount
			FROM ##tmpUnclaimedAffirmations
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpUnclaimedAffirmations') IS NOT NULL 
				DROP TABLE ##tmpUnclaimedAffirmations;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryUnclaimedAffirmations">
			<cfset local.arrData.append({
				"affirmationID": local.qryUnclaimedAffirmations.affirmationID,
				"itemID": local.qryUnclaimedAffirmations.itemID,
				"linkAffirmation": local.qryUnclaimedAffirmations.linkAffirmation,
				"validFormatInd": local.qryUnclaimedAffirmations.validFormatInd,
				"dateIssued": Dateformat(local.qryUnclaimedAffirmations.dateIssued,"m/d/yyyy"),
				"status": local.qryUnclaimedAffirmations.status,
				"affirmationCode": local.qryUnclaimedAffirmations.affirmationCode,
				"purchaserMemberID": local.qryUnclaimedAffirmations.purchaserMemberID,
				"purchaserName": local.qryUnclaimedAffirmations.purchaserName,
				"productTitle": local.qryUnclaimedAffirmations.productTitle,
				"purchaserName": local.qryUnclaimedAffirmations.purchaserName,
				"purchaserCompany": local.qryUnclaimedAffirmations.purchaserCompany,
				"canEdit": local.hasEditProductRights,
				"canDelete": local.hasDeleteProductRights,
				"DT_RowId": "unclaimedAffirmRow_#local.qryUnclaimedAffirmations.itemID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryUnclaimedAffirmations.totalcount),
			"recordsFiltered": val(local.qryUnclaimedAffirmations.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getClaimedAffirmations" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.hasEditProductRights = checkRights(arguments.event,'EditProducts');
			local.hasDeleteProductRights = checkRights(arguments.event,'DeleteProducts');
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"ca.dateClaimed #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"cl.contentTitle + ca.affirmationCode #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryClaimedAffirmations" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpClaimedAffirmations') IS NOT NULL
				DROP TABLE ##tmpUnclaimedAffirmations;
			CREATE TABLE ##tmpClaimedAffirmations (affirmationID int, itemID int, affirmationCode char(10), dateClaimed datetime, issuedByMemberID int, orderID int, 
				productFormatID int, offeringID int, status char(1), assignToMemberID int, formatName varchar(250), productTitle varchar(200), 
				assigneeMemberID int, assigneeName varchar(250), assigneeCompany varchar(200),purchaserMemberID int, purchaserName varchar(250), 
				purchaserCompany varchar(200),  row int);

			DECLARE @posStart int, @posStartAndCount int, @totalCount int, @searchValue varchar(300), @storeID int;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			SET @storeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">;

			INSERT INTO ##tmpClaimedAffirmations(affirmationID, itemID, affirmationCode, dateClaimed, issuedByMemberID, orderID, productFormatID, offeringID, status,
				assignToMemberID, formatName, productTitle, assigneeMemberID, assigneeName, assigneeCompany, purchaserMemberID, purchaserName, purchaserCompany, row)
			select ca.affirmationid, spf.itemid, ca.affirmationCode, ca.dateClaimed, ca.issuedByMemberID, ca.orderID,
				ca.productFormatID, ca.offeringID, ca.status, ca.assignToMemberID,  spf.name as formatName,
				cl.contentTitle as productTitle, m2.activememberID as assigneeMemberID,
				m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as assigneeName,
				isNull(m2.company, '') as assigneeCompany, m3.activememberID as purchaserMemberID,
				m3.lastname + ', ' + m3.firstname + isnull(' ' + m3.middlename,'') + ' (' + m3.membernumber + ')' as purchaserName,
				isnull(m3.company,'') as purchaserCompany, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			from dbo.crd_affirmations ca 
			inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = ca.affirmationTypeID and cat.affirmationType = 'paper'
			inner join dbo.store_productFormats spf on spf.formatID = ca.productFormatID
			inner join dbo.store_products sp on sp.storeID = @storeID and sp.itemid = spf.itemid 
			inner join dbo.cms_contentLanguages cl on cl.contentid = sp.productContentID and cl.languageID = 1
			inner join dbo.ams_members m on m.memberid = ca.assignToMemberID
			inner join dbo.ams_members m2 on m2.memberid = m.activememberid	
			inner join dbo.ams_members as m3 on m3.memberid = ca.issuedByMemberID
			inner join dbo.ams_members as m4 on m4.memberid = m3.activememberid	
			<cfif len(local.searchValue)>
				where (ca.affirmationCode like @searchValue or cl.contentTitle like @searchValue)
			</cfif>;

			SET @totalCount = @@rowcount;

			SELECT affirmationID, itemID, affirmationCode, dateClaimed, issuedByMemberID, orderID, productFormatID, offeringID, status,  
				assignToMemberID, formatName, productTitle, assigneeMemberID, assigneeName, assigneeCompany, purchaserMemberID, purchaserName, 
				purchaserCompany, @totalCount AS totalCount
			FROM ##tmpClaimedAffirmations
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpClaimedAffirmations') IS NOT NULL 
				DROP TABLE ##tmpClaimedAffirmations;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryClaimedAffirmations">
			<cfset local.arrData.append({
				"affirmationID": local.qryClaimedAffirmations.affirmationID,
				"itemID": local.qryClaimedAffirmations.itemID,
				"affirmationCode": local.qryClaimedAffirmations.affirmationCode,
				"dateClaimed": Dateformat(local.qryClaimedAffirmations.dateClaimed,"m/d/yyyy"),
				"issuedByMemberID": local.qryClaimedAffirmations.issuedByMemberID,
				"orderID": local.qryClaimedAffirmations.orderID,
				"productFormatID": local.qryClaimedAffirmations.productFormatID,
				"offeringID": local.qryClaimedAffirmations.offeringID,
				"status": local.qryClaimedAffirmations.status,
				"assignToMemberID": local.qryClaimedAffirmations.assignToMemberID,
				"formatName": local.qryClaimedAffirmations.formatName,
				"productTitle": local.qryClaimedAffirmations.productTitle,
				"assigneeMemberID": local.qryClaimedAffirmations.assigneeMemberID,
				"assigneeName": local.qryClaimedAffirmations.assigneeName,
				"assigneeCompany": local.qryClaimedAffirmations.assigneeCompany,
				"purchaserMemberID": local.qryClaimedAffirmations.purchaserMemberID,
				"purchaserName": local.qryClaimedAffirmations.purchaserName,
				"purchaserCompany": local.qryClaimedAffirmations.purchaserCompany,
				"canEdit": local.hasEditProductRights,
				"canDelete": local.hasDeleteProductRights,
				"DT_RowId": "claimedAffirmRow_#local.qryClaimedAffirmations.itemID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryClaimedAffirmations.totalcount),
			"recordsFiltered": val(local.qryClaimedAffirmations.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getStreamProfiles" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.hasEditRights = checkRights(arguments.event,'EditProducts');
			local.hasDeleteRights = checkRights(arguments.event,'DeleteProducts');
		</cfscript>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"p.profileName #arguments.event.getValue('orderDir')#, pp.providerName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"p.isActive #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryGetStreamProfiles" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpStreamProfiles') IS NOT NULL
				DROP TABLE ##tmpStreamProfiles;
			CREATE TABLE ##tmpStreamProfiles (profileID int, providerName varchar(100), profileName varchar(100), isActive bit, usageCount int, row int);

			DECLARE @siteID int, @posStart int, @posStartAndCount int, @totalCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpStreamProfiles(profileID, providerName, profileName, isActive, usageCount, row)
			SELECT p.profileID, pp.providerName, p.profileName, p.isActive,
				(SELECT COUNT(tmp.usageID) FROM dbo.stream_profileUsages AS tmp WHERE tmp.profileID = p.profileID),
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
			FROM dbo.stream_profiles as p
			INNER JOIN dbo.stream_providers as pp on pp.providerID = p.providerID
			WHERE p.siteID = @siteID
			AND pp.isActive = 1
			<cfif len(local.searchValue)>
				AND (p.profileName LIKE  @searchValue OR pp.providerName LIKE @searchValue)
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT profileID, providerName, profileName, isActive, usageCount, @totalCount AS totalCount
			FROM ##tmpStreamProfiles
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpStreamProfiles') IS NOT NULL 
				DROP TABLE ##tmpStreamProfiles;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryGetStreamProfiles">
			<cfset local.arrData.append({
				"profileID": local.qryGetStreamProfiles.profileID,
				"providerName": local.qryGetStreamProfiles.providerName,
				"profileName": local.qryGetStreamProfiles.profileName,
				"profileNameEnc": encodeForHTMLAttribute(local.qryGetStreamProfiles.profileName),
				"isActive": YesNoFormat(local.qryGetStreamProfiles.isActive),
				"canEdit": local.hasEditRights,
				"canDelete": local.hasDeleteRights AND local.qryGetStreamProfiles.usageCount eq 0,
				"DT_RowId": "streamProfileRow_#local.qryGetStreamProfiles.profileID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryGetStreamProfiles.totalcount),
			"recordsFiltered": val(local.qryGetStreamProfiles.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getStoreProductFormatsRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.hideInactiveFormatsAndRates = arguments.event.getValue('hideInactive',1);
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));	
			
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.objStore	= CreateObject("component","model.admin.store.store");
			
			local.qryAllRates = local.objStore.getRateByItemID(arguments.event.getValue('itemID'));
			if (local.hideInactiveFormatsAndRates)
				local.qryRates = QueryFilter(local.qryAllRates, function(thisRow) { return arguments.thisRow.status EQ 'A'; });
			else 
				local.qryRates = duplicate(local.qryAllRates);

			local.storeInfo = local.objStore.getStoreInfo(arguments.event);
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		</cfscript>

		<cfquery name="local.totalRateFormats" dbtype="query">
			SELECT DISTINCT formatID
			FROM [local].qryRates
			WHERE formatID > 0
		</cfquery>

		<cfquery name="local.qryMaxFormatOrder" dbtype="query">
			SELECT MAX(formatOrder) AS formatOrder
			FROM [local].qryAllRates
		</cfquery>

		<cfset local.arrData = []>
					
		<cfoutput query="local.qryRates" group="formatID">
			<cfset local.qryDocuments = local.objStore.getStoreDocuments(formatID=local.qryRates.formatID)>
			<cfset local.qryStreams = local.objStore.getStoreStreams(formatID=local.qryRates.formatID)>
			<cfset local.itemTypeName = local.qryRates.isAffirmation ? "Affirmation" : "Format">
			
			<cfif local.totalRateFormats.recordCount>
				<cfset local.tmpStr = {
					"formatID": local.qryRates.formatID,
					"formatName": htmleditformat(local.qryRates.formatName),
					"itemTypeName": local.itemTypeName,
					"itemTypeNameEnc": encodeForJavaScript(local.itemTypeName),
					"status": local.qryRates.status,
					"itemID": local.qryRates.itemID,
					"inventory": local.qryRates.inventory,
					"arrRates": [],
					"arrDocuments": [],
					"arrStreams": [],
					"canMoveUp": local.qryRates.formatOrder NEQ 1,
					"canMoveDown": local.qryRates.formatOrder NEQ local.qryMaxFormatOrder.formatOrder
				}>

				<cfset local.count = 0 />
				<cfoutput group="rateid">
					<cfset local.count++>
					<cfset local.rateStartDate = "">
					<cfset local.rateEndDate = "">
					<cfset local.qryRateOverrides = local.objStore.getRateOverrides(local.qryRates.rateID)>
					<cfif val(local.qryRates.rateID) gt 0>
						<cfscript>
							// convert times from central (how stored in db) to default timezone of site
							local.nowDate = '#now()#';
							if (local.regTimeZone neq "US/Central") {	
								local.nowDate = local.objTZ.convertTimeZone(dateToConvert=local.nowDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
								local.rateStartDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRates.startdate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
								if (len(local.qryRates.enddate))
									local.rateEndDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRates.enddate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
							} else {
								local.rateStartDate = local.qryRates.startdate;
								local.rateEndDate = local.qryRates.enddate;
							}

							local.isRateActive = 0;
							if (local.qryRates.status eq 'A' 
								AND local.nowDate gte local.rateStartDate
								AND (NOT len(local.rateEndDate) OR local.nowDate lte local.rateEndDate))
								local.isRateActive = 1;
						</cfscript>

						<cfset local.tmpRateStr = {
							"rateID": local.qryRates.rateID,
							"siteResourceID": local.qryRates.siteResourceID,
							"itemID": local.qryRates.itemID,
							"rateName": htmleditformat(local.qryRates.rateName),
							"rateNameJs": replace(replace(local.qryRates.rateName,chr(34),'','ALL'),"'","","ALL"),
							"isRateActive": local.isRateActive,
							"rate": dollarformat(local.qryRates.rate),
							"rateDateDisp": "#len(trim(local.rateEndDate)) GT 0 ? '#dateformat(local.rateStartDate,'m/d/yy')# - #dateformat(local.rateEndDate,'m/d/yy')#' : 'Perpetual (Starting #dateformat(local.rateStartDate,'m/d/yy')#)'#",
							"arrGroups": [],
							"arrRateOverride": [],
							"canEditProducts": (arguments.event.getValue('mc_adminToolInfo.myRights.EditProducts') is 1)
						}>

						<cfoutput>
							<cfif local.qryRates.groupID gt 0>
								<cfset local.tmpRateStr.arrGroups.append({
									"groupName": "#local.qryRates.groupName#",
									"include": local.qryRates.include,
									"groupID": local.qryRates.groupID
								})>
							</cfif>
						</cfoutput>

						<cfif local.qryRateOverrides.recordCount>
							<cfloop query="local.qryRateOverrides">
								<cfscript>
									// convert times from central (how stored in db) to default timezone of site
									local.rateOverrideStartDate = "";
									local.rateOverrideEndDate = "";
									if (local.regTimeZone neq "US/Central") {
										local.rateOverrideStartDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRateOverrides.overrideStartDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
										local.rateOverrideEndDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRateOverrides.overrideEndDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
									} else {
										local.rateOverrideStartDate = local.qryRateOverrides.overrideStartDate;
										local.rateOverrideEndDate = local.qryRateOverrides.overrideEndDate;
									}					
								</cfscript>
								<cfset local.tmpRateStr.arrRateOverride.append({
									"rateOverrideID": local.qryRateOverrides.rateOverrideID,
									"rateID": local.qryRateOverrides.rateID,
									"rateOverrideName": local.qryRateOverrides.rateOverrideName,
									"rateOverrideDatesDisp": "#dateformat(local.rateOverrideStartDate,'m/d/yy')# - #dateformat(local.rateOverrideEndDate,'m/d/yy')#",
									"rateOverride": dollarformat(local.qryRateOverrides.rateOverride)
								})>
							</cfloop>
						</cfif>

						<cfif NOT (local.hideInactiveFormatsAndRates AND local.isRateActive EQ 0)>
							<cfset local.tmpStr.arrRates.append(local.tmpRateStr)>
						</cfif>
					</cfif>	<!--- //if rateID gt 0 --->
				</cfoutput>
				<cfif local.qryDocuments.recordCount>
					<cfloop query="local.qryDocuments">
						<cfset local.previewLink = "/docDownload/#local.qryDocuments.documentID#&vid=#local.qryDocuments.documentVersionID#&lang=#local.qryDocuments.languageCode#" />
						<cfset local.tmpDocumentsStr = {
							"formatID": local.qryDocuments.formatID,
							"documentID": local.qryDocuments.documentID,
							"itemID": local.qryDocuments.ItemID,
							"usageID": local.qryDocuments.uid,
							"docTitle": htmleditformat(local.qryDocuments.docTitle),
							"fileName": htmleditformat(local.qryDocuments.fileName),
							"previewLink": "#local.previewLink#"
						}>
						<cfset local.tmpStr.arrDocuments.append(local.tmpDocumentsStr)>
					</cfloop>					
				</cfif>
				<cfif local.qryStreams.recordCount>
					<cfloop query="local.qryStreams">
						<cfset local.tmpStreamsStr = {
							"formatID": local.qryStreams.formatID,
							"usageID": local.qryStreams.usageID,
							"streamName": htmleditformat(local.qryStreams.streamName)
						}>
						<cfset local.tmpStr.arrStreams.append(local.tmpStreamsStr)>
					</cfloop>					
				</cfif>	
				<cfset local.arrData.append(local.tmpStr)>		
			</cfif>
		</cfoutput>		
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrData),
			"recordsFiltered": arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>