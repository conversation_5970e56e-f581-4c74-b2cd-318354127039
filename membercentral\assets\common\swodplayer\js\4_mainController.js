(function(){

	angular.module('module_1').controller('controller_1', function ($scope, $rootScope, $http, $window, $timeout, $location, $anchorScroll, cssData, dataBase, components, SeminarDataService,APIRequestService, AppManager,$interval,PromptTimerService,TimerService,PostTestTimerService) {			
	var self = this;
		$rootScope.currentActiveTab = '';
		$rootScope.activeAndInactiveBoth = false;
		$rootScope.showAlert = false;
		$rootScope.showLeftContent = true;
		$rootScope.seminarType = seminarType;
		$rootScope.currentLoadPoint = '';
		$rootScope.saveTextBtn = 'Save & Exit';
		$rootScope.CloseWindowBtn = 'YES';
		$rootScope.syncPointsPaper = '';
		$rootScope.showMsgPop = false;
		$rootScope.hasPaper = 0;
		$rootScope.showMsgCnt = '';
		$rootScope.sendExitEmail = "No";
		$rootScope.arrSyncPointsToDisp = [];
		$rootScope.showCompleteAlert = false;
		$rootScope.Math = window.Math;
		$rootScope.leftPanelLarge = 'large-4';
		$rootScope.rightPanelLarge = 'large-6';
		$rootScope.rightPanelPPTLarge = 'large-12';
		$rootScope.supportEmail = '';
		$rootScope.alertType = '';
		if(seminarType == 'swl'){
			$rootScope.rightPanelLarge = 'large-12';
			$rootScope.showLeftContent = false;
			$rootScope.saveTextBtn = 'Exit';
			$rootScope.CloseWindowBtn = 'Close Window';
		}
		
		if(userType == 1){
			$rootScope.userType = true;
		}else{
			$rootScope.userType = false;			
		}
		$rootScope.$watch( 'showAlert', function() {
			if( $rootScope.showAlert ) {
				$location.hash('alertContainer');
				$anchorScroll();
			}
		});		

		/*getting json data*/
		dataBase.jsonData.then( function( data ) {
			$scope.data = data.data;
			//$rootScope.requiredMaterialsPercent = $scope.data.requiredMaterialsPercent;
		} );
		var browserLostFocusTimer = '';
		var browserWithFocusTimer = '';
		var inactiveTimePeriod = '';
		var inactiveAndActiveTimePeriod = '';
		$scope.$on('serverOffline', function() {
			try {videoJS.mediaPause()} catch (e) {}	
		});
		$rootScope.showSyncSection = function () {
			$rootScope.leftPanelLarge = 'large-3';
			$rootScope.rightPanelLarge = 'large-6';
			$rootScope.rightPanelPPTLarge = 'large-6';
			angular.element('.progressSlider').addClass('sliderWidth');			
			
		}
		
		$rootScope.closeSyncSection = function () {
			$rootScope.leftPanelLarge = 'large-3';
			$rootScope.rightPanelLarge = 'large-6';
			$rootScope.rightPanelPPTLarge = 'large-12';
			angular.element('.progressSlider').removeClass('sliderWidth');
			angular.element('.mediaSyncWrap').hide();		
				angular.element('.syncWrapCnt').hide();	
			
		}
		$scope.closeAlert = function (alertType) {
			var userLoggedIn = true;
			if( $rootScope.hasOwnProperty( 'isUserLoggedIn' ) ) {
				userLoggedIn = $rootScope.isUserLoggedIn;
			}		
		
			$rootScope.showAlert = false;
			TimerService.resume();
			if( userLoggedIn ) {			
				
				try{
					PromptTimerService.stop();
					/*if(PromptTimerService.popupDisplayed){
						PromptTimerService.start();
					}*/
					PromptTimerService.start();
					//if(PromptTimerService.mediaPaused){
						if(!AppManager.testInProgress){
							PromptTimerService.mediaPaused = false;
							if((AppManager.semninarHasPreTest && AppManager.allPreTestCompleted) || alertType == 'Attendance' || alertType == 'fw'){
								$timeout(function() {
									videoJS.mediaPlay();
								}, 10);		
							}
						}
						
					//}
				}catch(e){}
				//console.log('angular.isDefined(browserLostFocusTimer = '+angular.isDefined(browserLostFocusTimer));
				if (angular.isDefined(browserLostFocusTimer)) {
					$interval.cancel(browserLostFocusTimer);
					//AppManager.addActivityLogEntry("Browser window gained focus during Exam.  Inactivity length: " + inactiveTimePeriod);
					//console.log('Timer stopped!!');
					//console.log('User was inactive for '+inactiveTimePeriod+' seconds');
				}
				if (angular.isDefined(browserWithFocusTimer)) {
					
					if(inactiveAndActiveTimePeriod > 0 && inactiveAndActiveTimePeriod <= 60){
					}else{
						$interval.cancel(browserWithFocusTimer);
						self.totalTimePrompt();
						if($rootScope.alertType == 'Attendance'){
							AppManager.addActivityLogEntry("Registrant Verified Attendance.");
						}
					}
					//AppManager.addActivityLogEntry("Browser window gained focus during Exam.  Inactivity length: " + inactiveTimePeriod);
					//console.log('Timer stopped!!');
					//console.log('User was inactive for '+inactiveTimePeriod+' seconds');
				}
			} else {
				AppManager.disconnectedFromServer();
			}
		};
		self.showInActivePopupAndLogMsg = function (logMsg) {
			AppManager.addActivityLogEntry(logMsg,true);
			$rootScope.showAlert = true;
			try {videoJS.mediaPause();} catch (e) {}	
		}
		window.onload = function(){
			self.totalTimePrompt();
		}
		self.totalTimePrompt = function () {
			inactiveAndActiveTimePeriod = 0;
			var attendanceVerificationShown = false;
			//try {components.media.pause(0)} catch (e) {}		
			browserWithFocusTimer = $interval(function(){
				inactiveAndActiveTimePeriod++;
				//console.log('inactiveTimePeriodNew = '+inactiveAndActiveTimePeriod);
				/* modified to display total time of popup irrespective of active or inactive */
				//if(!AppManager.promptUseActivity && AppManager.promptFrequency > 0 && !attendanceVerificationShown && parseFloat(inactiveAndActiveTimePeriod/60) >= AppManager.promptFrequency){
				
				if(AppManager.promptFrequency > 0 && !attendanceVerificationShown && parseFloat(inactiveAndActiveTimePeriod/60) >= AppManager.promptFrequency && AppManager.isCompleted != 1){
					attendanceVerificationShown = true;
					$rootScope.activeAndInactiveBoth = true;
					$rootScope.showAlert = true;
					//console.log('satisfied condition');
					$rootScope.alertType = 'Attendance';
					$rootScope.alertTopic = "Attendance Verification";
					//$rootScope.descriptionTopic = AppManager.promptMessage;
					$rootScope.descriptionTopic = "According to the credit selections you chose, we must verify that you are at your computer after every " + SeminarDataService.seminarDataObj.promptFrequency + " minute(s).  Please press OK to continue this seminar.";
					try {videoJS.mediaPause();} catch (e) {}
					AppManager.addActivityLogEntry("Registrant Prompted to Verify Attendance.");
				}
			},1000);
		}
		document.addEventListener( 'visibilitychange' , function() {
			$scope.checkInstanceExists();
		}, false );
		
		window.addEventListener("blur", function(event) {
			try{
				if(AppManager.testInProgress || AppManager.blankOnInactivity){
					TimerService.pause();
					PromptTimerService.stop();
				}

			}catch(e){}			
			if( (seminarType == 'swl' && $rootScope.currentLoadPoint != 'evaluation') || seminarType == undefined){
				if(AppManager.connectedToServer && (AppManager.testInProgress || AppManager.blankOnInactivity || AppManager.promptUseActivity)){
					var alertTopic = "Switching Windows Not Allowed";
					var descriptionTopic = "You are not allowed to switch windows on your computer or mobile device while taking this program.";
					if(AppManager.testInProgress){
						descriptionTopic="You are not allowed to switch windows on your computer or mobile device while attempting to complete your program. Click OK to resume your exam and/or evaluation.";
						self.showInActivePopupAndLogMsg("Browser window lost focus during Exam.");					
						$rootScope.alertTopic = alertTopic;
						$rootScope.descriptionTopic = descriptionTopic;
					}else if(AppManager.blankOnInactivity){
						self.showInActivePopupAndLogMsg("Browser window lost focus");	
						$rootScope.alertTopic = alertTopic;
						$rootScope.descriptionTopic = descriptionTopic;				
					}
					inactiveTimePeriod = 0;
					var attendanceVerificationShown = false;
					//try {components.media.pause(0)} catch (e) {}		
					browserLostFocusTimer = $interval(function(){
						inactiveTimePeriod++;
						//console.log('inactiveTimePeriod = '+inactiveTimePeriod);
						if(AppManager.promptUseActivity && AppManager.promptFrequency > 0 && !attendanceVerificationShown && parseFloat(inactiveTimePeriod/60) >= AppManager.promptFrequency){
							attendanceVerificationShown = true;
							$rootScope.showAlert = true;
							//console.log('satisfied condition');
							$rootScope.alertTopic = "Attendance Verification";
							$rootScope.descriptionTopic = "According to the credit selections you chose, we must verify that you are at your computer after every " + SeminarDataService.seminarDataObj.promptFrequency + " minute(s) of inactivity.  Please press OK to continue this seminar.";
							try {
								videoJS.mediaPause();
							} catch (e) {}	
						}
					},1000);
				}else if (angular.isDefined(browserLostFocusTimer)) {
					$interval.cancel(browserLostFocusTimer);
				}
			}
		}, false);
		window.addEventListener('focus',function(e){
			
			if( $rootScope.showAlert ) {
				$location.hash('alertContainer');
				$anchorScroll();
			}
			if (angular.isDefined(browserLostFocusTimer)) {
				$interval.cancel(browserLostFocusTimer);
				if(AppManager.testInProgress){
					self.logInactivity("Browser window gained focus during Exam.  Inactivity length: ");
				}else if(AppManager.blankOnInactivity){
					self.logInactivity("Browser window gained focus.  Inactivity length: ");
				}
			}
			
			$scope.checkInstanceExists();
		}, false);
		self.logInactivity = function(logMsg){
			AppManager.addActivityLogEntry(logMsg + parseFloat(inactiveTimePeriod/60),true);	
			//console.log(logMsg + parseFloat(inactiveTimePeriod/60).toFixed(2));
		}
		$scope.checkInstanceExists = function(){
			if(AppManager.instanceStartedAt != ''){
				var promise2 = APIRequestService.getInstanceStartDate(AppManager.instanceStartedAt);
				promise2.then(function (response) {					
					if(response.SUCCESS == true){		
						if(response.ISEXIST == 1){	

							hideShowPopUpElements('.seminarInstance.elementsPen');

							try{
								if(AppManager.testInProgress){
									TimerService.pause();
									PromptTimerService.stop();
								}
								var vid = document.getElementsByTagName("video");
								if(videoJS.isPlaying){
									try {videoJS.mediaPause()} catch (e) {}	
								}
								
							}catch(e){}
							$timeout(function(){
								$rootScope.exitInstance();
							},10000);
						}
					}
				});
			}
		}
		$scope.enableNotes = true;
		
		$scope.saveSystemDebugLog = function() {
			//saving screen resolution
			AppManager.addDebugLogEntry("Screen Resolution: "+window.screen.width+" x "+window.screen.height);
			
			//Is System supports printing
			if(window.print) {
				AppManager.addDebugLogEntry("Supports Printing: Yes");			
			}else{
				AppManager.addDebugLogEntry("Supports Printing: No");	
			}
			
			//Is System supports mp3 decoding
			var audio  = document.createElement("audio");
			var canPlayMP3 = (typeof audio.canPlayType === "function" && audio.canPlayType("audio/mpeg;codecs=mp3") !== "");
			
			if(canPlayMP3){
				AppManager.addDebugLogEntry("MP3 Decoder: Yes");	
			}else{
				AppManager.addDebugLogEntry("MP3 Decoder: No");	
			}
			
			//Getting Client Operating System
			var OSName="Unknown OS"; 
            if (navigator.appVersion.indexOf("Win")!=-1) OSName="Windows"; 
            if (navigator.appVersion.indexOf("Mac")!=-1) OSName="MacOS"; 
            if (navigator.appVersion.indexOf("X11")!=-1) OSName="UNIX"; 
            if (navigator.appVersion.indexOf("Linux")!=-1) OSName="Linux"; 
			
			AppManager.addDebugLogEntry("Operating System: "+OSName);
			
			var promiseServerData = APIRequestService.getSystemData();
			promiseServerData.then(function (response) {
				
				if(response.SUCCESS == true){					
					AppManager.addDebugLogEntry("Server Connected To: "+response.HOSTADDRESS);
					AppManager.addDebugLogEntry("Client Language: "+response.LANGUAGE);
					AppManager.addDebugLogEntry("Useragent: "+response.USERAGENT);
					AppManager.saveProgressFromPlayer();
				}
			});
		}

		// AppManager is calling this method form handleSeminarContent() 
		$scope.init = function() {
			$scope.saveSystemDebugLog();		
			AppManager.isFirstLoad = true;
			var promise = APIRequestService.getSeminarforPlayer();
			var promise1 = APIRequestService.saveInstanceStartDate();
			promise1.then(function (response) {
				if(response.SUCCESS == true){					
					AppManager.instanceStartedAt = response.SAVEDTIME ;
				}else{
					AppManager.instanceStartedAt = '' ;
				}
			});

			AppManager.getCurrentTime();
			promise.then(function (response) {
				if(response.userLoggedIn == 1){
					$rootScope.currentLoadPoint = AppManager.seminarDetailsObj.currentLoadPoint;
					if($rootScope.currentLoadPoint == ''){
						$rootScope.currentLoadPoint = 'evaluation';
					}
					
					$rootScope.supportEmail = response.returnData.supportEmail;
					$rootScope.isUserLoggedIn=true;
					$scope.seminarName = SeminarDataService.seminarDataObj.seminarName;
					$scope.topLeftLogo = SeminarDataService.seminarDataObj.topLeftLogo;
					$scope.topRightLogo = SeminarDataService.seminarDataObj.topRightLogo;
					$rootScope.seminarLayout = SeminarDataService.seminarDataObj.seminarLayout;
					$scope.enableNotes = Number(AppManager.dspNotes);
					$scope.introMessageText = SeminarDataService.seminarDataObj.introMessageText;
					// this will get list of learning materials.
					$scope.materialTitle = SeminarDataService.seminarDataObj.materialTitle;
					$scope.materialPairs = SeminarDataService.materialPairs;
					$rootScope.emailTo = AppManager.email;
					// Overwrite mediaRequirePct for local testing only
					$rootScope.requiredMaterialsPercent = AppManager.seminarCompletionCheckObj.mediaRequiredPct;
					AppManager.promptShowComplete = false;
					
					AppManager.signuporgcode = SeminarDataService.seminarDataObj.signuporgcode;
					AppManager.arrVideoSync = response.returnData.titleArray;
					$scope.initPercChckForAllMedia();
					if(AppManager.arrVideoSync[AppManager.currentVideoIndex] != undefined){
						if(AppManager.arrVideoSync[AppManager.currentVideoIndex].fileArray[0] != undefined){
							$rootScope.arrSyncPointsToDisp = AppManager.arrVideoSync[AppManager.currentVideoIndex].fileArray[0].syncArray;
							$rootScope.syncPointsPaper = SeminarDataService.materialPairs[AppManager.currentPaperIndex ].fileTitle;
						}
					}
					$timeout(function(){
						adjustBoxHeights();
					},300);
					
					updateTabs($scope.data.tabs);
					if(AppManager.completByPass == 0){
						hideShowPopUpElements('.creditExpired.elementsSem');
					}
				}
				else{
					AppManager.disconnectedFromServer();
				}
			});
		}
		$scope.initPercChckForAllMedia = function(){
			for(var k=0; k<SeminarDataService.materialPairs.length; k++){
				$scope.initPercCheck(k);
			}
		}
		
		$scope.initPercCheck = function(i){
				/*added  */
			var noOfOccurencesOfOne = 0;
			var materialAccessDetails = SeminarDataService.materialPairs[i].accessDetails;
			for(var k=0; k<materialAccessDetails.length; k++){
				if(materialAccessDetails[k] == 1){
					noOfOccurencesOfOne++;
				}
			}
			var streamAccessDetails = SeminarDataService.materialPairs[i].accessDetails.join("");
			var curPercent =  (noOfOccurencesOfOne / streamAccessDetails.length) *100;
			if($rootScope.materialsCompletedPercent instanceof Array){
				$rootScope.materialsCompletedPercent[i] = Math.round(curPercent)
			}else{
				$rootScope.materialsCompletedPercent = [];
			}
			/*added  */
			
			var total = 0;
			var length = 0;
			for (j = 0; j < $rootScope.materialsCompletedPercent.length; j++) {
				if ($rootScope.materialsCompletedPercent[j] >= 0) {
					length += 1;
					total += $rootScope.materialsCompletedPercent[j];
				}
			}
			$rootScope.materialsCompletedPercentAvg = total / length;

			var mediaRequiredPct = AppManager.seminarCompletionCheckObj.mediaRequiredPct;
			// console.log("$rootScope.materialsCompletedPercentAvg " + Math.round($rootScope.materialsCompletedPercentAvg) + " mediaRequiredPct " + mediaRequiredPct)
			// Show popup on required % of completed materials are found
			if ($rootScope.materialsCompletedPercentAvg >= $rootScope.requiredMaterialsPercent) {
				AppManager.promptShowComplete = true;
			}
		}

		function updateTabs(tabs){
			
			if(AppManager.offerQA == 0){
				delete tabs.tab3;
			}

			if(SeminarDataService.downloads.length == 0 && SeminarDataService.downloadsLink.length == 0) {
				delete tabs.tab2;
			}
			
			$scope.tabCount = Object.keys(tabs).length;
			
			$scope.cssBottomRightTab = {
				width: (100/Object.keys(tabs).length) + '%',
			}
		}
		
		angular.element('#right .screen:not(".learn")').displayHide();

		$scope.tabLable = "Program";
		$scope.tabImage = "icon_1";		
		$rootScope.selectedLeftTab = 1;
		
		$scope.getSelectedLeftTab = function(tabNum){
			return $rootScope.selectedLeftTab === tabNum;
		};
		
		$scope.switchLeftTab = function (tabNum) {			
			if (!angular.element('#activity').hasClass('sizeLarge')) {
				/*For medium and small devices when the tab is clicked twice the corresponding displayed content will be hidden*/
				if($rootScope.selectedLeftTab == tabNum){
					$rootScope.selectedLeftTab = 0;
				}else{
					$rootScope.selectedLeftTab = tabNum;
				}				
			}else{
				$rootScope.selectedLeftTab = tabNum;
			}			
		}	
			
		// 0 - no logo , 1- right logo 2- both left and right logo will be available.
		
		// for portrait mode to check whether Learning Material / Note opened or not. 
		$rootScope.isLmNotesOpen = false;

		/*'Save and Exit' button of top bar*/
		$scope.checkExamInProgress = function (e) {	
			if(AppManager.testInProgress){
				if(seminarType != 'swl'){
					AppManager.saveNotes();
					AppManager.addActivityLogEntry("User pressed Save & Close",true);
				}else{				
					AppManager.addActivityLogEntry("User pressed Close",true);
				}

				hideShowPopUpElements('.examInProgress.elementsPen');
			}else{
				hideShowPopUpElements();
				$rootScope.saveExitClick(e);
			}
		}
		/*'Save and Exit' button of top bar*/
		$rootScope.saveExitClick = function (e) {	
			

			if(seminarType != 'swl'){
				AppManager.saveNotes();
				AppManager.addActivityLogEntry("User pressed Save & Close",true);
			}else{				
				AppManager.addActivityLogEntry("User pressed Close",true);
			}
				
			var _this = e.currentTarget;
			if(!angular.element('#activity').hasClass('sizeLarge')){
				var height = Math.max(document.body.scrollHeight, document.body.offsetHeight,document.getElementById('wrapper_parent').clientHeight, document.getElementById('wrapper_parent').scrollHeight, document.getElementById('wrapper_parent').offsetHeight);
				angular.element("#background-color-patch").height(height);
			}else{
				angular.element("#background-color-patch").height('100%');
			}
			
			hideShowPopUpElements('.saveExit.elements');
		}

		/*'Save And Close Window' button of 'Save and Exit' popUp*/
		$scope.saveClick = function (e) {
			if(seminarType == 'swl'){
				var hostname = window.location.hostname;
				window.location.href = "//" + hostname + '/?pg=semwebCatalog&panel=My';
				
			}else{
				var _this = e.currentTarget;
				angular.element('#activity > .patch').displayHide();
				$rootScope.sendExitEmail = "Yes";
				AppManager.saveProgressFromPlayer();
			}
		}
		
		/*'Save note button click*/
		$scope.saveNoteClick = function (e) {
			angular.element('.saveNoteText').html('Saving...');
			AppManager.saveNotes();
		}
		$scope.downloadNote = function (e) {
			angular.element('.downloadNoteCell').addClass('displayHide');
			angular.element('.downloadNoteCellWait').removeClass('displayHide');
			AppManager.saveAndDownloadNotes();
		}
		$scope.syncManageBtnClick = function (e) {
			var _this = e.currentTarget;
			if(angular.element('.syncWrapCnt').is(':visible')){		
				angular.element('.mediaSyncWrap').hide();		
				angular.element('.syncWrapCnt').hide();
				$rootScope.closeSyncSection();
			}else{
				$rootScope.showSyncSection();
				angular.element('.syncWrapCnt').show();
				angular.element('.mediaSyncWrap').show();
				$rootScope.syncPointsPaper = SeminarDataService.materialPairs[AppManager.currentPaperIndex ].fileTitle;
				angular.element('.syncPointsPaper').html($rootScope.syncPointsPaper);
			}			
		}
		$scope.closeSync = function () {
			$rootScope.closeSyncSection();
			angular.element('.syncWrapCnt').hide();
			angular.element('.progressSlider').removeClass('sliderWidth');	
		}
		$scope.deleteSync = function (index) {
			$rootScope.arrSyncPointsToDisp.splice(index,1);						
		}
		$scope.resumeSync = function (index,timeCode) {
			
			if(videoJS.mediaObj != undefined){
				videoJS.startPlayAtTime(parseInt(timeCode));
				$rootScope.updatePptPage(parseInt(timeCode),index);
			}else{
				$rootScope.showMsgPop = true;
				$rootScope.showMsgCnt = 'No media selected';
				$timeout(function(){
					$rootScope.showMsgCnt = '';
					$rootScope.showMsgPop = false;
				},1500);
			}
		}
		$scope.saveSync = function () {
			$rootScope.showMsgPop = true;
			$rootScope.showMsgCnt = 'Saving...';
			if(AppManager.currentVideoIndex != undefined){
				fileIDActive = SeminarDataService.materialPairs[AppManager.currentVideoIndex].fileid;	
			}else{
				fileIDActive = AppManager.arrVideoSync[0].fileArray[AppManager.currentVideoIndex].fileid;	
			}
			
			var promise = APIRequestService.addSyncData($rootScope.arrSyncPointsToDisp,fileIDActive);
			promise.then(function (response) {
				if(response.returnData == 1){
					$rootScope.showMsgCnt = 'Sync points saved';
					$timeout(function(){
						$rootScope.showMsgCnt = '';
						$rootScope.showMsgPop = false;
					},1500);
				}else{
					AppManager.disconnectedFromServer();
				}			
				
			});
		}
		$scope.addSync = function () {
			if(videoJS.isPlaying == false){
				var arrTempNavList = AppManager.materialPairs;
				var itK = AppManager.currentVideoIndex;
				for(var topic of  arrTempNavList){ 
					if(topic.type == 'ppt'){
						AppManager.currentPaperIndex =  itK;
						$rootScope.hasPaper = 1;
						break;
					}
					itK++;					
				}
				if(AppManager.currentPaperIndex != undefined){
					fileID =  SeminarDataService.materialPairs[AppManager.currentPaperIndex].fileid;	
				}else{
					fileID = AppManager.arrVideoSync[0].fileArray[AppManager.currentPaperIndex].fileid;	
				}
				timeCode = SeminarDataService.materialPairs[AppManager.currentVideoIndex].lastTimeCode;
				inFlag = 0;
				for(var j=0 ; j < $rootScope.arrSyncPointsToDisp.length; j++){
					if($rootScope.currentPage[AppManager.currentPaperIndex] == $rootScope.arrSyncPointsToDisp[j].page || timeCode == $rootScope.arrSyncPointsToDisp[j].timecode){
						inFlag = 1;
						break;						
					}					
				}
				if(inFlag == 0){
					$rootScope.syncPointsPaper = SeminarDataService.materialPairs[AppManager.currentPaperIndex ].fileTitle;
					$rootScope.arrSyncPointsToDisp.push({"fileID":fileID,"page":$rootScope.currentPage[AppManager.currentPaperIndex],"seminarID":AppManager.seminarID,"timecode":timeCode});
				}else{
					$rootScope.showMsgPop = true;
					$rootScope.showMsgCnt = 'Cannot add duplicates';
					$timeout(function(){
						$rootScope.showMsgCnt = '';
						$rootScope.showMsgPop = false;
					},1500);
				}				
			}else{
				$rootScope.showMsgPop = true;
				$rootScope.showMsgCnt = 'No media selected';
				$timeout(function(){
					$rootScope.showMsgCnt = '';
					$rootScope.showMsgPop = false;
				},1500);
			}		
		}
		$scope.getfileTitle = function(fileId){
			textVl = '';
			for(var data of AppManager.arrVideoSync) {
				if(data.fileArray[0].fileid == fileId){
					textVl = data.fileArray[0].title;
					break;
				}				
			}
			return textVl;
		}
		$scope.closeEmailSentPopup = function () {
			angular.element('#activity > .patch').displayHide();
		}
		$scope.sendEmail = function () {
			angular.element('#note-panel').scope().sendNoteEmail($(".jqte_editor").html());
		}
		$scope.cancelEmail = function () {
			angular.element('#activity > .patch').displayHide();
		}
		$scope.reloadPage = function () {
			var hostname = window.location.hostname;
			window.location.href = "//" + hostname + '/?pg=semwebCatalog&panel=My';
		}
		$scope.gotoCompleted = function() {
			$timeout(function() {
				if(AppManager.completByPass == 0){
					/*angular.element('#activity .creditExpired.elementsSem').removeClass('displayHide');
					angular.element('#activity .creditExpired.elementsSem').removeClass('hide');*/

					hideShowPopUpElements('.creditExpired.elementsSem');
				}else{	
					if($rootScope.showCompleteStart == 1){
						angular.element('.gotoCompletedButton .lable').html('Processing...');
						AppManager.completeProcessing = 1;
						AppManager.finalCheckSeminarforCompletion(AppManager.userReportedTimeSpent);
					}else{						

						hideShowPopUpElements('.seminarCompleteProcess.elementsSem');
						angular.element('#activity .seminarCompleteProcess.elementsSem').removeClass('displayHide').removeClass('hide');
						angular.element("#examWrapper").show();
						var scope = angular.element('.seminarCompleteProcess').scope();
						scope.init();
						setExamPopup();
					}
				}
			}, 0);
		}		
		$rootScope.exitCompletionProcess = function (e) {
			
			angular.element('.seminarCompleteProcess').show();
			
			hideShowPopUpElements();
			
			angular.element("#examWrapper").hide();
			AppManager.testInProgress = false;
			resetExamScroll();
			PostTestTimerService.stop();
			if(PostTestTimerService.time > 1){
				PostTestTimerService.resumeTimer = PostTestTimerService.time ;
			}else{
				PostTestTimerService.resumeTimer = 0;
			}

			if(AppManager.evaluationFormStateStarted == 1){

			}
		}
		$rootScope.returnToCompletionProcess = function (e) {			
			angular.element('.confirmExamClose').addClass('displayHide');
			angular.element('.confirmExamClose').addClass('hide');
			angular.element('.seminarCompleteProcess').show();
		}
		$rootScope.closeCompletePopup = function (e) {
			
			document.getElementById('content_Complete').scrollIntoView();			
			angular.element('.seminarCompleteProcess').show();

			hideShowPopUpElements('.seminarCompleteProcess.elementsSem');
			angular.element('#activity .seminarCompleteProcess.elementsSem').removeClass('displayHide').removeClass('hide');
			angular.element("#examWrapper").hide();
			AppManager.testInProgress = false;
			resetExamScroll();
		}
		$rootScope.closeIconCompletePopup = function (e) {
			hideShowPopUpElements();
			angular.element('.seminarCompleteProcess').show();
			
			angular.element("#examWrapper").hide();
			AppManager.testInProgress = false;
			resetExamScroll();
		}
		$scope.gotoLearn = function() {
			$timeout(function() {
				if(videoJS.mediaObj.length != undefined){
					videoJS.startPlayAtTime(0)
				}else{
					AppManager.reviewMediaOnStart = 1;
				}

				hideShowPopUpElements();
			}, 0);
		}
		/*'Cancel - Return to Program' button of  'Save and Exit' popUp*/
		$scope.cancelClick = function (e) {
			AppManager.addActivityLogEntry("User cancelled Save & Close request");
			var _this = e.currentTarget;
			angular.element('#activity > .patch').displayHide();
			hideShowPopUpElements();
		}
		/*tab buttons -> Program, Other Resources, Q & A, Complete, Help buttons in the right panel*/
		$scope.tabClick = function (e, i, scope) {

			//$rootScope.alert('Attendance Verification', 'According to the credit selections you chose, we must verify that you are are at your computer after every 15 minute(s) of inactivity. Please press OK to continue this seminar.','OK');
			var _this = e.currentTarget;


		/*	if($rootScope.currentActiveTab == _this || angular.element(_this).hasClass('disable')){
				//returning if the same tab is clicked multiple times or disabled
				return;
			}*/

			if(AppManager.currentTab == scope.title && seminarType == 'swl' && angular.element(window).width() < 1024){
				if(AppManager.prevTab != undefined)
					return false;
			}

			$rootScope.currentActiveTab = _this;
			$(".top-bar").removeClass("expanded");

			if(typeof scope != 'undefined'){
				$scope.tabLable = scope.title;
				var showPopUp = 0;				

				//console.log( scope.title);
				AppManager.prevTab = AppManager.currentTab;

				if(typeof scope == 'string'){
					AppManager.currentTab = scope;
					strScope = scope;
				}else{
					AppManager.currentTab = scope.title;
					strScope = scope.title;
				}
				
				AppManager.addActivityLogEntry("User changed tab to: " + AppManager.currentTab);

				if(strScope == "Program"){
					var scope = angular.element(document.getElementById("learn-controller")).scope();
					$scope.tabImage = "icon_1";
					if(AppManager.learnTabInitialised != 1)
					scope.init();
				}
				else if(strScope == "Materials"){
					var scope = angular.element(document.getElementById("other-resources-controller")).scope();
					$scope.tabImage = "icon_2";
					scope.init();
				}
				else if(strScope == "Q & A"){
					var scope = angular.element(document.getElementById("qa-controller")).scope();
					$scope.tabImage = "icon_3";
					scope.init();
				}
				else if(strScope == "Complete"){
					if(AppManager.completByPass == 0){
						showPopUp = 1;

						hideShowPopUpElements('.creditExpired.elementsSem');
					}else{
						$scope.tabImage = "icon_4";
						AppManager.checkCompletionForSeminaronCompleteTab();
					}					
				}
				else if(strScope == "Help"){
					$scope.tabImage = "icon_6";
					var scope = angular.element(document.getElementById("help-controller")).scope();
					scope.init();
				}
				//flyout feeature in mobile
			}
			
			
			if (!angular.element(_this).hasClass('disable') && showPopUp == 0) {
				$rootScope.show.screen = $rootScope.show.screen.map(function () {return false});
				$rootScope.show.screen[i] = true;
				$rootScope.tabI = i;
				angular.element('#sections .tab').removeAddClassI('select', $rootScope.tabI);
			}

			if( angular.element(window).width() < 1024){
				if(AppManager.prevTab == undefined) AppManager.prevTab = 'Program';
				if(AppManager.currentTab == AppManager.prevTab && angular.element('#leftBox').hasClass('reachUp')){
					angular.element('#leftBox').removeClass('reachUp');
					angular.element('.mobile-bottom-bar .tab').removeClass('tabSelected');
				} else {
					angular.element('#leftBox').addClass('reachUp');
					var _tab = angular.element(_this).data('tab');
					if(_tab == undefined) _tab = AppManager.currentTab;
					angular.element('.mobile-bottom-bar .tab').removeClass('tabSelected');
					angular.element('.tab').removeClass('tabSelected');
					angular.element('.tab[data-tabname="'+_tab+'"]').addClass('tabSelected');
				}
			} else {
				angular.element('.tab').removeClass('tabSelected');
				var _tab = angular.element(_this).data('tab');
				if(_tab == undefined) _tab = AppManager.currentTab;
				angular.element('.tab[data-tabname="'+_tab+'"]').addClass('tabSelected');
			}

			if($('#closeSideBar').is(':visible'))
				$('#closeSideBar').trigger('click');
			
			exitWideScreen();
		}
		$scope.playNextTopic = function(e){
			var _i= e.currentTarget.dataset.nextindex;

			hideShowPopUpElements();

			$timeout(function() {
				$rootScope.sideBarClick = 1;
				angular.element('#leftBox #lm-panel #lmPanelContent .topics #topic_'+_i+' .videoTopic').trigger('click');
			},10);
		}
		$scope.navTopClick = function (e) {
			angular.element('#lm-panel').addClass('no-left-padding')

			var isLearningOpen = angular.element(e.target).hasClass('md-select')
			angular.element('#arrow_1, #arrow_2').css('display','none');
			angular.element('#arrow_1').css('display','block');
			angular.element('.md-button').removeClass('md-select');
			angular.element(e.target).addClass('md-select');
				
			if(!isLearningOpen){
				$rootScope.isLmNotesOpen = true;
				$("#lm-panel").removeClass('show-for-large-up show-for-small-only').addClass('show-for-medium-only');
				$("#note-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
			
			}else{
				$rootScope.isLmNotesOpen = false;
				angular.element('#arrow_1, #arrow_2').css('display','none');
				angular.element('.md-button').removeClass('md-select');
				$("#lm-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
			}
		}

		$scope.noteTopClick = function (e) {
			angular.element('#note-panel').addClass('no-left-padding')
			var isNoteOpen = angular.element(e.target).hasClass('md-select')
			angular.element('#arrow_1, #arrow_2').css('display','none');

			angular.element('#arrow_2').css('display','block');

			angular.element('.md-button').removeClass('md-select');
			angular.element(e.target).addClass('md-select');
			
			if(!isNoteOpen){
				$rootScope.isLmNotesOpen = true;
				$("#note-panel").removeClass('show-for-large-up show-for-small-only').addClass('show-for-medium-only');
				$("#lm-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
				
			}else{
				$rootScope.isLmNotesOpen = false;
				angular.element('#arrow_1, #arrow_2').css('display','none');
				angular.element('.md-button').removeClass('md-select');	

				$("#note-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
			}
		}
		$rootScope.alert = function (heading, content, buttonText){
			$rootScope.alertBoxHead = heading;
			$rootScope.alertBoxContent = content;
			$rootScope.alertBoxButton = buttonText;
			angular.element('#alert-box').displayShow();			
		}
		$rootScope.closeAlert = function (e){
			angular.element('#alert-box').displayHide();
		}
	
		$scope.mobileNavTopClick = function (e) {
			angular.element('#lm-panel').addClass('no-left-padding');
				
				var isLearningOpen = angular.element(e.target).hasClass('md-select')
				angular.element('#arrow_3').css('display','block');

				angular.element('.md-button').removeClass('md-select');
				angular.element(e.target).addClass('md-select');
				
				if(!isLearningOpen){
					$rootScope.isLmNotesOpen = true;
					$("#lm-panel").removeClass('show-for-large-up').addClass('show-for-small-only');
					$("#note-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
				
				}else{
					$rootScope.isLmNotesOpen = false;
					angular.element('#arrow_3').css('display','none');
					angular.element('.md-button').removeClass('md-select');
					$("#lm-panel").removeClass('show-for-medium-only').addClass('show-for-large-up')
				}
		}

		angular.element('body').bind('click', function () {
			angular.element('#right .screen.learn .ppts .scale .menu').addClass('height_zero');
		});
		$rootScope.show = {
			screen: [true, false, false, false, false, false]
		}
		$rootScope.$watch('show.screen', function (newVal, oldVal) {
			$rootScope.tabI = newVal.indexOf(true);
			if(typeof $scope.tabLable == 'undefined') $scope.tabLable = "Program";
			showHideTabs($scope.tabLable,AppManager.seminarLayout);
		});
		/*debugMode ...>*/
			// $rootScope.show.screen = [true, false, false, false, false, false];
			/*hiding showing left panel patch*/
			//angular.element('#left .box > .patch').displayHide();
		/*<... debugMode*/
		/*css properties of controller_1*/
		

		$scope.css = {
			change: true,
			saveExitAlertPadding: 10,
			saveExitAlertWidth: 650,
			saveExitAlertHeight: 124,
			saveExitTopPadding: 10,
			saveExitPadding: 8,
			/* top CSS */
			topHeight: 5,
			topBgColor: '#323232',
			topTitlePaddingLeft: 1,
			topTitleColor: '#FFFFFF',
			topTitleFontSize: 16,
			topSaveExitRight: 1,
			topSaveExitBgColor: '#1C8DDD',
			/* bottom CSS */
			bottomBgColor: "#FFFFFF",
			leftWidth: 37,
			leftBasePadding: 8,
			leftLogoHeight: 10,
			leftVideoHeight: 36,
			leftNavHeight: 21,
			leftLogoPaddingTop: 0,
			leftVideoPaddingTop: 0,
			bottomLeftSpaceHeight: 1,
			leftNavPaddingTop: 0,
			leftNotePaddingTop: 0,
			rightLogoHeight: 10,
			rightTabsHeight: 7,
			rightTabsFontSize: 16,
			bottomRightTabBgColor: '#1F6BBF',
			bottomRightTabColor: '#FFFFF',
			bottomRightTabDividerHeight: 80,
			bottomRightTabDividerBgColor: '#FFFFFF',
			bottomRightScreenBgColor: '#053468',
			bottomRightScreenFontSize: 16
		}
		cssData.controller_1 = $scope.css;
		/*watching css properties of controller_1, and applying these to ng-style values*/
		$scope.$watchCollection('css', function (newVal, oldVal) {
			
			if( angular.element(window).width() < 700){
				$scope.css.saveExitAlertWidth = angular.element(window).width() - 25;
			}
			if(angular.element(window).width() < 301)
				font_size = '13px';
			else if(angular.element(window).width() < 500)
				font_size = '15px';
			else font_size = $scope.css.topTitleFontSize + 'px'

			$scope.cssSaveExit = {
				padding: $scope.css.saveExitAlertPadding + 'px',
				width: $scope.css.saveExitAlertWidth + 'px',
				marginLeft: -($scope.css.saveExitAlertWidth/2) + 'px',
			}
			$scope.cssSaveExitButton = {
				padding: '0px ' + $scope.css.saveExitPadding + 'px'
			}
			$scope.cssSaveExitTop = {
				padding: $scope.css.saveExitTopPadding + 'px 0px',
			}
			$scope.cssSaveExitLable = {
				padding: $scope.css.saveExitTopPadding + 'px 0px',
			}
			/* Top-Section CSS */
			$scope.cssTop = {
				backgroundColor: $scope.css.topBgColor
			}
			$scope.cssTopTitle = {
				color: $scope.css.topTitleColor,
				fontSize: font_size
			}
			/* Bottom-Section > Left-Panel CSS */
			$scope.cssBottomLeftBase = {
				padding: '0px ' + $scope.css.leftBasePadding + 'px',
				paddingBottom: $scope.css.leftBasePadding + 'px'
			}
			$scope.cssBottomLeftLogo = {
				height: $scope.css.leftLogoHeight + '%',
				paddingTop: $scope.css.leftLogoPaddingTop + '%'
			}
			$scope.cssBottomLeftVideo = {
				paddingTop: $scope.css.leftVideoPaddingTop + '%'
			}
			$scope.cssBottomLeftNav = {
				height: $scope.css.leftNavHeight + '%',
			}
			$scope.css.leftNoteHeight = 100 - $scope.css.leftVideoHeight;
			$scope.cssBottomLeftNote = {
				paddingTop: $scope.css.leftNotePaddingTop + '%'
			}
			$scope.cssBottomLeftSpace = {
				height: $scope.css.bottomLeftSpaceHeight + '%'
			}
			/* Bottom-Section > Right-Panel CSS */
			$scope.cssBottomRightLogo = {
				height: $scope.css.rightLogoHeight + '%'
			}
			$scope.cssBottomRightTabs = {
				fontSize: $scope.css.rightTabsFontSize + 'px'
			}
			$scope.cssBottomRightTab = {
				width: (100/$scope.tabCount) + '%',
			}
			$scope.cssBottomRightTabMobile = {
				width: (100) + '%',
				backgroundColor: $scope.css.bottomRightTabBgColor,
				color: $scope.css.bottomRightTabColor
			}
			var cssBottomRightTabDividerTop = (100-$scope.css.bottomRightTabDividerHeight)/2;
			$scope.cssBottomRightTabDivider = {
				height: $scope.css.bottomRightTabDividerHeight + '%',
				top: cssBottomRightTabDividerTop + '%',
				backgroundColor: $scope.css.bottomRightTabDividerBgColor
			}
			var cssRightScreenHeight = 100 - $scope.css.rightLogoHeight - $scope.css.rightTabsHeight;
			$scope.cssBottomRightScreens = {
				fontSize: $scope.css.bottomRightScreenFontSize
			}
		});
		/*trigger window resize to align sliders and play video buttons vertically*/
		var g_width = angular.element(window).width(), g_height = angular.element(window).height();
		angular.element(window).trigger('resize');
		angular.element($window).on('resize', function () {
			/*fix for mobile scroll glitch begin*/
			//if(angular.element(window).width() != g_width || angular.element(window).height() != g_height){
				/*fix for mobile scroll glitch end*/
				
				if( angular.element(window).width() < 700){
					$scope.css.saveExitAlertWidth = angular.element(window).width() - 25;
				} 
				if( angular.element(window).width() < 1024){
					if($('#video-controller').attr('type') == 'large_video' ){
						$('#right').css('flex-grow','inherit');
						$('#right').css('display','block');
					} 
				} else {
					
					if($('#video-controller').attr('type') == 'large_video'){
						$('#right').css('flex-grow','1');
					}
				}

				$timeout(function(){
					showHideTabs($scope.tabLable,AppManager.seminarLayout);
					
					if($('#video-controller').attr('type') != 'audio'){
						if(angular.element(window).width() >= 1024 )
							angular.element('#viewModeBtn').show();
					} else {
						angular.element('#leftBox').addClass('fullAudioWidth marginTop100');
						angular.element('#viewModeBtn').hide();
					}
					
				},300);
				$timeout(function(){
					adjustBoxHeights();
					scrollFunction();
				},350);

				if( angular.element(window).width() < 1024){
					$timeout(function(){
						angular.element('#leftBox').css('position','absolute');
						angular.element('#leftBox').css('bottom','-30px');
						angular.element('#leftBox').css('top','calc(100% - 0px)');

					},350);

					$timeout(function(){
						adjustOutlineContent($('.playerWell').height());
					},350);
				} else {
					$timeout(function(){
						angular.element('#leftBox').css('bottom','0');
						angular.element('#leftBox').css('position','relative');
						angular.element('#leftBox').css('top','0');
					},350);
				} 

				$scope.cssSaveExit = {
					padding: $scope.css.saveExitAlertPadding + 'px',
					width: $scope.css.saveExitAlertWidth + 'px',
					marginLeft: -($scope.css.saveExitAlertWidth/2) + 'px',
				}
			//}
		});
	});
})()