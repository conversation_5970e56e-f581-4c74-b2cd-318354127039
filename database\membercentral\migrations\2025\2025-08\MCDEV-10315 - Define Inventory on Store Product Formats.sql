
use membercentral
GO

ALTER TABLE dbo.store_productformats ADD inventory int NULL;
GO

ALTER PROC dbo.store_createProductFormat 
@itemID int,
@formatName varchar(250),
@GLAccountID int,
@ShippingGLAccountID int,
@status char(1),
@offerAffirmations bit,
@isAffirmation bit,
@quantity int,
@inventory int,
@formatID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @formatOrder int;

	set @formatID = null;
	select @formatID = formatID from dbo.store_productformats where itemID = @itemID and name = @formatName and [status] = 'A';

	IF @formatID is null BEGIN
		select @formatOrder = isNull(max(pf.formatOrder),0)+1 
		FROM dbo.store_productFormats as pf
		WHERE pf.itemID = @itemID
		and pf.status = 'A';

		INSERT INTO dbo.store_productformats (itemID, [Name], GLAccountID, ShippingGLAccountID, [status], offerAffirmations, 
			isAffirmation, quantity, formatOrder, inventory)
		VALUES (@itemID, @formatName, @GLAccountID, @ShippingGLAccountID, @status, @offerAffirmations, @isAffirmation, 
			@quantity, @formatOrder, NULLIF(@inventory,0));
		
		set @formatID = SCOPE_IDENTITY();
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.store_getRatesForAdminByFormatID
@formatid int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @formatRecordCount int;

	select @formatRecordCount = count(Formatid)
	from dbo.store_rates
	where formatid = @formatid;

	IF @formatRecordCount > 0
		select r.rateid, r.siteresourceID, r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate,
			g.groupID, g.groupName, srrc.include, pf.Formatid, pf.Itemid, pf.Name as formatName,
			pf.GLAccountID as formatGLAccountID, pf.shippingGLAccountID as formatShippingGLAccountID,
			pf.status, pf.offerAffirmations, pf.isAffirmation, pf.quantity, pf.formatOrder, pf.inventory
		from dbo.store_rates as r
		right outer join dbo.store_productformats as pf on r.formatID = pf.formatID
		inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
		inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active'
		left outer join dbo.cms_siteResourceRightsCache as srrc
			inner join ams_groups g on g.groupID = srrc.groupID	on srrc.resourceid = r.siteresourceID
			and srrc.functionID = @qualifyFID
			and srrc.siteID = sr.siteID
		where r.formatid = @formatid
		and r.rate >= 0
		order by r.rateOrder;

	ELSE
		select 0 as rateid, 0 as siteresourceID, 0 as GLAccountID, null as rateName, 0 as rate, 
			null as startDate, null as endDate, 0 as groupID, null as groupName, 1 as include,
			pf.Formatid, pf.Itemid, pf.Name as formatName, 
			pf.GLAccountID as formatGLAccountID, pf.shippingGLAccountID as formatShippingGLAccountID,
			pf.status, pf.offerAffirmations, pf.isAffirmation, pf.quantity, pf.formatOrder, pf.inventory
		from dbo.store_productformats as pf
		where pf.formatID = @formatid;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.store_getRatesForAdminByItemID
@itemID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select r.rateid, r.siteresourceID, r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate,
		g.groupID, g.groupName, srrc.include, pf.Formatid, pf.Itemid, pf.Name as formatName,
		pf.GLAccountID as formatGLAccountID, pf.status, pf.offerAffirmations, pf.isAffirmation,
		pf.quantity, pf.formatOrder, r.rateOrder, pf.inventory
	from dbo.store_rates as r
	inner join dbo.store_productformats as pf on r.formatID = pf.formatID
		and pf.status <> 'D'
		and pf.Itemid = @itemID
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
		and sr.siteResourceStatusID = 1
	left outer join dbo.cms_siteResourceRightsCache as srrc 
		inner join dbo.ams_groups g on g.groupID = srrc.groupID
		on srrc.resourceid = r.siteresourceID and srrc.siteID = sr.siteID and srrc.functionID = @qualifyFID
	where r.rate >= 0
		UNION ALL
	select 0 as rateid, 0 as siteresourceID, 0 as GLAccountID, null as rateName, 0 as rate, null as startDate, 
		null as endDate, 0 as groupID, null as groupName, 1 as include, pf.Formatid, pf.Itemid, pf.Name as formatName,
		pf.GLAccountID as formatGLAccountID, pf.status, pf.offerAffirmations, pf.isAffirmation, pf.quantity,
		pf.formatOrder, 0 as rateOrder, pf.inventory
	from dbo.store_productformats as pf 
	where pf.Itemid = @itemID
	and pf.status <> 'D'
	and pf.formatID not in (
		select pf2.Formatid
		from dbo.store_rates as r
		inner join dbo.store_productformats as pf2 on r.formatID = pf2.formatID
			and pf2.status <> 'D'
			and pf2.Itemid = @itemID
		inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			and sr.siteResourceStatusID = 1
		left outer join dbo.cms_siteResourceRightsCache as srrc 
			inner join dbo.ams_groups g on g.groupID = srrc.groupID
			on srrc.resourceid = r.siteresourceID and srrc.siteID = sr.siteID and srrc.functionID = @qualifyFID
		where r.rate >= 0 
	)
	order by formatOrder, rateOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
