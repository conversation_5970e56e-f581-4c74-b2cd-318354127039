<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			// get app instance settings
			variables.instanceSettings = getInstanceSettings(this.appInstanceID);

			// build quick links
			this.link.login = '/?pg=login&returnurl=/?pg=emailPreferences';
			this.link.update = '/?pg=login&returnurl=/?pg=updatemember';
			this.link.list = buildLink("showEmailPreferences");
			this.link.message = buildLink("message");

			// determine view folder
			if ((application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)) {
				arguments.event.setValue('viewDirectory', 'responsive');
			} else {
				arguments.event.setValue('viewDirectory', 'default');
			}

			local.defaultEmailPreferencesStr = {
				"mode" = 'EmailSendingQueue',
				"memberID" = 0,
				"siteID" = 0,
				"date" = "01/01/2000",
				"email" = '',
				"messageID" = 0,
				"consentListIDs" = ''
			};

			//check for new optout merge tag in URL
			if ( not arguments.event.valueExists('id') and not arguments.event.valueExists('listserverid')) 
				local.mEmailPreferencesStr = application.mcCacheManager.sessionGetValue(keyname='mEmailPreferencesStr', defaultValue=local.defaultEmailPreferencesStr);			
			else if (arguments.event.valueExists('id')){
				local.d = structNew();
				try{
                    local.decryptString = decrypt(arguments.event.getValue('id'),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex");
                } catch(any e){
                    application.objCommon.redirect('#this.link.message#&message=5');
                }
				local.requestInfo = deserializeJSON(local.decryptString);
				local.mEmailPreferencesStr = local.defaultEmailPreferencesStr;
			
				if(structKeyExists(local.requestInfo,"m"))
					local.mEmailPreferencesStr.memberID  = int(val(application.objMember.getActiveMemberID(val(local.requestInfo.m))));
				if(structKeyExists(local.requestInfo,"s"))
					local.mEmailPreferencesStr.siteID  = local.requestInfo.s;
				if(structKeyExists(local.requestInfo,"d"))
					local.mEmailPreferencesStr.date  = local.requestInfo.d;
				if(structKeyExists(local.requestInfo,"e"))
					local.mEmailPreferencesStr.email  = local.requestInfo.e;
				if(structKeyExists(local.requestInfo,"em"))
					local.mEmailPreferencesStr.messageID  = local.requestInfo.em;
				if(structKeyExists(local.requestInfo,"l"))
					local.mEmailPreferencesStr.consentListIDs  = local.requestInfo.l;

				application.mcCacheManager.sessionSetValue(keyname='mEmailPreferencesStr', value=local.mEmailPreferencesStr);

				// set MCIDME cookie if not logged in
				if (not application.objUser.isLoggedIn(cfcuser=session.cfcuser) and local.mEmailPreferencesStr.memberID gt 0)
					application.objPlatform.setSignedCookie(cookiename="mcidme", value="#local.mEmailPreferencesStr.memberID#|#arguments.event.getValue('mc_siteinfo.orgID')#|#GetTickCount()#", expires="90");
			} else {
				// arguments.event.valueExists('listserverid')
				local.emailKey = arguments.event.getValue('listserverid')
				local.arrMember = queryExecute("
					SELECT lm.list_, lm.emailaddr_, isnull(m.memberID,0) as memberID, lm.SubType_, lm.MemberType_, l.DescShort_, sud.sendingHostname
					FROM dbo.members_ lm
					inner join dbo.lists_ l on l.name_ = lm.list_
						and lm.MCEmailKey = :MCEmailKey
					inner join dbo.lists_format as lf on lf.name = l.name_
						and lf.orgcode = :siteCode
					inner join membercentral.platformMail.dbo.sendgrid_subuserDomains sud on sud.subuserdomainID=l.MCSetting_subuserDomainID
					left outer join membercentral.membercentral.dbo.ams_members m on m.membernumber = lm.externalMemberID collate Latin1_General_CI_AI
						and m.orgID = :orgID
						and m.status in ('A','I')
					", 
					{
						MCEmailKey = {value=local.emailKey, cfsqltype="CF_SQL_VARCHAR"},
						siteCode = {value=arguments.event.getValue('mc_siteinfo.sitecode'), cfsqltype="CF_SQL_VARCHAR"},
						orgID = {value=arguments.event.getValue('mc_siteinfo.orgID'), cfsqltype="CF_SQL_INTEGER"},
					}, 
					{ datasource=application.dsn.trialslyris1.dsn,returntype="array" } 

				);
				if (not local.arrMember.len()){
					application.objCommon.redirect('#this.link.message#&message=5');
				};
				local.mEmailPreferencesStr = local.arrMember.first();
				local.mEmailPreferencesStr.mode = "lyris";
				application.mcCacheManager.sessionSetValue(keyname='mEmailPreferencesStr', value=local.mEmailPreferencesStr);
			}

			local.identifiedAsMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgid'));

			// check rights
			if (cgi.request_method eq "POST" and arguments.event.getValue('List-Unsubscribe','') eq 'One-Click' and local.mEmailPreferencesStr.mode eq "EmailSendingQueue" and len(local.mEmailPreferencesStr.consentListIDs))
				arguments.event.setValue('action','OneClickOptOut');
			else if (cgi.request_method eq "POST" and arguments.event.getValue('List-Unsubscribe','') eq 'One-Click' and local.mEmailPreferencesStr.mode eq "Lyris")
				arguments.event.setValue('action','OneClickOptOutLyris');
			else if (not arguments.event.valueExists('message') and local.mEmailPreferencesStr.mode eq "EmailSendingQueue") {
				if (arguments.event.valueExists('id') and application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
					application.objCommon.redirect('#this.link.message#&message=2');
				} else if (not arguments.event.valueExists('message') and local.mEmailPreferencesStr.memberID and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and local.mEmailPreferencesStr.memberID neq session.cfcuser.memberdata.memberid) {
					//logged in as someone else
					application.objCommon.redirect('#this.link.message#&message=4');
				} else if (local.mEmailPreferencesStr.memberID and (not application.objUser.isLoggedIn(cfcuser=session.cfcuser) OR local.mEmailPreferencesStr.memberID eq session.cfcuser.memberdata.memberid)) {
					// serialized link and not logged in, or you are logged in and memberID of login and serialized link match
					local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=local.mEmailPreferencesStr.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
					arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
					
					if (not val(local.tmpRights.view))
						application.objCommon.redirect('#this.link.message#&message=1');

					arguments.event.setValue('encLinkMode',1);
					arguments.event.setValue('memberID',local.mEmailPreferencesStr.memberID);
					arguments.event.setValue('consentListIDs',local.mEmailPreferencesStr.consentListIDs);
					arguments.event.setValue('messageID',local.mEmailPreferencesStr.messageID);
					arguments.event.setValue('overrideEmail',local.mEmailPreferencesStr.email);
				} else if (local.identifiedAsMemberID or session.cfcuser.memberdata.memberid) {
					local.memberID = local.identifiedAsMemberID;
					if(local.memberID EQ 0){
						local.memberID = session.cfcuser.memberdata.memberid;
					}
					arguments.event.setValue('memberID',local.memberID);
					arguments.event.setValue('consentListIDs',local.mEmailPreferencesStr.consentListIDs);
					arguments.event.setValue('messageID',local.mEmailPreferencesStr.messageID);
				} else {
					// if no memberid, get out
					application.objCommon.redirect('#this.link.login#');
				}
			} else if (not arguments.event.valueExists('message') and local.mEmailPreferencesStr.mode eq "Lyris") {
				arguments.event.setValue('action','ShowLyrisOptOut');
			}
		

			// param vars
			arguments.event.paramValue('action','showEmailPreferences');

			// record app hit
			application.objPlatformStats.recordAppHit(appname="emailPreferences",appsection="");
			
			// method to run
			local.methodToRun = this[arguments.event.getValue('action')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showEmailPreferences" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgid')>

		<cfset local.mEmailPreferencesStr = application.mcCacheManager.sessionGetValue(keyname='mEmailPreferencesStr', defaultValue={})>
		<cfset local.preferredEmail = "">
		<cfset local.excludedConsentIDs = "">
		
		<cfif arguments.event.getValue('encLinkMode',0) eq 1>
			<cfset local.memberEmailList = arguments.event.getValue('overrideEmail','')>
		<cfelse>
			<cfset local.qryMemberEmails = getMemberEmails(orgID=local.orgID, memberID=arguments.event.getValue('memberID'))>
			<cfset local.memberEmailList = valueList(local.qryMemberEmails.email)>
			<!--- no email address defined --->
			<cfif not listLen(local.memberEmailList)>
				<cflocation url="#this.link.message#&message=3" addtoken="false">
			</cfif>
		</cfif>
		
		<cfif structKeyExists(local.mEmailPreferencesStr, "email") AND len(trim(local.mEmailPreferencesStr.email))>
			<cfset local.preferredEmail = trim(local.mEmailPreferencesStr.email)>
			<cfset local.excludedConsentIDs = local.mEmailPreferencesStr.consentListIDs>
			<cfif listFind(local.memberEmailList, local.preferredEmail)>
				<cfset local.memberEmailList = local.preferredEmail & "," & listDeleteAt(local.memberEmailList, listFind(local.memberEmailList, local.preferredEmail))>
			</cfif>
		</cfif>

		<cfset local.qryEmailConsentListDetails = getEmailConsentListDetails(orgID=local.orgID)>
		<cfset local.strConsentListMembers = getConsentListMembers(orgID=local.orgID, memberEmailList=local.memberEmailList)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/emailPreferences/#arguments.event.getValue('viewDirectory')#/frm_emailPreferences.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="ShowLyrisOptOut" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.mEmailPreferencesStr = application.mcCacheManager.sessionGetValue(keyname='mEmailPreferencesStr', defaultValue={})>

		<cfif not len(local.mEmailPreferencesStr.list_) or not len(local.mEmailPreferencesStr.emailaddr_) or not len(local.mEmailPreferencesStr.SubType_) or not len(local.mEmailPreferencesStr.MemberType_)>
			<cflocation url="#this.link.message#&message=5" addtoken="false">
		</cfif>

		<cfif cgi.request_method eq "POST">
			<cfset doLyrisUnsubscribe(
				siteID=arguments.event.getValue('mc_siteinfo.siteID'),
				listname=local.mEmailPreferencesStr.list_, 
				emailaddr=local.mEmailPreferencesStr.emailaddr_,  
				actorMemberID=val(local.mEmailPreferencesStr.memberID)
			)>

			<cfsavecontent variable="local.data">
				<cfinclude template="/views/emailPreferences/#arguments.event.getValue('viewDirectory')#/frm_lyrisUnsubscribeFromListSuccess.cfm">
			</cfsavecontent>
			<cfset application.mcCacheManager.sessionDeleteValue(keyname='mEmailPreferencesStr')>

		<cfelse>
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/emailPreferences/#arguments.event.getValue('viewDirectory')#/frm_lyrisUnsubscribeFromList.cfm">
			</cfsavecontent>
		</cfif>


		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>


	<cffunction name="getMemberEmails" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryMemberEmails = "">

		<cfquery name="qryMemberEmails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			select distinct me.email
			from dbo.ams_memberEmails as me
			inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID
				and met.emailTypeID = me.emailTypeID
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = me.memberID
				and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			where me.orgID = @orgID
			and me.email <> ''
			order by me.email;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMemberEmails>
	</cffunction>

	<cffunction name="OneClickOptOut" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = {};
			local.results = { success:false, errmsg: '' };

			try {
				local.mEmailPreferencesStr = application.mcCacheManager.sessionGetValue(keyname='mEmailPreferencesStr', defaultValue={});
				local.siteMatch = local.mEmailPreferencesStr.siteID eq arguments.event.getValue('mc_siteInfo.siteID');

				if (not local.siteMatch)
					local.results.success=false;
				else {
					local.results = saveEmailPreferences (
						mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgid'),
						memberID=local.mEmailPreferencesStr.memberID,
						modeName='Opt-Out',
						consentListID=listFirst(local.mEmailPreferencesStr.consentListIDs),
						email=local.mEmailPreferencesStr.email,
						checked=false
					)
				}
			} catch (e) {
				application.objError.sendError(cfcatch=e, objectToDump=local, customMessage="Error processing one-click opt-out");
			}

			if (local.results.success)
				local.returnString = "<p>Your Unsubscribe request has been successfully processed</p>";
			else 
				local.returnString = "<p>Your Unsubscribe request has failed.</p>";

			return returnAppStruct(local.returnString,"echo");
		</cfscript>

	</cffunction>
	<cffunction name="OneClickOptOutLyris" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = {};
			local.results = { success:false, errmsg: '' };
			try {

				local.mEmailPreferencesStr = application.mcCacheManager.sessionGetValue(keyname='mEmailPreferencesStr', defaultValue={});
				doLyrisUnsubscribe(
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					listname=local.mEmailPreferencesStr.list_, 
					emailaddr=local.mEmailPreferencesStr.emailaddr_,  
					actorMemberID=val(local.mEmailPreferencesStr.memberID)
				);
				local.results.success = true;
			} catch (e) {
				application.objError.sendError(cfcatch=e, objectToDump=local, customMessage="Error processing Lyris one-click opt-out");
			}

			if (local.results.success)
				local.returnString = "<p>Your Unsubscribe request has been successfully processed</p>";
			else 
				local.returnString = "<p>Your Unsubscribe request has failed.</p>";

			return returnAppStruct(local.returnString,"echo");
		</cfscript>

	</cffunction>

	<cffunction name="getEmailConsentListDetails" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var qryEmailConsentListDetails = "">

		<cfquery name="qryEmailConsentListDetails" datasource="#application.dsn.platformmail.dsn#">
			select clt.consentListTypeID, clt.consentListTypeName, cl.consentListID, cl.consentListName, 
				cl.consentListDesc, clm.modeName
			from dbo.email_consentListTypes as clt
			inner join dbo.email_consentLists as cl on cl.consentListTypeID = clt.consentListTypeID
				and cl.[status] = 'A'
			inner join dbo.email_consentListModes as clm on clm.consentListModeID = cl.consentListModeID
			where clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			and clm.modeName <> 'Required'
			order by clt.orderNum, cl.orderNum;
		</cfquery>

		<cfreturn qryEmailConsentListDetails>
	</cffunction>

	<cffunction name="getConsentListMembers" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberEmailList" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryConsentListMembers" datasource="#application.dsn.platformmail.dsn#">
			select clm.email, STRING_AGG(clm.consentListID,',') as consentListIDList
			from dbo.email_consentListTypes as clt
			inner join dbo.email_consentLists as cl on cl.consentListTypeID = clt.consentListTypeID
				and clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and cl.[status] = 'A'
			inner join dbo.email_consentListMembers as clm on clm.consentListID = cl.consentListID
			where clm.email in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#arguments.memberEmailList#">)
			group by clm.email;
		</cfquery>

		<cfset local.strConsentListMembers = structNew()>

		<cfloop query="local.qryConsentListMembers">
			<cfset structInsert(local.strConsentListMembers, local.qryConsentListMembers.email, local.qryConsentListMembers.consentListIDList)>
		</cfloop>

		<cfreturn local.strConsentListMembers>
	</cffunction>

	<cffunction name="saveEmailPreferences" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="modeName" type="string" required="true">
		<cfargument name="consentListID" type="numeric" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="checked" type="boolean" required="true">
	
		<cfset var local = structNew()>
		<cfset local.result = { success:true, errmsg: '' }>

		<cfset local.isMemberOnGlobalOptOutList = false>
		<cfset local.qryGlobalOptOutConsentListMembers = getConsentListMembersByMode(orgID=arguments.mcproxy_orgID, memberEmailList=arguments.email, mode="GlobalOptOut")>
		<cfif local.qryGlobalOptOutConsentListMembers.recordCount>
			<cfset local.isMemberOnGlobalOptOutList = true>
		</cfif>

		<cfset local.qryEmailConsentListDetails = getEmailConsentListDetails(orgID=arguments.mcproxy_orgID)>
		<cfquery name="local.qryGlobalOptOutConsentList" dbtype="query">
			SELECT consentListID
			FROM [local].qryEmailConsentListDetails
			WHERE modeName = 'GlobalOptOut'
		</cfquery>
		<cfset local.globalOptOutConsentListID = local.qryGlobalOptOutConsentList.consentListID>

		<cfquery name="local.qryManageMemberConsentLists" datasource="#application.dsn.platformmail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @memberID int, @changeSetUID uniqueidentifier, @nowDate datetime, @addActionID int, @removeActionID int,
					@consentListID int, @email varchar(200), @globalOptOutConsentListID int;
				DECLARE @tblEmailsToBeAdded TABLE (consentListID int, email varchar(200));
				DECLARE @tblEmailsToBeRemoved TABLE (consentListID int, email varchar(200));
				DECLARE @tblConsentListsToBeAdded TABLE (consentListID int, email varchar(200));
				DECLARE @tblConsentListsToBeRemoved TABLE (consentListID int, email varchar(200));

				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
				SET @consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.consentListID#">;
				SET @email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.email#">;
				SET @globalOptOutConsentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.globalOptOutConsentListID#">;
				SET @changeSetUID = NEWID();
				SET @nowDate = GETDATE();

				SELECT @addActionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Add';
				SELECT @removeActionID = actionID FROM dbo.email_consentListMemberHistoryActions WHERE action = 'Removed';

				<cfif arguments.modeName eq 'GlobalOptOut'>
					<cfif arguments.checked>
						INSERT INTO @tblEmailsToBeAdded (consentListID, email)
						VALUES (@consentListID, @email);

						INSERT INTO @tblEmailsToBeRemoved (consentListID, email)
						SELECT clm.consentListID, clm.email
						FROM dbo.email_consentListMembers clm
						INNER JOIN email_consentLists AS cl ON cl.consentListID = clm.consentListID
							AND cl.[status] = 'A'
						INNER JOIN dbo.email_consentListModes as m ON m.consentListModeID = cl.consentListModeID
							AND m.modeName IN ('Opt-Out','Opt-In')
						INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
							AND clt.orgID = @orgID
						WHERE clm.email = @email;
					<cfelse>
						INSERT INTO @tblEmailsToBeRemoved (consentListID, email)
						VALUES (@consentListID, @email);

						INSERT INTO @tblEmailsToBeRemoved (consentListID, email)
						SELECT clm.consentListID, clm.email
						FROM dbo.email_consentListMembers clm
						INNER JOIN email_consentLists AS cl ON cl.consentListID = clm.consentListID
							AND cl.[status] = 'A'
						INNER JOIN dbo.email_consentListModes as m ON m.consentListModeID = cl.consentListModeID
							AND m.modeName = 'Opt-Out'
						INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
							AND clt.orgID = @orgID
						WHERE clm.email = @email;

						INSERT INTO @tblEmailsToBeAdded (consentListID, email)
						SELECT cl.consentListID, @email
						FROM dbo.email_consentLists AS cl
						INNER JOIN dbo.email_consentListModes as m ON m.consentListModeID = cl.consentListModeID
							AND m.modeName = 'Opt-In'
						INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
							AND clt.orgID = @orgID
						WHERE cl.[status] = 'A';
					</cfif>
				<cfelseif listFindNoCase("Opt-Out,Opt-In",arguments.modeName)>
					<cfif (arguments.checked and arguments.modeName eq "Opt-Out") OR (NOT arguments.checked and arguments.modeName eq "Opt-In")>
						INSERT INTO @tblEmailsToBeRemoved (consentListID, email)
						VALUES (@consentListID, @email);
					<cfelse>
						INSERT INTO @tblEmailsToBeAdded (consentListID, email)
						VALUES (@consentListID, @email);
					</cfif>

					<!--- change specific list when the email was on the global opt out list --->
					<cfif local.isMemberOnGlobalOptOutList>
						INSERT INTO @tblEmailsToBeRemoved (consentListID, email)
						VALUES (@globalOptOutConsentListID, @email);

						INSERT INTO @tblEmailsToBeAdded (consentListID, email)
						SELECT cl.consentListID, @email
						FROM dbo.email_consentLists AS cl
						INNER JOIN dbo.email_consentListModes as m ON m.consentListModeID = cl.consentListModeID
							AND m.modeName = 'Opt-Out'
						INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = cl.consentListTypeID
							AND clt.orgID = @orgID
						WHERE cl.consentListID <> @consentListID
						AND cl.[status] = 'A';
					</cfif>
				</cfif>

				INSERT INTO @tblConsentListsToBeAdded (consentListID, email)
				SELECT tmp.consentListID, tmp.email
				FROM @tblEmailsToBeAdded as tmp
				LEFT OUTER JOIN dbo.email_consentListMembers as clm on clm.consentListID = tmp.consentListID
					AND clm.email = tmp.email
				WHERE clm.consentListMemberID IS NULL;

				INSERT INTO @tblConsentListsToBeRemoved (consentListID, email)
				SELECT tmp.consentListID, tmp.email
				FROM @tblEmailsToBeRemoved as tmp
				INNER JOIN dbo.email_consentListMembers as clm on clm.consentListID = tmp.consentListID
					AND clm.email = tmp.email;
				
				BEGIN TRAN;
					INSERT INTO dbo.email_consentListMembers (consentListID, email, dateCreated)
					SELECT consentListID, email, @nowDate
					FROM @tblConsentListsToBeAdded;

					DELETE clm
					FROM dbo.email_consentListMembers AS clm
					INNER JOIN @tblConsentListsToBeRemoved AS tmp ON tmp.consentListID = clm.consentListID
						AND tmp.email = clm.email;
					
					INSERT INTO dbo.email_consentListMemberHistory (changeSetUID, email, consentListID, actionID, updateDate, enteredByMemberID)
					SELECT @changeSetUID, email, consentListID, @addActionID, @nowDate, @memberID
					FROM @tblConsentListsToBeAdded
						UNION ALL
					SELECT @changeSetUID, email, consentListID, @removeActionID, @nowDate, @memberID
					FROM @tblConsentListsToBeRemoved;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="buildLink" access="private" output="false" returntype="string">
		<cfargument name="action" type="string" required="true">
		<cfreturn "/?pg=emailPreferences&action=#arguments.action#">
	</cffunction>
	
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p class="tsAppBodyText">
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>The Email Preferences for this user cannot be viewed since you are logged in as an Administrator. Please log out.</cfcase>
							<cfcase value="3"><b>You do not have any email addresses defined. <a href="#this.link.update#">Click here</a> to update your profile.</b></cfcase>
							<cfcase value="4"><b>The Email Preferences for this user cannot be viewed since you are logged in as someone else. Please log out.</cfcase>
							<cfcase value="5"><b>The Unsubscribe Link is not valid and cannot be processed.</cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getConsentListMembersByMode" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberEmailList" type="string" required="true">
		<cfargument name="mode" type="string" required="false" default="">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryConsentListMembers" datasource="#application.dsn.platformmail.dsn#">
			select clm.consentListID, clm.email
			from dbo.email_consentListTypes as clt
			inner join dbo.email_consentLists as cl on cl.consentListTypeID = clt.consentListTypeID
				and clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and cl.[status] = 'A'
			inner join dbo.email_consentListMembers as clm on clm.consentListID = cl.consentListID
			inner join dbo.email_consentListModes as mode on mode.consentListModeID = cl.consentListModeID
				and mode.modeName in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#arguments.mode#">)
			where clm.email in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#arguments.memberEmailList#">);
		</cfquery>

		<cfreturn local.qryConsentListMembers>
	</cffunction>
	<cffunction name="doLyrisUnsubscribe" access="private" returntype="void" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="listname" type="string" required="true">
		<cfargument name="emailaddr" type="string" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfquery name="local.qryUpdate" datasource="#application.dsn.trialsLyris1.dsn#" timeout="5">
			SET NOCOUNT ON;
			
			IF OBJECT_ID('tempdb..##mc_listMembersUpdate') IS NOT NULL
			DROP TABLE ##mc_listMembersUpdate;
			
			CREATE TABLE ##mc_listMembersUpdate (listMemberID int, email varchar(100), domain varchar(250), username varchar(100), MCOption_lockAddress bit, MCOption_lockName bit, MCOption_keepActive bit, 
			subType varchar(20), fullname varchar(100), memberType varchar(20), receiveMCThreadIndex bit, receiveMCThreadDigest bit, matchingListMemberID int, skipUpdate bit);
			
			INSERT INTO ##mc_listMembersUpdate (listMemberID, email, fullname, domain, username, MCOption_lockAddress, 
			MCOption_lockName, MCOption_keepActive, memberType, subType, receiveMCThreadIndex, receiveMCThreadDigest, matchingListMemberID, skipUpdate)
			SELECT lm.[MemberID_], lm.[EmailAddr_], lm.[FullName_], lm.Domain_, lm.UserNameLC_, lm.[MCOption_lockAddress], lm.[MCOption_lockName], lm.[MCOption_keepActive],
				'unsub' as [MemberType_],lm.[SubType_],lm.[receiveMCThreadIndex], lm.[receiveMCThreadDigest],
				null as matchingListMemberID,
				0 as skipUpdate
			FROM trialslyris1.dbo.members_ as lm
			WHERE lm.list_ = <cfqueryparam value="#arguments.listname#" cfsqltype="CF_SQL_VARCHAR"> 
			AND lm.emailaddr_ = <cfqueryparam value="#arguments.emailaddr#" cfsqltype="CF_SQL_VARCHAR">
					
			EXEC lyrisCustom.dbo.mc_saveMemberListsInfo
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@receiverMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">,
				@recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">;
				
			IF OBJECT_ID('tempdb..##mc_listMembersUpdate') IS NOT NULL
				DROP TABLE ##mc_listMembersUpdate;
		</cfquery>
	</cffunction>
</cfcomponent>