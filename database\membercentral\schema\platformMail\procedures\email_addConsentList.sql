ALTER PROC dbo.email_addConsentList
@siteID int,
@consentListTypeID int,
@consentListName varchar(100),
@consentListDesc varchar(250),
@consentListModeID int,
@orgIdentityID int,
@isHidden bit,
@enteredByMemberID int,
@consentListID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @appCreatedContentResourceTypeID int, @resourceTypeID int, @defaultLanguageID int, 
		@footerContentID int, @intakeFormNonQualContentID int, @siteResourceID int, @trashID int;

	SET @consentListID = null;
	SELECT @appCreatedContentResourceTypeID = memberCentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SELECT @resourceTypeID  = memberCentral.dbo.fn_getResourceTypeId('EmailConsentList');
	SELECT @defaultLanguageID = defaultLanguageID from memberCentral.dbo.sites where siteID = @siteID;

	IF EXISTS (select top 1 consentListID from dbo.email_consentLists where consentListTypeID = @consentListTypeID and consentListName = @consentListName and [status] = 'A')
		RAISERROR('Consent List Name already in use', 16, 1);

	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1,
				@siteID=@siteID, @isVisible=1, @parentSiteResourceID=NULL, @siteResourceID=@siteResourceID OUTPUT;

		EXEC memberCentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID, 
			@contentID=@footerContentID OUTPUT, @siteResourceID=@trashID OUTPUT;

		INSERT INTO dbo.email_consentLists (consentListTypeID, consentListName, consentListDesc, consentListModeID, orgIdentityID, footerContentID, siteResourceID, orderNum, isHidden, [status])
		VALUES (@consentListTypeID, @consentListName, @consentListDesc, @consentListModeID, @orgIdentityID, @footerContentID, @siteResourceID, 9999, @isHidden, 'A');
			SET @consentListID = SCOPE_IDENTITY();

		EXEC dbo.email_reorderConsentLists @consentListTypeID=@consentListTypeID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
