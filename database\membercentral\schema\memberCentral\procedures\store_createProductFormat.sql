ALTER PROC dbo.store_createProductFormat 
@itemID int,
@formatName varchar(250),
@GLAccountID int,
@ShippingGLAccountID int,
@status char(1),
@offerAffirmations bit,
@isAffirmation bit,
@quantity int,
@inventory int,
@formatID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @formatOrder int;

	set @formatID = null;
	select @formatID = formatID from dbo.store_productformats where itemID = @itemID and name = @formatName and [status] = 'A';

	IF @formatID is null BEGIN
		select @formatOrder = isNull(max(pf.formatOrder),0)+1 
		FROM dbo.store_productFormats as pf
		WHERE pf.itemID = @itemID
		and pf.status = 'A';

		INSERT INTO dbo.store_productformats (itemID, [Name], GLAccountID, ShippingGLAccountID, [status], offerAffirmations, 
			isAffirmation, quantity, formatOrder, inventory)
		VALUES (@itemID, @formatName, @GLAccountID, @ShippingGLAccountID, @status, @offerAffirmations, @isAffirmation, 
			@quantity, @formatOrder, NULLIF(@inventory,0));
		
		set @formatID = SCOPE_IDENTITY();
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
