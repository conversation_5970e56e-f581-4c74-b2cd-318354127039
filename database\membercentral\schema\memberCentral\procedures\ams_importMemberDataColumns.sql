ALTER PROC dbo.ams_importMemberDataColumns
@siteID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- need this here so we can go in and out of snapshot as needed inside the transaction
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsAdded') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsAdded;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsUpdated') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsUpdated;
	IF OBJECT_ID('tempdb..#tmpDeleteMemberDataColumnValues') IS NOT NULL 
		DROP TABLE #tmpDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#mdcvToAdd') IS NOT NULL 
		DROP TABLE #mdcvToAdd;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tblMDDEFCols') IS NOT NULL 
		DROP TABLE #tblMDDEFCols;
	IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
		DROP TABLE #tblMDDEF;
	IF OBJECT_ID('tempdb..#tblExistingMDDEF') IS NOT NULL 
		DROP TABLE #tblExistingMDDEF;
	IF OBJECT_ID('tempdb..#tmpColumnConditions') IS NOT NULL 
		DROP TABLE #tmpColumnConditions;
	
	CREATE TABLE #tmpMemberDataColumns (columnName varchar(128), dataTypeID int, displayTypeID int, columnDesc varchar(255), allowNewValuesOnImport bit, allowNull bit, 
		isReadOnly bit, allowMultiple bit, [uid] uniqueidentifier,  minChars int, maxChars int, minSelected int, maxSelected int, minValueInt int, maxValueInt int, 
		minValueDecimal2 decimal(14, 2), maxValueDecimal2 decimal(14,2), minValueDate date, maxValueDate date, syncColumnID int, syncDefaultValueID int, 
		linkedDateColumnID int, linkedDateColumnUID uniqueidentifier, linkedDateCompareDate date, linkedDateCompareDateAFID int, linkedDateAdvanceDate date, 
		linkedDateAdvanceAFID int, finalAction char(1));
	CREATE TABLE #tmpUpdateMemberDataColumns (columnID int, columnName varchar(128), oldColumnName varchar(128), dataTypeID int, displayTypeID int, 
		dataTypeCode varchar(20), displayTypeCode varchar(20), oldDataTypeCode varchar(20), oldDisplayTypeCode varchar(20));
	CREATE TABLE #tmpMemberDataColumnsAdded (columnID int, [uid] uniqueidentifier);
	CREATE TABLE #tmpMemberDataColumnsUpdated (columnID int, [uid] uniqueidentifier);
	CREATE TABLE #tmpDeleteMemberDataColumnValues (valueID int);
	CREATE TABLE #mdcvToAdd (valueID int, columnID int, columnUID uniqueidentifier, columnValueString varchar(255), columnValueDecimal2 decimal(14,2), columnValueInteger int, 
		columnvalueDate date, columnValueBit bit, syncColumnID int, syncValueID int);
	CREATE TABLE #tmpMDCDef (columnID int PRIMARY KEY, dataTypeCode varchar(20), displayTypeCode varchar(20));
	CREATE TABLE #tblMDDEFCols (columnID int, valueID int);
	CREATE TABLE #tblMDDEF (memberID int);
	CREATE TABLE #tblExistingMDDEF (memberID int, columnID int);
	CREATE TABLE #tmpColumnConditions (columnID int, conditionID int, dataTypeID int);

	DECLARE @orgID int, @deleteColumnIDList varchar(max), @minValueID int, @minColumnID int;
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	INSERT INTO #tmpMemberDataColumns (columnName, dataTypeID, displayTypeID, columnDesc, allowNewValuesOnImport, allowNull, isReadOnly, allowMultiple, [uid], minChars, maxChars, 
		minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, syncColumnID, syncDefaultValueID, 
		linkedDateColumnUID, linkedDateCompareDate, linkedDateCompareDateAFID, linkedDateAdvanceDate, linkedDateAdvanceAFID, finalAction)
	select distinct smdc.columnName, mdata.dataTypeID, mdisp.displayTypeID, smdc.columnDesc, smdc.allowNewValuesOnImport, smdc.allowNull, smdc.isReadOnly, smdc.allowMultiple, 
		smdc.[uid], smdc.minChars, smdc.maxChars, smdc.minSelected, smdc.maxSelected, smdc.minValueInt, smdc.maxValueInt, smdc.minValueDecimal2, smdc.maxValueDecimal2, 
		smdc.minValueDate, smdc.maxValueDate, smdc.columnID, smdc.defaultValueID, smdc.linkedDateColumnUID, smdc.linkedDateCompareDate, af_c.AFID, 
		smdc.linkedDateAdvanceDate, af_adv.AFID, smdc.finalAction
	from datatransfer.dbo.sync_ams_memberDataColumns as smdc
	inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.[uid] = smdc.dataTypeUID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.[uid] = smdc.displayTypeUID
	left outer join dbo.af_advanceFormulas as af_c on af_c.siteID = @siteID and af_c.[uid] = smdc.linkedDateCompareDateAFUID
	left outer join dbo.af_advanceFormulas as af_adv on af_adv.siteID = @siteID and af_adv.[uid] = smdc.linkedDateAdvanceAFUID
	where smdc.orgID = @orgID
	and smdc.finalAction in ('A','C');

	INSERT INTO #tmpUpdateMemberDataColumns (columnID, columnName, oldColumnName, dataTypeID, displayTypeID, dataTypeCode, displayTypeCode, oldDataTypeCode, oldDisplayTypeCode)
	select mdc.columnID, tmp.columnName, mdc.columnName, mdataNew.dataTypeID, mdispNew.displayTypeID, mdataNew.dataTypeCode, mdispNew.displayTypeCode, 
		mdataOld.dataTypeCode, mdispOld.displayTypeCode
	from #tmpMemberDataColumns as tmp
	inner join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.[uid] = tmp.[uid]
	inner join dbo.ams_memberDataColumnDataTypes as mdataNew on mdataNew.dataTypeID = tmp.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdispNew on mdispNew.displayTypeID = tmp.displayTypeID
	inner join dbo.ams_memberDataColumnDataTypes as mdataOld on mdataOld.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as mdispOld on mdispOld.displayTypeID = mdc.displayTypeID;

	INSERT INTO #mdcvToAdd (columnID, columnUID, columnValueString, columnValueDecimal2, columnValueInteger, columnvalueDate, columnValueBit, syncColumnID, syncValueID)
	select distinct mdc.columnID, smdc.[uid], smdcv.columnValueString, smdcv.columnValueDecimal2, smdcv.columnValueInteger, smdcv.columnvalueDate, smdcv.columnValueBit, 
		smdc.columnID, smdcv.valueID
	from datatransfer.dbo.sync_ams_memberDataColumnValues as smdcv
	inner join datatransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.valueID = smdcv.valueID
	left outer join dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.[uid] = smdc.[uid]
	where smdcv.orgID = @orgID
	and smdcv.finalAction = 'A'
	and smdc.finalAction in ('A','C')
	and smdcv.useValue is null;

	-- delete columns list
	select @deleteColumnIDList =  coalesce(@deleteColumnIDList + ',','') +  cast(mdc.columnID as varchar(10))
	from dbo.ams_memberDataColumns as mdc
	left outer join dataTransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
	where mdc.orgID = @orgID
	and smdc.columnID is null
	group by mdc.columnID;

	-- delete field values
	INSERT INTO #tmpDeleteMemberDataColumnValues (valueID)
	select distinct mdcv.valueID
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnValues AS mdcv ON mdc.orgID = @orgID AND mdcv.columnID = mdc.columnID 
	inner join dataTransfer.dbo.sync_ams_memberDataColumns AS smdc ON smdc.orgID = @orgID and smdc.[uid] = mdc.[uid]
		and smdc.finalAction = 'C'
	left outer join dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.columnID = smdc.columnID
		and smdcv.useValue = mdcv.valueID
	where smdc.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
	and smdc.dataTypeCode <> 'BIT'
	and smdcv.valueID is null;

	IF NOT EXISTS (select 1 from #tmpMemberDataColumns) AND NOT EXISTS (select 1 from #tmpDeleteMemberDataColumnValues) AND @deleteColumnIDList IS NULL
		GOTO on_done;

	BEGIN TRAN;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @deleteColumnIDList IS NOT NULL BEGIN
			EXEC dbo.ams_removeMemberDataColumn @siteID=@siteID, @columnIDList=@deleteColumnIDList, @recordedByMemberID=@recordedByMemberID;
			WAITFOR DELAY '00:00:00:500'
		END

		-- new columns
		IF EXISTS (select 1 from #tmpMemberDataColumns where finalAction = 'A') BEGIN
			INSERT INTO dbo.ams_memberDataColumns (orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, defaultValueID, isReadOnly, 
				allowMultiple, minChars, maxChars, minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate)
				OUTPUT Inserted.columnID, Inserted.uid 
				INTO #tmpMemberDataColumnsAdded
			select @orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, NULL AS defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, 
				minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate
			from #tmpMemberDataColumns
			where finalAction = 'A'
				except 
			select orgID, [uid], columnName, columnDesc, dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, 
				minSelected, maxSelected, minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate
			from dbo.ams_memberDataColumns
			where orgID = @orgID;

			UPDATE mdcv 
			SET mdcv.columnID = tmp.columnID
			FROM #mdcvToAdd as mdcv
			INNER JOIN #tmpMemberDataColumnsAdded as tmp on tmp.[uid] = mdcv.columnUID;
		END

		-- update columns
		IF EXISTS (select 1 from #tmpMemberDataColumns where finalAction = 'C') BEGIN
			UPDATE mdc
			SET mdc.columnName = tmp.columnName,
				mdc.columnDesc = tmp.columnDesc,
				mdc.allowNull = tmp.allowNull,
				mdc.defaultValueID = null,
				mdc.allowNewValuesOnImport = tmp.allowNewValuesOnImport,
				mdc.isReadOnly = tmp.isReadOnly,
				mdc.allowMultiple = tmp.allowMultiple,
				mdc.minChars = tmp.minChars,
				mdc.maxChars = tmp.maxChars,
				mdc.minSelected = tmp.minSelected,
				mdc.maxSelected = tmp.maxSelected,
				mdc.minValueInt = tmp.minValueInt,
				mdc.maxValueInt = tmp.maxValueInt,
				mdc.minValueDecimal2 = tmp.minValueDecimal2,
				mdc.maxValueDecimal2 = tmp.maxValueDecimal2,
				mdc.minValueDate = tmp.minValueDate,
				mdc.maxValueDate = tmp.maxValueDate
					OUTPUT inserted.columnID, inserted.uid
					INTO #tmpMemberDataColumnsUpdated
			FROM #tmpMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.uid = tmp.uid
			WHERE tmp.finalAction = 'C';
		END

		-- create field values
		INSERT INTO #tmpMDCDef (columnID, dataTypeCode, displayTypeCode)
		select c.columnID, dt.dataTypeCode, dp.displayTypeCode
		from (
			select distinct columnID from #mdcvToAdd
		) as tmp
		INNER JOIN dbo.ams_memberDataColumns as c on c.columnID = tmp.columnID
		INNER JOIN dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
		INNER JOIN dbo.ams_memberDataColumnDisplayTypes as dp on dp.displayTypeID = c.displayTypeID;

		IF @@ROWCOUNT > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValueBulk @orgID=@orgID;

			-- update valueID
			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef ON tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'STRING' 
			AND tmpDef.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
			AND tmp.columnValueString = mdcv.columnValueString;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'STRING' 
			AND tmpDef.displayTypeCode NOT IN ('SELECT','RADIO','CHECKBOX')
			AND tmp.columnValueString = mdcv.columnValueString COLLATE Latin1_General_CS_AS;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'DECIMAL2' 
			AND tmp.columnValueDecimal2 = mdcv.columnValueDecimal2;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'INTEGER' 
			AND tmp.columnValueInteger = mdcv.columnValueInteger;

			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'DATE' 
			AND tmp.columnValueDate = mdcv.columnValueDate;
			
			UPDATE tmp
			SET tmp.valueID = mdcv.valueID
			FROM #mdcvToAdd AS tmp
			INNER JOIN #tmpMDCDef AS tmpDef on tmpDef.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberDataColumnValues AS mdcv ON mdcv.columnID = tmp.columnID
			WHERE tmp.valueID IS NULL
			AND tmpDef.dataTypeCode = 'BIT' 
			AND tmp.columnValueBit = mdcv.columnValueBit;

			UPDATE smdcv
			SET smdcv.useValue = mdcv.valueID
			FROM dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv
			INNER JOIN datatransfer.dbo.sync_ams_memberDataColumns as smdc on smdc.orgID = @orgID and smdc.defaultValueID = smdcv.valueID
			INNER JOIN #mdcvToAdd AS mdcv ON mdcv.syncValueID = smdc.defaultValueID
			WHERE smdcv.orgID = @orgID
			AND smdcv.useValue IS NULL;
		END

		-- update defaultValue
		IF EXISTS (select 1 from #tmpMemberDataColumns where syncDefaultValueID is not null) BEGIN
			UPDATE mdc 
			SET mdc.defaultValueID = smdcv.useValue
			FROM dbo.ams_memberDataColumns AS mdc 
			INNER JOIN #tmpMemberDataColumns AS tmp ON tmp.[uid] = mdc.[uid]
			INNER JOIN dataTransfer.dbo.sync_ams_memberDataColumnValues AS smdcv ON smdcv.orgID = @orgID AND smdcv.valueID = tmp.syncDefaultValueID
			WHERE mdc.orgID = @orgID;

			INSERT INTO #tblMDDEFCols (columnID, valueID)
			select columnID, defaultValueID
			from dbo.ams_memberDataColumns
			where orgID = @orgID
			and defaultValueID is not null;

			INSERT INTO #tblExistingMDDEF (memberID, columnID)
			select distinct mActive.memberID, mdc.columnID
			from dbo.ams_members as m 
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberData as md on md.memberID = mActive.memberID
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			inner join dbo.ams_memberDataColumns AS mdc on mdc.orgID = @orgID and mdc.columnID = mdcv.columnID
			where mdc.defaultValueID is not null;

			-- Anyone who doesnt have a value for this column needs this value.
			INSERT INTO dbo.ams_memberData (memberID, valueID)
				OUTPUT Inserted.memberID
				INTO #tblMDDEF
			select distinct mActive.memberID, tmp.valueID
			from dbo.ams_members as m 
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			cross join #tblMDDEFCols as tmp
			where not exists (select 1 from #tblExistingMDDEF as def where def.memberID = mActive.memberID and def.columnID = tmp.columnID);

			UPDATE m
			SET m.dateLastUpdated = GETDATE()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID;
		END

		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where displayTypeCode <> olddisplayTypeCode or dataTypeCode <> olddataTypeCode) BEGIN
			UPDATE vgc
			SET processValuesSection = 
						case
						when vge.expression in ('eq','neq') and tmp.dataTypeCode in ('STRING','DECIMAL2','DATE') and tmp.displayTypeCode in ('RADIO','SELECT','CHECKBOX') then 1
						when vge.expression in ('eq','neq','lt','lte','gt','gte') and tmp.dataTypeCode = 'INTEGER' then 1
						when vge.expression in ('datepart','datediff') then 1
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'STRING' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 2
						when vge.expression in ('lt','lte','gt','gte','contains','contains_regex') and tmp.dataTypeCode = 'STRING' then 2
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'BIT' then 3
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'DECIMAL2' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 4
						when vge.expression in ('lt','lte','gt','gte') and tmp.dataTypeCode = 'DECIMAL2' then 4
						when vge.expression in ('eq','neq') and tmp.dataTypeCode = 'DATE' and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 5
						when vge.expression in ('lt','lte','gt','gte') and tmp.dataTypeCode = 'DATE' then 5
						else null end
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			INNER JOIN dbo.ams_virtualGroupExpressions as vge on vge.expressionID = vgc.expressionID
			WHERE tmp.displayTypeCode <> tmp.oldDisplayTypeCode 
			OR tmp.dataTypeCode <> tmp.oldDataTypeCode;
		END

		-- if changing the display type
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where displayTypeCode <> oldDisplayTypeCode) BEGIN
			UPDATE mdc
			SET mdc.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			WHERE mdc.orgID = @orgID;

			UPDATE mf
			SET mf.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE mdc.orgID = @orgID;

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			UPDATE vgcv
			SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.columnID = tmp.columnID
				and tmp.dataTypeCode <> 'BIT' 
				and tmp.oldDataTypeCode <> 'BIT' 
				and tmp.oldDisplayTypeCode in ('RADIO','SELECT','CHECKBOX') 
				and tmp.displayTypeCode not in ('RADIO','SELECT','CHECKBOX')
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
				and vgc.expressionID in (1,2)
			INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
			WHERE cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue;
			
			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			INSERT INTO #tmpColumnConditions (columnID, conditionID, dataTypeID)
			SELECT tmp.columnID, vgc.conditionID, vgc.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.orgID = @orgID and mdc.columnID = tmp.columnID
				and tmp.dataTypeCode <> 'BIT' 
				and tmp.oldDataTypeCode <> 'BIT' 
				and tmp.displayTypeCode in ('RADIO','SELECT','CHECKBOX') 
				and tmp.oldDisplayTypeCode not in ('RADIO','SELECT','CHECKBOX')
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE vgc.expressionID in (1,2);

			-- get the valueID
			UPDATE vgcv
			SET vgcv.conditionValue = tmp.valueID
			FROM dbo.ams_virtualGroupConditions as vgc
			INNER JOIN #tmpColumnConditions as tmpC on tmpC.conditionID = vgc.conditionID
			INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
			INNER JOIN (
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 1 
				and vgcv.conditionValue = mdcv.columnvalueString
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 2
				and cast(vgcv.conditionValue as decimal(14,2)) = mdcv.columnvalueDecimal2
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 3
				and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
					union
				select tmp.conditionID, mdcv.valueID
				from #tmpColumnConditions as tmp
				inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = tmp.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
				where tmp.dataTypeID = 4
				and cast(vgcv.conditionValue as date) = mdcv.columnvalueDate
			) as tmp on tmp.conditionID = vgc.conditionID;

			
			UPDATE vgc
			SET vgc.displayTypeID = tmp.displayTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));
		END

		-- if changing the data type
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where dataTypeCode <> oldDataTypeCode) BEGIN
			UPDATE mdc
			SET mdc.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			WHERE mdc.orgID = @orgID;

			UPDATE mf
			SET mf.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
			WHERE mdc.orgID = @orgID;

			-- string to decimal
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueString as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH
			END

			-- string to integer
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'INTEGER') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueInteger = cast(mdcv.columnValueString as int)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'INTEGER';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1);
				END CATCH
			END

			-- string to date
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'DATE') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDate = cast(mdcv.columnValueString as date)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'DATE';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1);
				END CATCH
			END

			-- string to bit
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'STRING' and dataTypeCode = 'BIT') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueBit = cast(mdcv.columnValueString as bit)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'STRING'
					AND tmp.dataTypeCode = 'BIT';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueString = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'STRING'
			AND tmp.dataTypeCode <> 'STRING';
			
			-- decimal2 to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'DECIMAL2' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueDecimal2 as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'DECIMAL2'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueDecimal2 = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'DECIMAL2'
			AND tmp.dataTypeCode <> 'DECIMAL2';

			-- integer to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueInteger as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1);
				END CATCH
			END

			-- integer to decimal2
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueInteger as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Decimal data type.', 16, 1);
				END CATCH
			END
			
			-- integer to bit
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'INTEGER' and dataTypeCode = 'BIT') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueBit = cast(mdcv.columnValueInteger as bit)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'INTEGER'
					AND tmp.dataTypeCode = 'BIT';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueInteger = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'INTEGER'
			AND tmp.dataTypeCode <> 'INTEGER';

			-- date to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'DATE' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = convert(varchar(10),mdcv.columnValueDate,101)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'DATE'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are date values not compatible with the Text string data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueDate = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'DATE'
			AND tmp.dataTypeCode <> 'DATE';
			
			-- bit to string
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'STRING') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueString = cast(mdcv.columnValueBit as varchar(255))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'STRING';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Text string data type.', 16, 1);
				END CATCH
			END

			-- bit to decimal2
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'DECIMAL2') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueDecimal2 = cast(mdcv.columnValueBit as decimal(14,2))
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'DECIMAL2';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH
			END

			-- bit to integer
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode = 'INTEGER') BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE mdcv
					SET mdcv.columnValueInteger = cast(mdcv.columnValueBit as int)
					FROM #tmpUpdateMemberDataColumns as tmp
					INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					WHERE tmp.oldDataTypeCode = 'BIT'
					AND tmp.dataTypeCode = 'INTEGER';
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
						RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1);
				END CATCH
			END

			UPDATE mdcv
			SET mdcv.columnValueBit = NULL
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
			WHERE tmp.oldDataTypeCode = 'BIT'
			AND tmp.dataTypeCode <> 'BIT';

			-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode IN ('STRING','INTEGER','DECIMAL2') and dataTypeCode = 'BIT' and olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX')) BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = mdcv.columnValueBit
				FROM #tmpUpdateMemberDataColumns as tmp
				INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.expressionID in (1,2)
				AND tmp.oldDataTypeCode IN ('STRING','INTEGER','DECIMAL2')
				AND tmp.dataTypeCode = 'BIT'
				AND tmp.oldDisplayTypeCode IN ('RADIO','SELECT','CHECKBOX');
			END

			-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
			IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where oldDataTypeCode = 'BIT' and dataTypeCode IN ('STRING','INTEGER','DECIMAL2') and displayTypeCode in ('RADIO','SELECT','CHECKBOX')) BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = mdcv.valueID
				FROM #tmpUpdateMemberDataColumns as tmp
				INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = tmp.columnID
					and mdcv.columnvalueString = vgcv.conditionValue
				WHERE vgc.expressionID in (1,2)
				AND tmp.dataTypeCode IN ('STRING','INTEGER','DECIMAL2')
				AND tmp.oldDataTypeCode = 'BIT'
				AND tmp.displayTypeCode IN ('RADIO','SELECT','CHECKBOX');
			END

			-- update virtual group conditions
			UPDATE vgc
			SET vgc.dataTypeID = tmp.dataTypeID
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
			CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

			UPDATE vgc
			SET vgc.subProc = CASE WHEN e.expression='datediff' then 'MD_DATEDIFF'
								WHEN e.expression='datepart' then 'MD_DATEPART'
								WHEN e.expression='contains' and tmp.dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
								WHEN e.expression='contains_regex' and tmp.dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
								WHEN e.expression='eq' and tmp.dataTypeCode='STRING' then 'MD_EQ_STRING'
								WHEN e.expression='eq' and tmp.dataTypeCode='BIT' then 'MD_EQ_BIT'
								WHEN e.expression='eq' and tmp.dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
								WHEN e.expression='eq' and tmp.dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
								WHEN e.expression='eq' and tmp.dataTypeCode='DATE' then 'MD_EQ_DATE'
								WHEN e.expression='exists' and tmp.dataTypeCode='STRING' then 'MD_EXISTS_STRING'
								WHEN e.expression='exists' and tmp.dataTypeCode='BIT' then 'MD_EXISTS_BIT'
								WHEN e.expression='exists' and tmp.dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
								WHEN e.expression='exists' and tmp.dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
								WHEN e.expression='exists' and tmp.dataTypeCode='DATE' then 'MD_EXISTS_DATE'
								WHEN e.expression='exists' and tmp.dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
								WHEN e.expression='exists' and tmp.dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
								WHEN e.expression='gt' and tmp.dataTypeCode='STRING' then 'MD_GT_STRING'
								WHEN e.expression='gt' and tmp.dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
								WHEN e.expression='gt' and tmp.dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
								WHEN e.expression='gt' and tmp.dataTypeCode='DATE' then 'MD_GT_DATE'
								WHEN e.expression='gte' and tmp.dataTypeCode='STRING' then 'MD_GTE_STRING'
								WHEN e.expression='gte' and tmp.dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
								WHEN e.expression='gte' and tmp.dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
								WHEN e.expression='gte' and tmp.dataTypeCode='DATE' then 'MD_GTE_DATE'
								WHEN e.expression='lt' and tmp.dataTypeCode='STRING' then 'MD_LT_STRING'
								WHEN e.expression='lt' and tmp.dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
								WHEN e.expression='lt' and tmp.dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
								WHEN e.expression='lt' and tmp.dataTypeCode='DATE' then 'MD_LT_DATE'
								WHEN e.expression='lte' and tmp.dataTypeCode='STRING' then 'MD_LTE_STRING'
								WHEN e.expression='lte' and tmp.dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
								WHEN e.expression='lte' and tmp.dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
								WHEN e.expression='lte' and tmp.dataTypeCode='DATE' then 'MD_LTE_DATE'
								WHEN e.expression='neq' and tmp.dataTypeCode='STRING' then 'MD_NEQ_STRING'
								WHEN e.expression='neq' and tmp.dataTypeCode='BIT' then 'MD_NEQ_BIT'
								WHEN e.expression='neq' and tmp.dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
								WHEN e.expression='neq' and tmp.dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
								WHEN e.expression='neq' and tmp.dataTypeCode='DATE' then 'MD_NEQ_DATE'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
								WHEN e.expression='not_exists' and tmp.dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
							END
				OUTPUT INSERTED.orgID, NULL, INSERTED.conditionID INTO #tblMCQRun (orgID, memberID, conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions AS vgc ON vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON e.expressionID = vgc.expressionID;

			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10));

			-- reprocess conditions based on field
			IF EXISTS (SELECT 1 FROM #tblMCQRun)
				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
		END
		
		
		-- if there was a change in columnname
		IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where columnName <> oldColumnName COLLATE Latin1_General_CS_AI) BEGIN
			-- update member fields
			UPDATE mf
			SET mf.dbField = tmp.columnName
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_memberFields as mf on mf.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			WHERE tmp.columnName <> tmp.oldColumnName COLLATE Latin1_General_CS_AI;

			-- update virtual group conditions
			UPDATE vgc
			SET vgc.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
			FROM #tmpUpdateMemberDataColumns as tmp
			INNER JOIN dbo.ams_virtualGroupConditions as vgc on vgc.orgID = @orgID and vgc.fieldCode = 'md_' + cast(tmp.columnID as varchar(10))
			WHERE tmp.columnName <> tmp.oldColumnName COLLATE Latin1_General_CS_AI;
		END

		-- linked date custom fields
		UPDATE tmp
		SET tmp.linkedDateColumnID = mdc.columnID
		FROM #tmpMemberDataColumns AS tmp
		INNER JOIN dbo.ams_memberDataColumns AS mdc ON mdc.orgID = @orgID
			AND mdc.[uid] = tmp.linkedDateColumnUID;

		UPDATE mdc
		SET mdc.linkedDateColumnID = tmp.linkedDateColumnID, 
			mdc.linkedDateCompareDate = tmp.linkedDateCompareDate, 
			mdc.linkedDateCompareDateAFID = tmp.linkedDateCompareDateAFID,
			mdc.linkedDateAdvanceDate = tmp.linkedDateAdvanceDate, 
			mdc.linkedDateAdvanceAFID = tmp.linkedDateAdvanceAFID
		FROM dbo.ams_memberDataColumns AS mdc
		INNER JOIN #tmpMemberDataColumns AS tmp ON tmp.uid = mdc.uid
		WHERE mdc.orgID = @orgID;
	COMMIT TRAN;

	-- check for subscription member date rules we may need to update
	IF EXISTS (select 1 from #tmpUpdateMemberDataColumns where columnName <> oldColumnName and dataTypeCode = 'DATE') BEGIN
		DECLARE @tblDateColumns TABLE (columnID int, columnName varchar(128), oldColumnName varchar(128));
		DECLARE @tblJD TABLE (udid int);

		INSERT INTO @tblDateColumns (columnID, columnName, oldColumnName)
		select columnID, columnName, oldColumnName
		from #tmpUpdateMemberDataColumns 
		where columnName <> oldColumnName 
		and dataTypeCode = 'DATE';

		INSERT INTO @tblJD (udid)
		select distinct udid 
		from customapps.dbo.schedTask_memberJoinDates as jd
		inner join dbo.sites as s on s.orgID = @orgID and s.siteCode = jd.siteCode
		inner join @tblDateColumns as tmp on 
			( tmp.oldColumnName = jd.joinDateFieldName
				or tmp.oldColumnName = jd.rejoinDateFieldName
				or tmp.oldColumnName = jd.droppedDateFieldName 
				or tmp.oldColumnName = jd.paidThruDateFieldName 
				or tmp.oldColumnName = jd.renewalDateFieldName )

		IF @@ROWCOUNT > 0 BEGIN
			UPDATE jd
			SET jd.joinDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.joinDateFieldName;

			UPDATE jd
			SET jd.rejoinDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.rejoinDateFieldName;

			UPDATE jd
			SET jd.droppedDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.droppedDateFieldName;

			UPDATE jd
			SET jd.paidThruDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.paidThruDateFieldName;

			UPDATE jd
			SET jd.renewalDateFieldName = dc.columnName
			FROM customapps.dbo.schedTask_memberJoinDates as jd
			INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
			INNER JOIN @tblDateColumns as dc on dc.oldColumnName = jd.renewalDateFieldName;
		END
	END

	SELECT @minValueID = MIN(valueID) FROM #tmpDeleteMemberDataColumnValues;
	WHILE @minValueID IS NOT NULL BEGIN
		EXEC dbo.ams_removeMemberDataColumnValue @valueID=@minValueID, @recordedByMemberID=@recordedByMemberID;
		WAITFOR DELAY '00:00:00:300';

		SELECT @minValueID = MIN(valueID) FROM #tmpDeleteMemberDataColumnValues WHERE valueID > @minValueID;
	END

	-- run linked date custom field rule
	SELECT @minColumnID = MIN(columnID) FROM dbo.ams_memberDataColumns WHERE orgID = @orgID AND linkedDateColumnID IS NOT NULL;
	WHILE @minColumnID IS NOT NULL BEGIN
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@minColumnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

		SELECT @minColumnID = MIN(columnID) 
			FROM dbo.ams_memberDataColumns 
			WHERE orgID = @orgID
			AND linkedDateColumnID IS NOT NULL
			AND columnID > @minColumnID;
	END

	-- recreate vw
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- delete org sync member data column rows
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumnValues WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_ams_memberDataColumns WHERE orgID = @orgID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpUpdateMemberDataColumns') IS NOT NULL
		DROP TABLE #tmpUpdateMemberDataColumns;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsAdded') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsAdded;
	IF OBJECT_ID('tempdb..#tmpMemberDataColumnsUpdated') IS NOT NULL 
		DROP TABLE #tmpMemberDataColumnsUpdated;
	IF OBJECT_ID('tempdb..#tmpDeleteMemberDataColumnValues') IS NOT NULL 
		DROP TABLE #tmpDeleteMemberDataColumnValues;
	IF OBJECT_ID('tempdb..#mdcvToAdd') IS NOT NULL 
		DROP TABLE #mdcvToAdd;
	IF OBJECT_ID('tempdb..#tmpMDCDef') IS NOT NULL 
		DROP TABLE #tmpMDCDef;
	IF OBJECT_ID('tempdb..#tblMDDEFCols') IS NOT NULL 
		DROP TABLE #tblMDDEFCols;
	IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
		DROP TABLE #tblMDDEF;
	IF OBJECT_ID('tempdb..#tblExistingMDDEF') IS NOT NULL 
		DROP TABLE #tblExistingMDDEF;
	IF OBJECT_ID('tempdb..#tmpColumnConditions') IS NOT NULL 
		DROP TABLE #tmpColumnConditions;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
