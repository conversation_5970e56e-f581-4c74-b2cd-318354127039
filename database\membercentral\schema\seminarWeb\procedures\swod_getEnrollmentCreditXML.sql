ALTER PROC dbo.swod_getEnrollmentCreditXML
@enrollmentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	select (
		select authorityName, lastDateToComplete 
		from (
			SELECT tblCreditAuthorities.authorityName, tblEnrollmentsAndCredit.lastDateToComplete
			FROM dbo.tblSeminars 
			INNER JOIN dbo.tblEnrollments ON tblSeminars.seminarID = tblEnrollments.seminarID
			INNER JOIN dbo.tblEnrollmentsAndCredit ON tblEnrollments.enrollmentID = tblEnrollmentsAndCredit.enrollmentID 
			INNER JOIN dbo.tblSeminarsAndCredit ON tblSeminars.seminarID = tblSeminarsAndCredit.seminarID 
				AND tblEnrollmentsAndCredit.seminarCreditID = tblSeminarsAndCredit.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities ON tblSeminarsAndCredit.CSALinkID = tblCreditSponsorsAndAuthorities.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities ON tblCreditSponsorsAndAuthorities.authorityID = tblCreditAuthorities.authorityID
			INNER JOIN dbo.tblCreditAuthoritiesSWOD ON tblCreditAuthorities.authorityID = tblCreditAuthoritiesSWOD.authorityID 
			where tblEnrollments.enrollmentid = @enrollmentID
		) as EnrollmentCredit
		order by lastDateToComplete, authorityName
		FOR XML auto,elements,type,root('EnrollmentCredits')
	) as enrollmentCreditXML;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
