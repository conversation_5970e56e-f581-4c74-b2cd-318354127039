<cfcomponent output="no">

	<cffunction name="getPublicationDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		
		<cfset var qryPublication = "">

		<cfquery name="qryPublication" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select p.publicationID, ai.siteResourceID, ai.applicationInstanceID, ai.applicationInstanceName, ai.applicationInstanceDesc,
				p.supportsEmailEditions, p.supportsOnlineEditions, p.supportsPDFEditions, p.emailEditionSenderName, 
				p.emailEditionReplyT<PERSON><PERSON>ddress, p.emailEditionPreviewEmailAddresses, isnull(p.onlineEditionPreviewableEditionCount,0) as onlineEditionPreviewableEditionCount,
				p.categoryTreeID, p.emailConsentListID, p.webHeaderTemplateIsFullWidth, p.webFooterTemplateIsFullWidth,
				ficu_i.featureImageConfigID as issueFeatureImageConfigID, ficu_it.featureImageConfigID as issueItemFeatureImageConfigID, 
				p.allowImportedHTMLEditions, p.allowFrontEndSubmissions, p.frontEndSubmissionsBlogID, p.featuredImageLocation, p.utm_autoappend, p.utm_campaign,
				pe_html.templateID as htmlEmailTemplateID, pe_text.templateID as textEmailTemplateID, 
				pw_head.templateID as webHeaderTemplateID, pw_home.templateID as webHomeContentTemplateID, 
				pw_foot.templateID as webFooterTemplateID, pw_side.templateID as webSidebarTemplateID,
				clm.modeName as consentListModeName, ISNULL(p.parentPublicationID, 0) as parentPublicationID,
				p.autoApproveIssues, p.autoApproveTime, p.orgIdentityID,
				p.isParentPublication, ISNULL(p.syndicationNetworkID,0) as syndicationNetworkID, n.networkName as syndicationNetworkName,
				pai.applicationInstanceName as parentPublicationName,
				(select count(tmp.publicationID) from dbo.pub_publications as tmp where isnull(tmp.parentPublicationID,0) = p.publicationID) as childPublicationsCount,
				(
					select count(tmp2.publicationID)
					from dbo.pub_publications as tmp2
					inner join dbo.cms_applicationInstances as ai2 on ai2.applicationInstanceID = tmp2.applicationInstanceID
					inner join dbo.sites as s on s.siteID = ai.siteID
					where isnull(tmp2.parentPublicationID,0) = p.publicationID
					and s.siteID <> @siteID
				) as crossSiteChildPublicationsCount,
				ficus_issueItem.featureImageSizeID as issueItemFeatureImageSizeID,
				ficus_issue.featureImageSizeID as issueArchiveFeatureImageSizeID
			from dbo.pub_publications as p
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'publications'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			left outer join dbo.cms_featuredImageConfigUsages as ficu_i on ficu_i.referenceID = p.publicationID and ficu_i.referenceType = 'publicationIssue'
			left outer join dbo.cms_featuredImageConfigUsages as ficu_it on ficu_it.referenceID = p.publicationID and ficu_it.referenceType = 'publicationIssueItem'
			left outer join platformMail.dbo.email_consentLists AS cl ON cl.consentListID = p.emailConsentListID
				and cl.[status] = 'A'
			left outer join platformMail.dbo.email_consentListModes as clm on clm.consentListModeID = cl.consentListModeID
			left outer join dbo.template_usages as pe_html on pe_html.referenceID = p.publicationID and pe_html.referenceType = 'PubEmailHtml'
			left outer join dbo.template_usages as pe_text on pe_text.referenceID = p.publicationID and pe_text.referenceType = 'PubEmailText'
			left outer join dbo.template_usages as pw_home on pw_home.referenceID = p.publicationID and pw_home.referenceType = 'PubWebHomepage'
			left outer join dbo.template_usages as pw_head on pw_head.referenceID = p.publicationID and pw_head.referenceType = 'PubWebHeader'
			left outer join dbo.template_usages as pw_foot on pw_foot.referenceID = p.publicationID and pw_foot.referenceType = 'PubWebFooter'
			left outer join dbo.template_usages as pw_side on pw_side.referenceID = p.publicationID and pw_side.referenceType = 'PubWebSidebar'
			left outer join dbo.networks AS n on n.networkID = isnull(p.syndicationNetworkID,0)
			left outer join dbo.pub_publications AS pp on pp.publicationID = isnull(p.parentPublicationID,0)
			left outer join dbo.cms_applicationInstances as pai on pai.applicationInstanceID = pp.applicationInstanceID
			left outer join dbo.cms_featuredImageConfigUsagesAndSizes as ficus_issue on ficus_issue.featureImageConfigUsageID = ficu_i.featureImageConfigUsageID
				and ficus_issue.referenceType = 'publicationIssueArchive'
			left outer join dbo.cms_featuredImageConfigUsagesAndSizes as ficus_issueItem on ficus_issueItem.featureImageConfigUsageID = ficu_it.featureImageConfigUsageID
				and ficus_issueItem.referenceType = 'publicationIssueItem'
			where ai.siteID = @siteID
			and p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryPublication>
	</cffunction>

	<cffunction name="getAvailablePublicationSources" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		
		<cfset var qryPublicationSources = "">

		<cfquery name="qryPublicationSources" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ai.applicationInstanceID, at.applicationTypeName, ai.applicationInstanceName
			from dbo.cms_applicationInstances as ai 
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName in ('blog','publications')
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			left outer join dbo.pub_publications as p on p.applicationInstanceID = ai.applicationInstanceID
				and at.applicationTypeName = 'publications'
			left outer join dbo.pub_publicationSources as ps on ps.sourceApplicationInstanceID = ai.applicationInstanceID
				and ps.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and ps.publicationSourceID is null
			and (at.applicationTypeName = 'blog' or isnull(p.publicationID,0) <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">)
			order by at.applicationTypeName, ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryPublicationSources>
	</cffunction>

	<cffunction name="getPublicationInstanceSiteResourceID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		
		<cfset var qryPublication = "">

		<cfquery name="qryPublication" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.publicationID, ai.siteResourceID
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID AND at.applicationTypeName = 'publications'
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			WHERE ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryPublication.siteResourceID>
	</cffunction>

	<cffunction name="savePublicationSources" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="publicationSourceIDList" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="Edit")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryInsertPublicationSources" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				
				DECLARE @siteID int, @publicationID int, @publicationSourceIDList varchar(8000);
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
				SET @publicationSourceIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.publicationSourceIDList#">;

				INSERT INTO dbo.pub_publicationSources (publicationID, sourceApplicationInstanceID)
				select @publicationID, ai.applicationInstanceID
				from dbo.fn_intListToTable(@publicationSourceIDList,',') as tmp
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = tmp.listitem
				inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName in ('blog','publications')
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				left outer join dbo.pub_publications as p on p.applicationInstanceID = ai.applicationInstanceID
					and at.applicationTypeName = 'publications'
				left outer join dbo.pub_publicationSources as ps on ps.sourceApplicationInstanceID = ai.applicationInstanceID
					and ps.publicationID = @publicationID
				where ai.siteID = @siteID
				and ps.publicationSourceID is null
				and (at.applicationTypeName = 'blog' or isnull(p.publicationID,0) <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">)
			</cfquery>
			
			<cfset local.returnStruct.success = true>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removePublicationSource" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="publicationSourceID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="Edit")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryDeletePublicationSource" datasource="#application.dsn.membercentral.dsn#">
				DELETE FROM dbo.pub_publicationSources
				WHERE publicationSourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationSourceID#">
				AND publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePublicationSettings" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="publicationName" type="string" required="yes">
		<cfargument name="publicationDesc" type="string" required="yes">
		<cfargument name="orgIdentityID" type="numeric" required="yes">
		<cfargument name="categoryTreeID" type="numeric" required="yes">
		<cfargument name="emailPreviewsTo" type="string" required="yes">
		<cfargument name="autoApproveIssues" type="boolean" required="yes">
		<cfargument name="autoApproveTime" type="string" required="yes">
		<cfargument name="autoApprovalDays" type="string" required="yes">
		<cfargument name="supportsEmailEditions" type="boolean" required="yes">
		<cfargument name="supportsOnlineEditions" type="boolean" required="yes">
		<cfargument name="supportsPDFEditions" type="boolean" required="yes">
		<cfargument name="senderName" type="string" required="yes">
		<cfargument name="replyTo" type="string" required="yes">
		<cfargument name="previewEditionCount" type="numeric" required="yes">
		<cfargument name="htmlEmailTemplateID" type="numeric" required="yes">
		<cfargument name="textEmailTemplateID" type="numeric" required="yes">
		<cfargument name="utmAutoAppend" type="boolean" required="yes">
		<cfargument name="utmCampaign" type="string" required="yes">
		<cfargument name="emailTagTypeIDs" type="string" required="yes">
		<cfargument name="emailConsentListID" type="numeric" required="yes">
		<cfargument name="issueFeatureImageConfigID" type="numeric" required="yes">
		<cfargument name="issueArchiveFeatureImageSizeID" type="numeric" required="yes">
		<cfargument name="issueItemFeatureImageConfigID" type="numeric" required="yes">
		<cfargument name="issueItemFeaturedImageLocation" type="string" required="yes">
		<cfargument name="issueItemFeatureImageSizeID" type="numeric" required="yes">
		<cfargument name="allowImportedHTMLEditions" type="boolean" required="yes">
		<cfargument name="allowFrontEndSubmissions" type="boolean" required="yes">
		<cfargument name="frontEndSubmissionsBlogID" type="numeric" required="yes">
		<cfargument name="webHomeContentTemplateID" type="numeric" required="yes">
		<cfargument name="webHeaderTemplateID" type="numeric" required="yes">
		<cfargument name="webFooterTemplateID" type="numeric" required="yes">
		<cfargument name="webSidebarTemplateID" type="numeric" required="yes">
		<cfargument name="webHeaderTemplateIsFullWidth" type="boolean" required="yes">
		<cfargument name="webFooterTemplateIsFullWidth" type="boolean" required="yes">
		<cfargument name="fieldSetID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.PublicationAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PublicationAdmin',siteID=arguments.siteID)>

		<cfquery name="local.qryUpdatePublication" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int, @publicationID int, @orgEmailTagIDList varchar(8000), @emailConsentListID int, @issueFeatureImageConfigID int, 
					@issueItemFeatureImageConfigID int, @featureImageConfigUsageID int, @htmlEmailTemplateID int,
					@textEmailTemplateID int, @webSidebarTemplateID int, @webFooterTemplateID int, @webHeaderTemplateID int, @webHomeContentTemplateID int,
					@templateUsageID int, @fieldID int, @valueID int, @detail varchar(max), @dataID int, @autoApprovalDaysList varchar(50), @PublicationAdminSRID int, 
					@currentFSUseID int, @currentFSID int, @newFSID int, @FSArea varchar(20), @useID int;

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
				<cfif len(arguments.emailTagTypeIDs)>
					SET @orgEmailTagIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.emailTagTypeIDs#">;
				</cfif>
				<cfif len(arguments.autoApprovalDays)>
					SET @autoApprovalDaysList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.autoApprovalDays#">;
				</cfif>
				SET @issueFeatureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueFeatureImageConfigID#">;
				SET @issueItemFeatureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemFeatureImageConfigID#">;
				SET @htmlEmailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.htmlEmailTemplateID#">;
				SET @textEmailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.textEmailTemplateID#">;
				SET @webSidebarTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webSidebarTemplateID#">;
				SET @webHomeContentTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webHomeContentTemplateID#">;
				SET @webHeaderTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webHeaderTemplateID#">;
				SET @webFooterTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webFooterTemplateID#">;
				SET @PublicationAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.PublicationAdminSRID#">;

				SELECT @emailConsentListID = consentListID
				FROM platformMail.dbo.email_consentLists AS cl
				INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.consentListTypeID = cl.consentListTypeID
				WHERE cl.consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailConsentListID#">
				AND clt.orgID = @orgID
				and cl.[status] = 'A';

				BEGIN TRAN;
					update ai 
					set applicationInstanceName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publicationName#">,
						applicationInstanceDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publicationDesc#">
					from dbo.pub_publications as p
					inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
					where ai.siteID = @siteID
					and p.publicationID = @publicationID;

					update dbo.pub_publications
					set categoryTreeID = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryTreeID#">,0),
						emailEditionPreviewEmailAddresses = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailPreviewsTo#">,''),
						autoApproveIssues = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.autoApproveIssues#">, 
						autoApproveTime = nullif(<cfqueryparam cfsqltype="CF_SQL_TIME" value="#arguments.autoApproveTime#">,''),
						supportsEmailEditions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.supportsEmailEditions#">, 
						supportsOnlineEditions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.supportsOnlineEditions#">, 
						supportsPDFEditions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.supportsPDFEditions#">, 
						emailEditionSenderName = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.senderName#">,''), 
						emailEditionReplyToAddress = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.replyTo#">,''),
						emailConsentListID = @emailConsentListID,
						onlineEditionPreviewableEditionCount = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.previewEditionCount#">,0),
						allowImportedHTMLEditions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.allowImportedHTMLEditions#">,
						allowFrontEndSubmissions = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.allowFrontEndSubmissions#">,
						frontEndSubmissionsBlogID = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.frontEndSubmissionsBlogID#">,0),
						webHeaderTemplateIsFullWidth = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.webHeaderTemplateIsFullWidth#">, 
						webFooterTemplateIsFullWidth = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.webFooterTemplateIsFullWidth#">,
						orgIdentityID = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">,0),
						featuredImageLocation = nullIf(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.issueItemFeaturedImageLocation#">,''),
						utm_autoappend = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.utmAutoAppend#">,
						utm_campaign = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.utmCampaign#">,'')
					where publicationID = @publicationID;

					<cfif len(arguments.emailTagTypeIDs)>
						DELETE FROM dbo.pub_publicationEmailTagTypes
						WHERE publicationID = @publicationID
						AND emailTagTypeID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.emailTagTypeIDs#" list="true">);

						INSERT INTO dbo.pub_publicationEmailTagTypes (publicationID, emailTagTypeID)
						SELECT @publicationID, tmp.listitem
						FROM dbo.fn_intListToTable(@orgEmailTagIDList,',') AS tmp
						LEFT JOIN dbo.pub_publicationEmailTagTypes pt ON pt.publicationID = @publicationID AND pt.emailTagTypeID = tmp.listitem
						WHERE pt.emailTagTypeID IS NULL;
					</cfif>

					<cfif NOT arguments.autoApproveIssues>
						DELETE FROM dbo.pub_publicationAutoApprovalDays WHERE publicationID = @publicationID;
					<cfelseif len(arguments.autoApprovalDays)>						
						DELETE FROM dbo.pub_publicationAutoApprovalDays
						WHERE publicationID = @publicationID
						AND weekDay NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.autoApprovalDays#" list="true">);

						INSERT INTO dbo.pub_publicationAutoApprovalDays (publicationID, weekDay)
						SELECT @publicationID, tmp.listitem
						FROM dbo.fn_intListToTable(@autoApprovalDaysList,',') AS tmp
						LEFT JOIN dbo.pub_publicationAutoApprovalDays ad ON ad.publicationID = @publicationID AND ad.weekDay = tmp.listitem
						WHERE ad.autoApprovalDayID IS NULL;
					</cfif>

					DELETE FROM dbo.template_usages
					WHERE referenceID = @publicationID
					AND referenceType IN ('PubEmailHtml','PubEmailText','PubWebHomepage','PubWebHeader','PubWebFooter','PubWebSidebar');

					<cfif arguments.htmlEmailTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@htmlEmailTemplateID, @referenceType='PubEmailHtml',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>
					<cfif arguments.textEmailTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@textEmailTemplateID, @referenceType='PubEmailText',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>
					<cfif arguments.webHomeContentTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@webHomeContentTemplateID, @referenceType='PubWebHomepage',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>
					<cfif arguments.webHeaderTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@webHeaderTemplateID, @referenceType='PubWebHeader',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>
					<cfif arguments.webFooterTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@webFooterTemplateID, @referenceType='PubWebFooter',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>
					<cfif arguments.webSidebarTemplateID gt 0>
						EXEC dbo.template_createTemplateUsage @templateID=@webSidebarTemplateID, @referenceType='PubWebSidebar',	
							@referenceID=@publicationID, @templateUsageID=@templateUsageID OUTPUT;
					</cfif>

					SELECT @currentFSUseID = NULL, @currentFSID = NULL, @newFSID = NULL, @FSArea = NULL;
					SET @newFSID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">;
					SET @FSArea = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="pubAuthorInfo">;

					SELECT TOP 1 @currentFSUseID = useID, @currentFSID = fieldsetID
					FROM membercentral.dbo.ams_memberFieldUsage
					WHERE siteResourceID = @PublicationAdminSRID
					AND area = @FSArea;

					IF ISNULL(@currentFSID,0) <> @newFSID BEGIN
						IF @currentFSUseID IS NOT NULL
							DELETE FROM membercentral.dbo.ams_memberFieldUsage
							WHERE useID = @currentFSUseID;
						IF @newFSID > 0
						EXEC membercentral.dbo.ams_createMemberFieldUsage @siteResourceID=@PublicationAdminSRID, @fieldsetID=@newFSID, @area=@FSArea,
							@createSiteResourceID=0, @useID=@useID OUTPUT;
					END

					-- clear any existing template field data
					DELETE fd
					FROM dbo.cf_fieldData as fd
					INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
					INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
					WHERE fd.itemID = @publicationID
					AND fd.itemType='PubTemplate'
					AND ft.displayTypeCode <> 'FEATUREDIMG';

					-- update template field details
					<cfloop array="#arguments.arrTemplateFields#" index="local.cf">
						<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
							<cfset local.tempSQL = addTemplate_cf_option(itemType='PubTemplate', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
							#preserveSingleQuotes(local.tempSQL)#
						<cfelseif local.cf.displayTypeCode neq "FEATUREDIMG" and len(local.cf.value)>
							<cfset local.tempSQL = addTemplate_cf_nonOption(itemType='PubTemplate', fieldID=local.cf.fieldID, customText=local.cf.value)>
							#preserveSingleQuotes(local.tempSQL)#
						</cfif>
					</cfloop>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<!--- clearing unused featured image custom field data through deleting featuredImageUsage --->
		<cfquery name="local.qryAllFieldsFtdImg" datasource="#application.dsn.memberCentral.dsn#">
			SELECT fd.fieldID
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
			INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
			WHERE fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			AND fd.itemType='PubTemplate'
			AND ft.displayTypeCode = 'FEATUREDIMG';
		</cfquery>

		<cfif local.qryAllFieldsFtdImg.recordCount>
			<cfset local.arrTemplateFieldsFtdImg = arrayFilter(arguments.arrTemplateFields, function(item){ return arguments.item.displayTypeCode eq "FEATUREDIMG"; })>
			<cfset local.fieldIDList = "">
			<cfloop array="#local.arrTemplateFieldsFtdImg#" index="local.thisField">
				<cfset local.fieldIDList = listAppend(local.fieldIDList, local.thisField.fieldID)>
			</cfloop>
			<cfquery name="local.qryFieldsFtdImgNotInUse" dbtype="query">
				select fieldID
				from [local].qryAllFieldsFtdImg
				where fieldID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fieldIDList#" list="true">)
			</cfquery>

			<cfloop query="local.qryFieldsFtdImgNotInUse">
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteFeaturedImageUsage">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="cf_#local.qryFieldsFtdImgNotInUse.fieldID#">
				</cfstoredproc>
			</cfloop>
		</cfif>

		<cfset local.arrIssueArchiveFeaturedImageSizes = [ { "referenceType":"publicationIssueArchive", "sizeID":arguments.issueArchiveFeatureImageSizeID }]>
		<cfset local.objFeaturedImages.saveFeaturedImageConfigSettings(
			featureImageConfigID=arguments.issueFeatureImageConfigID, referenceID=arguments.publicationID, 
			referenceType="publicationIssue", arrFeaturedImageSizes=local.arrIssueArchiveFeaturedImageSizes)>
		
		<cfset local.arrIssueItemFeaturedImageSizes = [ { "referenceType":"publicationIssueItem", "sizeID":arguments.issueItemFeatureImageSizeID }]>
		<cfset local.objFeaturedImages.saveFeaturedImageConfigSettings(
			featureImageConfigID=arguments.issueItemFeatureImageConfigID, referenceID=arguments.publicationID, 
			referenceType="publicationIssueItem", arrFeaturedImageSizes=local.arrIssueItemFeaturedImageSizes)>

	</cffunction>

	<cffunction name="addTemplate_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addPubTemplateFieldSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@publicationID, @itemType='#arguments.itemType#', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addPubTemplateFieldSQL = "">
		</cfif>

		<cfreturn local.addPubTemplateFieldSQL>
	</cffunction>

	<cffunction name="addTemplate_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.addPubTemplateFieldSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@publicationID, @itemType='#arguments.itemType#', 
							@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addPubTemplateFieldSQL>
	</cffunction>

	<cffunction name="getPublicationCategoryTrees" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local.qryPublicationVolumes = "">

		<cfquery name="local.qryPublicationCategoryTrees" datasource="#application.dsn.memberCentral.dsn#">
			SELECT DISTINCT t.categoryTreeID, t.categoryTreeName 
			FROM dbo.cms_postTypeCategoryTrees pt
			INNER JOIN dbo.cms_categoryTrees t ON t.categoryTreeID = pt.categoryTreeID
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = t.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
				AND srs.siteResourceStatusDesc = 'Active'
			WHERE t.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY t.categoryTreeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationCategoryTrees>
	</cffunction>

	<cffunction name="getVolumesFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateCreated")>
		<cfset arrayAppend(local.arrCols,"volumeName")>
		<cfset arrayAppend(local.arrCols,"issuesCount")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderdir')#">
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryVolumes" result="local.qryVolumesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpVolumesSearch') IS NOT NULL 
					DROP TABLE ##tmpVolumesSearch;
				CREATE TABLE ##tmpVolumesSearch (volumeID INT PRIMARY KEY);

				DECLARE @publicationID INT, @fCreatedFrom DATE, @fCreatedTo DATE;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pid',0)#">;

				<cfif arguments.event.getTrimValue('fCreatedFrom','') NEQ ''>
					SET @fCreatedFrom = <cfqueryparam value="#arguments.event.getTrimValue('fCreatedFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedTo','') NEQ ''>
					SET @fCreatedTo = <cfqueryparam value="#arguments.event.getTrimValue('fCreatedTo')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				
				INSERT INTO ##tmpVolumesSearch (volumeID)
				SELECT volumeID
				FROM dbo.pub_volumes
				WHERE publicationID = @publicationID
				<cfif arguments.event.getTrimValue('fCreatedFrom','') NEQ ''>
					AND dateCreated >= @fCreatedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fCreatedTo','') NEQ ''>
					AND dateCreated <= dateadd(day,1,@fCreatedTo)
				</cfif>;

				DECLARE @posStart INT, @posStartPlusCount INT, @totalCount INT;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpVolumes') IS NOT NULL
					DROP TABLE ##tmpVolumes;
				CREATE TABLE ##tmpVolumes (volumeID INT, publicationID INT, dateCreated DATETIME, volumeName VARCHAR(20), issuesCount INT, row INT);

				INSERT INTO ##tmpVolumes (volumeID, publicationID, dateCreated, volumeName, issuesCount, row)
				SELECT e.volumeID, publicationID, dateCreated, volumeName, issuesCount,
						ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
				FROM
				(
					SELECT v.volumeID, v.publicationID, v.dateCreated, v.volumeName, 
					(SELECT COUNT(*) 
						FROM dbo.pub_issues tmp 
						WHERE tmp.volumeID = v.volumeID
					) AS issuesCount
					FROM dbo.pub_volumes AS v
				) e
				INNER JOIN ##tmpVolumesSearch AS s ON s.volumeID = e.volumeID;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT volumeID, publicationID, dateCreated, volumeName, issuesCount, @totalCount as totalCount
				FROM ##tmpVolumes
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpVolumes') IS NOT NULL
					DROP TABLE ##tmpVolumes;

				IF OBJECT_ID('tempdb..##tmpVolumesSearch') IS NOT NULL 
					DROP TABLE ##tmpVolumesSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryVolumes>
	</cffunction>

	<cffunction name="getVolumeDetails" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryVolumeDetails = ''>

		<cfquery name="local.qryVolumeDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT v.volumeID, v.publicationID, v.dateCreated, v.volumeName, v.enteredByMemberID, m.firstName + ' ' + m.lastName + ', ' + oi.organizationName AS memberDisplayName,
				(SELECT COUNT(issueID) 
					FROM dbo.pub_issues tmp 
					WHERE tmp.volumeID = v.volumeID
				) AS issuesCount
			FROM dbo.pub_volumes AS v
			INNER JOIN dbo.ams_members AS m ON m.memberid = v.enteredByMemberID
			INNER JOIN dbo.organizations AS o ON o.orgID = m.orgID
			INNER JOIN dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			WHERE v.volumeID = <cfqueryparam value="#arguments.volumeID#" cfsqltype="CF_SQL_INTEGER">
			AND v.publicationID = <cfqueryparam value="#arguments.publicationID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryVolumeDetails>
	</cffunction>

	<cffunction name="hasDuplicateVolume" access="public" output="true" returntype="boolean" hint="check if there is a duplicate volume">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">
		<cfargument name="volumeName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryHasDuplicateVolume" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @volumeID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">;
			SELECT volumeID
			FROM dbo.pub_volumes
			WHERE publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			AND volumeName = '#arguments.volumeName#'
			AND (@volumeID = 0 OR volumeID <> @volumeID);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryHasDuplicateVolume.recordCount GT 0>
			<cfreturn true>
		<cfelse>
			<cfreturn false>
		</cfif>
	</cffunction>

	<cffunction name="saveVolume" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">
		<cfargument name="volumeName" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		<cfset local.returnStruct.duplicate = false>

		<cftry>
			<cfset local.hasDuplicate = hasDuplicateVolume(publicationID=arguments.publicationID, volumeID=arguments.volumeID, volumeName=arguments.volumeName)>
			<cfif NOT local.hasDuplicate>
				<cfif arguments.volumeID gt 0>
					<cfset updateVolume(publicationID=arguments.publicationID, volumeID=arguments.volumeID, volumeName=arguments.volumeName)>
				<cfelse>
					<cfset local.volumeID = insertVolume(publicationID=arguments.publicationID, volumeName=arguments.volumeName)>
				</cfif>
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.duplicate = true>
				<cfset local.returnStruct.success = false>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertVolume" access="public" output="true" returntype="numeric" hint="Insert a new volume">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryInsertVolume" datasource="#application.dsn.membercentral.dsn#">
			INSERT INTO dbo.pub_volumes (publicationID, dateCreated, volumeName, enteredByMemberID)
			VALUES (
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">, 
				getDate(),
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.volumeName#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
			);

			SELECT SCOPE_IDENTITY() AS volumeID;

			SET NOCOUNT OFF;
		</cfquery>
		
		<cfreturn local.qryInsertVolume.volumeID>
	</cffunction>

	<cffunction name="updateVolume" access="public" output="true" returntype="void" hint="Update a volume">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">
		<cfargument name="volumeName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryUpdateVolume" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.pub_volumes
			SET volumeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.volumeName#">
			WHERE (
				volumeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">
				AND publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			)
			OR ISNULL(parentPublicationVolumeID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">;
		</cfquery>
	</cffunction>

	<cffunction name="deleteVolume" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
			
		<cfquery name="local.qryDeleteVolume" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @volumeID int, @publicationID int;
				SET @volumeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

				BEGIN TRAN;
					DELETE it
					FROM dbo.pub_issueItems AS it 
					INNER JOIN dbo.pub_issues AS i ON i.issueID = it.issueID
					WHERE i.volumeID = @volumeID;

					DELETE ish
					FROM dbo.pub_issueStatusHistory as ish
					INNER JOIN dbo.pub_issues AS i ON i.issueID = ish.issueID
					WHERE i.volumeID = @volumeID;

					DELETE FROM dbo.pub_issues
					WHERE volumeID = @volumeID;

					DELETE FROM dbo.pub_volumes
					WHERE volumeID = @volumeID
					AND publicationID = @publicationID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getIssuesFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="filterMode" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"h.updateDate")>
		<cfset arrayAppend(local.arrCols,"s.statusName")>
		<cfif arguments.filterMode eq 'templateTest'>
			<cfset arrayAppend(local.arrCols,"ai.applicationInstanceName")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"v.volumeName")>
		<cfset arrayAppend(local.arrCols,"i.issueNumber")>
		<cfset arrayAppend(local.arrCols,"i.issueDate")>
		<cfset arrayAppend(local.arrCols,"i.issueTitle")>
		<cfset arrayAppend(local.arrCols,"i.dateCreated")>

		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderdir')#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryIssues" result="local.qryIssuesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpIssueSearch') IS NOT NULL 
					DROP TABLE ##tmpIssueSearch;
				IF OBJECT_ID('tempdb..##tmpIssues') IS NOT NULL
					DROP TABLE ##tmpIssues;
				CREATE TABLE ##tmpIssueSearch (issueID INT PRIMARY KEY);
				<cfif arguments.filterMode EQ 'pubIssuesList'>
					CREATE TABLE ##tmpIssues (issueID INT, issueType char(1), publicationID INT, volumeID INT, volumeName VARCHAR(20), applicationInstanceID int, applicationInstanceName VARCHAR(200), issueStatusID INT, 
					issueStatus VARCHAR(20), dateCreated DATETIME, publishedDate DATETIME, issueNumber INT, issueDate DATETIME, issueTitle VARCHAR(100), pdfEditionDocumentID INT, supportsEmailEditions int, supportsOnlineEditions int, supportsPDFEditions int, webHomeContentTemplateID int, row INT);
				<cfelse>
					CREATE TABLE ##tmpIssues (issueID INT, issueType char(1), publicationID INT, volumeID INT, volumeName VARCHAR(20), applicationInstanceName VARCHAR(200), issueStatusID INT, 
					issueStatus VARCHAR(20), dateCreated DATETIME, publishedDate DATETIME, issueNumber INT, issueDate DATETIME, issueTitle VARCHAR(100), pdfEditionDocumentID INT, row INT);

				</cfif>

				DECLARE @siteID int, @publicationID INT, @fPublishedFrom DATE, @fPublishedTo DATETIME, @fIssueDateFrom DATE, @fIssueDateTo DATETIME, 
					@issueStatusID INT, @totalCount INT;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">

				<cfif arguments.event.getValue('fPublication',0) gt 0>
					SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fPublication')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedFrom','') NEQ ''>
					SET @fPublishedFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fPublishedFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedTo','') NEQ ''>
					SET @fPublishedTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fPublishedTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fIssueDateFrom','') NEQ ''>
					SET @fIssueDateFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fIssueDateFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fIssueDateTo','') NEQ ''>
					SET @fIssueDateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fIssueDateTo')# 23:59:59.997">;
				</cfif>

				SELECT @issueStatusID = issueStatusID FROM dbo.pub_statuses WHERE statusName = 'Published';
				
				INSERT INTO ##tmpIssueSearch (issueID)
				SELECT DISTINCT i.issueID
				FROM dbo.pub_issues as i
				INNER JOIN dbo.pub_volumes as v on v.volumeID = i.volumeID
					<cfif arguments.event.getValue('fPublication',0) gt 0>
						AND v.publicationID = @publicationID
					</cfif>
				INNER JOIN dbo.pub_publications as p on p.publicationID = v.publicationID
				INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					AND srs.siteResourceStatusDesc = 'Active'
				LEFT JOIN dbo.pub_issueStatusHistory as h ON h.issueID = i.issueID 
					AND h.issueStatusID = @issueStatusID
				WHERE ai.siteID = @siteID
				<cfif ListLen(arguments.event.getValue('fPublications',''))>
					and p.publicationID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fPublications')#" list="true">)
				</cfif>
				<cfif arguments.event.getTrimValue('volumeID',0) GT 0>
					AND i.volumeID = <cfqueryparam value="#arguments.event.getTrimValue('volumeID')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedFrom','') NEQ '' and arguments.event.getTrimValue('fPublishedTo','') NEQ ''>
					AND h.updateDate BETWEEN @fPublishedFrom AND @fPublishedTo
				<cfelseif arguments.event.getTrimValue('fPublishedFrom','') NEQ ''>
					AND h.updateDate >= @fPublishedFrom
				<cfelseif arguments.event.getTrimValue('fPublishedTo','') NEQ ''>
					AND h.updateDate <= @fPublishedTo
				</cfif>
				<cfif arguments.event.getTrimValue('fIssueDateFrom','') NEQ '' and arguments.event.getTrimValue('fIssueDateTo','') NEQ ''>
					AND i.issueDate BETWEEN @fIssueDateFrom AND @fIssueDateTo
				<cfelseif arguments.event.getTrimValue('fIssueDateFrom','') NEQ ''>
					AND i.issueDate >= @fIssueDateFrom
				<cfelseif arguments.event.getTrimValue('fIssueDateTo','') NEQ ''>
					AND i.issueDate <= @fIssueDateTo
				</cfif>
				<cfif ListLen(arguments.event.getValue('fPublicationVolume',''))>
					AND i.volumeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fPublicationVolume')#" list="true">)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fPublicationStatus',''))>
					AND i.issueStatusID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fPublicationStatus')#" list="true">)
				</cfif>
				<cfif len(arguments.event.getTrimValue('searchKeyword',''))>
					AND i.issueTitle LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('searchKeyword')#%">
				</cfif>;
				<cfif arguments.filterMode EQ 'pubIssuesList'>
					INSERT INTO ##tmpIssues (issueID, issueType, publicationID, volumeID, volumeName, applicationInstanceID, applicationInstanceName, issueStatusID, issueStatus, dateCreated,
						publishedDate, issueNumber, issueDate, issueTitle, pdfEditionDocumentID, supportsEmailEditions, supportsOnlineEditions, supportsPDFEditions, webHomeContentTemplateID, row)
					SELECT i.issueID, i.issueType, p.publicationID, i.volumeID, v.volumeName, ai.applicationInstanceID, ai.applicationInstanceName, i.issueStatusID, s.statusName, i.dateCreated, 
						h.updateDate, i.issueNumber, i.issueDate, i.issueTitle, i.pdfEditionDocumentID, p.supportsEmailEditions, p.supportsOnlineEditions, p.supportsPDFEditions, pw_home.templateID as webHomeContentTemplateID, ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
					FROM dbo.pub_issues AS i
					INNER JOIN ##tmpIssueSearch AS tmp ON tmp.issueID = i.issueID
					INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID
					INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
					INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
					INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
					LEFT OUTER JOIN dbo.pub_issueStatusHistory AS h ON h.issueID = i.issueID AND h.issueStatusID = @issueStatusID
					left outer join dbo.template_usages as pw_home on pw_home.referenceID = p.publicationID and pw_home.referenceType = 'PubWebHomepage';
				<cfelse>
					INSERT INTO ##tmpIssues (issueID, issueType, publicationID, volumeID, volumeName, applicationInstanceName, issueStatusID, issueStatus, dateCreated,
						publishedDate, issueNumber, issueDate, issueTitle, pdfEditionDocumentID, row)
					SELECT i.issueID, i.issueType, p.publicationID, i.volumeID, v.volumeName, ai.applicationInstanceName, i.issueStatusID, s.statusName, i.dateCreated, 
						h.updateDate, i.issueNumber, i.issueDate, i.issueTitle, i.pdfEditionDocumentID, ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
					FROM dbo.pub_issues AS i
					INNER JOIN ##tmpIssueSearch AS tmp ON tmp.issueID = i.issueID
					INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID
					INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
					INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
					INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
					LEFT OUTER JOIN dbo.pub_issueStatusHistory AS h ON h.issueID = i.issueID AND h.issueStatusID = @issueStatusID;
				</cfif>	

				SELECT @totalCount = @@ROWCOUNT;

				<cfif listFindNoCase("pubIssues,pubVolIssues,templateTest",arguments.filterMode)>
					DECLARE @posStart INT, @posStartPlusCount INT;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					SELECT issueID, issueType, publicationID, volumeID, volumeName, applicationInstanceName, issueStatusID, issueStatus, dateCreated, 
						publishedDate, issueNumber, issueDate, issueTitle, ISNULL(pdfEditionDocumentID,0) AS pdfEditionDocumentID, @totalCount AS totalCount
					FROM ##tmpIssues
					WHERE row > @posStart
					AND row <= @posStartPlusCount
					ORDER BY row;
				<cfelseif arguments.filterMode EQ 'pubIssuesList'>
					DECLARE @posStart INT, @posStartPlusCount INT;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					SELECT issueID, issueType, publicationID, volumeID, volumeName, applicationInstanceID, applicationInstanceName, issueStatusID, issueStatus, dateCreated, 
						publishedDate, issueNumber, issueDate, issueTitle, ISNULL(pdfEditionDocumentID,0) AS pdfEditionDocumentID, supportsEmailEditions, supportsOnlineEditions, supportsPDFEditions, webHomeContentTemplateID, @totalCount AS totalCount
					FROM ##tmpIssues
					WHERE row > @posStart
					AND row <= @posStartPlusCount
					ORDER BY row;

				<cfelse>
					SELECT issueID
					FROM ##tmpIssues;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpIssues') IS NOT NULL
					DROP TABLE ##tmpIssues;
				IF OBJECT_ID('tempdb..##tmpIssueSearch') IS NOT NULL 
					DROP TABLE ##tmpIssueSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfreturn local.qryIssues>
	</cffunction>

	<cffunction name="getPublicationStatuses" access="public" output="false" returntype="query">
		<cfset var local.qryPublicationStatuses = "">

		<cfquery name="local.qryPublicationStatuses" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT issueStatusID, statusName
			FROM dbo.pub_statuses;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationStatuses>
	</cffunction>

	<cffunction name="getPublicationVolumes" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var qryPublicationVolumes = "">

		<cfquery name="qryPublicationVolumes" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT volumeID, volumeName
			FROM dbo.pub_volumes
			WHERE 1=1
			<cfif arguments.publicationID gt 0>
				AND publicationID = <cfqueryparam value="#arguments.publicationID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>	
			ORDER BY dateCreated DESC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPublicationVolumes>
	</cffunction>

	<cffunction name="getIssueDetails" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var qryIssueDetails = "">

		<cfquery name="qryIssueDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT i.issueID, i.volumeID, v.volumeName, i.issueStatusID, s.statusName, i.dateCreated, i.issueDate,
				i.autoPublishDate, i.bit_send_email, i.issueNumber, i.issueTitle, i.issueType, i.pdfEditionDocumentID, dv.fileName,
				ficu_i.featureImageConfigID as issueFeatureImageConfigID,
				(SELECT COUNT(issueItemID) 
					FROM dbo.pub_issueItems ii 
					WHERE ii.issueID = i.issueID
				) AS includedItemsCount,
				(SELECT COUNT(suggestedItemID) 
					FROM dbo.pub_suggestedItems tmp 
					WHERE tmp.publicationID = v.publicationID
				) AS suggestedItemsCount,
				ISNULL(p.categoryTreeID,0) AS categoryTreeID, ISNULL(i.htmlContentID,0) AS htmlContentID, 
				ISNULL(rd.redirectID,0) as redirectID, rd.redirectName,
				fiu_i.featureImageID as issueFeatureImageID, fi_i.fileExtension as issueFeatureImageFileExt
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
			LEFT OUTER JOIN dbo.siteRedirects rd ON rd.redirectID = i.redirectID
			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu_i ON ficu_i.referenceID = p.publicationID 
				AND ficu_i.referenceType = 'publicationIssue'
			LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu_i 
				INNER JOIN dbo.cms_featuredImages AS fi_i ON fi_i.featureImageID = fiu_i.featureImageID
				ON fiu_i.referenceID = i.issueID AND fiu_i.referenceType = 'publicationIssue'
			LEFT OUTER JOIN dbo.cms_documents as d 
				INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
				ON d.documentID = i.pdfEditionDocumentID
			WHERE i.issueID = <cfqueryparam value="#arguments.issueID#" cfsqltype="CF_SQL_INTEGER">
			AND p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryIssueDetails>
	</cffunction>

	<cffunction name="hasDuplicateIssue" access="public" output="true" returntype="boolean" hint="check if there is a duplicate issue">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="volumeID" type="numeric" required="true">		
		<cfargument name="issueNumber" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryHasDuplicateIssue" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @issueID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
			SELECT issueID
			FROM dbo.pub_issues
			WHERE volumeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">
			AND issueNumber = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueNumber#">
			AND (@issueID = 0 OR issueID <> @issueID);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryHasDuplicateIssue.recordCount GT 0>
			<cfreturn true>
		<cfelse>
			<cfreturn false>
		</cfif>
	</cffunction>

	<cffunction name="saveIssue" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="issueTitle" type="string" required="true">
		<cfargument name="volumeID" type="numeric" required="true">
		<cfargument name="issueDate" type="string" required="true">
		<cfargument name="autoPublishDate" type="string" required="true">
		<cfargument name="issueNumber" type="numeric" required="false">
		<cfargument name="issueType" type="string" required="false" default="P">
		<cfargument name="currentRedirectName" type="string" required="false" default="">
		<cfargument name="newRedirectName" type="string" required="false" default="">
		<cfargument name="redirectID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "issueid": arguments.issueID, "errmsg": "", "duplicate": true }>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
		
		<cftry>
			<cfset local.issueDate = trim(replace(arguments.issueDate,' - ',' '))>
			<cfif len(local.issueDate) gt 0>
				<cfset local.issueDate = ParseDateTime(local.issueDate)>
			</cfif>
			<cfset local.autoPublishDate = trim(replace(arguments.autoPublishDate,' - ',' '))>
			<cfif len(local.autoPublishDate) gt 0>
				<cfset local.autoPublishDate = ParseDateTime(local.autoPublishDate)>
			</cfif>
			<cfif arguments.issueID gt 0>
				<cfset local.hasDuplicate = hasDuplicateIssue(volumeID = arguments.volumeID, issueID=arguments.issueID, issueNumber=arguments.issueNumber)>
				<cfif NOT local.hasDuplicate>
					<cfset updateIssue(issueID=arguments.issueID,
						issueTitle=arguments.issueTitle, 
						volumeID=arguments.volumeID, 
						issueNumber=arguments.issueNumber,
						issueType=arguments.issueType,
						issueDate = local.issueDate,
						autoPublishDate = local.autoPublishDate)>
				<cfelse>
					<cfset local.returnStruct.duplicate = true>
					<cfset local.returnStruct.success = false>
				</cfif>
			<cfelse>
				<cfset local.publicationSiteResourceID = getPublicationInstanceSiteResourceID(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>
				<cfset local.qryPublication = getPublicationDetails(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>
				<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=local.publicationSiteResourceID, permission="CreateEditions") or val(local.qryPublication.parentPublicationID) gt 0>
					<cfset local.returnStruct.success = false>
					<cfset local.returnStruct.errmsg = "You do not have rights to perform this operation.">
					<cfreturn local.returnStruct>
				</cfif>

				<cfset local.returnStruct.issueid = insertIssue(siteID=arguments.mcproxy_siteID, 
					issueTitle=arguments.issueTitle,
					volumeID=arguments.volumeID, 
					issueNumber=arguments.issueNumber,
					issueType=arguments.issueType,
					issueDate = local.issueDate,
					autoPublishDate = local.autoPublishDate)>
			</cfif>

			<cfif arguments.currentRedirectName NEQ arguments.newRedirectName>
				<cfif len(arguments.newRedirectName)>
					<cfset local.qryPublicationDetail = getPublicationDetails(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>
					<cfset local.issueBaseLink = application.objApplications.getAppBaseLink(siteID=arguments.mcproxy_siteID, applicationInstanceID=local.qryPublicationDetail.applicationInstanceID)>
					<cfset local.redirectURL = "#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/?#local.issueBaseLink#&pubAction=viewIssue&pubIssueID=#local.returnStruct.issueid#">
					<cfset addRedirect(siteID=arguments.mcproxy_siteID, issueID=local.returnStruct.issueid, redirectName=arguments.newRedirectName,
						redirectURL=local.redirectURL, oldRedirectID=arguments.redirectID)>
				<cfelseif arguments.redirectID gt 0>
					<cfset CreateObject("component","model.admin.alias.alias").deleteAlias(redirectIDList=arguments.redirectID,siteID=arguments.mcproxy_siteID)>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertIssue" access="public" output="true" returntype="numeric" hint="Insert a new issue">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="issueTitle" type="string" required="true">
		<cfargument name="volumeID" type="numeric" required="true">
		<cfargument name="issueType" type="string" required="true">
		<cfargument name="issueDate" type="string" required="true">
		<cfargument name="autoPublishDate" type="string" required="true">
		
		<cfset var issueID = 0>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_createIssue">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.issueTitle#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.issueType#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.issueDate#">
			<cfif len(arguments.autoPublishDate)>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.autoPublishDate#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" null="true">
			</cfif>
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
			<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="issueID">
		</cfstoredproc>
		
		<cfreturn issueID>
	</cffunction>

	<cffunction name="updateIssue" access="public" output="true" returntype="void" hint="Update an issue">
		<cfargument name="issueID" type="numeric" required="true">	
		<cfargument name="issueTitle" type="string" required="true">
		<cfargument name="volumeID" type="numeric" required="true">	
		<cfargument name="issueType" type="string" required="true">	
		<cfargument name="issueNumber" type="numeric" required="true">
		<cfargument name="issueDate" type="string" required="true">
		<cfargument name="autoPublishDate" type="string" required="true">
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_updateIssue">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.volumeID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.issueTitle#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueNumber#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#arguments.issueDate#">
			<cfif len(arguments.autoPublishDate)>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.autoPublishDate#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" null="true">
			</cfif>
		</cfstoredproc>
	</cffunction>

	<cffunction name="addRedirect" access="public" output="false" returntype="void" hint="add Quick Link for publication issue">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="redirectName" type="string" required="true">
		<cfargument name="redirectURL" type="string" required="true">
		<cfargument name="oldRedirectID" type="numeric" required="true">

		<cfscript>
			var local = structNew();
			local.objAlias = CreateObject("component","model.admin.alias.alias");
			local.redirectID = local.objAlias.insertAlias(siteID=arguments.siteID, redirectName=arguments.redirectName, redirectURL=arguments.redirectURL);
			updateIssueRedirect(issueID=arguments.issueID, redirectID=local.redirectID);
			if(arguments.oldRedirectID){
				local.objAlias.deleteAlias(redirectIDList=arguments.oldRedirectID,siteID=arguments.siteID);
			}
		</cfscript>
	</cffunction>

	<cffunction name="updateIssueRedirect" access="public" output="false" returntype="void" hint="update publication issue quick link">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="redirectID" type="numeric" required="false">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			UPDATE dbo.pub_issues 
			SET redirectID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.redirectID#">
			WHERE issueID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.issueID#">
		</cfquery>
	</cffunction>

	<cffunction name="deleteIssue" access="public" output="false" returntype="struct">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset deleteIssuePDF(issueID=arguments.issueID)>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_deleteIssue">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getIssueItemsList" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryIssueItems" result="local.qryIssueItemsResult">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


			DECLARE @issueID INT, @totalCategories INT, @totalCount INT;
			SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('issueID',0)#">;

			IF OBJECT_ID('tempdb..##tmpIssueItems') IS NOT NULL
				DROP TABLE ##tmpIssueItems;
			CREATE TABLE ##tmpIssueItems (issueItemID INT, source VARCHAR(100), contentType VARCHAR(20), title VARCHAR(1000), blogStatusName varchar(50), blogEntryID INT, blogID INT, 
				siteResourceID INT, postDate DATETIME, enteredByMemberID INT, enteredByMemberName VARCHAR(100), categoryName VARCHAR(200), categoryID int, itemPosition INT, 
				issueItemFeatureImageConfigID INT, hasImage BIT, isCrossSiteArticle BIT, minParentSort INT, maxParentSort INT, row int);

			IF OBJECT_ID('tempdb..##tmpIssueCategories') IS NOT NULL
				DROP TABLE ##tmpIssueCategories;
			CREATE TABLE ##tmpIssueCategories (categoryID int, categoryPath varchar(500));

			INSERT INTO ##tmpIssueItems (issueItemID, source, contentType, title, blogStatusName, blogEntryID, blogID, siteResourceID, postDate, 
				enteredByMemberID, enteredByMemberName, categoryName, categoryID, itemPosition, issueItemFeatureImageConfigID, hasImage, isCrossSiteArticle, row)
			SELECT itm.issueItemID, CASE WHEN itm.sourceIssueItemID IS NOT NULL THEN sp_ai.applicationInstanceName ELSE b_ai.applicationInstanceName END AS source,
				pt.typeName, be.blogTitle, bs.statusName, be.blogEntryID, be.blogID, itm.itemSiteResourceID, be.postDate, itm.enteredByMemberID, 
				m.firstName + ' ' + m.lastName, c.categoryName, c.categoryID, itm.itemPosition, ficu.featureImageConfigID, 
				case when fiu.featureImageUsageID is not null then 1 else 0 end,
				CASE WHEN itm.sourceIssueItemID IS NOT NULL AND p_ai.siteID <> sp_ai.siteID THEN 1 ELSE 0 END AS isCrossSiteArticle,
				ROW_NUMBER() OVER (ORDER BY c.treeOrder, itm.itemPosition)
			FROM dbo.pub_issueItems AS itm
			INNER JOIN dbo.pub_issues AS i ON i.issueID = itm.issueID
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
			INNER JOIN dbo.cms_applicationInstances AS p_ai ON p_ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.bl_entry AS be ON be.siteResourceID = itm.itemSiteResourceID
			INNER JOIN dbo.bl_statuses bs on bs.statusID = be.statusID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.bl_blog AS b ON b.blogID = be.blogID
			INNER JOIN dbo.cms_applicationInstances AS b_ai ON b_ai.applicationInstanceID = b.applicationInstanceID
			INNER JOIN dbo.cms_postTypes AS pt ON pt.postTypeID = be.postTypeID
			INNER JOIN dbo.ams_members AS m ON m.memberid = itm.enteredByMemberID
			INNER JOIN dbo.cms_categories AS c ON c.categoryID = itm.categoryID
			LEFT OUTER JOIN dbo.pub_issues as si
				INNER JOIN dbo.pub_volumes as sv ON sv.volumeID = si.volumeID
				INNER JOIN dbo.pub_publications as sp ON sp.publicationID = sv.publicationID
				INNER JOIN dbo.cms_applicationInstances AS sp_ai ON sp_ai.applicationInstanceID = sp.applicationInstanceID
				ON si.issueID = itm.sourceIssueID
			LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu on fiu.referenceID = itm.issueItemID AND fiu.referenceType = 'publicationIssueItem'
			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu on ficu.referenceID = p.publicationID AND ficu.referenceType = 'publicationIssueItem'
			WHERE itm.issueID = @issueID;

			SELECT @totalCount = @@ROWCOUNT;

			INSERT INTO ##tmpIssueCategories (categoryID, categoryPath)
			SELECT DISTINCT c.categoryID, c.categoryPath
			FROM ##tmpIssueItems as tmp
			INNER JOIN dbo.cms_categories AS c ON c.categoryID = tmp.categoryID;

			SET @totalCategories = @@ROWCOUNT;

			update tmp
			set tmp.minParentSort = tmpAggr.minSort,
				tmp.maxParentSort = tmpAggr.maxSort
			from ##tmpIssueItems as tmp
			inner join (
				select categoryID, min(itemPosition) as minSort, max(itemPosition) as maxSort
				from ##tmpIssueItems
				group by categoryID
			) as tmpAggr on isnull(tmpAggr.categoryID,0) = isnull(tmp.categoryID,0);

			SELECT ii.issueItemID, ii.source, ii.contentType, ii.title, ii.blogStatusName, ii.blogEntryID, ii.blogID, ii.siteResourceID, ii.enteredByMemberID, ii.enteredByMemberName, 
				ii.categoryID, ii.categoryName, ic.categoryPath, ii.itemPosition, ii.issueItemFeatureImageConfigID, ii.hasImage, ii.isCrossSiteArticle,
				case when ii.itemPosition = ii.minParentSort then 1 else 0 end as listFirstItem,
				case when ii.itemPosition = ii.maxParentSort then 1 else 0 end as listLastItem, 
				@totalCategories as totalCategories, @totalCount AS totalCount
			FROM ##tmpIssueItems ii
			INNER JOIN ##tmpIssueCategories ic on ii.categoryID = ic.categoryID
			ORDER BY ii.row;

			IF OBJECT_ID('tempdb..##tmpIssueItems') IS NOT NULL
				DROP TABLE ##tmpIssueItems;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryIssueItems>
	</cffunction>

	<cffunction name="getSources" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qrySources = "">

		<cfquery name="qrySources" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ai.applicationInstanceID AS sourceID, ai.applicationInstanceName AS sourceName
			FROM dbo.cms_applicationInstances ai
			INNER JOIN cms_applicationTypes t ON ai.applicationTypeID = t.applicationTypeID AND t.applicationTypeName = 'Blog'
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySources>
	</cffunction>

	<cffunction name="getConnectedPublicationSources" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">

		<cfset var local.qryPublicationSources = "">

		<cfquery name="local.qryPublicationSources" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ai.applicationInstanceID AS sourceID, at.applicationTypeName AS sourceType, ai.applicationInstanceName AS sourceName
			FROM dbo.pub_publicationSources AS ps 
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = ps.publicationID
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = ps.sourceApplicationInstanceID
			INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			WHERE ai.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND p.publicationID = <cfqueryparam value="#arguments.publicationID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY at.applicationTypeName, ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationSources>
	</cffunction>

	<cffunction name="getPostTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local.qryPostTypes = "">

		<cfquery name="local.qryPostTypes" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT postTypeID, typeName
			FROM dbo.cms_postTypes 
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPostTypes>
	</cffunction>

	<cffunction name="getSuggestedItemsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"e.postDate")>
		<cfset arrayAppend(local.arrCols,"ai.applicationInstanceName")>
		<cfset arrayAppend(local.arrCols,"pt.typeName")>
		<cfset arrayAppend(local.arrCols,"e.blogTitle")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderdir')#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySuggestedItems" result="local.qrySuggestedItemsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpsuggestedItemsSearch') IS NOT NULL 
					DROP TABLE ##tmpsuggestedItemsSearch;
				CREATE TABLE ##tmpsuggestedItemsSearch (suggestedItemID INT PRIMARY KEY);

				DECLARE @publicationID INT, @issueID INT, @fPublishedFrom DATE, @fPublishedTo DATE;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pID',0)#">;
				SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('issueID',0)#">;

				<cfif arguments.event.getTrimValue('fSuggestedItemPublishedFrom','') NEQ ''>
					SET @fPublishedFrom = <cfqueryparam value="#arguments.event.getTrimValue('fSuggestedItemPublishedFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fSuggestedItemPublishedTo','') NEQ ''>
					SET @fPublishedTo = <cfqueryparam value="#arguments.event.getTrimValue('fSuggestedItemPublishedTo')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				
				INSERT INTO ##tmpsuggestedItemsSearch (suggestedItemID)
				SELECT DISTINCT suggestedItemID
				FROM dbo.pub_suggestedItems AS s
				INNER JOIN dbo.cms_siteResources AS sr	ON sr.siteResourceID = s.itemsiteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc ='Active'
				INNER JOIN dbo.bl_entry AS e ON e.siteResourceID = s.itemSiteResourceID
				INNER JOIN dbo.bl_blog AS b ON b.blogID = e.blogID				
				LEFT JOIN dbo.pub_issueItems AS ii ON ii.itemSiteResourceID = e.siteResourceID AND ii.issueID = @issueID
				WHERE s.publicationID = @publicationID
				AND ii.itemSiteResourceID IS NULL
				<cfif arguments.event.getTrimValue('fSuggestedItemPublishedFrom','') NEQ ''>
					AND isnull(e.postDate,e.dateCreated) >= @fPublishedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fSuggestedItemPublishedTo','') NEQ ''>
					AND isnull(e.postDate,e.dateCreated) <= dateadd(day,1,@fPublishedTo)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fSuggestedItemSource',''))>
					AND b.applicationInstanceID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fSuggestedItemSource')#" list="true">)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fSuggestedItemPostType',''))>
					AND e.postTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fSuggestedItemPostType')#" list="true">)
				</cfif>;

				DECLARE @posStart INT, @posStartPlusCount INT, @totalCount INT;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpsuggestedItems') IS NOT NULL
					DROP TABLE ##tmpsuggestedItems;
				CREATE TABLE ##tmpsuggestedItems (suggestedItemID INT, publicationID INT, blogEntryID INT, source VARCHAR(100), postType VARCHAR(20), title VARCHAR(1000), blogStatusName varchar(50), publishDate DATETIME, enteredByMemberID INT, enteredByMemberName VARCHAR(100), siteResourceID INT, row INT);

				INSERT INTO ##tmpsuggestedItems (suggestedItemID, publicationID, blogEntryID, source, postType, title, blogStatusName, publishDate, enteredByMemberID, enteredByMemberName, siteResourceID, row)
				SELECT s.suggestedItemID, s.publicationID, e.blogEntryID, ai.applicationInstanceName, pt.typeName, e.blogTitle, bs.statusName, e.postDate, s.enteredByMemberID, m.firstName + ' ' + m.lastName, s.itemsiteResourceID,
						ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
				FROM dbo.pub_suggestedItems s
				INNER JOIN ##tmpsuggestedItemsSearch tmp ON tmp.suggestedItemID = s.suggestedItemID
				INNER JOIN cms_siteResources sr	ON sr.siteResourceID = s.itemsiteResourceID
				INNER JOIN cms_siteResourceStatuses srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc ='Active'
				INNER JOIN bl_entry e ON e.siteResourceID = s.itemSiteResourceID				
				INNER JOIN dbo.bl_statuses bs on bs.statusID = e.statusID
				INNER JOIN bl_blog b ON b.blogID = e.blogID
				INNER JOIN cms_postTypes pt ON pt.postTypeID = e.postTypeID
				INNER JOIN cms_applicationInstances ai ON ai.applicationInstanceID = b.applicationInstanceID
				INNER JOIN dbo.ams_members AS m ON m.memberid = s.enteredByMemberID				

				SELECT @totalCount = @@ROWCOUNT;

				SELECT suggestedItemID, publicationID, blogEntryID, source, postType, title, blogStatusName, publishDate, enteredByMemberID, enteredByMemberName, siteResourceID, @totalCount AS totalCount
				FROM ##tmpsuggestedItems
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpsuggestedItems') IS NOT NULL
					DROP TABLE ##tmpsuggestedItems;

				IF OBJECT_ID('tempdb..##tmpsuggestedItemsSearch') IS NOT NULL 
					DROP TABLE ##tmpsuggestedItemsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	
		<cfreturn local.qrySuggestedItems>
	</cffunction>

	<cffunction name="getConnectedSourcesItemsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.orderDir = arguments.event.getValue('orderDir')>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"e.postDate #local.orderDir#")>
		<cfset arrayAppend(local.arrCols,"e.blogTitle #local.orderDir#, pt.typeName, tmp.sourcesList")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryConnectedSourcesItems">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpConnectedSourcesItemsSearch') IS NOT NULL 
					DROP TABLE ##tmpConnectedSourcesItemsSearch;
				CREATE TABLE ##tmpConnectedSourcesItemsSearch (sourceType VARCHAR(20), blogEntryID INT, sourcesList VARCHAR(1000), publishDate DATETIME, sourceIssueItemID INT);

				DECLARE @publicationID INT, @issueID INT, @fPublishedFrom DATE, @fPublishedTo DATE, @fBlogTitle varchar(1000);
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pID',0)#">;
				SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('issueID',0)#">;

				<cfif arguments.event.getTrimValue('fConnectedSourcesItemTitle','') NEQ ''>
					SET @fBlogTitle = replace(<cfqueryparam value="#arguments.event.getTrimValue('fConnectedSourcesItemTitle')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedFrom','') NEQ ''>
					SET @fPublishedFrom = <cfqueryparam value="#arguments.event.getTrimValue('fConnectedSourcesItemPublishedFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedTo','') NEQ ''>
					SET @fPublishedTo = <cfqueryparam value="#arguments.event.getTrimValue('fConnectedSourcesItemPublishedTo')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				
				<!--- source: blog --->
				INSERT INTO ##tmpConnectedSourcesItemsSearch (sourceType, blogEntryID, sourcesList, publishDate)
				SELECT DISTINCT 'blog', e.blogEntryID,
					sourcesList = STUFF((
							SELECT '/' + ai.applicationInstanceName
							FROM dbo.pub_publicationSources ps2
							INNER JOIN dbo.bl_blog b2 ON b2.applicationInstanceID = ps2.sourceApplicationInstanceID
							INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = b2.applicationInstanceID
							INNER JOIN dbo.bl_blogsAndEntries AS bae2 ON bae2.blogID = b2.blogID							
							WHERE ps2.publicationID = @publicationID AND bae2.blogEntryID = e.blogEntryID
							ORDER BY ai.applicationInstanceName
							FOR XML PATH ('')
					),1,1,''),
					e.postDate
				FROM dbo.pub_publicationSources ps
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = ps.sourceApplicationInstanceID
				INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID
					AND at.applicationTypeName = 'blog'
				INNER JOIN dbo.bl_blog b ON b.applicationInstanceID = ps.sourceApplicationInstanceID
				INNER JOIN dbo.bl_blogsAndEntries AS bae ON bae.blogID = b.blogID
				INNER JOIN dbo.bl_entry e ON e.blogEntryID = bae.blogEntryID
				INNER JOIN dbo.cms_siteResources sr	ON sr.siteResourceID = e.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc ='Active'
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemCategory',''))>
					INNER JOIN dbo.cms_categorySiteResources csr ON csr.siteResourceID = e.siteResourceID
						AND csr.categoryID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemCategory')#" list="true">)
				</cfif>
				WHERE ps.publicationID = @publicationID
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemTitle','') NEQ ''>
					AND e.blogTitle like '%'+@fBlogTitle+'%' ESCAPE('\')
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedFrom','') NEQ ''>
					AND isnull(e.postDate,e.dateCreated) >= @fPublishedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedTo','') NEQ ''>
					AND isnull(e.postDate,e.dateCreated) <= dateadd(day,1,@fPublishedTo)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemSource',''))>
					AND b.applicationInstanceID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemSource')#" list="true">)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemPostType',''))>
					AND e.postTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemPostType')#" list="true">)
				</cfif>;

				<!--- source: publication --->
				INSERT INTO ##tmpConnectedSourcesItemsSearch (sourceType, blogEntryID, sourcesList, publishDate, sourceIssueItemID)
				SELECT DISTINCT 'publications', e.blogEntryID, ai.applicationInstanceName, i.issueDate, ii.issueItemID
				FROM dbo.pub_publicationSources ps
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = ps.sourceApplicationInstanceID
				INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID
					AND at.applicationTypeName = 'publications'
				INNER JOIN dbo.pub_publications AS p ON p.applicationInstanceID = ps.sourceApplicationInstanceID
				INNER JOIN dbo.pub_volumes AS v ON v.publicationID = p.publicationID
				INNER JOIN dbo.pub_issues AS i ON i.volumeID = v.volumeID
				INNER JOIN dbo.pub_statuses AS istat ON istat.issueStatusID = i.issueStatusID
					AND istat.statusName = 'Published'
				INNER JOIN dbo.pub_issueItems AS ii ON ii.issueID = i.issueID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ii.itemSiteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
					AND srs.siteResourceStatusDesc = 'Active'
				INNER JOIN dbo.bl_entry AS e ON e.siteResourceID = ii.itemSiteResourceID
				WHERE ps.publicationID = @publicationID
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemCategory',''))>
					AND ii.categoryID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemCategory')#" list="true">)
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemTitle','') NEQ ''>
					AND e.blogTitle like '%'+@fBlogTitle+'%' ESCAPE('\')
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedFrom','') NEQ ''>
					AND i.issueDate >= @fPublishedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fConnectedSourcesItemPublishedTo','') NEQ ''>
					AND i.issueDate <= dateadd(day,1,@fPublishedTo)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemSource',''))>
					AND p.applicationInstanceID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemSource')#" list="true">)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fConnectedSourcesItemPostType',''))>
					AND e.postTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fConnectedSourcesItemPostType')#" list="true">)
				</cfif>;

				DECLARE @posStart INT, @posStartPlusCount INT, @totalCount INT;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartPlusCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpConnectedSourcesItems') IS NOT NULL
					DROP TABLE ##tmpConnectedSourcesItems;
				CREATE TABLE ##tmpConnectedSourcesItems (blogEntryID INT, publicationID INT, sourceType VARCHAR(20), source VARCHAR(1000), postType VARCHAR(20), title VARCHAR(1000),
					blogStatusName varchar(50), publishDate DATETIME, siteResourceID INT, sourceIssueItemID INT, isAlreadyAdded BIT, row INT);

				INSERT INTO ##tmpConnectedSourcesItems (blogEntryID, publicationID, sourceType, source, postType, title, blogStatusName, publishDate, siteResourceID, sourceIssueItemID, isAlreadyAdded, row)
				SELECT e.blogEntryID, @publicationID AS publicationID, tmp.sourceType, tmp.sourcesList, pt.typeName, e.blogTitle, bs.statusName, tmp.publishDate, e.siteResourceID, ISNULL(tmp.sourceIssueItemID,0),
						<cfif listFindNoCase("issueContentTable,schedItemsTable", arguments.mode)>
							CASE 
							<cfif arguments.mode EQ 'issueContentTable'>
								WHEN EXISTS (SELECT 1 FROM dbo.pub_issueItems WHERE itemSiteResourceID = e.siteResourceID AND issueID = @issueID) THEN 1 
							<cfelseif arguments.mode EQ 'schedItemsTable'>
								WHEN EXISTS (SELECT 1 FROM dbo.pub_issueItemsScheduled WHERE itemSiteResourceID = e.siteResourceID AND publicationID = @publicationID) THEN 1 
							</cfif>
							ELSE 0 END,
						<cfelse>
							0,
						</cfif>
						ROW_NUMBER() OVER (ORDER BY #local.orderby#) AS row
				FROM dbo.bl_entry e
				INNER JOIN ##tmpConnectedSourcesItemsSearch tmp ON tmp.blogEntryID = e.blogEntryID
				INNER JOIN dbo.bl_statuses bs on bs.statusID = e.statusID
				INNER JOIN dbo.bl_blog b ON b.blogID = e.blogID
				INNER JOIN dbo.cms_postTypes pt ON pt.postTypeID = e.postTypeID;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT blogEntryID, publicationID, sourceType, source, postType, title, blogStatusName, publishDate, siteResourceID, sourceIssueItemID, isAlreadyAdded, @totalCount AS totalCount
				FROM ##tmpConnectedSourcesItems
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpConnectedSourcesItems') IS NOT NULL
					DROP TABLE ##tmpConnectedSourcesItems;

				IF OBJECT_ID('tempdb..##tmpConnectedSourcesItemsSearch') IS NOT NULL 
					DROP TABLE ##tmpConnectedSourcesItemsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	
		<cfreturn local.qryConnectedSourcesItems>
	</cffunction>

	<cffunction name="addItemToEdition" access="public" output="false" returntype="struct">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="suggestedItemID" type="numeric" required="true">
		<cfargument name="sourceIssueItemID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="skipFeaturedImage" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cfstoredproc procedure="pub_addIssueItem" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.suggestedItemID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceIssueItemID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.skipFeaturedImage#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.data.issueItemID">
		</cfstoredproc>

		<cfquery name="local.qryIssueItem" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT be.blogTitle, c.categoryName
			FROM dbo.pub_issueItems AS itm
			INNER JOIN dbo.bl_entry AS be ON be.siteResourceID = itm.itemSiteResourceID
			INNER JOIN dbo.cms_categories c ON c.categoryID = itm.categoryID
			WHERE itm.issueItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.data.issueItemID#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.title = local.qryIssueItem.blogTitle>
		<cfset local.data.categoryname = local.qryIssueItem.categoryName>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateIssueItemCategory" access="public" output="true" returntype="struct">
		<cfargument name="issueItemID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfstoredproc procedure="pub_updateIssueItemCategory" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
		</cfstoredproc>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="removeIssueItem" access="public" output="false" returntype="struct">
		<cfargument name="issueItemID" type="numeric" required="true">		

		<cfset var local = structNew()>
		
		<cfquery name="local.qryRemoveIssueItem" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @issueItemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">;

				BEGIN TRAN;
					DELETE FROM dbo.pub_issueItems
					WHERE issueItemID = @issueItemID;

					EXEC dbo.cms_deleteFeaturedImageUsage @referenceID=@issueItemID, @referenceType='publicationIssueItem';
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSuggestedItem" access="public" output="false" returntype="struct">
		<cfargument name="suggestedItemID" type="numeric" required="true">		

		<cfset var local = structNew()>
		<cfset local.data = structNew()>		
		
		<cfquery name="local.qryRemoveSuggestedItem" datasource="#application.dsn.membercentral.dsn#">
			DELETE FROM dbo.pub_suggestedItems
			WHERE suggestedItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.suggestedItemID#">;
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPublicationsToSuggest" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local.qryPublicationsToSuggest = "">

		<cfquery name="local.qryPublicationsToSuggest" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT DISTINCT p.publicationID, ai.applicationInstanceName AS publicationName
			FROM dbo.pub_publications as p
			INNER JOIN dbo.cms_applicationInstances as ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
				AND sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			LEFT JOIN dbo.pub_suggestedItems s ON s.publicationID = p.publicationID AND s.itemSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			WHERE p.publicationID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			AND s.suggestedItemID IS NULL
			ORDER BY ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationsToSuggest>
	</cffunction>

	<cffunction name="getConnectedSourcesList" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfset var qryConnectedSourcesList = "">
		<cfquery name="qryConnectedSourcesList" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@publicationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			select ai.applicationInstanceName, bl.blogID, sr.siteResourceID
			from dbo.pub_publicationSources as ps 
			inner join dbo.pub_publications as p on p.publicationID = ps.publicationID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = ps.sourceApplicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.bl_blog as bl on bl.applicationInstanceID = ps.sourceApplicationInstanceID
			where ai.siteID = @siteID
			and p.publicationID = @publicationID
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfreturn qryConnectedSourcesList>
	</cffunction>

	<cffunction name="addSuggestedItems" access="public" output="false" returntype="struct">
		<cfargument name="publicationIDList" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">		

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cfif len(arguments.publicationIDList)>
			<cfquery name="local.qryAddSuggestedItems" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @siteResourceID INT, @enteredByMemberID INT;
				SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				INSERT INTO dbo.pub_suggestedItems (publicationID, itemSiteResourceID, enteredByMemberID)
				select listitem, @siteResourceID, @enteredByMemberID
				from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publicationIDList#">,',');
			</cfquery>

			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPublicationContentCategories" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local.qryPublicationContentCategories = "">

		<cfquery name="local.qryPublicationContentCategories" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT c.categoryID, c.categoryName, c.categoryPath
			FROM dbo.pub_publications p
			INNER JOIN dbo.cms_categories c ON c.categoryTreeID = p.categoryTreeID
			WHERE p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			AND c.isActive = 1
			ORDER BY c.categoryPath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationContentCategories>
	</cffunction>

	<cffunction name="getDefaultCategoryForItemSelection" access="public" output="false" returntype="numeric">
		<cfargument name="itemSiteResourceID" type="numeric" required="true">
		<cfargument name="categoryTreeID" type="numeric" required="true">
		<cfargument name="sourceIssueItemID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.defaultCategoryID = 0>
		
		<cfquery name="local.qryArticleCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT c.categoryID, c.categoryName
			FROM dbo.cms_categorySiteResources AS csr
			INNER JOIN dbo.cms_categories c ON c.categoryID = csr.categoryID
			WHERE csr.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemSiteResourceID#">
			AND c.categoryTreeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryTreeID#">
			ORDER BY c.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryArticleCategories.recordCount eq 1>
			<cfset local.defaultCategoryID = local.qryArticleCategories.categoryID>
		<cfelseif arguments.sourceIssueItemID gt 0>
			<cfquery name="local.qrySourceItemCategory" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT categoryID
				FROM dbo.pub_issueItems
				WHERE issueItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceIssueItemID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.defaultCategoryID = local.qrySourceItemCategory.categoryID>
		</cfif>
		
		<cfreturn local.defaultCategoryID>
	</cffunction>

	<cffunction name="getValidDefaultCategoryForItemSelection" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="itemSiteResourceID" type="numeric" required="true">
		<cfargument name="categoryTreeID" type="numeric" required="true">
		<cfargument name="sourceIssueItemID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.result = { "success" : false, categoryid: 0 }>

		<cfset local.selectedItemCategoryID = getDefaultCategoryForItemSelection(itemSiteResourceID=arguments.itemSiteResourceID, categoryTreeID=arguments.categoryTreeID, sourceIssueItemID=arguments.sourceIssueItemID)>
		<cfset local.qryPublicationContentCategories = getPublicationContentCategories(publicationID=arguments.publicationID)>

		<cfquery name="local.qryCategory" dbtype="query">
			select categoryID
			from [local].qryPublicationContentCategories
			where categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.selectedItemCategoryID#">
		</cfquery>

		<cfif local.qryCategory.recordCount>
			<cfset local.result["success"] = true>
			<cfset local.result["categoryid"] = local.selectedItemCategoryID>
		</cfif>
		
		<cfreturn local.result>
	</cffunction>

	<cffunction name="getSitePublications" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryPublications = "">

		<cfquery name="qryPublications" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select p.publicationID, ai.applicationInstanceName
			from dbo.pub_publications as p
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPublications>
	</cffunction>

	<cffunction name="getPublicationVolumesForFilter" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryPublicationVolumes = getPublicationVolumes(publicationID=arguments.publicationID)>
		
		<cfset local.data = structNew()>
		<cfset local.data['arrvolumes'] = arrayNew(1)>

		<cfloop query="local.qryPublicationVolumes">
			<cfset local.tmpStr = structNew()>
			<cfset local.tmpStr['volumeid'] = local.qryPublicationVolumes.volumeID>
			<cfset local.tmpStr['volumename'] = local.qryPublicationVolumes.volumeName>
			<cfset arrayAppend(local.data['arrvolumes'],local.tmpStr)>
		</cfloop>

		<cfset local.data.success = true>

		<cfreturn local.data> 
	</cffunction>

	<cffunction name="getIssueItemsInfo" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.JSONInsanityChars = "^~~~^">
		
		<!--- publication info --->
		<cfquery name="local.qryPublicationInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.orgID, o.orgcode, s.sitecode, oi.organizationName as orgName, oi.organizationShortName as orgShortName, oi.website as orgURL, s.sitename, ai.applicationInstanceName, ai.applicationInstanceDesc, p.publicationID,
				p.applicationInstanceID, p.emailEditionReplyToAddress, p.emailEditionSenderName, p.supportsPDFEditions, p.supportsOnlineEditions, p.supportsEmailEditions,
				v.volumeID, v.volumeName, i.issueID, i.issueNumber, i.issueTitle, i.issueDate, i.autoPublishDate, i.pdfEditionDocumentID, p.orgIdentityID
			from dbo.pub_publications as p
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
				and p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.pub_volumes as v on v.publicationID = p.publicationID
			inner join dbo.pub_issues as i on i.volumeID = v.volumeID
				and i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
			inner join dbo.sites as s on s.siteID = ai.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			where s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- articles info --->
		<cfquery name="local.qryArticlesInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ii.issueItemID, ii.itemPosition, c.categoryID, c.categoryCode, c.categoryName, c.categoryPath, parentc.categoryID as parentCategoryID, 
				parentc.categoryCode as parentCategoryCode, parentc.categoryName as parentCategoryName, e.blogEntryID, e.blogTitle, e.postDate, 
				e.summaryContentID, e.blogContentID, c.treeOrder, c.sortOrder, e.siteResourceID, pt.typeName, pt.postTypeID, e.blogID, pt.siteID as postTypeSiteID
			from dbo.pub_publications as p
			inner join dbo.pub_volumes as v on p.publicationID = v.publicationID
				and p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			inner join dbo.pub_issues as i on i.volumeID = v.volumeID
				and i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
			inner join dbo.pub_issueItems as ii on ii.issueID = i.issueID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ii.itemSiteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.cms_categories as c on c.categoryID = ii.categoryID	
			left outer join dbo.cms_categories as parentc on parentc.categoryID = c.parentCategoryID
			inner join dbo.bl_entry as e on e.siteResourceID = ii.itemSiteResourceID
			inner join dbo.cms_postTypes pt on pt.postTypeID = e.postTypeID
			order by c.treeOrder, ii.itemPosition;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var qryArticleAuthors = "">
		<cfset var PublicationAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PublicationAdmin',siteID=arguments.siteID)>

		<cfquery name="qryArticleAuthors" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @PublicationAdminSRID int, @FSID int, @FSArea varchar(20) ,@siteID int, @orgID int, @outputFieldsXML xml;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
			SET @PublicationAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#PublicationAdminSRID#">;
			SET @FSArea = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="pubAuthorInfo">;
			
			SELECT TOP 1 @FSID = fieldsetID
			FROM dbo.ams_memberFieldUsage
			WHERE siteResourceID = @PublicationAdminSRID
			AND area = @FSArea;

			IF OBJECT_ID('tempdb..##tmpPrepArticleAuthors') IS NOT NULL
				DROP TABLE ##tmpPrepArticleAuthors;
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;

			CREATE TABLE ##tmpPrepArticleAuthors (blogEntryID int, firstName varchar(75), lastName varchar(75), memberNumber varchar(50),company varchar(200), memberID int, orgcode varchar(10),hasMemberPhoto bit,hasMemberPhotoThumb bit);
			CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);

			INSERT INTO ##tmpPrepArticleAuthors(blogEntryID, firstName, lastName, memberNumber, company, memberID, orgcode, hasMemberPhoto , hasMemberPhotoThumb  ) 
			select be.blogEntryID, mActive.firstName as firstname, mActive.lastName as lastname, mActive.memberNumber as membernumber, mActive.company, mActive.memberID, org.orgcode, mActive.hasMemberPhoto, mActive.hasMemberPhotoThumb
			from dbo.bl_authors as ba
			inner join dbo.bl_entry as be on be.blogEntryID = ba.blogEntryID
			inner join dbo.ams_members as m on m.memberID = ba.memberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			inner join dbo.organizations as org on org.orgID = mActive.orgID
			where be.blogEntryID in (<cfqueryparam value="0#valueList(local.qryArticlesInfo.blogEntryID)#" list="true" cfsqltype="CF_SQL_INTEGER">);

			INSERT INTO ##tmp_membersForFS (memberID)
			select distinct memberID
			from ##tmpPrepArticleAuthors;

			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@FSID, @existingFields='',
				@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
				@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

			select tmp.blogEntryID, tmp.firstname, tmp.lastname,tmp. membernumber, tmp.company, tmp.orgcode, tmp.hasMemberPhoto, tmp.hasMemberPhotoThumb, m.*
			from ##tmpPrepArticleAuthors as tmp
			inner join ##tmpMembers as m ON m.memberID = tmp.memberID

			IF OBJECT_ID('tempdb..##tmpPrepArticleAuthors') IS NOT NULL
					DROP TABLE ##tmpPrepArticleAuthors;
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var qryArticleSourceLinks = "">
		<cfquery name="qryArticleSourceLinks" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT sl.blogEntryID, CASE WHEN s.approved = 'Y' THEN sl.sourceLinkURL ELSE s.homepageURL END as sourceLinkURL,
				sl.articleDate, sl.byline, s.company, s.description, s.isFree, s.approved, s.city, s.state, s.homepageURL, sl.orderNum
			FROM bl_sourceLinks sl
			INNER JOIN bl_sources s ON s.sourceID = sl.sourceID 
			WHERE sl.blogEntryID in (<cfqueryparam value="0#valueList(local.qryArticlesInfo.blogEntryID)#" list="true" cfsqltype="CF_SQL_INTEGER">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var qryArticleDocuments = "">
		<cfquery name="qryArticleDocuments" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ed.blogEntryID, dv.fileName AS filename, '/docDownload/' + cast(ed.documentID AS VARCHAR(10)) AS url
			FROM dbo.bl_entryDocuments AS ed
			INNER JOIN dbo.cms_documents AS d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages AS dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions AS dv ON dl.documentLanguageID = dv.documentLanguageID AND dv.isActive = 1
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			WHERE ed.blogEntryID IN (<cfqueryparam value="0#valueList(local.qryArticlesInfo.blogEntryID)#" list="true" cfsqltype="CF_SQL_INTEGER">)
			ORDER BY dv.fileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var qryIssueFeaturedImages = "">
		<cfquery name="qryIssueFeaturedImages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fiu.referenceID, fiu.referenceType, lower(fics.featuredImageSizeCode) as featuredImageSizeCode, fics.width, fics.height,
				cast(fi.featureImageID as varchar(10)) + '-' + cast(fics.featureImageSizeID as varchar(10)) + '.' + fics.fileExtension as filename,
				o.orgCode, s.siteCode
			from dbo.cms_featuredImageUsages as fiu
			inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				and fiu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
				and fiu.referenceType = 'publicationIssue'
			inner join dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
			inner join dbo.cms_featuredImageConfigSizes as fics on fics.featureImageConfigID = fic.featureImageConfigID
			inner join dbo.sites as s on s.siteID = fi.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
				union
			select fiu.referenceID, fiu.referenceType, lower(fics.featuredImageSizeCode) as featuredImageSizeCode, fics.width, fics.height,
				cast(fi.featureImageID as varchar(10)) + '-' + cast(fics.featureImageSizeID as varchar(10)) + '.' + fics.fileExtension as filename,
				o.orgCode, s.siteCode
			from dbo.cms_featuredImageUsages as fiu
			inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				and fiu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
				and fiu.referenceType = 'publicationIssuePDF'
			inner join dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
			inner join dbo.cms_featuredImageConfigSizes as fics on fics.featureImageConfigID = fic.featureImageConfigID
			inner join dbo.sites as s on s.siteID = fi.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
				union
			select fiu.referenceID, fiu.referenceType, lower(fics.featuredImageSizeCode) as featuredImageSizeCode, fics.width, fics.height,
				cast(fi.featureImageID as varchar(10)) + '-' + cast(fics.featureImageSizeID as varchar(10)) + '.' + fics.fileExtension as filename,
				o.orgCode, s.siteCode
			from dbo.cms_featuredImageUsages as fiu
			inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				and fiu.referenceID in (<cfqueryparam value="0#valueList(local.qryArticlesInfo.issueItemID)#" list="true" cfsqltype="CF_SQL_INTEGER">)
				and fiu.referenceType = 'publicationIssueItem'
			inner join dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
			inner join dbo.cms_featuredImageConfigSizes as fics on fics.featureImageConfigID = fic.featureImageConfigID
			inner join dbo.sites as s on s.siteID = fi.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
				union
			select fiu.referenceID, fiu.referenceType, lower(fics.featuredImageSizeCode) as featuredImageSizeCode, fics.width, fics.height,
				cast(fi.featureImageID as varchar(10)) + '-' + cast(fics.featureImageSizeID as varchar(10)) + '.' + fics.fileExtension as filename,
				o.orgCode, s.siteCode
			from dbo.cms_featuredImageUsages as fiu
			inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				and fiu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
				and fiu.referenceType = 'DefaultPublicationIssue'
			inner join dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
			inner join dbo.cms_featuredImageConfigSizes as fics on fics.featureImageConfigID = fic.featureImageConfigID
			inner join dbo.sites as s on s.siteID = fi.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strTemplateCustomFields = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate").getTemplateFieldData(orgCode=local.qryPublicationInfo.orgCode, siteCode=local.qryPublicationInfo.siteCode, itemID=arguments.publicationID, itemType='PubTemplate', memberID=arguments.memberID)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(local.qryPublicationInfo.sitecode).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(local.qryPublicationInfo.sitecode).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<cfset local.strArticles = structNew()>
		<cfif local.qryPublicationInfo.recordCount>
			<cfset local.strArticles['orgcode'] = local.qryPublicationInfo.orgcode>
			<cfset local.strArticles['sitecode'] = local.qryPublicationInfo.sitecode>
			<cfset local.strArticles['orgname'] = local.qryPublicationInfo.orgName>
			<cfset local.strArticles['orgshortname'] = local.qryPublicationInfo.orgshortname>
			<cfset local.strArticles['orgurl'] = local.qryPublicationInfo.orgURL>
			<cfset local.strArticles['sitename'] = local.qryPublicationInfo.sitename>
			<cfset local.strArticles['publicationname'] = local.qryPublicationInfo.applicationInstanceName>
			<cfset local.strArticles['publicationid'] = local.qryPublicationInfo.publicationID>
			<cfset local.strArticles['publicationdescription'] = local.qryPublicationInfo.applicationInstanceDesc>
			<cfset local.strArticles['emaileditionreplytoaddress'] = local.qryPublicationInfo.emailEditionReplyToAddress>
			<cfset local.strArticles['emaileditionsendername'] = local.qryPublicationInfo.emailEditionSenderName>
			<cfset local.strArticles['volumeid'] = local.qryPublicationInfo.volumeID>
			<cfset local.strArticles['volumename'] = local.qryPublicationInfo.volumeName>
			<cfset local.strArticles['issueid'] = local.qryPublicationInfo.issueID>
			<cfset local.strArticles['issuenumber'] = local.qryPublicationInfo.issueNumber>
			<cfset local.strArticles['issuetitle'] = local.qryPublicationInfo.issueTitle>
			<cfset local.strArticles['issuedate'] = local.qryPublicationInfo.issueDate>
			<cfset local.strArticles['autopublishdate'] = local.qryPublicationInfo.autoPublishDate>
			<cfset local.strArticles['totalitemcount'] = local.qryArticlesInfo.recordCount>
			<cfset local.strArticles['orgidentityinfo'] = structNew()>

			<cfif local.qryPublicationInfo.orgIdentityID gt 0>
				<cfset local.strArticles['orgidentityinfo'] = CreateObject("component","model.admin.organization.organization").getorgIdentityDetailsStruct(orgID=local.qryPublicationInfo.orgID, orgIdentityID=local.qryPublicationInfo.orgIdentityID)>
			</cfif>

			<cfif local.qryPublicationInfo.supportsOnlineEditions is 1 or local.qryPublicationInfo.supportsPDFEditions is 1>
				<cfset local.basePublicationLink = application.objApplications.getAppBaseLink(applicationInstanceID=local.qryPublicationInfo.applicationInstanceID, siteID=arguments.siteID)>
				<cfset local.strArticles['publicationurl'] = "#local.thisScheme#://#local.thisHostname#/?#local.basePublicationLink#">
				<cfset local.strArticles['issueurl'] = "#local.strArticles.publicationurl#&pubAction=viewIssue&pubIssueID=#local.qryPublicationInfo.issueID#">
			<cfelse>
				<cfset local.strArticles['publicationurl'] = "">
				<cfset local.strArticles['issueurl'] = "">
			</cfif>

			<cfset local.strArticles['templatecustomfields'] = local.strTemplateCustomFields>
			<cfset local.strArticles['issuefeaturedimage'] = structNew()>

			<cfquery name="local.qryFeaturedImages" dbtype="query">
				select featuredImageSizeCode, width, height, filename, orgCode, siteCode
				from qryIssueFeaturedImages
				where referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
				and referenceType = 'publicationIssue'
			</cfquery>
			<cfif not local.qryFeaturedImages.recordCount>
				<cfquery name="local.qryFeaturedImages" dbtype="query">
					select featuredImageSizeCode, width, height, filename, orgCode, siteCode
					from qryIssueFeaturedImages
					where referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
					and referenceType = 'DefaultPublicationIssue'
				</cfquery>
			</cfif>

			<cfloop query="local.qryFeaturedImages">
				<cfset local.featuredImageRootPath = "#local.thisScheme#://#local.thisHostname#/userassets/#LCASE(local.qryFeaturedImages.orgCode)#/#LCASE(local.qryFeaturedImages.siteCode)#/featuredimages/thumbnails/">
				<cfset local.strArticles['issuefeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode] = structNew()>
				<cfset local.strArticles['issuefeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['width'] = local.qryFeaturedImages.width>
				<cfset local.strArticles['issuefeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['height'] = local.qryFeaturedImages.height>
				<cfset local.strArticles['issuefeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['filename'] = local.qryFeaturedImages.filename>
				<cfset local.strArticles['issuefeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['url'] = "#local.featuredImageRootPath##local.qryFeaturedImages.filename#">
			</cfloop>

			<cfset local.strArticles['issuepdf'] = structNew()>
			<cfset local.strArticles['issuepdffeaturedimage'] = structNew()>

			<cfif local.qryPublicationInfo.supportsPDFEditions is 1 and val(local.qryPublicationInfo.pdfEditionDocumentID) gt 0>
				<cfquery name="local.qryFileDetails" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT d.documentID, dv.fileName
					FROM dbo.cms_documents as d 
					INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
					INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
					INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
					INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					WHERE d.documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPublicationInfo.pdfEditionDocumentID#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfif local.qryFileDetails.recordCount>
					<cfset local.strArticles['issuepdf']['filename'] = local.qryFileDetails.fileName>
					<cfset local.strArticles['issuepdf']['url'] = "#local.strArticles.publicationurl#&pubAction=downloadIssue&pubIssueID=#local.qryPublicationInfo.issueID#">

					<cfquery name="local.qryFeaturedImages" dbtype="query">
						select featuredImageSizeCode, width, height, filename, orgCode, siteCode
						from qryIssueFeaturedImages
						where referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
						and referenceType = 'publicationIssuePDF'
					</cfquery>

					<cfloop query="local.qryFeaturedImages">
						<cfset local.featuredImageRootPath = "#local.thisScheme#://#local.thisHostname#/userassets/#LCASE(local.qryFeaturedImages.orgCode)#/#LCASE(local.qryFeaturedImages.siteCode)#/featuredimages/thumbnails/">
						<cfset local.strArticles['issuepdffeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode] = structNew()>
						<cfset local.strArticles['issuepdffeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['width'] = local.qryFeaturedImages.width>
						<cfset local.strArticles['issuepdffeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['height'] = local.qryFeaturedImages.height>
						<cfset local.strArticles['issuepdffeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['filename'] = local.qryFeaturedImages.filename>
						<cfset local.strArticles['issuepdffeaturedimage'][local.qryFeaturedImages.featuredImageSizeCode]['url'] = "#local.featuredImageRootPath##local.qryFeaturedImages.filename#">
					</cfloop>
				</cfif>
			</cfif>

			<cfset var objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

			<cfset var strPostTypeFields = {}>
			<cfloop query="local.qryArticlesInfo">
				<cfif not structKeyExists(strPostTypeFields, local.qryArticlesInfo.postTypeID)>
					<cfset local.postTypesAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PostTypesAdmin',siteID=local.qryArticlesInfo.postTypeSiteID)>
					<cfset local.thisPostTypeFieldsXML = objCustomFields.getFieldsXML(siteID=local.qryArticlesInfo.postTypeSiteID, resourceType='PostTypesAdmin', areaName='PostType', csrid=local.postTypesAdminSiteResourceID, detailID=local.qryArticlesInfo.postTypeID, hideAdminOnly=0)>
					<cfset strPostTypeFields[local.qryArticlesInfo.postTypeID] = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren>
				</cfif>
			</cfloop>

			<cfscript>
				var objBlog = createObject("component","model.admin.blogs.blog");
				var siteID = arguments.siteID;
				var thisHostname = local.thisHostname;
				var thisScheme = local.thisScheme;
				var issueURL = local.strArticles.issueurl;
				var JSONInsanityChars = local.JSONInsanityChars;
				var strCategoryItems = {};
				var featuredImageRootPath = '';
				local.threads = 1;

				// loop per article
				QueryEach(local.qryArticlesInfo, function(struct thisArticle, numeric currentrow, query qryArticlesInfo) {
					var tmpStr = {
						'title': arguments.thisArticle.blogTitle,
						'posttype': arguments.thisArticle.typeName,
						'postdate': arguments.thisArticle.postDate,
						'summary': '',
						'content': '',
						'itemurl': len(issueURL) GT 0 ? '#issueURL#&pubIssueItemID=#arguments.thisArticle.issueItemID#' : '',
						'featuredimages': {},
						'authors': [],
						'attachments': [],
						'posttype_#lCase(arguments.thisArticle.typeName)#': []
					};

					var blogSummaryContent = application.objCMS.getStaticContent(contentID=arguments.thisArticle.summaryContentID, languageID=session.mcstruct.languageID, skipMerge=1).rawContent;
					var blogContent = application.objCMS.getStaticContent(contentID=arguments.thisArticle.blogContentID, languageID=session.mcstruct.languageID, skipMerge=1).rawContent;
					
					tmpStr['summary'] = application.objCommon.getCleanContentData(HTMLContent=blogSummaryContent);
					tmpStr['content'] = application.objCommon.getCleanContentData(HTMLContent=blogContent);
					
					var qryFeaturedImages = queryExecute("
						SELECT featuredImageSizeCode, width, height, filename, orgCode, siteCode
						FROM qryIssueFeaturedImages
						WHERE referenceID = :referenceID
						AND referenceType = 'publicationIssueItem'
					",
						{ referenceID = { value=arguments.thisArticle.issueItemID, cfsqltype="CF_SQL_INTEGER" }	},
						{ dbtype="query" }
					);

					loop query="#qryFeaturedImages#" {
						featuredImageRootPath = "#thisScheme#://#thisHostname#/userassets/#LCASE(qryFeaturedImages.orgCode)#/#LCASE(qryFeaturedImages.siteCode)#/featuredimages/thumbnails/";
						tmpStr['featuredimages'][qryFeaturedImages.featuredImageSizeCode] = {
							"width": qryFeaturedImages.width,
							"height": qryFeaturedImages.height,
							"filename": qryFeaturedImages.filename,
							"url": "#featuredImageRootPath##qryFeaturedImages.filename#"
						};
					}

					tmpStr['authors'] = queryExecute("
						SELECT *
						FROM qryArticleAuthors
						WHERE blogEntryID = :blogEntryID
					",
						{ blogEntryID = { value=arguments.thisArticle.blogEntryID, cfsqltype="CF_SQL_INTEGER" }	},
						{ dbtype="query", returntype="array" }
					);

					// remove blogEntryID key from the author structs
					tmpStr['authors'] = arrayMap(tmpStr['authors'], (author) => { 
						if (isStruct(author)) 
							author.delete('blogEntryID');
						return author;
					});

					tmpStr['sourcelinks'] = queryExecute("
						SELECT sourcelinkurl, articledate, byline, company, description, isfree, approved, city, state, homepageurl
						FROM qryArticleSourceLinks
						WHERE blogEntryID = :blogEntryID
						ORDER BY orderNum
					",
						{ blogEntryID = { value=arguments.thisArticle.blogEntryID, cfsqltype="CF_SQL_INTEGER" }	},
						{ dbtype="query", returntype="array" }
					);

					//lowercase sourcelinks structs in array
					tmpStr['sourcelinks'] = tmpStr['sourcelinks'].map(function(thisArrayElement){
						var lcaseStruct = {};
						thisArrayElement.each(function(key,value,struct){
							lcaseStruct["#lcase(key)#"] = value;
						});
						return lcaseStruct
					});

					tmpStr['attachments'] = queryExecute("
						SELECT filename, url
						FROM qryArticleDocuments
						WHERE blogEntryID = :blogEntryID
					",
						{ blogEntryID = { value=arguments.thisArticle.blogEntryID, cfsqltype="CF_SQL_INTEGER" }	},
						{ dbtype="query", returntype="array" }
					);

					tmpStr['blogcategories'] = objBlog.getBlogEntryBeforeArchiveCategoryDetails(entrySiteResourceID=arguments.thisArticle.siteResourceID);

					if (arrayLen(strPostTypeFields[arguments.thisArticle.postTypeID])) {
						var arrPostTypeFields = objBlog.prepareResourceFieldsArr(itemID=arguments.thisArticle.blogEntryID, itemType='PostTypeCustom', arrResourceFields=strPostTypeFields[arguments.thisArticle.postTypeID], objCustomFields=objCustomFields);

						var currentPostTypeField ={};
						loop array="#arrPostTypeFields#" index="currentPostTypeField" {
							var thisElement = {
								"fieldname": replace(currentPostTypeField.attributes.fieldText,JSONInsanityChars,"","all"),
								"exportlabel": replace(currentPostTypeField.attributes.fieldReference,JSONInsanityChars,"","all"),
								"value": ""
							};

							if (listFindNoCase("SELECT,RADIO,CHECKBOX",currentPostTypeField.attributes.displayTypeCode)) {
								var valueIDList = "";

								if (listFindNoCase("SELECT,RADIO",currentPostTypeField.attributes.displayTypeCode)) {
									valueIDList = currentPostTypeField.value;
								} elseif (currentPostTypeField.attributes.displayTypeCode EQ "CHECKBOX" AND isArray(currentPostTypeField.value) AND arrayLen(currentPostTypeField.value)) {
									valueIDList = arrayToList(currentPostTypeField.value);
								}
								
								if (listLen(valueIDList)) {
									var currentPostTypeFieldValues = {};
									loop array="#currentPostTypeField.children#" index="currentPostTypeFieldValues" {
										if (listFind(valueIDList,currentPostTypeFieldValues.attributes.valueID)) {
											if (len(thisElement.value)) { 
												thisElement.value = "#thisElement.value#, ";
											}
											thisElement.value = thisElement.value & replace(currentPostTypeFieldValues.attributes.fieldValue,JSONInsanityChars,"","all");
										}
									}
								}
							} else if (currentPostTypeField.attributes.fieldTypeCode EQ 'DECIMAL2TEXTBOXAMT') {
								thisElement.value = Replace(dollarFormat(replace(currentPostTypeField.value,JSONInsanityChars,"","all")),".00","");
							} else {
								thisElement.value = replace(currentPostTypeField.value,JSONInsanityChars,"","all");
							}

							tmpStr["posttype_#lCase(arguments.thisArticle.typeName)#"].append(thisElement)
						}
					}

					if (NOT strCategoryItems.keyExists(arguments.thisArticle.categoryID)) {
						strCategoryItems[arguments.thisArticle.categoryID] = [];
					}

					strCategoryItems[arguments.thisArticle.categoryID].append(tmpStr);

				}, false, local.threads);
			</cfscript>

			<cfset local.strArticles['issueitems'] = arrayNew(1)>

			<cfquery name="local.qryIssueCategoryOrder" dbtype="query">
				select distinct categoryid, categoryCode, categoryPath, categoryName, parentcategoryCode, parentcategoryName, treeOrder
				from [local].qryArticlesInfo
				order by treeOrder
			</cfquery>


			<cfloop query="local.qryIssueCategoryOrder">
				<cfset local.thisCategoryPathArray = listToArray(local.qryIssueCategoryOrder.categorypath,"\").map((item) => trim(item))>
				<cfset arrayAppend(local.strArticles['issueitems'], {
					"categoryid": local.qryIssueCategoryOrder.categoryid,
					"categorycode": local.qryIssueCategoryOrder.categoryCode,
					"categorypath": local.qryIssueCategoryOrder.categoryPath,
					"categoryname": local.qryIssueCategoryOrder.categoryName,
					"categorypatharray": local.thisCategoryPathArray,
					"categorytreeorder": local.qryIssueCategoryOrder.treeorder,
					"parentcategorycode": local.qryIssueCategoryOrder.parentcategoryCode,
					"parentcategoryname": local.qryIssueCategoryOrder.parentcategoryName,
					"items": strCategoryItems[local.qryIssueCategoryOrder.categoryid],
				})>
			</cfloop>
		</cfif>

		<cfset local.data = structNew()>
		<cfset local.data['success'] = true>
		<cfset local.data['issuedata'] = local.strArticles>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPublicationEmailTagTypes" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">

		<cfset var qryPublicationEmailTagTypes = "">

		<cfquery name="qryPublicationEmailTagTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT emailTagTypeID
			FROM dbo.pub_publicationEmailTagTypes
			WHERE publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn qryPublicationEmailTagTypes>
	</cffunction>

	<cffunction name="getPublicationAutoApprovalDays" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">

		<cfset var qryPublicationAutoApprovalDays = "">

		<cfquery name="qryPublicationAutoApprovalDays" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT weekDay
			FROM dbo.pub_publicationAutoApprovalDays
			WHERE publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn qryPublicationAutoApprovalDays>
	</cffunction>

	<cffunction name="getPublicationRecipients" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="mode" type="numeric" required="yes">
		<cfargument name="emailTypeID" type="string" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.receiveRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Publications", functionName="ReceiveEditionEmails")>

		<cfstoredproc procedure="pub_getEmailRecipients" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.publicationID#">
			<cfprocparam cfsqltype="CF_SQL_TINYINT" type="in" value="#arguments.mode#">
			<cfif arguments.emailTypeID gt 0>
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.emailTypeID#">
			<cfelse>
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
			</cfif>
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.receiveRFID#">
			<cfprocresult name="local.qryPublicationRecipients" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryPublicationRecipients>
	</cffunction>

	<cffunction name="updateIssueStatus" access="public" output="false" returntype="struct">
		<cfargument name="issueID" type="numeric" required="yes">
		<cfargument name="statusName" type="string" required="yes">
		<cfargument name="enteredByMemberID" type="number" required="no" default="#session.cfcuser.memberData.memberID#">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryUpdateIssueStatus" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @issueID int, @newIssueStatusID int, @oldIssueStatusID int, @enteredByMemberID int,
					@draftStatusID int, @awaitingApprovalStatusID int, @approvedStatusID int, @publishedStatusID int, @statusHistoryID int,
					@errMsg varchar(100);
				SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enteredByMemberID#">;

				SELECT @oldIssueStatusID = issueStatusID
				FROM dbo.pub_issues
				WHERE issueID = @issueID;

				SELECT @newIssueStatusID = issueStatusID 
				FROM dbo.pub_statuses
				WHERE statusName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusName#">;

				SELECT @draftStatusID = issueStatusID FROM dbo.pub_statuses	WHERE statusName = 'Draft';
				SELECT @awaitingApprovalStatusID = issueStatusID FROM dbo.pub_statuses	WHERE statusName = 'Awaiting Approval';
				SELECT @approvedStatusID = issueStatusID FROM dbo.pub_statuses	WHERE statusName = 'Approved';
				SELECT @publishedStatusID = issueStatusID FROM dbo.pub_statuses	WHERE statusName = 'Published';

				-- same status
				IF @oldIssueStatusID = @newIssueStatusID
					SET @errMsg = 'This issue already has this status';
				
				-- check invalid status
				ELSE BEGIN
					-- if the new status is Awaiting Approval, then the issue must be in Draft Status
					IF @newIssueStatusID = @awaitingApprovalStatusID AND @oldIssueStatusID <> @draftStatusID
						SET @errMsg = 'Invalid status';
					
					-- if the new status is Approved, then the issue must be in Draft or Awaiting Approval Status
					IF @newIssueStatusID = @approvedStatusID AND @oldIssueStatusID NOT IN (@draftStatusID,@awaitingApprovalStatusID,@publishedStatusID)
						SET @errMsg = 'Invalid status';

					-- if the new status is Published, then the issue must be in Draft or Awaiting Approval or Approved Status
					IF @newIssueStatusID = @publishedStatusID AND @oldIssueStatusID NOT IN (@draftStatusID,@awaitingApprovalStatusID,@approvedStatusID)
						SET @errMsg = 'Invalid status';
				END

				IF ISNULL(@errMsg,'') <> ''
					GOTO on_done;

				BEGIN TRAN;
					UPDATE dbo.pub_issues
					SET issueStatusID = @newIssueStatusID
					WHERE issueID = @issueID;

					INSERT INTO dbo.pub_issueStatusHistory (issueID, issueStatusID, oldIssueStatusID, updateDate, enteredByMemberID)
					VALUES (@issueID, @newIssueStatusID, @oldIssueStatusID, GETDATE(), @enteredByMemberID);
					SET @statusHistoryID = SCOPE_IDENTITY();
				COMMIT TRAN;

				on_done:
				SELECT ISNULL(@errMsg,'') AS errMsg, @oldIssueStatusID AS oldIssueStatusID, @statusHistoryID as statusHistoryID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif len(local.qryUpdateIssueStatus.errMsg)>
			<cfset local.returnStruct = { "success":false, "errmsg":local.qryUpdateIssueStatus.errMsg, "oldIssueStatusID":local.qryUpdateIssueStatus.oldIssueStatusID, "statusHistoryID":local.qryUpdateIssueStatus.statusHistoryID }>
		<cfelse>
			<cfset local.returnStruct = { "success":true, "errmsg":"", "oldIssueStatusID":local.qryUpdateIssueStatus.oldIssueStatusID, "statusHistoryID":local.qryUpdateIssueStatus.statusHistoryID }>
		</cfif>	

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doIssueItemCategoryMoveUp" access="public" output="false" returntype="struct">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_issueItemCategoryMoveUp">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfstoredproc>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doIssueItemCategoryMoveDown" access="public" output="false" returntype="struct">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_issueItemCategoryMoveDown">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doIssueItemMove" access="public" output="false" returntype="struct">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="issueItemID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_issueItemMove">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteIssuePDF" access="public" output="false" returntype="struct">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryIssuePDF" datasource="#application.dsn.memberCentral.dsn#">
			select i.pdfEditionDocumentID, d.siteID
			from dbo.pub_issues as i
			inner join dbo.cms_documents as d on d.documentID = i.pdfEditionDocumentID
			where i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfquery>

		<cfif val(local.qryIssuePDF.pdfEditionDocumentID) gt 0>
			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=local.qryIssuePDF.siteID, documentID=local.qryIssuePDF.pdfEditionDocumentID)>

			<cfquery name="local.qryDeleteIssuePDF" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					UPDATE dbo.pub_issues
					SET pdfEditionDocumentID = NULL
					WHERE issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveIssueHTML" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="htmlContent" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.duplicate = false>

		<cftry>
			<cfif arguments.issueID gt 0>
				<cfquery name="local.qryUpdateIssue" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					DECLARE @issueID INT, @issueType char(1), @issueTitle varchar(100), @enteredByMemberID INT, @htmlContentID INT, @rawcontent VARCHAR(MAX);		
					SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;	
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
					SET @rawcontent= <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.htmlContent#">;

					SELECT @htmlContentID = htmlContentID, @issueType=issueType , @issueTitle=issueTitle FROM dbo.pub_issues WHERE issueID = @issueID;

					IF @issueType = 'I' BEGIN
						EXEC dbo.cms_updateContent @contentID=@htmlContentID, @languageID=1, @isHTML=1, @contentTitle=@issueTitle,
									@contentDesc='', @rawcontent=@rawcontent, @memberID=@enteredByMemberID;
					END
				</cfquery>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="requestIssueApproval" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data.success = false>
		<cfset local.siteID = application.objSiteInfo.getSiteInfo(arguments.mcproxy_orgCode).siteID>

		<cftry>
			<cfset local.retStruct = updateIssueStatus(issueID=arguments.issueID, statusName="Awaiting Approval")>
			<cfif local.retStruct.success>
				<cfset local.retStruct = sendIssueApprovalRequest(siteCode=arguments.mcproxy_orgCode, issueID=arguments.issueID, publicationID=arguments.publicationID,
					emailMode="requestApproval", actorMemberID=session.cfcuser.memberdata.memberID)>
				<cfset local.data.success = local.retStruct.success>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="approveIssue" access="public" output="false" returntype="struct"> 
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "errmsg":"", "statusname":"", "norecipients":0 }>

		<cftry>
			<cfset local.qryPublicationDetails = getPublicationDetails(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>

			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=local.qryPublicationDetails.siteResourceID, permission="ApproveEditions")>
				<cfset local.data.success = false>
				<cfset local.data.errmsg = "You do not have rights to perform this operation.">
				<cfreturn local.data>
			</cfif>

			<cfset local.retStruct = updateIssueStatus(issueID=arguments.issueID, statusName="Approved")>
			<cfif NOT local.retStruct.success>
				<cfset local.data.success = false>
				<cfset local.data.errmsg = local.retStruct.errmsg>
				<cfreturn local.data>
			<cfelseif local.retStruct.success>
				<cfset local.data.statusname = "Approved">
				<cfset local.data.success = true>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="publishIssue" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="bit_send_email" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "errmsg":"", "norecipients":0 }>

		<cftry>
			<cfset local.qryPublicationDetails = getPublicationDetails(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>
			<cfset updateIssueSendEmailSetting(issueID=arguments.issueID, bit_send_email = arguments.bit_send_email )>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=local.qryPublicationDetails.siteResourceID, permission="ApproveEditions")>
				<cfset local.data.success = false>
				<cfset local.data.errmsg = "You do not have rights to perform this operation.">
				<cfreturn local.data>
			</cfif>
			<cfset local.retStruct = updateIssueStatus(issueID=arguments.issueID, statusName="Published")>
			<cfset local.data.success = local.retStruct.success>
			<cfset local.data.errmsg = local.retStruct.errmsg>
			
			<cfif local.retStruct.success>
				<cfif local.qryPublicationDetails.supportsEmailEditions AND arguments.bit_send_email>
					<cftry>
						<cfset local.emailResult = sendEmailEdition(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, issueID=arguments.issueID, publicationID=arguments.publicationID)>
						<cfif not local.emailResult.success and local.emailResult.norecipients>
							<cfset local.data.norecipients = 1>
						<cfelseif not local.emailResult.success>
							<cfset local.data.errmsg = "Issue Publishing Failed due to problem rendering issue. Status Changed to Approved">
							<cfset local.data.success = false>
							<cfthrow message="#local.data.errmsg#">
						</cfif>
						<cfcatch type="any">
							<cfset local.retStruct = updateIssueStatus(issueID=arguments.issueID, statusName="Approved")>
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump={emailResult=local.emailResult, qryIssueDetails=local.qryIssueDetails}, customMessage="Issue Publishing Failed due to problem rendering issue. Status Changed to Approved")>
						</cfcatch>
					</cftry>
				</cfif>
				<cfif local.data.success and local.qryPublicationDetails.isParentPublication and local.qryPublicationDetails.childPublicationsCount gt 0>
					<cfset addArticlesToChildPublications(siteID=arguments.mcproxy_siteID, issueID=arguments.issueID, publicationID=arguments.publicationID, actorMemberID=session.cfcuser.memberdata.memberID)>
				</cfif>
				<cfif local.data.success>
					<cfset updateIssueAutoPublishDate(issueID=arguments.issueID, autoPublishDate = '' )>
				</cfif>
			</cfif>
			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="sendEmailEdition" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryIssueDetails = getIssueDetails(publicationID=arguments.publicationID, issueID=arguments.issueID)>
		<cfif local.qryIssueDetails.issueType EQ "I">
			<cfquery name="local.getImportedHTML" datasource="#application.dsn.membercentral.dsn#">
				select rawContent
				from dbo.fn_getContent(<cfqueryparam value="#local.qryIssueDetails.HTMLcontentID#" cfsqltype="CF_SQL_INTEGER">,1)
			</cfquery>
			<cfset local.strRenderedTemplateContent.content = local.getImportedHTML.rawContent>
			<cfset local.strRenderedTemplateContent.success = true>
		<cfelse>		
			<cfset local.strRenderedTemplateContent = getPreviewIssueTemplateContent(siteID=arguments.siteID, publicationID=arguments.publicationID, issueID=arguments.issueID)>
		</cfif>
		<cfif local.strRenderedTemplateContent.success>
			<cfset local.retStruct = doSendEmailIssue(mcproxy_siteCode=arguments.siteCode, publicationID=arguments.publicationID, issueID=arguments.issueID, overrideMemberID=0,
				overrideEmailAddress='', templateContent=local.strRenderedTemplateContent.content, isTestEmail=0)>
		<cfelse>			
			<cfset local.retStruct = { "success":false, "errmsg":local.strRenderedTemplateContent.content, "norecipients":0 }>			
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getPreviewIssueTemplateContent" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objResourceTemplate = CreateObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
		
		<cfset local.qryPublication = getPublicationDetails(siteID=arguments.siteID, publicationID=arguments.publicationID)>
		<cfset local.htmlTemplateDetails = local.objResourceTemplate.getResourceTemplateDetails(siteID=arguments.siteID, templateID=local.qryPublication.htmlEmailTemplateID, resourceType='publicationAdmin')>
		<cfset local.model = getIssueItemsInfo(siteID=arguments.siteID, publicationID=arguments.publicationID, issueID=arguments.issueID)>
		
		<cfset local.strTemplate = local.objResourceTemplate.doRenderResourceTemplate(
				template=local.htmlTemplateDetails.templateContent,
				model = local.model.issueData,
				templateFormat=local.htmlTemplateDetails.templateFormat
			)>
		
		<cfreturn local.strTemplate>
	</cffunction>

	<cffunction name="sendIssuePreviewEmailMessage" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="emailSubject" type="string" required="true">
		<cfargument name="headerHTML" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		<cfset local.retStruct.success = false>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.qryIssueDetails = getIssueDetails(publicationID=arguments.publicationID, issueID=arguments.issueID)>
		<cfset local.qryPublication = getPublicationDetails(siteID=local.mc_siteinfo.siteID, publicationID=arguments.publicationID)>

		<cfset local.strOverrideApprovalEmails = getOverrideApprovalEmailsForPublication(siteID=local.mc_siteinfo.siteID, publicationSiteResourceID=local.qryPublication.siteResourceID)>

		<cfif (not local.strOverrideApprovalEmails.success and not len(local.qryPublication.emailEditionPreviewEmailAddresses)) or local.qryIssueDetails.recordCount is 0 or not local.qryPublication.supportsEmailEditions>
			<cfreturn local.retStruct>
		</cfif>

		<cfif local.qryIssueDetails.issueType EQ "I">
			<cfquery name="local.getImportedHTML" datasource="#application.dsn.membercentral.dsn#">
				select rawContent
				from dbo.fn_getContent(<cfqueryparam value="#local.qryIssueDetails.HTMLcontentID#" cfsqltype="CF_SQL_INTEGER">,1)
			</cfquery>
			<cfset local.strRenderedTemplate.content = local.getImportedHTML.rawContent>
			<cfset local.strRenderedTemplate.success = true>
		<cfelse>
			<cfset local.strRenderedTemplate = getPreviewIssueTemplateContent(siteID=local.mc_siteinfo.siteID, publicationID=arguments.publicationID, issueID=arguments.issueID)>
		</cfif>
		
		<cfif local.strRenderedTemplate.success>
			<!--- message prep --->
			<cfset local.templateContent = local.strRenderedTemplate.content>
			<cfif NOT application.objEmailWrapper.isCompleteHTMLDocument(htmlcontent=local.templateContent)>
				<cfset local.templateContent = application.objEmailWrapper.cleanupHTML(htmlcontent=local.templateContent)>
			</cfif>
			<cfif NOT reFindNoCase("\[\[(emailOptOutStatement|emailOptOutURL|consentListManagementURL)\]\]", local.templateContent)>
				<cfset local.footercontent = '<div style="padding:5px;background-color:##fff;text-align:center;">[[emailOptOutStatement]]</div>'>
				<cfset local.templateContent = application.objEmailWrapper.injectFooter(htmlcontent=local.templateContent, footercontent=local.footercontent)>
			</cfif>
			<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=local.templateContent, siteID=local.mc_siteinfo.siteID, qualURL="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/")>
			<cfset local.templateContent = application.objEmailWrapper.disableClickTrackingForSpecificLinks(htmlcontent=local.templateContent)>
			<cfset local.parseContent = parseContentWithMergeCodes(sitecode=local.mc_siteinfo.sitecode,memberID=session.cfcuser.memberdata.memberID, content=local.templateContent, emailConsentListID=val(local.qryPublication.emailConsentListID), email=session.cfcuser.memberdata.email)>
			<cfif len(arguments.headerHTML)>
				<cfset local.parseContent.content = application.objEmailWrapper.injectHeader(htmlcontent=local.parseContent.content, headercontent=arguments.headerHTML)>
			</cfif>

			<cfif local.qryPublication.utm_autoappend>
				<cfset local.utmCampaign = len(local.qryPublication.utm_campaign) ? local.qryPublication.utm_campaign : local.qryPublication.applicationInstanceName>
				<cfset local.parseContent.content = application.objEmailWrapper.appendUTMCodesToLinks(
						htmlcontent=local.parseContent.content,
						utm_campaign=local.utmCampaign,
						utm_source="MemberCentralPublications",
						utm_medium="email",
						utm_content=local.qryIssueDetails.issueTitle
					)>
			</cfif>
			<cfset local.arrEmailTo = []>

			<cfif local.strOverrideApprovalEmails.success>
				<cfset local.arrEmailTo = local.strOverrideApprovalEmails.arrEmailTo>
			<cfelse>
				<cfscript>
					local.toEmailArr = listToArray(local.qryPublication.emailEditionPreviewEmailAddresses,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>
			</cfif>

			<cfif arrayLen(local.arrEmailTo)>
				<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=local.qryPublication.emailEditionSenderName, email=local.mc_siteInfo.networkEmailFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.qryPublication.emailEditionReplyToAddress,
					emailsubject=arguments.emailSubject,
					emailtitle=local.mc_siteInfo.orgname,
					emailhtmlcontent=local.parseContent.content,
					siteID=local.mc_siteinfo.siteID,
					memberID=local.mc_siteInfo.sysMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="PUBLICATIONS"),
					sendingSiteResourceID=local.qryPublication.siteResourceID,
					referenceType='publicationIssue',
					referenceID=arguments.issueID,
					doWrapEmail=false
				)>
				<cfset local.retStruct.success = true>
			</cfif>
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="parseContentWithMergeCodes" access="public" output="no" returntype="struct">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="content" type="string" required="yes">
		<cfargument name="emailConsentListID" type="numeric" required="yes">
		<cfargument name="email" type="string" required="yes">

		<cfset var local = StructNew()>

		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>

		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteinfo.orgID, memberID=val(local.memberInfo.memberID), content=arguments.content)>

        <cfif application.MCEnvironment eq "production">
            <cfset local.thisHostname = local.mc_siteinfo.mainhostname>
        <cfelse>
            <cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
        </cfif>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix,
			prefix=local.memberInfo.Prefix, membernumber=local.memberInfo.membernumber, professionalSuffix=local.memberInfo.professionalSuffix, 
			orgcode=local.memberInfo.orgcode, siteID=local.mc_siteinfo.siteID,
			hostname=local.thisHostname, useRemoteLogin=local.mc_siteinfo.useRemoteLogin, recipientEmail=arguments.email }>
		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>

		<cfset local.strArgs = { content=arguments.content, memberdata=local.tempMemberData, orgcode=local.mc_siteinfo.orgcode, sitecode=local.mc_siteinfo.siteCode }>
		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset local.strReturn = { content=local.strMergedContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="doSendEmailIssue" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="overrideMemberID" type="numeric" required="true">
		<cfargument name="overrideEmailAddress" type="string" required="true">
		<cfargument name="templateContent" type="string" required="yes">
		<cfargument name="isTestEmail" type="boolean" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "errmsg":"", "norecipients":0 }>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
		<cfsetting requesttimeout="600">

		<cfif arguments.isTestEmail AND (val(arguments.overrideMemberID) eq 0 OR NOT len(arguments.overrideEmailAddress))>
			<cfset local.data.success = false>
			<cfreturn local.data>
		</cfif>

		<!--- For test emails, validate and clean up multiple email addresses --->
		<cfif arguments.isTestEmail>
			<cfset local.validatedEmailAddresses = application.objCommon.getValidatedEmailAddresses(emailAddressList=arguments.overrideEmailAddress, delimiter=";")>
			<cfif NOT len(local.validatedEmailAddresses)>
				<cfset local.data.success = false>
				<cfset local.data.errmsg = "No valid email addresses provided.">
				<cfreturn local.data>
			</cfif>
			<cfset arguments.overrideEmailAddress = local.validatedEmailAddresses>
		</cfif>

		<cfset local.templateContent = arguments.templateContent>
		<cfif NOT application.objEmailWrapper.isCompleteHTMLDocument(htmlcontent=local.templateContent)>
			<cfset local.templateContent = application.objEmailWrapper.cleanupHTML(htmlcontent=local.templateContent)>
		</cfif>
		<cfif NOT reFindNoCase("\[\[(emailOptOutStatement|emailOptOutURL|consentListManagementURL)\]\]", local.templateContent)>
			<cfset local.footercontent = '<div style="padding:5px;background-color:##fff;text-align:center;">[[emailOptOutStatement]]</div>'>
			<cfset local.templateContent = application.objEmailWrapper.injectFooter(htmlcontent=local.templateContent, footercontent=local.footercontent)>
		</cfif>
		
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=local.templateContent, siteID=local.mc_siteinfo.siteID,qualURL="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/")>

		<cfquery name="local.qryGetPubIssueInfo" datasource="#application.dsn.membercentral.dsn#">
			SELECT a.applicationInstanceName, i.issueTitle, p.utm_autoappend, p.utm_campaign
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
			INNER JOIN dbo.cms_applicationInstances a ON a.applicationInstanceID=p.applicationInstanceID 
			WHERE i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfquery>
		
		<cfif local.qryGetPubIssueInfo.utm_autoappend>
			<cfset local.utmCampaign = len(local.qryGetPubIssueInfo.utm_campaign) ? local.qryGetPubIssueInfo.utm_campaign : local.qryGetPubIssueInfo.applicationInstanceName>
			<cfset local.templateContent = application.objEmailWrapper.appendUTMCodesToLinks(
					htmlcontent=local.templateContent,
					utm_campaign=local.utmCampaign,
					utm_source="MemberCentralPublications",
					utm_medium="email",
					utm_content=local.qryGetPubIssueInfo.issueTitle
				)>
		</cfif>
		<cfset local.templateContent = application.objEmailWrapper.disableClickTrackingForSpecificLinks(htmlcontent=local.templateContent)>

		<cfif arguments.isTestEmail>
			<cfset local.markRecipientAsReady = 1>
			<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=local.mc_siteinfo.siteID, rawContent=local.templateContent, extraMergeTagList="consentListManagementURL")>
			<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
				<cfset local.markRecipientAsReady = 0>
			</cfif>
		</cfif>
		<cftry>
			<cfquery name="local.qryRecipients" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @publicationID int, @issueID int, @resourceTypeID int, @parentSiteResourceID int,
						@emailSubject varchar(200), @rawContent varchar(max), @contentID int, @siteResourceID int, @sendOnDate datetime, @contentVersionID int,
						@enteredByMemberID int, @overrideMemberID int, @overrideEmailAddress varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">;
					SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
					SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
					SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
					SET @overrideMemberID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.overrideMemberID#">,0);
					SET @overrideEmailAddress = NULLIF(<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.overrideEmailAddress#">,'');
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @sendOnDate = GETDATE();
					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				
					SELECT @parentSiteResourceID = st.siteResourceID 
					FROM dbo.admin_tooltypes as tt
					INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
						AND st.siteID = @siteID
						AND tt.toolType = 'PublicationAdmin'
					INNER JOIN dbo.cms_siteResources as sr ON sr.siteResourceID = st.siteResourceID
						AND sr.siteResourceStatusID = 1;

					SELECT @issueID = i.issueID, @emailSubject = ai.applicationInstanceName + ' - ' + i.issueTitle
					FROM dbo.pub_volumes as v
					INNER JOIN dbo.pub_issues as i ON i.volumeID = v.volumeID
					INNER JOIN dbo.pub_publications as p on p.publicationID = v.publicationID
					INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
					WHERE v.publicationID = @publicationID
					AND i.issueID = @issueID;

					<cfif arguments.isTestEmail>
						SET @emailSubject = '**TEST** ' + @emailSubject;
					</cfif>

					EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
						@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
						@rawContent=@rawContent, @memberID=@enteredByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

					select top 1 @contentVersionID = cv.contentVersionID
					from dbo.cms_contentLanguages as cl
					inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
						and cv.contentID = @contentID
						and cv.contentLanguageID = cl.contentLanguageID 
						and cv.isActive = 1
					where cl.siteID = @siteID
					and cl.contentID = @contentID
					and cl.languageID = 1;

					EXEC dbo.pub_sendEmailIssue @siteID=@siteID, @issueID=@issueID, @emailSubject=@emailSubject, @rawContent=@rawContent, 
						@contentVersionID=@contentVersionID, @overrideMemberID=@overrideMemberID, @overrideEmailAddress=@overrideEmailAddress, 
						@recordedByMemberID=@enteredByMemberID, @sendOnDate=@sendOnDate, @isTestMessage=<cfif arguments.isTestEmail IS 1>1<cfelse>0</cfif>;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif local.qryRecipients.recordCount>
				<cfset local.data.outputMessage = local.qryRecipients.outputMessage>
				<cfif arguments.isTestEmail AND local.qryRecipients.numRecipients>
					<cfif not local.markRecipientAsReady>
						<cfloop query="local.qryRecipients">
							<cfset local.strMergeTagArgs = { siteID=local.mc_siteinfo.siteID, orgcode=local.mc_siteinfo.orgcode, recipientID=local.qryRecipients.recipientID, 
								messageID=local.qryRecipients.messageID, recipientMemberID=local.qryRecipients.recipientMemberID, 
								memberID=local.qryRecipients.recipientMemberID, membernumber=local.qryRecipients.membernumber, 
								recipientEmail=local.qryRecipients.recipientEmail, consentListEmail=local.qryRecipients.consentListEmail, 
								hostname=local.mc_siteinfo.mainhostname, useRemoteLogin=local.mc_siteinfo.useRemoteLogin, 
								strRecipientExtMergeTags=local.strRecipientExtMergeTags }>

							<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>

							<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryChangeStatus">
								exec dbo.email_setMessageRecipientHistoryStatus
									@siteID= <cfqueryparam value="#local.mc_siteinfo.siteID#" cfsqltype="CF_SQL_INTEGER">,
									@messageID= <cfqueryparam value="#local.qryRecipients.messageID#" cfsqltype="CF_SQL_INTEGER">,
									@recipientID= <cfqueryparam value="#local.qryRecipients.recipientID#" cfsqltype="CF_SQL_INTEGER">,
									@statusCode= <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
									@updateDate= <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
							</cfquery>
						</cfloop>
					</cfif>

					<cfhttp method="get" url="#application.paths.backendPlatform.internalUrl#?event=integrations.sendgrid.processmessage&messageID=#local.qryRecipients.messageID#">
				</cfif>
				
				<cfset local.data.success = true>
			<cfelse>
				<cfset local.data.success = false>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients",cfcatch.detail)>
				<cfset local.data.errmsg = "No recipients found for the publication.">
				<cfset local.data.norecipients = 1>
			</cfif>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getOverrideApprovalEmailsForPublication" access="public" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationSiteResourceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success:false, arrEmailTo:[] }>
		<cfset local.qryOverrideEmails = "">
		<cfset local.overrideReceiveApprovalEmailsPublicationsRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Publications", functionName="overrideReceiveApprovalEmails")>

		<cfquery name="local.qryPermissionGroups" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @functionID int, @siteID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.overrideReceiveApprovalEmailsPublicationsRFID#">;

			SELECT gprp.groupPrintID
			FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			WHERE srfrp.siteID = @siteID
			AND srfrp.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationSiteResourceID#">
			AND srfrp.functionID = @functionID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryPermissionGroups.recordCount>
			<cfquery name="local.qryOverrideEmails" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @functionID int, @siteID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.overrideReceiveApprovalEmailsPublicationsRFID#">;

				SELECT mActive.memberID, me.email, mActive.firstName + ' ' + mActive.lastName as memberName
				FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
					AND srfrp.rightPrintID = gprp.rightPrintID
				INNER JOIN dbo.sites AS s ON s.siteID = @siteID
				INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
				INNER JOIN dbo.ams_members AS m ON m.groupPrintID = gprp.groupPrintID AND m.orgID in (1,o.orgID)
				INNER JOIN dbo.ams_members AS mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
				INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID in (1,o.orgID)
					and me.memberID = mActive.memberID
				INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID in (1,o.orgID) 
					and metag.memberID = me.memberID 
					AND metag.emailTypeID = me.emailTypeID
				INNER JOIN dbo.ams_memberEmailTagTypes AS mett ON mett.orgID in (1,o.orgID) 
					and mett.emailTagTypeID = metag.emailTagTypeID
					AND mett.emailTagType = 'Primary'
				WHERE srfrp.siteID = @siteID
				AND srfrp.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationSiteResourceID#">
				AND srfrp.functionID = @functionID
				AND LEN(me.Email) > 0;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.returnStruct.success = true>
			<cfloop query="local.qryOverrideEmails">
				<cfset local.returnStruct.arrEmailTo.append({ name:local.qryOverrideEmails.memberName, email:local.qryOverrideEmails.email })>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendIssueApprovalRequest" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="emailMode" type="string" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.qryIssueDetails = getIssueDetails(publicationID=arguments.publicationID, issueID=arguments.issueID)>
		<cfset local.qryPublication = getPublicationDetails(siteID=local.mc_siteinfo.siteID, publicationID=arguments.publicationID)>
		<cfset local.strOverrideApprovalEmails = getOverrideApprovalEmailsForPublication(siteID=local.mc_siteinfo.siteID, publicationSiteResourceID=local.qryPublication.siteResourceID)>

		<cfif (not local.strOverrideApprovalEmails.success and not len(local.qryPublication.emailEditionPreviewEmailAddresses)) or local.qryIssueDetails.recordCount is 0>
			<cfset local.retStruct.success = false>
			<cfreturn local.retStruct>
		</cfif>

		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SELECT mActive.firstName + ' ' + mActive.lastName as memberName, mActive.memberNumber
			FROM dbo.ams_members AS m
			INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
			WHERE m.memberID = <cfqueryparam value="#arguments.actorMemberID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfset local.editPublicationLink = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PublicationAdmin',mca_ta='editIssue') & '&pid=#arguments.publicationID#&issueID=#arguments.issueID#'>
		<cfsavecontent variable="local.issueApprovalHTML">
			<cfoutput>
				<table style="width:100%;margin-bottom:20px;background-color:##dedede;">
					<tbody>
						<tr>
							<td></td>
							<td class="display:block!important;max-width:600px!important;margin:0 auto!important;clear:both!important;">
								<div class="padding: 15px;max-width: 600px;margin: 0 auto;display: block;">
									<table style="width:100%;background-color:##dedede;">
										<tbody>
											<tr>
												<td style="text-align:center;padding-bottom:15px;">
													<cfswitch expression="#arguments.emailMode#">
														<cfcase value="requestApproval">
															<h3 style="color:##000000;font-weight:bold;">Approval Requested<br/><span style="font-size:.8em;">by #local.qryMember.memberName# (#local.qryMember.memberNumber#)</span></h3>
														</cfcase>
														<cfcase value="childIssueDistribution">
															<h3 style="color:##000000;font-weight:bold;">New Issue Added: #local.qryPublication.applicationInstanceName#<br/><span style="font-size:.8em;">by #local.qryMember.memberName# (#local.qryMember.memberNumber#)</span></h3>
															<small>
																<cfif len(local.qryIssueDetails.autoPublishDate)>
																	This issue will be automatically approved on #DateFormat(local.qryIssueDetails.autoPublishDate, "mm/dd/yyyy")# @ #TimeFormat(local.qryIssueDetails.autoPublishDate, "hh:mm tt")# Central.
																<cfelse>
																	You must manually approve this issue.
																</cfif>
															</small><br>
														</cfcase>
													</cfswitch>
													<small><strong>Issue:</strong> #local.qryIssueDetails.issueTitle# (#local.qryIssueDetails.volumeName#)</small><br>
													<a href="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname##local.editPublicationLink#" style="margin-top:10px;text-decoration: none;color: ##FFF;background-color: ##666;padding: 10px 16px;font-weight: bold;margin-right: 10px;text-align: center;cursor: pointer;display: inline-block;">Open Issue in Control Panel</a>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</cfoutput>
		</cfsavecontent>

		<cfif arguments.emailMode eq "childIssueDistribution">
			<cfset local.subjectLine = "New Issue Added: #local.qryPublication.applicationInstanceName# - #local.qryIssueDetails.issueTitle#">
		<cfelse>
			<cfset local.subjectLine = "Issue Awaiting Approval: #local.qryPublication.applicationInstanceName# - #local.qryIssueDetails.issueTitle#">
		</cfif>

		<cfif local.qryPublication.supportsEmailEditions>
			<cfset local.retStruct = sendIssuePreviewEmailMessage(siteCode=arguments.siteCode, issueID=arguments.issueID, publicationID=arguments.publicationID,
				emailSubject=local.subjectLine, headerHTML=local.issueApprovalHTML)>
			<cfset local.retStruct.success = local.retStruct.success>
		<cfelse>
			<cfset local.arrEmailTo = []>

			<cfset local.strOverrideApprovalEmails = getOverrideApprovalEmailsForPublication(siteID=local.mc_siteinfo.siteID, publicationSiteResourceID=local.qryPublication.siteResourceID)>
			<cfif local.strOverrideApprovalEmails.success>
				<cfset local.arrEmailTo = local.strOverrideApprovalEmails.arrEmailTo>
			<cfelse>
				<cfscript>
					local.toEmailArr = listToArray(local.qryPublication.emailEditionPreviewEmailAddresses,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>
			</cfif>
			
			<cfif arrayLen(local.arrEmailTo)>
				<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=local.mc_siteinfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.mc_siteInfo.networkEmailFrom,
					emailsubject=local.subjectLine,
					emailtitle=local.mc_siteInfo.orgname,
					emailhtmlcontent=local.issueApprovalHTML,
					siteID=local.mc_siteinfo.siteID,
					memberID=local.mc_siteInfo.sysmemberid,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="PUBLICATIONS"),
					sendingSiteResourceID=local.qryPublication.siteResourceID,
					referenceType='publicationIssue',
					referenceID=arguments.issueID
				)>
				<cfset local.retStruct.success = true>
			<cfelse>
				<cfset local.retStruct.success = false>
			</cfif>
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="previewIssueEmailEdition" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="emailConsentListID" type="numeric" required="yes">
		<cfargument name="email" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.strRenderedTemplate = getPreviewIssueTemplateContent(siteID=arguments.siteID, publicationID=arguments.publicationID, issueID=arguments.issueID)>
		<cfset local.strReturn = getPreviewOfIssueEmailEdition(memberID=arguments.memberID, templateContent=local.strRenderedTemplate.content, emailConsentListID=arguments.emailConsentListID, email=arguments.email)>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getPreviewOfIssueEmailEdition" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="templateContent" type="string" required="yes">
		<cfargument name="emailConsentListID" type="numeric" required="yes">
		<cfargument name="email" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode)>
		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>

		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteinfo.orgID, memberID=val(local.memberInfo.memberID), content=arguments.templateContent)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteinfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix,
			prefix=local.memberInfo.Prefix, membernumber=local.memberInfo.membernumber, professionalSuffix=local.memberInfo.professionalSuffix, 
			orgcode=local.memberInfo.orgcode, siteID=local.mc_siteinfo.siteID,
			hostname=local.thisHostname, useRemoteLogin=local.mc_siteinfo.useRemoteLogin, recipientEmail=arguments.email }>
		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>

		<cfset local.strArgs = { content=arguments.templateContent, memberdata=local.tempMemberData, orgcode=local.mc_siteinfo.orgcode, sitecode=local.mc_siteinfo.siteCode }>
		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>
		<cfset local.strReturn = { success=true, content=local.strMergedContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="doSendTestEmailIssueFromTemplates" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="overrideEmailAddress" type="string" required="true">
		<cfargument name="templateContent" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
		<cfsetting requesttimeout="600">
		<cfset local.listID = ''>

		<cfif NOT len(arguments.overrideEmailAddress) OR NOT isValid("regex",arguments.overrideEmailAddress,application.regEx.email)>
			<cfset local.data.success = false>
			<cfreturn local.data>
		</cfif>
		
		<cfset arguments.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.templateContent, siteID=local.mc_siteinfo.siteID,qualURL="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/")>

		<cfquery name="local.qryGetPubIssueInfo" datasource="#application.dsn.membercentral.dsn#">
			SELECT a.applicationInstanceName, i.issueTitle, p.utm_autoappend, p.utm_campaign
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
			INNER JOIN dbo.cms_applicationInstances a ON a.applicationInstanceID=p.applicationInstanceID 
			WHERE i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
		</cfquery>

		<cfif local.qryGetPubIssueInfo.utm_autoappend>
			<cfset local.utmCampaign = len(local.qryGetPubIssueInfo.utm_campaign) ? local.qryGetPubIssueInfo.utm_campaign : local.qryGetPubIssueInfo.applicationInstanceName>
			<cfset arguments.templateContent = application.objEmailWrapper.appendUTMCodesToLinks(
					htmlcontent=arguments.templateContent,
					utm_campaign=local.utmCampaign,
					utm_source="MemberCentralPublications",
					utm_medium="email",
					utm_content=local.qryGetPubIssueInfo.issueTitle
				)>
		</cfif>
		
		<cfquery name="local.qryTestEmailDetails" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int, @publicationID int, @issueID int, @emailSubject varchar(200), @rawContent varchar(max), @contentID int, @siteResourceID int, @sendOnDate datetime, @overrideEmailAddress varchar(200), @supportProviderName varchar(100), @supportProviderEmail VARCHAR(100)
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
				SET @issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
				SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.templateContent#">;
				SET @overrideEmailAddress = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.overrideEmailAddress#">,'');

				SELECT TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
					FROM dbo.networks AS net
					INNER JOIN dbo.networkSites AS ns ON net.networkID = ns.networkID
					INNER JOIN dbo.sites AS s ON s.siteID = ns.siteID
					WHERE s.siteID = @siteID 
					AND ns.isLoginNetwork = 1;
					
				SELECT p.publicationID,
					ISNULL(p.emailEditionSenderName, @supportProviderName) as emailEditionSenderName, 
					ISNULL(p.emailEditionReplyToAddress, @supportProviderEmail)  as emailEditionReplyToAddress,
					sr.siteResourceID as publicationSiteResourceID,
					i.issueID, '**TEST** ' + ai.applicationInstanceName + ' - ' + i.issueTitle as emailSubject,
					i.issueTitle as emailTitle,
					p.emailConsentListID,
					clm.modeName
				FROM dbo.pub_volumes AS v
				INNER JOIN dbo.pub_issues AS i ON i.volumeID = v.volumeID
				INNER JOIN dbo.pub_publications AS p 
					ON p.publicationID = v.publicationID
					and i.issueID = @issueID
				INNER JOIN dbo.cms_applicationInstances AS ai 
					ON ai.applicationInstanceID = p.applicationInstanceID 
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
					AND srs.siteResourceStatusDesc = 'Active'
				LEFT JOIN platformMail.dbo.email_consentLists cl
					ON cl.consentListID = p.emailConsentListID
					AND cl.[status] = 'A'
				LEFT JOIN platformMail.dbo.email_consentListModes clm
					ON clm.consentListModeID = cl.consentListModeID
				LEFT JOIN platformMail.dbo.email_consentListTypes clt
					ON clt.consentListTypeID = cl.consentListTypeID
					AND clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.orgID#">
					AND sr.siteID = @siteID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfif local.qryTestEmailDetails.recordCount AND len(arguments.overrideEmailAddress)>
			<cfif LEN(local.qryTestEmailDetails.modeName) AND local.qryTestEmailDetails.modeName eq "Opt-Out">
				<cfquery name="local.qryGlobalOptOut" datasource="#application.dsn.membercentral.dsn#">
					SELECT TOP 1 cl.consentListID AS globalOptOutListID
					FROM platformMail.dbo.email_consentLists cl
					INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID
						AND clt.consentListTypeName = 'Global Lists'
						AND clt.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.orgID#">
						AND cl.[status] = 'A'
					INNER JOIN platformMail.dbo.email_consentListModes clm
						ON clm.consentListModeID = cl.consentListModeID
						AND clm.modeName = 'GlobalOptOut';
				</cfquery>
				<cfset local.globalOptOutListID = local.qryGlobalOptOut.globalOptOutListID>

				<cfset local.listID = local.qryTestEmailDetails.emailConsentListID>
				<cfset local.modeName = local.qryTestEmailDetails.modeName>
				<cfif not len(local.listID)>
					<cfquery name="local.qryDefaultConsentID" datasource="#application.dsn.membercentral.dsn#">
						SELECT cast(defaultConsentListID as varchar(max)) as defaultConsentListID
						from sites s 
						where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteID#">
					</cfquery>
					<cfset local.listID = local.qryDefaultConsentID.defaultConsentListID>
				</cfif>

				<cfquery name="local.qryInConsentList" datasource="#application.dsn.membercentral.dsn#">
					SELECT 1, cl.consentListName
					FROM platformMail.dbo.email_consentLists cl
					INNER JOIN platformMail.dbo.email_consentListMembers cm ON cl.consentListID = cm.consentListID
					WHERE cl.consentListID IN (
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.listID#">,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.globalOptOutListID#">
					)
					AND cl.[status] = 'A'
					AND cm.email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.overrideEmailAddress#">
				</cfquery>

				<cfif local.qryInConsentList.recordCount>
					<cfset local.data.message =
					"The test message to email #arguments.overrideEmailAddress# " & " will not be sent as it is in the opt-out list: '#local.qryInConsentList.consentListName#'">
				</cfif>
			<cfelseif LEN(local.qryTestEmailDetails.modeName) AND local.qryTestEmailDetails.modeName eq "Opt-In">
				<cfquery name="local.qryInConsentList" datasource="#application.dsn.membercentral.dsn#">
					SELECT 1, cl.consentListName
					FROM platformMail.dbo.email_consentLists cl
					LEFT JOIN platformMail.dbo.email_consentListMembers cm
					ON cm.email = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.overrideEmailAddress#">
					AND cm.consentListID = cl.consentListID
					WHERE cl.consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryTestEmailDetails.emailConsentListID#">
					AND cl.[status] = 'A'
					AND cm.email IS NULL;
				</cfquery>

				<cfif local.qryInConsentList.recordCount>
					<cfset local.data.message =
						"The test message to email #arguments.overrideEmailAddress# " & " will not be sent as it is NOT in the opt-in list: '#local.qryInConsentList.consentListName#'">
				</cfif>
			</cfif>

			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(arguments.overrideEmailAddress,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>
			
			<cfif arrayLen(local.arrEmailTo)>
				<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=local.qryTestEmailDetails.emailEditionSenderName, email=local.mc_siteInfo.networkEmailFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.mc_siteInfo.networkEmailFrom,
					emailsubject=local.qryTestEmailDetails.emailSubject,
					emailtitle=local.qryTestEmailDetails.emailTitle,
					emailhtmlcontent=arguments.templateContent,
					siteID=local.mc_siteinfo.siteID,
					memberID=local.mc_siteInfo.sysmemberid,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="PUBLICATIONS"),
					sendingSiteResourceID=local.qryTestEmailDetails.publicationSiteResourceID,
					referenceType='publicationIssue',
					referenceID=arguments.issueID,
					doWrapEmail=false,
					isTestMessage=1,
					consentListID=local.listID
				)>
				<cfset local.data.success = true>
			<cfelse>
				<cfset local.data.success = false>
			</cfif>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPublicationTemplateFieldsInfo" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.objResourceTemplates = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>

		<cfset arguments.fd = DeserializeJSON(arguments.fd)>
		<cfif not structKeyExists(arguments.fd,"siteID")>
			<cfset arguments.fd['siteID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"publicationID")>
			<cfset arguments.fd['publicationID'] = 0>
		</cfif>
		
		<cfset local.qryTemplates = local.objResourceTemplates.getResourceTemplatesForResourceType(siteID=arguments.fd.siteID, resourceType='publicationAdmin')>

		<cfset local.data['arrTemplates'] = arrayNew(1)>
		
		<cfloop query="local.qryTemplates">
			<cfset local.strTmp = structNew()>
			<cfset local.strTmp['templateID'] = local.qryTemplates.templateID>
			<cfset local.strTmp['displayName'] = local.qryTemplates.displayName>
			
			<cfset local.thisTemplateFieldsXML = local.objCustomFields.getFieldsXML(siteID=local.qryTemplates.siteID, resourceType='EditableTemplate', areaName='Template', csrid=local.qryTemplates.siteResourceID, detailID=0, hideAdminOnly=0)>
			<cfset local.templateFieldsXML = xmlParse(local.thisTemplateFieldsXML.returnXML).xmlRoot>
			
			<cfset local.strTmp['hasTemplateFields'] = arrayLen(local.templateFieldsXML.xmlChildren) gt 0>
			<cfif local.strTmp['hasTemplateFields']>
				<cfset local.strTmp['arrTemplateFields'] = local.objCustomFields.getResourceFieldsArrayFromFieldsXML(itemID=arguments.fd.publicationID, itemType='PubTemplate', usageRT='EditableTemplate', 
						usageAN='Template', csrid=local.qryTemplates.siteResourceID, detailID=0, fieldsXML=local.templateFieldsXML)>
			<cfelse>
				<cfset local.strTmp['arrTemplateFields'] = arrayNew(1)>
			</cfif>
			
			<cfset arrayAppend(local.data['arrTemplates'],local.strTmp)>
		</cfloop>
		
		<cfset local.data['success'] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepareResourceFieldsArr" access="private" output="false" returntype="array">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="resourceFieldsArr" type="array" required="true">
		<cfargument name="objCustomFields" type="any" required="true">

		<cfset var local = structNew()>

		<cfset local.JSONInsanityChars = "^~~~^">
		<cfset local.thisResourceFieldsArr = arrayNew(1)>
		
		<cfloop array="#arguments.resourceFieldsArr#" index="local.thisfield">
			<cfset local.tmpFieldsStr = structNew()>
			<cfset local.tmpFieldsStr['fieldID'] = local.thisfield.xmlattributes.fieldID>
			<cfset local.tmpFieldsStr['itemID'] = arguments.itemID>
			<cfset local.tmpFieldsStr['attributes'] = local.thisfield.xmlattributes>
			<cfset local.tmpFieldsStr['attributes']['fieldText'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldText']>
			<cfset local.tmpFieldsStr['attributes']['fieldReference'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldReference']>
			<cfset local.tmpFieldsStr['dataTypeCode'] = local.thisfield.xmlattributes.dataTypeCode>
			<cfset local.tmpFieldsStr['displayTypeCode'] = local.thisfield.xmlattributes.displayTypeCode>
			<cfset local.tmpFieldsStr['fieldTypeCode'] = local.thisfield.xmlattributes.fieldTypeCode>
			<cfset local.tmpFieldsStr['supportAmt'] = val(local.thisfield.xmlattributes.supportAmt)>
			<cfset local.tmpFieldsStr['supportQty'] = val(local.thisfield.xmlattributes.supportQty)>
			<cfset local.tmpFieldsStr['isRequired'] = val(local.thisfield.xmlattributes.isRequired)>
			<cfset local.tmpFieldsStr['requiredMsg'] = local.thisfield.xmlattributes.requiredMsg>
			<cfset local.tmpFieldsStr['children'] = arrayNew(1)>
			<cfset local.tmpFieldsStr['allOptionEmptyOrDisabled'] = 0>
			<cfset local.tmpFieldsStr['value'] = "">

			<cfif arguments.itemID gt 0>
				<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
					<cfset local.tmpFieldsStr['value'] = arguments.objCustomFields.getFieldOptionsSelected(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
				<cfelse>
					<cfset local.tmpFieldsStr['value'] = arguments.objCustomFields.getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
					<cfif local.thisfield.xmlattributes.displayTypeCode is 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
						<cfset local.tmpFieldsStr['value'] = val(local.tmpFieldsStr['value'])>
					</cfif>
				</cfif>
			</cfif>

			<cfif local.thisfield.xmlattributes.displayTypeCode eq 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
				<cfset local.maxQtyAllowed = 99999>
				<cfif local.thisfield.xmlattributes.fieldInventory gt 0>
					<cfif local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldinventoryCount>
						<cfset local.maxQtyAllowed = 0>
						<cfset local.tmpFieldsStr['isRequired'] = 0>
					<cfelse>
						<cfset local.maxQtyAllowed = local.thisfield.xmlattributes.fieldInventory-local.thisfield.xmlattributes.fieldinventoryCount>
						<!--- edit case of non monetary qty-field --->
						<cfif local.thisfield.xmlattributes.supportAmt is 0 and val(local.tmpFieldsStr['value']) gt 0>
							<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				</cfif>
				<cfset local.tmpFieldsStr['maxQtyAllowed'] = local.maxQtyAllowed>
			</cfif>

			<!--- dont show question at all if select,radio,checkbox and no options defined --->
			<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
				<cfif arrayLen(local.thisfield.xmlchildren) is 0>
					<cfset local.tmpFieldsStr.allOptionEmptyOrDisabled = 1>
				<cfelse>
					<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
						<cfset local.tmpOptionStr = structNew()>
						<cfset local.tmpOptionStr['attributes'] = local.thisoption.xmlattributes>
						<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.JSONInsanityChars & local.tmpOptionStr['attributes']['fieldValue']>
						<cfset local.tmpOptionStr['unavailable'] = 0>
						
						<!--- skip unavailability check for an already selected option --->
						<cfif len(local.tmpFieldsStr['value']) and listFind(local.tmpFieldsStr['value'],local.thisoption.xmlattributes.valueID)>
							
						<cfelseif local.thisoption.xmlattributes.optionInventory gt 0 and local.thisoption.xmlattributes.optionInventory lte local.thisoption.xmlattributes.optioninventoryCount>
							<cfset local.tmpOptionStr.unavailable = 1>
						</cfif>
						<cfset arrayAppend(local.tmpFieldsStr.children, local.tmpOptionStr)>
					</cfloop>

					<cfif local.tmpFieldsStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpFieldsStr['value'])>
						<cfset local.tmpFieldsStr['value'] = listToArray(local.tmpFieldsStr['value'])>
					</cfif>
				</cfif>
			<!--- append json insanity vars for other display type values --->
			<cfelseif len(local.tmpFieldsStr['value'])>
				<cfset local.tmpFieldsStr['value'] = local.JSONInsanityChars & local.tmpFieldsStr['value']>
			</cfif>
	
			<cfset arrayAppend(local.thisResourceFieldsArr, local.tmpFieldsStr)>
		</cfloop>
		
		<cfreturn local.thisResourceFieldsArr>
	</cffunction>

	<cffunction name="getNetworksBySiteID" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryNetworks = structNew()>

		<cfquery name="qryNetworks" datasource="#application.dsn.memberCentral.dsn#">
			SELECT DISTINCT n.networkID, n.networkName
			FROM dbo.networks AS n
			INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
			WHERE ns.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			ORDER BY n.networkName
		</cfquery>

		<cfreturn qryNetworks>
	</cffunction>

	<cffunction name="enableSyndicatedParentSetting" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="syndicationNetworkID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="manageSyndication")>
				<cfthrow message="invalid request">
			</cfif>
		
			<cfquery name="local.qryUpdatePublication" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.pub_publications
				SET isParentPublication = 1,
					syndicationNetworkID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.syndicationNetworkID#">,0)
				WHERE publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateSyndicationNetwork" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="syndicationNetworkID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="manageSyndication")>
				<cfthrow message="invalid request">
			</cfif>
			<cfquery name="qryUpdateSyndicationNetwork" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.pub_publications
				SET syndicationNetworkID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.syndicationNetworkID#">,0)
				WHERE publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="disableSyndicatedParentSetting" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="manageSyndication")>
				<cfthrow message="invalid request">
			</cfif>
		
			<cfquery name="qryUpdatePublication" datasource="#application.dsn.membercentral.dsn#">
				DECLARE @publicationID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
				
				IF NOT EXISTS(SELECT 1 FROM dbo.pub_publications WHERE ISNULL(parentPublicationID,0) = @publicationID)
					UPDATE dbo.pub_publications
					SET isParentPublication = 0, syndicationNetworkID = NULL
					WHERE publicationID = @publicationID;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSelectableNetworksForParentPublication" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfquery name="local.qrySelectableNetworks" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID INT, @referencesCount INT;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			DECLARE @crossSiteReferences TABLE (siteID INT);

			INSERT INTO @crossSiteReferences (siteID)
			SELECT DISTINCT s.siteID
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
			WHERE isnull(p.parentPublicationID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			AND s.siteID <> @siteID;

			IF EXISTS (SELECT 1 FROM @crossSiteReferences) BEGIN
				INSERT INTO @crossSiteReferences VALUES (@siteID);

				SELECT @referencesCount = COUNT(siteID) FROM @crossSiteReferences;

				SELECT DISTINCT n.networkID, n.networkName
				FROM dbo.networks AS n
				INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				INNER JOIN @crossSiteReferences AS s ON s.siteID = ns.siteID
				WHERE n.networkID <> 1
				GROUP BY n.networkID, n.networkName
				HAVING COUNT(ns.siteID) = @referencesCount
				ORDER BY n.networkName;
			END
			ELSE BEGIN
				SELECT DISTINCT n.networkID, n.networkName
				FROM dbo.networks AS n
				INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				WHERE ns.siteID = @siteID
				ORDER BY n.networkName
			END

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qrySelectableNetworks>
	</cffunction>

	<cffunction name="setAsSyndicatedChild" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="parentPublicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="manageSyndication")>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="pub_setPublicationAsSyndicatedChild">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.parentPublicationID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
			</cfstoredproc>	
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="disconnectSyndicatedChild" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.siteResourceID, permission="manageSyndication")>
				<cfthrow message="invalid request">
			</cfif>
		
			<cfquery name="qryUpdatePublication" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @publicationID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

					BEGIN TRAN;
						-- removes from sources if it is cross-site syndicated publication
						DELETE ps
						FROM dbo.pub_publicationSources AS ps
						INNER JOIN dbo.pub_publications AS p ON p.publicationID = ps.publicationID
						INNER JOIN dbo.pub_publications AS pp ON pp.publicationID = ISNULL(p.parentPublicationID,0)
							AND ps.sourceApplicationInstanceID = pp.applicationInstanceID
						INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = pp.applicationInstanceID
							AND ai.siteID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						WHERE ps.publicationID = @publicationID;

						UPDATE dbo.pub_publications
						SET parentPublicationID = NULL
						WHERE publicationID = @publicationID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getParentPublications" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfquery name="local.qryParentPublications" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID INT, @siteID INT;
			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT s.siteCode, s.siteName, p.publicationID, ai.applicationInstanceName, 0 AS isCrossSite
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
				AND ai.siteID = @siteID
			INNER JOIN dbo.sites as s on s.siteID = ai.siteID
			WHERE p.publicationID <> @publicationID
			AND p.isParentPublication = 1
			AND ISNULL(p.parentPublicationID,0) <> @publicationID
				UNION
			SELECT s.siteCode, s.siteName, p.publicationID, ai.applicationInstanceName, 1 AS isCrossSite
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
				AND ai.siteID <> @siteID
			INNER JOIN dbo.sites as s on s.siteID = ai.siteID
			INNER JOIN dbo.networks AS n ON n.networkID = p.syndicationNetworkID
			INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				AND ns.siteID = @siteID
			WHERE p.isParentPublication = 1
			AND ISNULL(p.parentPublicationID,0) <> @publicationID
			ORDER BY siteName, applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryParentPublications>
	</cffunction>

	<cffunction name="addArticlesToChildPublications" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qryPopulateQueue" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @statusReady int, @parentSiteID int, @parentPublicationID int, @parentPublicationIssueID int,
				@enteredByMemberID int, @nowDate datetime = GETDATE();
			SET @parentSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @parentPublicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			SET @parentPublicationIssueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
			SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">;
			EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='pubSyndIssueDist', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

			INSERT INTO platformQueue.dbo.queue_pubSyndIssueDist (parentSiteID, parentPublicationID, parentPublicationIssueID, childSiteID, childPublicationID,
				enteredByMemberID, statusID, dateAdded, dateUpdated)
			SELECT @parentSiteID, @parentPublicationID, @parentPublicationIssueID, ai.siteID, p.publicationID, @enteredByMemberID, @statusReady, @nowDate, @nowDate
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			WHERE ISNULL(p.parentPublicationID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			IF @@ROWCOUNT > 0
				EXEC dbo.sched_resumeTask @name='Process Syndicated Issue Distribution Queue', @engine='MCLuceeLinux';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getChildPublications" access="public" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfquery name="local.qryParentPublications" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID INT, @publishedStatusID INT, @environmentID INT, @environmentName VARCHAR(50);

			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			SET @environmentName = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;
			SELECT @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;
			SELECT @publishedStatusID = issueStatusID FROM dbo.pub_statuses WHERE statusName = 'Published';

			SELECT s.siteCode, ai.applicationInstanceName AS publicationName,
				p.autoApproveIssues, autoApproveTime,
				approvalDays = STUFF((
						SELECT ', ' + CAST (ad.[weekDay] AS VARCHAR(5))
						FROM dbo.pub_publicationAutoApprovalDays ad
						WHERE ad.publicationID = p.publicationID
						ORDER BY ad.weekDay
						FOR XML PATH ('')
				),1,1,''),
				(SELECT TOP 1 updateDate 
					FROM pub_issueStatusHistory AS h
					INNER JOIN dbo.pub_issues AS i ON i.issueID = h.issueID
						AND i.issueStatusID = @publishedStatusID
					INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
						AND v.publicationID = p.publicationID
					order by updatedate desc
				) AS lastEditionPublishedDate,
				(SELECT COUNT(ii.issueItemID)
					FROM dbo.pub_issueItems AS ii
					INNER JOIN dbo.pub_issues AS i ON i.issueID = ii.issueID
						AND issueStatusID <> @publishedStatusID
					INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
						AND v.publicationID = p.publicationID
				) AS itemsInUnreleasedIssuesCount,
				case when shn.hasssl = 1 then 'https' else 'http' end + '://'+ shn.hostname + '/?pg=admin&jumpToTool=PublicationAdmin%7ClistPublications%7CeditPublication&pID=' + CAST(p.publicationID AS varchar(20)) AS publicationAdminURL
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
			INNER JOIN dbo.siteHostNames AS shn ON shn.siteID = s.siteID
			INNER JOIN dbo.siteEnvironments se ON se.siteID=shn.siteID
				AND se.environmentID = @environmentID
				AND shn.hostNameID = se.mainHostnameID
			WHERE p.parentPublicationID = @publicationID
			ORDER BY s.siteCode, applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryParentPublications>
	</cffunction>

	<cffunction name="getPreviewEmailEditionRecipients" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="limitCount" type="numeric" required="false" default="100">

		<cfset var qryConsentListModeName = "">
		<cfset var qryRecipients = "">

		<cfquery name="qryConsentListModeName" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID int;
			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			SELECT clm.modeName
			FROM dbo.pub_publications p
			INNER JOIN platformMail.dbo.email_consentLists cl
				on cl.consentListID = p.emailConsentListID
				AND cl.[status] = 'A'
			INNER JOIN platformMail.dbo.email_consentListModes clm
				on clm.consentListModeID = cl.consentListModeID
			WHERE p.publicationID = @publicationID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.ReceiveEditionEmailsPublicationsRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Publications", functionName="ReceiveEditionEmails")>

		<cfquery name="qryRecipients" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @siteID int, @publicationID int, @fID int, @emailConsentListID int, @limitCount int, 
				@globalOptOutListID int, @randomModFactor int = datepart(SECOND,getdate());
			SET @limitCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.limitCount))#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			SET @fID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ReceiveEditionEmailsPublicationsRFID#">;

			IF OBJECT_ID('tempdb..##tmpPubTagTypes') IS NOT NULL
				DROP TABLE ##tmpPubTagTypes;
			IF OBJECT_ID('tempdb..##tmpMemberPool') IS NOT NULL
				DROP TABLE ##tmpMemberPool;
			IF OBJECT_ID('tempdb..##tmpMemberPoolTags') IS NOT NULL
				DROP TABLE ##tmpMemberPoolTags;
			IF OBJECT_ID('tempdb..##tmpOptOuts') IS NOT NULL
				DROP TABLE ##tmpOptOuts;
			IF OBJECT_ID('tempdb..##tmpOptIns') IS NOT NULL
				DROP TABLE ##tmpOptIns;
			CREATE TABLE ##tmpPubTagTypes (emailTagTypeID int PRIMARY KEY);
			CREATE TABLE ##tmpMemberPool (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpMemberPoolTags (memberID int PRIMARY KEY, email varchar(500));
			CREATE TABLE ##tmpOptOuts (email varchar(500) PRIMARY KEY);
			CREATE TABLE ##tmpOptIns (email varchar(500) PRIMARY KEY);

			SELECT @emailConsentListID = p.emailConsentListID
			FROM dbo.pub_publications p
			WHERE p.publicationID = @publicationID;

			SELECT TOP 1 @globalOptOutListID = cl.consentListID
			FROM platformMail.dbo.email_consentLists cl
			INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID 
				AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
			INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID 
				AND modeName = 'GlobalOptOut'
			WHERE cl.[status] = 'A';

			INSERT INTO ##tmpPubTagTypes (emailTagTypeID)
			SELECT pet.emailTagTypeID
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.pub_publicationEmailTagTypes as pet on pet.publicationID = p.publicationID
			WHERE p.publicationID = @publicationID;

			<cfif qryConsentListModeName.modeName eq 'Opt-Out'>
				insert into ##tmpOptOuts (email)
				select distinct email
				from platformMail.dbo.email_consentListMembers
				where consentListID IN (@emailConsentListID,@globalOptOutListID);
			</cfif>

			<cfif qryConsentListModeName.modeName eq 'Opt-In'>
				insert into ##tmpOptIns (email)
				select distinct email
				from platformMail.dbo.email_consentListMembers
				where consentListID = @emailConsentListID;
			</cfif>

			INSERT INTO ##tmpMemberPool (memberID)
			select m.memberID
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
				and ai.applicationInstanceID = p.applicationInstanceID
				and p.publicationID = @publicationID
			INNER JOIN dbo.cms_siteResources AS sr on sr.siteID = @siteID
				AND sr.siteResourceID = ai.siteResourceID 
				and sr.siteResourceStatusID = 1
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				AND srfrp.siteResourceID = sr.siteResourceID 
				AND srfrp.functionID = @fID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
				AND m.groupPrintID = gprp.groupPrintID
				AND m.memberID = m.activeMemberID;

			insert into ##tmpMemberPoolTags (memberID, email)
			SELECT TOP (@limitCount) m.memberID, me.email
			FROM ##tmpMemberPool as m
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.memberID = m.memberID
			INNER JOIN ##tmpPubTagTypes as tag on tag.emailTagTypeID = metag.emailTagTypeID
			INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
				AND me.memberID = m.memberID
				AND me.emailTypeID = metag.emailTypeID
				and me.email <> ''
			<cfif qryConsentListModeName.modeName eq 'Opt-In'>
				inner join ##tmpOptIns as optins on optins.email = me.email
			<cfelseif qryConsentListModeName.modeName eq 'Opt-Out'>
				left outer join ##tmpOptOuts as optouts on optouts.email = me.email
				where optouts.email is null
			</cfif>
			ORDER BY NEWID();

			SELECT m.memberID, m.lastName + ' ' + m.firstName as memberName, m.memberNumber, m.company, mptag.email
			FROM ##tmpMemberPool as mp
			INNER JOIN ##tmpMemberPoolTags as mptag on mptag.memberID = mp.memberID
			inner join dbo.ams_members m ON m.orgID = @orgID and m.memberID = mp.memberID
			ORDER BY m.lastName, m.firstName, m.memberNumber;

			IF OBJECT_ID('tempdb..##tmpPubTagTypes') IS NOT NULL
				DROP TABLE ##tmpPubTagTypes;
			IF OBJECT_ID('tempdb..##tmpMemberPool') IS NOT NULL
				DROP TABLE ##tmpMemberPool;
			IF OBJECT_ID('tempdb..##tmpMemberPoolTags') IS NOT NULL
				DROP TABLE ##tmpMemberPoolTags;
			IF OBJECT_ID('tempdb..##tmpOptOuts') IS NOT NULL
				DROP TABLE ##tmpOptOuts;
			IF OBJECT_ID('tempdb..##tmpOptIns') IS NOT NULL
				DROP TABLE ##tmpOptIns;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRecipients>
	</cffunction>

	<cffunction name="getIssueItemDetails" access="public" output="false" returntype="query">
		<cfargument name="issueItemID" type="numeric" required="true">

		<cfset qryIssueItemDetails = "">

		<cfquery name="qryIssueItemDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT it.issueItemID, it.categoryID, it.itemPosition, i.issueID, v.volumeID, v.publicationID
			FROM dbo.pub_issueItems AS it
			INNER JOIN dbo.pub_issues AS i ON i.issueID = it.issueID
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			WHERE it.issueItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryIssueItemDetails>
	</cffunction>

	<cffunction name="getScheduledIssueItemDetails" access="public" output="false" returntype="query">
		<cfargument name="scheduledItemID" type="numeric" required="true">

		<cfset qryScheduledIssueItemDetails = "">

		<cfquery name="qryScheduledIssueItemDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT scheduledItemID, startDate, endDate, categoryID
			FROM dbo.pub_issueItemsScheduled
			WHERE scheduledItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduledItemID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryScheduledIssueItemDetails>
	</cffunction>

	<cffunction name="getScheduledIssueItems" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledIssueItems" result="local.qryScheduledIssueItemsResult">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID int, @totalCategories int, @totalCount int;
			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pid',0)#">;

			IF OBJECT_ID('tempdb..##tmpScheduledIssueItems') IS NOT NULL
				DROP TABLE ##tmpScheduledIssueItems;
			CREATE TABLE ##tmpScheduledIssueItems (scheduledItemID int, startDate date, endDate date, source varchar(100), 
				contentType varchar(20), title varchar(1000), blogStatusName varchar(50), blogEntryID int, blogID int, 
				siteResourceID int, postDate datetime, enteredByMemberID int, enteredByMemberName varchar(100), 
				categoryName varchar(200), categoryID int, isCrossSiteArticle bit, hasImage BIT);

			IF OBJECT_ID('tempdb..##tmpIssueCategories') IS NOT NULL
				DROP TABLE ##tmpIssueCategories;
			CREATE TABLE ##tmpIssueCategories (categoryID int, categoryPath varchar(500));

			INSERT INTO ##tmpScheduledIssueItems (scheduledItemID, startDate, endDate, source, contentType, title, 
				blogStatusName, blogEntryID, blogID, siteResourceID, postDate, enteredByMemberID, enteredByMemberName, 
				categoryName, categoryID, isCrossSiteArticle, hasImage)
			SELECT its.scheduledItemID, its.startDate, its.endDate,
				CASE WHEN its.sourceIssueItemID IS NOT NULL THEN sp_ai.applicationInstanceName ELSE b_ai.applicationInstanceName END AS source,
				pt.typeName, be.blogTitle, bs.statusName, be.blogEntryID, be.blogID, its.itemSiteResourceID, be.postDate, 
				its.enteredByMemberID, m.firstName + ' ' + m.lastName, c.categoryName, c.categoryID, 
				CASE WHEN its.sourceIssueItemID IS NOT NULL AND p_ai.siteID <> sp_ai.siteID THEN 1 ELSE 0 END AS isCrossSiteArticle,
				case when fiu.featureImageUsageID is not null then 1 else 0 end
			FROM dbo.pub_issueItemsScheduled AS its
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = its.publicationID
			INNER JOIN dbo.cms_applicationInstances AS p_ai ON p_ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.bl_entry AS be ON be.siteResourceID = its.itemSiteResourceID
			INNER JOIN dbo.bl_statuses AS bs on bs.statusID = be.statusID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.bl_blog AS b ON b.blogID = be.blogID
			INNER JOIN dbo.cms_applicationInstances AS b_ai ON b_ai.applicationInstanceID = b.applicationInstanceID
			INNER JOIN dbo.cms_postTypes AS pt ON pt.postTypeID = be.postTypeID
			INNER JOIN dbo.ams_members AS m ON m.memberid = its.enteredByMemberID
			INNER JOIN dbo.cms_categories AS c ON c.categoryID = its.categoryID
			LEFT OUTER JOIN dbo.pub_issues as si
				INNER JOIN dbo.pub_volumes as sv ON sv.volumeID = si.volumeID
				INNER JOIN dbo.pub_publications as sp ON sp.publicationID = sv.publicationID
				INNER JOIN dbo.cms_applicationInstances AS sp_ai ON sp_ai.applicationInstanceID = sp.applicationInstanceID
				ON si.issueID = its.sourceIssueID
			LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu on fiu.referenceID = its.scheduledItemID AND fiu.referenceType = 'publicationScheduledItem'
			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu on ficu.referenceID = p.publicationID AND ficu.referenceType = 'publicationIssueItem'
			WHERE its.publicationID = @publicationID;

			SELECT @totalCount = @@ROWCOUNT;

			INSERT INTO ##tmpIssueCategories (categoryID, categoryPath)
			SELECT DISTINCT c.categoryID, c.categoryPath
			FROM ##tmpScheduledIssueItems as tmp
			INNER JOIN dbo.cms_categories AS c ON c.categoryID = tmp.categoryID;

			SET @totalCategories = @@ROWCOUNT;

			SELECT ii.scheduledItemID, ii.startDate, ii.endDate, ii.source, ii.contentType, ii.title, 
				ii.blogStatusName, ii.blogEntryID, ii.blogID, ii.siteResourceID, ii.enteredByMemberID, 
				ii.enteredByMemberName, ii.categoryID, ii.categoryName, ic.categoryPath, ii.isCrossSiteArticle,
				ii.hasImage, @totalCategories as totalCategories, @totalCount AS totalCount
			FROM ##tmpScheduledIssueItems ii
			INNER JOIN ##tmpIssueCategories ic on ii.categoryID = ic.categoryID
			ORDER BY ii.categoryName, ii.title;

			IF OBJECT_ID('tempdb..##tmpScheduledIssueItems') IS NOT NULL
				DROP TABLE ##tmpScheduledIssueItems;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryScheduledIssueItems>
	</cffunction>

	<cffunction name="scheduleIssueItem" access="public" output="false" returntype="struct">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="itemSiteResourceID" type="numeric" required="true">
		<cfargument name="sourceIssueItemID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="schedItemStartDate" type="string" required="true">
		<cfargument name="schedItemEndDate" type="string" required="true">
		<cfargument name="addToDraftIssues" type="boolean" required="true">

		<cfset var scheduledItemID = 0>
		
		<cfstoredproc procedure="pub_scheduleIssueItem" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.itemSiteResourceID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#arguments.schedItemStartDate#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#arguments.schedItemEndDate#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceIssueItemID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.addToDraftIssues#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="scheduledItemID">
		</cfstoredproc>

		<cfreturn { "success":true, "scheduledItemID":scheduledItemID }>
	</cffunction>

	<cffunction name="updateScheduledItemSettings" access="public" output="false" returntype="struct">
		<cfargument name="scheduledItemID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="schedItemStartDate" type="string" required="true">
		<cfargument name="schedItemEndDate" type="string" required="true">
		<cfargument name="addToDraftIssues" type="boolean" required="true">

		<cfstoredproc procedure="pub_updateScheduledIssueItem" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduledItemID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#arguments.schedItemStartDate#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#arguments.schedItemEndDate#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.addToDraftIssues#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>

		<cfreturn { "success":true, "scheduledItemID":arguments.scheduledItemID }>
	</cffunction>

	<cffunction name="deleteScheduledItem" access="public" output="false" returntype="struct">
		<cfargument name="scheduledItemID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var qryDeleteScheduledItem = "">

		<cfquery name="qryDeleteScheduledItem" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @scheduledItemID int, @publicationID int ;
				SET @scheduledItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduledItemID#">;
				SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

				BEGIN TRAN;
					DELETE FROM dbo.pub_issueItemsScheduled
					WHERE scheduledItemID = @scheduledItemID
					AND publicationID = @publicationID;

					EXEC dbo.cms_deleteFeaturedImageUsage @referenceID=@scheduledItemID, @referenceType='publicationScheduledItem';
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="getBlogsOnSiteWithAddPermissionForPublication" access="public" returntype="array">
		<cfargument name="siteID" type="numeric" required="yes"/>
		<cfargument name="publicationID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>

		<cfset local.arrBlogsWithAddPostRights = CreateObject("component","model.admin.blogs.blog").getBlogsOnSiteByPermission(functionName="AddBlog", siteID=arguments.siteID)>

		<cfset var qryConnectedBlogsOnSite = "">
		<cfquery name="qryConnectedBlogsOnSite" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT bl.blogID
			FROM dbo.pub_publicationSources as ps
			INNER JOIN dbo.pub_publications as p on p.publicationID = ps.publicationID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = ps.sourceApplicationInstanceID
			INNER JOIN dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.bl_blog as bl on bl.applicationInstanceID = ps.sourceApplicationInstanceID
			WHERE ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND p.publicationID = <cfqueryparam value="#arguments.publicationID#" cfsqltype="CF_SQL_INTEGER">
			AND at.applicationTypeName = 'Blog';
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrBlogs = arrBlogsWithAddPostRights.filter(function(thisRow) { return listFindNoCase(valueList(qryConnectedBlogsOnSite.blogID), arguments.thisRow.blogID); })>

		<cfreturn local.arrBlogs />
	</cffunction>

	<cffunction name="getPublicationIDFromIssueID" access="public" output="false" returntype="numeric">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var qryPublication = "">
		
		<cfquery name="qryPublication" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT v.publicationID
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			WHERE i.issueID = <cfqueryparam value="#arguments.issueID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryPublication.publicationID)>
	</cffunction>

	<cffunction name="getPublicationSendingHistorySummary" access="public" output="false" returntype="query">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryPublicationSummary" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @issueID int = <cfqueryparam value="#arguments.issueID#" cfsqltype="CF_SQL_INTEGER">, @messageID int, @messagecount int, @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">, @orgID int;

			select @orgID = orgID from sites where siteID = @siteID

			select @messagecount=count(*), @messageID=max(m.messageID)
			from platformmail.dbo.email_messages m
			WHERE m.siteID=@siteID
				and m.[status]='A'
				and m.referenceType='publicationIssue' 
				and m.referenceID = @issueID

			SELECT @messageCount AS messageCount, e.sendOnDate, mActive.firstName + ' ' + mActive.lastName as recentSentByMember
			FROM platformmail.dbo.email_messages AS e
			LEFT OUTER JOIN dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS mActive 
					ON mActive.orgID = m.orgID 
					and mActive.memberID = m.activeMemberID
				ON m.orgID in (1,@orgID) and m.memberid = e.recordedByMemberID
			WHERE e.siteID = @siteID 
			and m.[status]='A'
			and e.messageID = @messageID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryPublicationSummary>
	</cffunction>

	<cffunction name="hasPublicationRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="permission" type="string" required="true">

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn structKeyExists(tmpRights, arguments.permission) AND tmpRights[arguments.permission] is 1>
	</cffunction>
	<cffunction name="getPublicationsOnSiteByPermission" access="public" returntype="array">
		<cfargument name="functionName" type="string" required="yes"/>
		<cfargument name="siteID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		<cfset local.arrPublicationsWithRights = arrayNew()>
		
		<cfquery name="local.qryPublicationsOnSite" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @RTID int, @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT p.publicationid, ai.siteResourceID,
				ai.applicationInstanceName as publicationName
					
			FROM dbo.pub_publications AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
				AND ai.siteID = @siteID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
			LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
				INNER JOIN dbo.cms_applicationInstances AS CommunityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
				ON grandparentResource.siteID = @siteID 
					AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					AND grandparentResource.resourceTypeID = @RTID
			ORDER BY ai.applicationInstanceName, communityInstances.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryPublicationsOnSite">
			<cfset local.tmpBlogRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryPublicationsOnSite.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>			
			<cfif local.tmpBlogRights[arguments.functionName]>
				<cfset arrayAppend(local.arrPublicationsWithRights, { publicationid=local.qryPublicationsOnSite.publicationid, publicationName=local.qryPublicationsOnSite.publicationName })>
			</cfif>
		</cfloop>

		<cfreturn local.arrPublicationsWithRights />
	</cffunction>
	
	<cffunction name="getublicationsForFilters" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryPublications = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryPublications">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpPublications') IS NOT NULL
				DROP TABLE ##tmpPublications;
			CREATE TABLE ##tmpPublications (publicationID int,  publicationName varchar(100), row int);

			DECLARE @siteID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">;
			

			INSERT INTO ##tmpPublications (publicationID, publicationName, row)
			select publicationID, publicationName, ROW_NUMBER() OVER (ORDER BY publicationName asc) as row
			from
				(
				select p.publicationID, ai.applicationInstanceName as publicationName
				from 
					dbo.pub_publications as p
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
				inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID 
					and at.applicationTypeName = 'publications'
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where ai.siteID = @siteID) as  tmp;
			

			SELECT tmp.publicationID, tmp.publicationName 
			FROM ##tmpPublications AS tmp
			
			ORDER BY tmp.row;

			IF OBJECT_ID('tempdb..##tmpPublications') IS NOT NULL
				DROP TABLE ##tmpPublications;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPublications>
	</cffunction>

	<cffunction name="schedulePublish" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="publicationID" type="numeric" required="true">
		<cfargument name="issueID" type="numeric" required="true">	
		<cfargument name="autoPublishDate" type="string" required="true">
		<cfargument name="bit_send_email" type="boolean" required="true">	
			
		<cfset var local = structNew()>
		<cfset local.data = { "success":true, "errmsg":"" }>
		
		<cftry>				
			<cfif arguments.issueID gt 0>
				<cfset updateIssueSendEmailSetting(issueID=arguments.issueID, bit_send_email = arguments.bit_send_email )>
				<cfset updateIssueAutoPublishDate(issueID=arguments.issueID, autoPublishDate = arguments.autoPublishDate )>
				<cfset local.qryIssueDetails = getIssueDetails(publicationID=arguments.publicationID, issueID=arguments.issueID)>			
				<cfif local.qryIssueDetails.statusName NEQ "Approved">
					<cfset local.data= approveIssue(mcproxy_siteID=arguments.mcproxy_siteID, mcproxy_siteCode =arguments.mcproxy_siteCode, issueID=arguments.issueID, publicationID=arguments.publicationID )>	
				</cfif>	
			</cfif>		
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="updateIssueSendEmailSetting" access="public" output="true" returntype="void" hint="Update a volume">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="bit_send_email" type="boolean" required="true">	
		
		<cfset var local = structNew()>
		<cfquery name="local.qryUpdateIssueSendEmailSetting" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.pub_issues
			SET bit_send_email = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.bit_send_email#">
			WHERE 
			issueID= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">;
		</cfquery>
	</cffunction>
	<cffunction name="updateIssueAutoPublishDate" access="public" output="true" returntype="void" hint="Update a volume">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="autoPublishDate" type="string" required="true">	
		
		<cfset var local = structNew()>
		<cfset local.autoPublishDate = trim(replace(arguments.autoPublishDate,' - ',' '))>
			<cfif len(local.autoPublishDate) gt 0>
				<cfset local.autoPublishDate = ParseDateTime(local.autoPublishDate)>
			<cfelse>
				<cfset local.autoPublishDate =''>	
			</cfif>
			<cfquery name="local.qryUpdateIssueAutoPublishDate" datasource="#application.dsn.membercentral.dsn#">			
				UPDATE dbo.pub_issues 
				SET autoPublishDate = 
				<cfif len(local.autoPublishDate) gt 0>
					<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.autoPublishDate#" >
				<cfelse>
					<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.autoPublishDate#" null="true">	
				</cfif>
				WHERE issueID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.issueID#">		
			</cfquery>
	</cffunction>
	
	<cffunction name="resendEmailEdition" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="issueID" type="numeric" required="true">
		<cfargument name="publicationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "errmsg":"", "norecipients":0 }>

		<cftry>
			<cfset local.qryPublicationDetails = getPublicationDetails(siteID=arguments.mcproxy_siteID, publicationID=arguments.publicationID)>	
			<cfif not hasPublicationRights(siteID=arguments.mcproxy_siteID, siteResourceID=local.qryPublicationDetails.siteResourceID, permission="ApproveEditions")>
				<cfset local.data.success = false>
				<cfset local.data.errmsg = "You do not have rights to perform this operation.">
				<cfreturn local.data>
			</cfif>

			<cfset local.qryIssueDetails = getIssueDetails(publicationID=arguments.publicationID, issueID=arguments.issueID)>			
			<cfif local.qryPublicationDetails.supportsEmailEditions>
				<cftry>
					<cfset local.emailResult = sendEmailEdition(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, issueID=arguments.issueID, publicationID=arguments.publicationID)>
					
					<cfif not local.emailResult.success and local.emailResult.norecipients>
						<cfset local.data.norecipients = 1>
					<cfelseif not local.emailResult.success>
						<cfset local.data.errmsg = "Issue Publishing Failed due to problem rendering issue. Status Changed to Approved">
						<cfset local.data.success = false>
						<cfthrow message="#local.data.errmsg#">
					<cfelseif local.emailResult.success>
						<cfset local.data.success = true>	
					</cfif>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump={emailResult=local.emailResult, qryIssueDetails=local.qryIssueDetails}, customMessage="Issue Publishing Failed due to problem rendering issue. Status Changed to Approved")>
					</cfcatch>
				</cftry>
			</cfif>									
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMatchingScheduledIssueItemCount" access="public" output="true" returntype="numeric">
		<cfargument name="issueID" type="numeric" required="true">
		
		<cfset var qryMatchingScheduledIssueItems = "">
		
		<cfquery name="qryMatchingScheduledIssueItems" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @issueID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">,
				@publicationID int, @issueDate datetime;

			SELECT @publicationID = v.publicationID, @issueDate = i.issueDate
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID
			WHERE i.issueID = @issueID;

			SELECT COUNT(DISTINCT its.itemSiteResourceID) AS itemCount
			FROM dbo.pub_issueItemsScheduled AS its
			LEFT OUTER JOIN dbo.pub_issueItems AS it ON it.issueID = @issueID
				AND it.itemSiteResourceID = its.itemSiteResourceID
			WHERE its.publicationID = @publicationID
			AND @issueDate BETWEEN its.startDate AND its.endDate
			AND it.issueItemID IS NULL;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn val(qryMatchingScheduledIssueItems.itemCount)>
	</cffunction>

	<cffunction name="addMatchingScheduledIssueItems" access="public" output="false" returntype="struct">
		<cfargument name="issueID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false }>

		<cftry>
			<cfquery name="local.qryAddMatchingScheduledIssueItems" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.pub_addMatchingScheduledIssueItems
					@issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">,
					@enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
</cfcomponent>