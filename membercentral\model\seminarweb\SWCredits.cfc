<cfcomponent>
	<cffunction name="getCreditsforSeminarList" access="public" returntype="query" output="no">
		<cfargument name="seminarIDList" type="string" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditsforSeminarList" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarIDList#" null="No">
			<cfprocresult name="local.qryCredits" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryCredits>
	</cffunction>

	<cffunction name="getCreditsforSeminar" access="public" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditsforSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#" >
			<cfprocresult name="local.qryCredit" resultset="1">
		</cfstoredproc>
		
		<cfquery name="local.qryCreditDistinct" dbtype="query">
			select distinct authorityID, authorityCode
			from [local].qryCredit
			order by authorityCode
		</cfquery>

		<cfreturn local>
	</cffunction>

	<cffunction name="getCreditsforBundle" access="public" returntype="struct" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditsforBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
			<cfprocresult name="local.qryCredit" resultset="1">
		</cfstoredproc>
		
		<cfquery name="local.qryCreditDistinct" dbtype="query">
			select distinct authorityID, authorityCode
			from [local].qryCredit
			order by authorityCode
		</cfquery>
		
		<cfreturn local>
	</cffunction>

	<cffunction name="getCreditsGrid" access="public" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="restrictByCompletionDate" type="string" required="no" default="">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditsGrid" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
			<cfif isDate(arguments.restrictByCompletionDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.restrictByCompletionDate#" null="No">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="Yes">
			</cfif>
			<cfprocresult name="local.qryCreditsGrid" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryCreditsGrid>
	</cffunction>

	<cffunction name="getCreditsFromWDDX" access="public" returntype="query" output="no">
		<cfargument name="creditTypes" type="any" required="yes">
		<cfargument name="creditsAvailable" type="any" required="yes">
		<cfargument name="withCreditsOnly" type="boolean" required="yes">

		<!--- we cant assume the numcredits is a value -- it has been used as a string. --->

		<cfset var local = structnew()>
		<cfset local.arrCreditTypes = ArrayNew(1)>
		<cfset local.arrCreditsAvailable = ArrayNew(1)>
		<cfset local.qryCredits = QueryNew("fieldname,displayname,numcredits","varchar,varchar,varchar")>

		<cfif isWddx(arguments.creditTypes)>
			<cfwddx action="WDDX2CFML" input="#arguments.creditTypes#" output="local.arrCreditTypes">
		</cfif>
		<cfif isWddx(arguments.creditsAvailable)>
			<cfwddx action="WDDX2CFML" input="#arguments.creditsAvailable#" output="local.arrCreditsAvailable">
		</cfif>
		
		<cfscript>
		for (local.x = 1; local.x lte arrayLen(local.arrCreditTypes); local.x = local.x + 1) {
			QueryAddRow(local.qryCredits);
			QuerySetCell(local.qryCredits,"fieldname",local.arrCreditTypes[local.x].fieldname);
			QuerySetCell(local.qryCredits,"displayname",local.arrCreditTypes[local.x].displayname);
			QuerySetCell(local.qryCredits,"numcredits","0");
			for (local.y = 1; local.y lte arrayLen(local.arrCreditsAvailable); local.y = local.y + 1) {
				if (local.arrCreditsAvailable[local.y].fieldname eq local.arrCreditTypes[local.x].fieldname) {
					QuerySetCell(local.qryCredits,"numcredits",local.arrCreditsAvailable[local.y].value);
					break;
				}
			}
		}
		</cfscript>

		<cfif arguments.withCreditsOnly>
			<cfquery name="local.qryCredits" dbtype="query">
				select fieldname, displayname, numcredits
				from [local].qryCredits
				where numcredits > '0'
				order by displayname
			</cfquery>
		</cfif>
			
		<cfreturn local.qryCredits>
	</cffunction>

	<cffunction name="addEnrollmentCredit" access="public" returntype="void" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarCreditID" type="numeric" required="yes">
		<cfargument name="idnumber" type="string" required="yes">

		<cfstoredproc procedure="sw_addEnrollmentCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarCreditID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.idnumber#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="removeEnrollmentCredit" access="public" returntype="void" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="seminarCreditID" type="numeric" required="yes">

		<cfstoredproc procedure="sw_removeEnrollmentCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarCreditID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getCreditsByEnrollmentID" access="public" returntype="query" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditsByEnrollmentID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
			<cfprocresult name="local.qrySelected" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qrySelected>
	</cffunction>

	<cffunction name="getEnrollmentCreditsByEnrollmentID" access="public" returntype="query">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="swl_getEnrollmentCreditsByEnrollmentID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			<cfprocresult name="local.qrySelectedCredits" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qrySelectedCredits>
	</cffunction>

	<cffunction name="getEnrollmentsInfo" access="public" returntype="query">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfquery name="local.qryGetEnrollmentsInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select dateCompleted
			from dbo.tblEnrollments
			where enrollmentID = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryGetEnrollmentsInfo>
	</cffunction>

	<cffunction name="getEnrollmentsDurations" access="public" returntype="query">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfquery name="local.qryGetDurations" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select CASE WHEN eswl.swlid is null then 'SWOD' ELSE 'SWL' END as swType, eswl.duration, eswl.durationphone, eswod.calcTimeSpent
			from dbo.tblEnrollments as e
			left outer join dbo.tblEnrollmentsSWOD as eswod on eswod.enrollmentid = e.enrollmentID
			left outer join dbo.tblEnrollmentsSWLive as eswl on eswl.enrollmentid = e.enrollmentID
			where e.enrollmentid = <cfqueryparam value="#arguments.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryGetDurations>
	</cffunction>

	<cffunction name="getAuthority" access="public" returntype="query" output="no">
		<cfargument name="authorityID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditAuthority" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.authorityID#" null="No">
			<cfprocresult name="local.qryAuthority" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryAuthority>
	</cffunction>	
	
	<cffunction name="getSponsor" access="public" returntype="query" output="no">
		<cfargument name="sponsorID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditSponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#" null="No">
			<cfprocresult name="local.qrySponsor" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySponsor>
	</cffunction>	

	<cffunction name="getCreditInfoForCatalog" access="public" returntype="struct" output="no">
		<cfargument name="qryCredits" type="query" required="yes">
		<cfargument name="isControlPanel" type="boolean" required="false" default="0">
	
		<cfset var local = StructNew()>
		<cfset local.JSStruct = StructNew()>

		<cfoutput query="arguments.qryCredits" group="authorityID">
			<cfsavecontent variable="local.tmpText">
				<div>
					<cfif arguments.isControlPanel>
						<div class="font-weight-bold mb-2"><u>#arguments.qryCredits.authorityJurisdiction#</u></div>
					<cfelse>
						<div style="margin-bottom:8px;"><strong>#arguments.qryCredits.authorityJurisdiction#</strong></div>
					</cfif>
					<cfoutput>
					<div style="margin-bottom:20px;">
						<div class="<cfif arguments.isControlPanel>text-grey<cfelse>muted</cfif>"><b>#arguments.qryCredits.sponsorName#</b></div>
						<b>#arguments.qryCredits.status#</b>. 
						<cfif arguments.qryCredits.status eq "Self-Submitting">
							Credit may be earned by self-submitting 
						<cfelse>
							Credit has been 
							<cfif arguments.qryCredits.status eq "Pending">submitted and is pending approval<cfelse>approved</cfif> 
						</cfif>
						with the #arguments.qryCredits.authorityName# for 
						<cfset local.qryCreditMatch = getCreditsFromWDDX(arguments.qryCredits.wddxCreditTypes,arguments.qryCredits.wddxCreditsAvailable,true)>
						<cfloop query="local.qryCreditMatch">
							<b><cfif local.qryCreditMatch.numcredits gt 0>#local.qryCreditMatch.numcredits# #local.qryCreditMatch.displayname# credit<cfif local.qryCreditMatch.numcredits is not 1>s</cfif></cfif></b>
							<cfif local.qryCreditMatch.currentrow is local.qryCreditMatch.recordcount>. 
							<cfelse>
								<cfif local.qryCreditMatch.recordcount is 2> and 
								<cfelse>
									<cfif local.qryCreditMatch.currentrow is local.qryCreditMatch.recordcount - 1>, and 
									<cfelse>, 
									</cfif>
								</cfif>
							</cfif>
						</cfloop>
						<cfif isDefined("arguments.qryCredits.creditOfferedEndDate") and len(arguments.qryCredits.creditOfferedEndDate) and arguments.qryCredits.status eq "Approved">
							This credit is offered until #dateformat(arguments.qryCredits.creditOfferedEndDate, "mm/dd/yyyy")#. 
						</cfif>
						#arguments.qryCredits.statementAppProvider#
						<cfif len(arguments.qryCredits.creditMessage)><div style="margin-top:5px;">#arguments.qryCredits.creditMessage#</div></cfif>
					</div>
					</cfoutput>
				</div>
			</cfsavecontent>
			<cfset local.JSStruct[authorityID] = replace(replace(replace(local.tmptext,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>
		</cfoutput>		
		
		<cfreturn local.JSStruct>
	</cffunction>

	<cffunction name="getSponsors" access="public" returntype="query" output="no">
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditSponsors" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qrySponsors" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySponsors>
	</cffunction>

	<cffunction name="getAuthorities" access="public" returntype="query" output="no">
		<cfset var qryAuthorities = "">
		
		<cfstoredproc procedure="sw_getCreditAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="qryAuthorities" resultset="1">
		</cfstoredproc>

		<cfreturn qryAuthorities>
	</cffunction>

	<cffunction name="getAuthoritiesLinkedToSponsor" access="public" returntype="query" output="no">
		<cfargument name="sponsorID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditAuthoritiesLinkedToSponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			<cfprocresult name="local.qryAuthorities" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryAuthorities>
	</cffunction>	

	<cffunction name="getSponsorsLinkedToAuthority" access="public" returntype="query" output="no">
		<cfargument name="authorityID" type="numeric" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditSponsorsLinkedToAuthority" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.authorityID#">
			<cfprocresult name="local.qrySponsors" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySponsors>
	</cffunction>	

	<cffunction name="getLinkedNationalPrograms" access="public" returntype="query" output="no">
		<cfargument name="CSALinkIDs" type="string" required="yes">

		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getLinkedNationalPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.CSALinkIDs#">
			<cfprocresult name="local.qryPrograms" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryPrograms>
	</cffunction>

	<cffunction name="getCreditStatuses" access="public" returntype="query" output="no">
		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditStatuses" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qryCreditStatuses" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryCreditStatuses>
	</cffunction>

	<cffunction name="getCreditPrompts" access="public" returntype="query" output="no">
		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getCreditPrompts" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qryCreditPrompts" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryCreditPrompts>
	</cffunction>

	<cffunction name="updateECCert" access="public" returntype="void" output="no">
		<cfargument name="enrollmentid" type="numeric" required="yes">
		<cfargument name="seminarCreditID" type="numeric" required="yes">
		<cfargument name="earnedCertificate" type="boolean" required="yes">
		<cfargument name="finalTimeSpent" type="numeric" required="yes">
		
		<cfset var local = structnew()>

		<cfquery name="local.qryUpdate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			DECLARE @seminarID int, @seminarCreditID int, @enrollmentID int, @earnedCertificate BIT, @finalTimeSpent INT,
				@orgID int, @siteID int, @programType VARCHAR(4), @registrantName VARCHAR(300), @authorityJurisdiction VARCHAR(500),
				@recordedByMemberID INT, @crlf VARCHAR(10), @msgjson VARCHAR(MAX), @siteResourceID int, @dataXML xml;
			DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

			SET @crlf = CHAR(13) + CHAR(10);
			SET @enrollmentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.enrollmentid#">;
			SET @seminarCreditID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.seminarCreditID#">;
			SET @earnedCertificate = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.earnedCertificate#">;
			SET @finalTimeSpent = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.finalTimeSpent#">;
			SET @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;
			
			-- get seminarID from seminarCreditID (disregard what we pass in - deprecated)
			SELECT @seminarID = s.seminarID, @orgID = mcs.orgID, @siteID = mcs.siteID, @siteResourceID=mcs.siteResourceID,
				@programType = CASE WHEN swl.seminarID IS NOT NULL THEN 'SWL' WHEN swod.seminarID IS NOT NULL THEN 'SWOD' END,
				@authorityJurisdiction = ca.jurisdiction + ' via ' + cs.sponsorName
			FROM tblSeminarsAndCredit AS sac
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID
			INNER JOIN dbo.tblCreditSponsors AS cs ON cs.sponsorID = csa.sponsorID
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
			INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
			INNER JOIN membercentral.dbo.sites as mcs on mcs.siteCode = p.orgCode
			LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
			WHERE sac.seminarCreditID = @seminarCreditID;

			SELECT @registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) + ']' + ISNULL(' (' + m2.membernumber + ')','')
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
			LEFT OUTER JOIN membercentral.dbo.ams_members AS m
				INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
			ON m.memberID = e.MCMemberID
			WHERE e.enrollmentID = @enrollmentID;

			UPDATE dbo.tblEnrollmentsAndCredit
			SET earnedCertificate = @earnedCertificate, finalTimeSpent = @finalTimeSpent
			WHERE enrollmentID = @enrollmentID
			AND seminarCreditID = @seminarCreditID;

			IF @earnedCertificate = 1 BEGIN
				SELECT @dataXML = 
					ISNULL((
						SELECT 'semweb' AS itemtype, @enrollmentID AS itemid for xml path ('data')
					),'<data/>');

				INSERT INTO @tblHookListeners (executionType, objectPath)
				EXEC memberCentral.dbo.hooks_runHook @event='creditAwarded', @siteResourceID=@siteResourceID, @dataXML=@dataXML;
			END

			SET @msgjson = 'Enrollment credit settings for the jurisdiction [' + @authorityJurisdiction + '] has been updated for registrant '+ @registrantName +' on '+ @programType + '-' + CAST(@seminarID AS VARCHAR(10)) + @crlf
				+ 'Earned Certificate? : ' + CASE WHEN @earnedCertificate = 1 THEN 'Yes' ELSE 'No' END + @crlf
				+ 'Final Time Spend : ' + CAST(@finalTimeSpent AS VARCHAR(10)) + ' min';
			
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			VALUES ('{ "c":"auditLog", "d": {
				"AUDITCODE":"SW",
				"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
				"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
				"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
				"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
		</cfquery>
	</cffunction>
	
	<cffunction name="getCreditAuthoritiesByOrgCode" access="public" returntype="query" output="no">
		<cfargument name="eventArgs" type="struct" required="yes">
		
		<cfset var local = StructNew()>
			
		<cfstoredproc procedure="sw_getCreditSponsorIDByOrgCode" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_Varchar" value="#arguments.eventArgs.mc_siteInfo.orgCode#">
			<cfprocresult name="local.qryGetSponsorID" resultset="1">
		</cfstoredproc>
		
		<cfstoredproc procedure="sw_getCreditAuthoritiesLinkedToSponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGetSponsorID.sponsorID#">
			<cfif StructKeyExists(arguments.eventArgs, "CSALinkID")>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventArgs.CSALinkID#">
			</cfif>
			<cfprocresult name="local.qryGetCreditAuthorities" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryGetCreditAuthorities>
	</cffunction>
	
	<cffunction name="addSelfReportedCredits" access="public" returntype="void" output="no">
		<cfargument name="event" type="any">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
				
		<cfset var local = structnew()>
		
		<cfset local.aCreditTypes = arrayNew(1)>
			
		<!---create array of structures w/ CLE Credit Type data--->
		<cfloop collection="#arguments.event.getCollection()#" item="local.i">
			<cfif left(local.i,10) eq "CREDITTYPE" and len(arguments.event.getvalue(local.i))>
				<cfset local.structTemp = structNew()>
				<cfset local.structTemp["value"] = arguments.event.getValue(local.i)>
				<cfset local.structTemp["fieldname"] = lcase(right(local.i,len(local.i)-10))>
				<cfset arrayAppend(local.aCreditTypes,local.structTemp)>
			</cfif>
		</cfloop>
		
		<cfwddx action="cfml2wddx" input="#local.aCreditTypes#" output="local.wddxCreditTypes">

		<cfstoredproc procedure="sw_addSelfReportedEvent" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getvalue('EventName')#" maxlength="200">
			<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('eventDate')#">
			<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.depoMemberDataID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('courseNumber')#" maxlength="50">
			<cfprocresult name="local.qryAddSelfReportedEvent" resultset="1">
		</cfstoredproc>
		<cfstoredproc procedure="sw_addSelfReportEventAndCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryAddSelfReportedEvent.selfID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.event.getValue('CSALinkID')#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('IDNumber')#" maxlength="50">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.wddxCreditTypes#">
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="deleteSelfReportedCredits" access="public" returntype="void" output="no">
		<cfargument name="ID" type="numeric" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
				
		<cfset var local = structnew()>
		
		<cfstoredproc procedure="sw_removeSelfReportedCredit" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getCreditsGridBySeminar" access="public" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">
		
		<cfset var local = structnew()>

		<cfquery name="local.qryCreditsGrid" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT sac.seminarCreditID, ca.authorityID, cs.sponsorID, cs.orgCode AS creditSponsorOrgCode, 
				cstat.statusID, cstat.status, sac.courseApproval, sac.wddxCreditsAvailable, ca.code AS authorityCode, 
				ca.authorityName, ca.wddxCreditTypes, cs.sponsorName, cstat.statusOrder AS bestCategory, 
				sac.creditOfferedStartDate, sac.creditOfferedEndDate, sac.CreditCompleteByDate, 
				sac.isCreditRequired, sac.isIDRequired, sac.csalinkID, sac.isCreditDefaulted, p.orgcode, sac.hasForm1, 
				CASE WHEN EXISTS(SELECT TOP 1 enrollCreditID FROM dbo.tblEnrollmentsAndCredit WHERE seminarCreditID = sac.seminarCreditID) THEN 1 
					ELSE 0 END AS creditsAssigned
			FROM dbo.tblSeminarsAndCredit AS sac 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
			INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
			INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = sac.seminarID
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
			WHERE s.seminarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.seminarID#">
			ORDER BY case when cs.orgcode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.siteCode#"> then 1 else 0 end desc, ca.code, cs.sponsorName;
		</cfquery>

		<cfreturn local.qryCreditsGrid>
	</cffunction>

	<cffunction name="getSponsorsAndAuthorities" access="public" returntype="query">
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getCreditSponsorsAndAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qrySponsorsAndAuthorities" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySponsorsAndAuthorities>
	</cffunction>

	<cffunction name="getNationalPrograms" access="public" returntype="query">
		<cfset var local = StructNew()>
		
		<cfstoredproc procedure="sw_getNationalPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qryNationalPrograms" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryNationalPrograms>
	</cffunction>

	<cffunction name="getCreditsGridBySponsorSWL" access="public" returntype="query">
		<cfargument name="sponsorID" type="numeric" required="yes">
		<cfargument name="start" type="numeric" required="yes">
		<cfargument name="maxrows" type="numeric" required="yes">

		<cfset var qryCreditsGrid = "">

		<cfstoredproc procedure="swl_getCreditsGridBySponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.start#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxrows#">
			<cfprocresult name="qryCreditsGrid" resultset="1">
		</cfstoredproc>

		<cfreturn qryCreditsGrid>
	</cffunction>

	<cffunction name="getCreditsGridBySponsorSWOD" access="public" returntype="query">
		<cfargument name="sponsorID" type="numeric" required="yes">
		<cfargument name="start" type="numeric" required="yes">
		<cfargument name="maxrows" type="numeric" required="yes">

		<cfset var qryCreditsGrid = "">

		<cfstoredproc procedure="swod_getCreditsGridBySponsor" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.start#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxrows#">
			<cfprocresult name="qryCreditsGrid" resultset="1">
		</cfstoredproc>

		<cfreturn qryCreditsGrid>
	</cffunction>

</cfcomponent>