ALTER PROC dbo.email_previewEmailBlastSummary
@blastID int,
@mode tinyint,
@emailTypeID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @ruleID int, @orgID int, @globalOptOutListID INT, @excludeOptOuts bit = 0, @optOutMembersCount int = 0;

	select @ruleID = b.ruleID, @orgID = s.orgID
	from dbo.email_EmailBlasts as b
	inner join dbo.sites as s on s.siteID = b.siteID
	where b.blastID = @blastID;

	-- run rule to get members
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	CREATE TABLE #tmpVGRMembers (memberID int PRIMARY KEY);

	EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;


	IF OBJECT_ID('tempdb..#tblM') IS NOT NULL 
		DROP TABLE #tblM;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	CREATE TABLE #tblM (memberid int, emailTypeID int, hasEmail bit);
	CREATE TABLE #tmpRecipients (memberID INT, firstName varchar(75), lastName varchar(75), memberName varchar(200), memberCompany varchar(200), memberEmail varchar(200), emailTypeID int);
	CREATE TABLE #tmpFinalRecipients (memberID INT, firstName varchar(75), lastName varchar(75), memberName varchar(200), memberCompany varchar(200), memberEmail varchar(200), emailTypeID int);

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut'
	WHERE cl.[status] = 'A';

	IF @mode in (0,1,2,3,4,5)
		INSERT INTO #tblM (memberid, emailTypeID, hasEmail)
		SELECT m2.memberid, me.emailTypeID, case when len(me.email) > 0 then 1 else 0 end
		FROM #tmpVGRMembers rm
		INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
			AND m2.memberid = rm.memberID
		INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
			AND me.memberid = m2.memberid
		INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
			AND ebet.blastID = @blastID	
			UNION
		SELECT m2.memberid, me.emailTypeID, case when len(me.email) > 0 then 1 else 0 end
		FROM #tmpVGRMembers rm
		INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
			AND m2.memberid = rm.memberID
		INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
			AND me.memberid = m2.memberid
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
			AND metag.memberID = me.memberID
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.email_emailBlastEmailTagTypes as ebett on ebett.emailTagTypeID = metag.emailTagTypeID
			AND ebett.blastID = @blastID;
	IF @mode = 6
		INSERT INTO #tblM (memberid, emailTypeID, hasEmail)
		SELECT TOP 100 tmp.memberid, tmp.emailTypeID, tmp.hasEmail
		FROM (
			SELECT m2.memberid, me.emailTypeID, 1 as hasEmail
			FROM #tmpVGRMembers rm
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
				AND m2.memberid = rm.memberID
			INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
				AND me.memberid = m2.memberid
			INNER JOIN dbo.email_emailBlastEmailTypes as ebet on ebet.emailTypeID = me.emailTypeID
				AND ebet.blastID = @blastID
			WHERE me.email <> ''
				UNION
			SELECT m2.memberid, me.emailTypeID, 1 as hasEmail
			FROM #tmpVGRMembers rm
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID
				AND m2.memberid = rm.memberID
			INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
				AND me.memberid = m2.memberid
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID
				AND metag.memberID = me.memberID
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.email_emailBlastEmailTagTypes as ebett on ebett.emailTagTypeID = metag.emailTagTypeID
				AND ebett.blastID = @blastID
			WHERE me.email <> ''
		) tmp
		ORDER BY NEWID();
	
	-- get recipient member details
	INSERT INTO #tmpRecipients (memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT DISTINCT m.memberID, m.firstname, m.lastname, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company, me.email, me.emailTypeID
	FROM #tblM as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberid
	INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
		AND me.memberid = m.memberid 
		AND me.emailTypeID = tmp.emailTypeID
	WHERE tmp.hasEmail = 1;

	INSERT INTO #tmpFinalRecipients (memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID)
	SELECT memberID, firstName, lastName, memberName, memberCompany, memberEmail, emailTypeID
	FROM #tmpRecipients;

	IF EXISTS (SELECT 1 FROM dbo.email_emailBlastConsentLists where blastID = @blastID) BEGIN
		SET @excludeOptOuts = 1;

		DELETE tmp
		FROM #tmpFinalRecipients AS tmp
		INNER JOIN platformMail.dbo.email_consentListMembers AS clm
			INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
				AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
			ON clm.email = tmp.memberEmail;

		SELECT @optOutMembersCount = COUNT(DISTINCT tmpR.memberID)
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
			AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
	END

	-- summary counts
	IF @mode = 0 BEGIN
		select summaryRow, summaryItem, emailTypeID, summaryItemCount, summaryLinkText = 
			case 
			when summaryRow in (1,3,4) and summaryItemCount = 0 then '0 recipients'
			when summaryRow in (1,3,4) and summaryItemCount > 0 then cast(summaryItemCount as varchar(10)) + ' recipients'
			when summaryRow in (2,5) and summaryItemCount = 0 then '0 recipients'
			when summaryRow in (2,5) and summaryItemCount > 0 then cast(summaryItemCount as varchar(10)) + ' recipients'
			end
		from (
			select 1 as summaryRow, 'Recipients matching filter criteria' as summaryItem, 0 as emailTypeID, count(distinct memberid) as summaryItemCount
			from #tblM
				union all
			select 2, 'Recipients using e-mail type: ' + met.emailType, met.emailTypeID, count(tmp.memberID)
			from #tmpRecipients as tmp
			inner join dbo.ams_memberEmailTypes as met on met.emailTypeID = tmp.emailTypeID
			group by met.emailType, met.emailTypeID
				union all
			SELECT 3, 'Recipients with no e-mail address', 0, COUNT(tmp.memberID)
			FROM #tblM AS tmp
			LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
			WHERE tmpR.memberID IS NULL
				union all
			SELECT 4, 'Recipients within Opt-Out lists', 0, @optOutMembersCount
			WHERE @excludeOptOuts = 1
				union all
			select 5, 'Total messages to be sent', 0, count(*)
			from (
				select memberID, memberEmail
				from #tmpFinalRecipients
				group by memberID, memberEmail
			) as tmp
		) as outertmp
		order by summaryRow;
	END

	-- row 1 detail
	IF @mode = 1
		select distinct m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' as memberName, m.company as memberCompany
		from #tblM as tblM
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = tblM.memberid
		order by memberName;

	-- row 2 detail
	IF @mode = 2
		select distinct memberName, memberCompany, memberEmail
		from #tmpRecipients
		where emailTypeID = @emailTypeID
		order by memberName;

	-- row 3 detail
	IF @mode = 3
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany
		FROM #tblM AS tmp
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID and m.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpRecipients AS tmpR on tmpR.memberID = tmp.memberID
		WHERE tmpR.memberID IS NULL
		ORDER BY memberName;

	-- Members within Opt-Out list
	IF @mode = 4 BEGIN
		SELECT DISTINCT m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany, clm.email AS memberEmail
		FROM platformMail.dbo.email_consentListMembers AS clm
		INNER JOIN #tmpRecipients AS tmpR ON tmpR.memberEmail = clm.email
		INNER JOIN dbo.ams_members AS m on m.orgID = @orgID
			AND m.memberID = tmpR.memberID
		INNER JOIN dbo.email_emailBlastConsentLists AS bcl ON bcl.blastID = @blastID
			AND clm.consentListID IN (bcl.consentListID, @globalOptOutListID)
		WHERE @excludeOptOuts = 1
		ORDER BY memberName;
	END

	-- row 5 detail and preview detail 
	IF @mode in (5,6)
		select memberID, firstName, lastName, memberName, memberCompany, memberEmail, min(emailTypeID) as emailTypeID
		from #tmpFinalRecipients
		group by memberID, firstName, lastName, memberName, memberCompany, memberEmail
		order by memberName, memberCompany, memberEmail;

	IF OBJECT_ID('tempdb..#tblM') IS NOT NULL 
		DROP TABLE #tblM;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpFinalRecipients') IS NOT NULL 
		DROP TABLE #tmpFinalRecipients;
	IF OBJECT_ID('tempdb..#tmpVGRMembers') IS NOT NULL
		DROP TABLE #tmpVGRMembers;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
