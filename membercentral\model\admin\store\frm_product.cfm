<cfset local.selectedTab = event.getTrimValue("tab","Product")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>
<cfsavecontent variable="local.productFormJS">
	<cfoutput>
	<cfif structKeyExists(local, "strProductRevenueGLAcctWidget") and structKeyExists(local, "strShippingRevenueGLAcctWidget")>
		#local.strProductRevenueGLAcctWidget.js#
		#local.strShippingRevenueGLAcctWidget.js#
	</cfif>
	<script type="text/javascript">
		var #ToScript(this.link.permissionsGridAdd,"mca_permsadd_link")#
		let listProductFormatsTable;
		
		var gridInitArray = new Array();
		gridInitArray["Product"] = false;
		gridInitArray["productRates"] = false;
	
		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "Product":
						break;
					<cfif arguments.event.getValue('mca_ta','') EQ 'editProduct'>
						case "productRates":
							initProductFormatAndRatesTable(); break;
					</cfif>
				}
			}
		}
		function reloadProductFormatsTable(){
			listProductFormatsTable.draw(false);
		}
		function addDocument(formatID,itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Attach Document to Product Format',
				iframe: true,
				contenturl: '#this.link.addDocument#&formatID=' + formatID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitDocFormat',
					extrabuttonlabel: 'Save Document',
				}
			});
		}
		function editDocument(uid,formatID,itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Edit Attached Document',
				iframe: true,
				contenturl: '#this.link.editDocument#&usageID=' + uid + '&formatID=' + formatID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitDocFormat',
					extrabuttonlabel: 'Save Document',
				}
			});
		} 
		function confirmDeleteDocument(did,fid,iid) {
			var removeDocument = function(mg) {
				if (mg.success && mg.success.toLowerCase() == 'true'){
					reloadProductFormatsTable();
				} else {
					alert('We were unable to remove this document.');
				}
			};
			let delElement = $('##btnRemoveDoc_'+did+'_'+fid+'_'+iid);
			mca_initConfirmButton(delElement, function(){
				var objParams = { documentID:did, formatID:fid, itemID:iid, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteFormatDocument',objParams,removeDocument,removeDocument,10000,removeDocument);
			},'<i class="fa-solid fa-trash-alt"></i>');
		}
		function addStream(formatID,itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Attach Stream to Product Format',
				iframe: true,
				contenturl: '#this.link.addStream#&formatID=' + formatID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitStream',
					extrabuttonlabel: 'Save Stream',
				}
			});
		}
		function editStream(uid,formatID,itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Edit Attached Stream',
				iframe: true,
				contenturl: '#this.link.editStream#&usageID=' + uid + '&formatID=' + formatID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitStream',
					extrabuttonlabel: 'Save Stream',
				}
			});
		} 
		function confirmDeleteStream(uid) {
			var msg = 'Are you sure you want to delete this stream from this product format?';
			if (confirm(msg)) {
				var removeStream = function(mg) {
					if (mg.success && mg.success.toLowerCase() == 'true'){
						reloadProductFormatsTable();
					} else {
						alert('We were unable to remove this stream.');
					}
				};
			}
			var objParams = { usageID:uid, storeSRID:#this.siteResourceID# };
			TS_AJX('ADMINSTORE','deleteFormatStream',objParams,removeStream,removeStream,10000,removeStream);
		}
		function closeBox() { MCModalUtils.hideModal(); }
		function replaceUnicode(message) {
			message= message.replace(/[\u0091|\u0092|\u2018|\u2019|\u201A]/g, "\'");
	
			message= message.replace(/[\u0093|\u0094|\u201C|\u201D|\u201E]/g, "\"");

			message= message.replace(/[\u0096|\u0097]/g, "--");
			message= message.replace(/[\u2013|\u2014|\u2012|\u2015]/g, "-");
	
			message= message.replace(/\u2026/g, "...");
	
			message= message.replace(/\u02C6/g, "^");
	
			message= message.replace(/\u2039/g, "<");
	
			message= message.replace(/\u203A/g, ">");
	
			message= message.replace(/[\u02DC|\u00A0]/g, " ");
			
			return message;
		}
		function chkProductID() {
			var chkResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.isavailable && r.isavailable == true) {
						$("button##saveBtn").prop('disabled',true);
						mca_hideAlert('err_product');
						document.frmProduct.submit();
					} else {
						mca_showAlert('err_product', 'Product ID is already in use. Please enter a new Product ID.', true);
						$("button##saveBtn").prop('disabled',false);
					}
				}
				else mca_showAlert('err_product', 'Unable to check Product ID.', true);
			};

			if (_CF_checkfrmProduct(document.frmProduct)) {
				var arrReq = new Array();
				if ($('##productID').val().trim() == '') arrReq[arrReq.length] = 'Product ID is required.';
				if (($('##contentTitle').val() || '') == '') arrReq[arrReq.length] = 'Enter the Product Title.';
				
				if (arrReq.length > 0) {
					mca_showAlert('err_product', arrReq.join('<br/>'), true);
					$("button##saveBtn").prop('disabled',false);
					return false;
				} else {
					$("button##saveBtn").prop('disabled',true);
					mca_hideAlert('err_product');
				}
					
				var elemProd = document.getElementById('productID');
				var elemItem = document.getElementById('itemID');
				elemProd.value = replaceUnicode(elemProd.value);
				var objParams = { storeID:#local.storeID#, itemID:elemItem.value, productID:elemProd.value };
				TS_AJX('ADMINSTORE','checkProductID',objParams,chkResult,chkResult,10000,chkResult);
			}
		}
		function confirmFormatDelete(fid, iid) {
			var removeFormat = function(mg) {
				if (mg.success && mg.success.toLowerCase() == 'true'){
					reloadProductFormatsTable();
				} else {
					delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-alt"></i>');
					alert('We were unable to remove this format.');
				}
			};		
			
			let delElement = $('##btnRemoveFormat_'+fid+'_'+iid);
			mca_initConfirmButton(delElement, function(){
				var objParams = { storeID:#local.storeID#, formatID:fid, itemID:iid, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteFormat',objParams,removeFormat,removeFormat,10000,removeFormat);
			},'<i class="fa-solid fa-trash-alt"></i>');
		}
		function refreshRateTab() { 
			var suffix = '&tab=productRates'; 
			var currentUrl = window.location.href;  
			if (currentUrl[currentUrl.length-1] == '##') {
				currentUrl = currentUrl.substring(0, currentUrl.length-1);
			}
			if (currentUrl.match(suffix + '$') != suffix) { 
				window.location.href = window.location.href + suffix; 
			} else location.reload(true);
		}
		function addRateOverride(rateID, itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Add New Rate Override',
				iframe: true,
				contenturl: '#this.link.addRateOverride#&rateID=' + rateID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitRateOverride',
					extrabuttonlabel: 'Save Rate Override',
				}
			});			
		}
		function editRateOverride(rateOverrideID,rateID,itemID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Edit Rate Override',
				iframe: true,
				contenturl: '#this.link.editrateOverride#&rateOverrideID=' + rateOverrideID + '&rateID=' + rateID + '&itemID=' + itemID,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitRateOverride',
					extrabuttonlabel: 'Save Rate Override',
				}
			});
		} 
		function confirmRateOverrideDelete(roid, rid, iid) {
			var removeResult = function(mg) {
				if (mg.success && mg.success.toLowerCase() == 'true'){
					reloadProductFormatsTable();
				} else {
					alert('We were unable to remove this rate override.');
				}
			};	
			let delElement = $('##btnRemoveOverride_'+roid+'_'+rid+'_'+iid);
			mca_initConfirmButton(delElement, function(){
				var objParams = { rateOverrideID:roid, rateID:rid, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteRateOverride',objParams,removeResult,removeResult,10000,removeResult);
			},'<i class="fa-solid fa-trash-alt"></i>');
		}
		function removeMemberGroup(rateid,grpid,inc) {
			var removeMGData = function(mg) {
				if (mg.success && mg.success.toLowerCase() == 'true'){
					reloadProductFormatsTable();
				} else {
					alert('We were unable to remove this group from this rate.');
				}
			};

			let delElement = $('##btnRemoveGrp_'+rateid+'_'+grpid+'_'+inc);
			mca_initConfirmButton(delElement, function(){
				var objParams = { rateid:rateid, groupid:grpid, include:inc, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteMemberGroup',objParams,removeMGData,removeMGData,10000,removeMGData);
			},'<i class="fa-solid fa-trash-alt"></i>');
		}
		function removeRate(ID) {
			var removeRateData = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadProductFormatsTable();
				} else {
					alert('We were unable to remove this rate.');
				}
			};

			let delElement = $('##btnDeleteRate_'+ID);
			mca_initConfirmButton(delElement, function(){
				var objParams = { rateID:ID, storeSRID:#this.siteResourceID# };
				TS_AJX('ADMINSTORE','deleteRate',objParams,removeRateData,removeRateData,10000,removeRateData);
			},'<i class="fa-solid fa-trash-alt"></i>');
		}
		function addRateWindow(fid){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Add New Store Rate',
				iframe: true,
				contenturl: '#this.link.addRate#&formatID=' + fid,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitRateForm',
					extrabuttonlabel: 'Save Rate',
				}
			});
		}
		function editRateWindow(rid){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Edit Store Rate',
				iframe: true,
				contenturl: '#this.link.editRate#&rateID=' + rid,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitRateForm',
					extrabuttonlabel: 'Save Rate',
				}
			});
		}
		function addFormatWindow(isAffirmation){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Add New Product Format',
				iframe: true,
				contenturl: '#this.link.addFormat#&itemID=#local.ItemID#&isAffirmation=' + isAffirmation,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitFormatForm',
					extrabuttonlabel: 'Save',
				}
			});
		}		
		function editFormatWindow(fid){
			MCModalUtils.hideModal();
			setTimeout(function(){
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Edit Product Format',
					iframe: true,
					contenturl: '#this.link.editFormat#&itemID=#local.ItemID#&formatID=' + fid,
					strmodalfooter : {
						classlist: 'text-right',
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.submitFormatForm',
						extrabuttonlabel: 'Save',
					}
				});
			},600);
		}
		function moveFormat(ID, dir) {
			var moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){reloadProductFormatsTable();}
				else alert('We were unable to move '+ dir +' this format.');
			};
			var objParams = { storeID:#local.storeID#, formatID:ID, dir:dir };
			TS_AJX('ADMINSTORE','doFormatMove',objParams,moveResult,moveResult,10000,moveResult);
		}
		function ratePermClose(){
			reloadProductFormatsTable();
			MCModalUtils.hideModal();
		}
		<cfif arguments.event.getValue('mca_ta','') EQ 'editProduct'>
		function filterProductRates() {
			if ($('##divFilterForm').hasClass('d-none')) {
				$('##divFilterForm').removeClass('d-none');
			}
		}
		function filterProductRatesGrid() {
			reloadProductFormatsTable();
			return false;
		}
		function initProductFormatAndRatesTable() {
			let ProductFormatsRatesTemplateSource = $('##mc_ProductFormatsRatesTemplate').html().replace(/\n/g,'');
			let ProductFormatsRatesTemplate = Handlebars.compile(ProductFormatsRatesTemplateSource);
			listProductFormatsTable = $('##listProductFormatsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"ajax": { 
					"url": "#local.storeProductFormatsRatesLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##divFilterForm :input').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 
						"className": 'details-control border-right-0',
						"orderable": false,
						"data": null,
						"defaultContent": '',
						"width": "3%"
					},
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								var statusData = "";
								if(data.status == 'I'){
									statusData = '<small class="float-right"><span class="badge badge-dark ml-1">Inactive</span></small>';
								}
								renderData += "<b>" + data.itemTypeName + ":</b> "+ data.formatName + statusData;
							}
							return type === 'display' ? renderData : data;
						},
						"width": "44%"
					},
					{ "data": null, "defaultContent": '', "width": "15%", "orderable": false },
					{ "data": null, "defaultContent": '', "width": "8%", "orderable": false },
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Add Rate" onclick="addRateWindow('+data.formatID+');return false;"><i class="fa-solid fa-plus"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-users"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Edit '+data.itemTypeNameEnc+'" onclick="editFormatWindow('+data.formatID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-danger p-1" title="Remove '+data.itemTypeNameEnc+'" id="btnRemoveFormat_'+data.formatID+'_'+data.itemID+'" onclick="confirmFormatDelete('+data.formatID+','+data.itemID+');return false;"><i class="fa-solid fa-trash-alt"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Attach Document to this Format" onclick="addDocument('+data.formatID+','+data.itemID+');return false;"><i class="fa-solid fa-file"></i></a>';
								<cfif this.storeInfo.offerStreams is 1>
									renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Attach Stream to this Format" onclick="addStream('+data.formatID+','+data.itemID+');return false;"><i class="fa-solid fa-rss"></i></a>';
								</cfif>
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-money-bill-1 text-green"></i></a>';									
								if(data.canMoveUp){
									renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Move '+data.itemTypeNameEnc+' Up" onclick="moveFormat('+data.formatID+',\'up\');return false;"><i class="fa-solid fa-arrow-up"></i></a>';
								}else{
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>';
								}
								if(data.canMoveDown){
									renderData += '<a href="##" class="btn btn-xs text-primary p-1" title="Move '+data.itemTypeNameEnc+' Down" onclick="moveFormat('+data.formatID+',\'down\');return false;"><i class="fa-solid fa-arrow-down"></i></a>';
								}else{
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>';
								}
								renderData += '<span class="mx-1 d-inline-block text-right'+(data.inventory.length == 0 ? ' invisible' : '')+'" style="min-width:70px;">'+data.inventory+'</span>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "30%",
						"className": 'text-center'
					},
				],
				"ordering": false,
				"searching": false,
				"drawCallback": function(settings) {
					$('##listProductFormatsTable > tbody > tr > td.details-control').trigger('click');
				}
			});

			$('##listProductFormatsTable').on('click', '> tbody > tr > td.details-control', function () {
				var tr = $(this).closest('tr');
				var row = listProductFormatsTable.row( tr );

				if ( row.child.isShown() ) {
					row.child.hide();
					tr.removeClass('shown');
				} else {
					row.child( ProductFormatsRatesTemplate(row.data()), 'border-top-0 p-0' ).show();
					tr.addClass('shown');
					$('##listRatesTable > tbody > tr > td.details-control').unbind('click');
					$('##listRatesTable > tbody > tr > td.details-control').on('click', function () {
						var tr = $(this).closest('tr');
						var trnext = $(this).closest('tr').next('.rateGroups');
						if(trnext.is(":visible")){
							trnext.hide();
							tr.removeClass('shown');
						}else{
							trnext.show();
							tr.addClass('shown');
						}
					});
				}
			});
		}
		</cfif>

		$(function() {
			mca_setupDatePickerField('productDate');
			mca_setupCalendarIcons('frmProduct');
			mca_setupSelect2();
			mca_initNavPills('editProductPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
			$('##saveArea').removeClass('d-none');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.productFormJS#">

<cfif local.product.showAvailable is 0 or local.product.showAvailable is ''>
	<cfset local.showAvailable = 0 />
<cfelse>
	<cfset local.showAvailable = 1 />
</cfif>

<cfoutput>
<cfform method="POST" action="#local.urlString#" name="frmProduct" id="frmProduct">
	<cfinput type="hidden" name="itemID" id="itemID" value="#local.ItemID#">
	<cfinput type="hidden" name="productContentID" id="productContentID" value="#local.product.productContentID#">
	<cfinput type="hidden" name="showQuantity" id="showQuantity" value="0">
	<cfif structKeyExists(local,"copyFromitemID")>
		<cfinput type="hidden" name="copyFromitemID" id="copyFromitemID" value="#local.copyFromitemID#">
	</cfif>

	<span id="saveArea" class="float-right d-none"><span id="saveMsg"></span></span>

	<h4><cfif not val(local.itemID) and not structKeyExists(local,"copyFromitemID")>New<cfelseif structKeyExists(local,"copyFromitemID")>Copy</cfif> Product <cfif val(local.itemID) gt 0>(#local.product.productID#)  #local.product.contentTitle#</cfif></h4> 

	<div id="divRateForm">
		<ul class="nav nav-pills nav-pills-dotted editProductPills" role="tablist" id="editProductPills">
			<cfset local.thisTabID = "Product">
			<cfset local.thisTabName = "Product">
			<li class="nav-item"><a 
				id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
				class="nav-link storeAdminPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Product</a>
			</li>

			<cfif arguments.event.getValue('mca_ta','') EQ 'editProduct'>
				<cfset local.thisTabID = "productRates">
				<cfset local.thisTabName = "productRates">
				<li class="nav-item"><a 
					id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
					class="nav-link storeAdminPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Formats/Rates</a>
				</li>
			</cfif>
		</ul>
		<div class="tab-content mc_tabcontent p-3 pb-0" id="pills-tabContent">
			<div class="tab-pane fade" id="pills-Product" role="tabpanel" aria-labelledby="Product">
				<div id="err_product" class="alert alert-danger mb-2 mt-2 d-none"></div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<cfselect name="status" id="status" class="form-control">
								<option value="A" <cfif local.product.status eq "A">selected</cfif>>Active</option>
								<option value="D" <cfif local.product.status eq "D">selected</cfif>>Deleted</option>
							</cfselect>
							<label for="status" >Status</label>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<select name="showAvailable" id="showAvailable" class="form-control">
								<option value="0" <cfif local.showAvailable is 0>selected</cfif>>No</option>
								<option value="1" <cfif local.showAvailable is not 0>selected</cfif>>Yes</option>
							</select>
							<label for="showAvailable" >Show As Available</label>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<input name="productID" id="productID" required="yes" message="Product ID is required" type="text" size="20" maxlength="50" value="#local.product.productID#" class="form-control">
							<small class="form-text text-black-50">(provide a unique ID or code for this product)</small>
							<label for="productID" >Product ID</label>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<input name="contentTitle"  id="contentTitle" required="yes" message="Enter the product title" type="text" size="70" maxlength="200" value="#local.product.contentTitle#" class="form-control"/>
							<label for="contentTitle" >Title</label>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group mb-2">
							<input name="old_productDate"  id="old_productDate" type="hidden" value="#local.product.productDate#">
							<div class="input-group dateFieldHolder">
								<input type="text" name="productDate" id="productDate" value="#DateFormat(local.product.productDate, "mm/dd/yyyy")#" class="form-control dateControl">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="productDate"><i class="fa-solid fa-calendar"></i></span>
									<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('productDate');"><i class="fa-solid fa-circle-xmark"></i></a></span>
								</div>
								<label for="productDate">Product Date</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col-sm-12">
						#local.strProductRevenueGLAcctWidget.html#
					</div>
				</div>
				<div class="form-row mb-3">
					<div class="col-sm-12">
						#local.strShippingRevenueGLAcctWidget.html#
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<select name="categoryID" id="categoryID" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Any Category" multiple="yes">
								<cfloop query="local.qryCategories">
									<option value="#local.qryCategories.categoryID#" <cfif listFindNoCase(local.product.categoryIDList,local.qryCategories.categoryID)>selected</cfif>>#local.qryCategories.thePathExpanded#</option>
								</cfloop>
							</select>
							<label for="categoryID" >Product Categories</label>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							#application.objWebEditor.showContentBoxWithLinks(fieldname='productContent', fieldlabel='Description', contentID=val(local.product.productContentID), content=local.product.rawContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col">
						<div class="form-label-group">
							<input type="hidden" name="summaryContentID" value="#local.product.summaryContentID#">
							<textarea name="productSummary" id="productSummary" style="width:100%;" rows="5" class="form-control">#ReReplace(local.product.summaryRawContent, "<[^<|>]+?>", "","ALL")#</textarea>
							<label for="productSummary" >Summary (No HTML)</label>
						</div>
					</div>
				</div>
				<cfif arguments.event.getValue('mca_ta') eq "editProduct">					
					<div class="form-row">
						<div class="col">
							<div class="form-group">
								<div class="form-label-group">
									<div class="input-group input-group">
										<input type="text" name="justview1" id="justview1" value="/?pg=store&sa=viewDetails&itemID=#local.ItemID#" class="form-control" readonly="readonly" onclick="this.select();"/>
										<div class="input-group-append">
											<span class="input-group-text"><a target="_blank" href="/?pg=store&sa=viewDetails&itemID=#local.ItemID#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
										</div>
										<label for="justview1">Internal Product URL</label>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="form-row">
						<div class="col">
							<div class="form-group">
								<div class="form-label-group">
									<div class="input-group input-group">
										<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=store&sa=viewDetails&itemID=#local.ItemID#" class="form-control" readonly="readonly" onclick="this.select();"/>
										<div class="input-group-append">
											<span class="input-group-text"><a target="_blank" href="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=store&sa=viewDetails&itemID=#local.ItemID#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
										</div>
										<label for="justview2">External Product URL</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</cfif>
				<div class="form-row">
					<div class="col">
						<div class="form-group text-right mt-4">
							<button id="saveBtn" type="button" onClick="chkProductID();" class="btn btn-sm btn-primary">Save Product</button>
							<button id="cancelBtn" type="button" onClick="location.href='#this.link.home#&tab=products';" class="btn btn-sm btn-secondary">Cancel</button>
						</div>
					</div>
				</div>
			</div>
			<cfif arguments.event.getValue('mca_ta','') EQ 'editProduct'>
				<div class="tab-pane fade" id="pills-productRates" role="tabpanel" aria-labelledby="productRates">
					<div class="toolButtonBar">
						<div><a href="javascript:filterProductRates();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter Formats/Rates."><i class="fa-regular fa-filter"></i> Filter Formats/Rates </a></div>
						<div class="form-group float-right">
							<button name="btnAddFormat" class="btn btn-sm btn-primary" type="button" onClick="addFormatWindow(0);">
								<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span> <span class="btn-wrapper--label">Add Format</span>
							</button>
							<cfif this.storeInfo.offerAffirmations is 1>
								<button name="btnAddFormat" class="btn btn-sm btn-primary" type="button" onClick="addFormatWindow(1);"><span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span> <span class="btn-wrapper--label">Add Affirmation</span></button>
							</cfif>
						</div>
					</div>

					<div id="divFilterForm" class="d-none">
						<div class="card card-box mt-2 mb-3">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Filter Formats/Rates
								</div>
							</div>
							<div class="card-body">
								<form name="frmFilter" id="frmFilter">
									<div class="form-label-group mb-0">
										<select name="hideInactive" id="hideInactive" class="custom-select" onchange="filterProductRatesGrid();">
											<option value="1">Hide Inactive Formats/Rates</option>
											<option value="0">Show Inactive Formats/Rates</option>
										</select>
										<label for="hideInactive">Format/Rates Status</label>
									</div>
								</form>
							</div>
						</div>
					</div>
					<table id="listProductFormatsTable" class="table table-sm table-bordered" style="width:100%">
						<thead>
							<tr>
								<th></th>
								<th>Format</th>
								<th class="text-center">Dates</th>
								<th class="text-right">Amount</th>
								<th class="text-center">Actions</th>
							</tr>
						</thead>
					</table>
					<script id="mc_ProductFormatsRatesTemplate" type="text/x-handlebars-template">
						{{##if arrRates}}
							<table class="table table-sm table-borderless m-0" id="listRatesTable">
								<tbody>
								{{##each arrRates}}
									<tr class="shown">
										<td style="width:3%;"></td>
										<td style="width:3%;" class="{{##if arrGroups}}details-control{{/if}}"></td>
										<td style="width:41%;" class="border-right" colspan="2"><i class="fa-solid fa-money-bill-1 text-green fa-sm"></i> <b>Rate:</b> {{rateName}}{{##compare isRateActive '==' 0}}<small class="float-right"><span class="badge badge-dark ml-1">Inactive</span></small>{{/compare}}</td>
										<td style="width:15%;" class="border-right text-center">{{rateDateDisp}}</td>
										<td style="width:8%;" class="border-right text-right">{{rate}}</td>
										<td style="width:30%;" class="text-center">											
											{{##if canEditProducts}}
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-plus"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1" title="Add Group to Rate" onclick='mca_showPermissions({{siteResourceID}},"{{rateNameJs}}",null,null,null,null,"Add Group for {{rateNameJs}}",1,"ratePermClose");return false;'><i class="fa-solid fa-users"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1" title="Edit Rate" onclick="editRateWindow({{rateID}});return false;"><i class="fa-solid fa-pencil"></i></a>
												<a href="##" class="btn btn-xs text-danger p-1" title="Remove Rate" id="btnDeleteRate_{{rateID}}" onclick="removeRate({{rateID}});return false;"><i class="fa-solid fa-trash-alt"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-file"></i></a>
												<cfif this.storeInfo.offerStreams is 1>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-rss"></i></a>
												</cfif>
												<a href="##" class="btn btn-xs text-success p-1" title="Add Rate Override" onclick="addRateOverride({{rateID}},{{itemID}});return false;"><i class="fa-solid fa-money-bill-1 text-green"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>
											{{/if}}
										</td>
									</tr>
									{{##if arrGroups}}
										<tr class="border-top-0 p-0 rateGroups">
											<td class="border-top-0 p-0" colspan="8">
												<table class="table table-sm table-borderless m-0">
												<tbody>
													{{##each arrGroups}}
														<tr>
															<td style="width:3%;"></td>
															<td style="width:3%;"></td>
															<td style="width:1%;"></td>
															<td style="width:40%;" class="border-right" colspan="2"><i class="fa-solid fa-users fa-sm"></i> {{groupName}} {{##compare include '==' 0}}<small class="float-right"><span class="badge badge-warning ml-1">Denied</span></small>{{/compare}}</td>
															<td style="width:15%;" class="border-right"></td>
															<td style="width:8%;" class="border-right"></td>
															<td style="width:30%;" class="text-center">
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-plus"></i></a>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-users"></i></a>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-pencil"></i></a>
																<a href="##" class="btn btn-xs text-danger p-1" title="Remove {{groupName}} from Rate" id="btnRemoveGrp_{{../rateID}}_{{groupID}}_{{include}}" onclick="removeMemberGroup({{../rateID}},{{groupID}},{{include}});return false;"><i class="fa-solid fa-trash-alt"></i></a>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-file"></i></a>
																<cfif this.storeInfo.offerStreams is 1>
																	<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-rss"></i></a>
																</cfif>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-money-bill-1 text-green"></i></a>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>
																<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>
															</td>
														</tr>
													{{/each}}
												</tbody>
												</table>
											</td>
										</tr>
									{{/if}}
									{{##if arrRateOverride}}
										{{##each arrRateOverride}}
											<tr>
												<td style="width:3%;"></td>
												<td style="width:3%;"></td>
												<td style="width:41%;" class="border-right" colspan="2"><i class="fa-solid fa-money-bill-1 text-green fa-sm"></i> <b>Rate:</b> {{rateOverrideName}}{{##compare isRateActive '==' 0}}<small class="float-right"><span class="badge badge-dark ml-1">Inactive</span></small>{{/compare}}</td>
												<td style="width:13%;" class="border-right text-center">{{rateOverrideDatesDisp}}</td>
												<td style="width:10%;" class="border-right text-right">{{rateOverride}}</td>
												<td style="width:30%;" class="text-center">	
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-plus"></i></a>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-users"></i></a>
													<a href="##" class="btn btn-xs text-primary p-1" title="Edit {{rateOverrideName}}" onclick="editRateOverride({{rateOverrideID}},{{rateID}},{{../itemID}});return false;"><i class="fa-solid fa-pencil"></i></a>
													<a href="##" class="btn btn-xs text-danger p-1" title="Remove {{rateOverrideName}}" id="btnRemoveOverride_{{rateOverrideID}}_{{rateID}}_{{../itemID}}" onclick="confirmRateOverrideDelete({{rateOverrideID}},{{rateID}},{{../itemID}});return false;"><i class="fa-solid fa-trash-alt"></i></a>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-file"></i></a>
													<cfif this.storeInfo.offerStreams is 1>
														<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-rss"></i></a>
													</cfif>
													<a href="##" class="btn btn-xs text-success p-1 invisible"><i class="fa-solid fa-money-bill-1 text-green"></i></a>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>
												</td>
											</tr>
										{{/each}}
									{{/if}}				
								{{/each}}

								{{##if arrDocuments}}
									{{##each arrDocuments}}
										<tr>
											<td style="width:3%;"></td>
											<td style="width:3%;"></td>
											<td style="width:41%;" class="border-right" colspan="2"><i class="fa-solid fa-solid fa-file fa-sm"></i> <b>Document:</b> {{docTitle}}</td>
											<td style="width:13%;" class="border-right text-center"></td>
											<td style="width:10%;" class="border-right text-right"></td>
											<td style="width:30%;" class="text-center">
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-plus"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-users"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1" title="Edit {{docTitle}}" onclick="editDocument({{usageID}},{{formatID}},{{itemID}});return false;"><i class="fa-solid fa-pencil"></i></a>
												<a href="##" class="btn btn-xs text-danger p-1" title="Remove {{docTitle}}" id="btnRemoveDoc_{{documentID}}_{{formatID}}_{{itemID}}" onclick="confirmDeleteDocument({{documentID}},{{formatID}},{{itemID}});return false;"><i class="fa-solid fa-trash-alt"></i></a>
												<a href="{{previewLink}}" class="btn btn-xs text-primary p-1" title="Preview {{docTitle}}" target="_blank"><i class="fa-solid fa-magnifying-glass"></i></a>
												<cfif this.storeInfo.offerStreams is 1>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-rss"></i></a>
												</cfif>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-money-bill-1 text-green"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>
											</td>
										</tr>
									{{/each}}
								{{/if}}
								{{##if arrStreams}}
									{{##each arrStreams}}
										<tr>
											<td style="width:3%;"></td>
											<td style="width:3%;"></td>
											<td style="width:41%;"class="border-right" colspan="2"><i class="fa-solid fa-rss fa-sm"></i> <b>Stream:</b> {{streamName}}</td>
											<td style="width:13%;" class="border-right text-center"></td>
											<td style="width:10%;" class="border-right text-right"></td>
											<td style="width:30%;" class="text-center">
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-plus"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-users"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1" title="Edit Stream" onclick="editStream({{usageID}},{{formatID}},{{itemID}});return false;"><i class="fa-solid fa-pencil"></i></a>
												<a href="##" class="btn btn-xs text-danger p-1" title="Remove Stream from Product Format" onclick="confirmDeleteStream({{usageID}});return false;"><i class="fa-solid fa-trash-alt"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-file"></i></a>
												<cfif this.storeInfo.offerStreams is 1>
													<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-rss"></i></a>
												</cfif>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-money-bill-1 text-green"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-up"></i></a>
												<a href="##" class="btn btn-xs text-primary p-1 invisible"><i class="fa-solid fa-arrow-down"></i></a>
											</td>
										</tr>
									{{/each}}
								{{/if}}
								</tbody>
							</table>
						{{/if}}						
					</script>
				</div>
			</cfif>
		</div>
	</div>
</cfform>
</cfoutput>