ALTER PROC dbo.ams_exportVirtualGroupRulesStructure
@orgID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgcode varchar(10), @cmd varchar(4000), @svrName varchar(40);
	DECLARE @tblMHCats TABLE (categoryID int, categoryName varchar(200), thePathExpanded varchar(MAX));
	DECLARE @tblRT TABLE (recordTypeID int);

	select @orgcode = orgcode from dbo.organizations where orgID = @orgID;
	set @svrName = CAST(serverproperty('servername') as varchar(40));

	-- delete org sync condition rows
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgcode = @orgcode;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgcode = @orgcode;

	INSERT INTO @tblMHCats 
	SELECT cat.categoryID, cat.categoryName, cat.thePathExpanded
	FROM dbo.sites as s
	cross apply dbo.fn_getRecursiveCategoriesByResourceType(s.siteID,'MemberHistoryAdmin') AS cat
	WHERE s.orgID = @orgID;

	INSERT INTO @tblRT
	select distinct recordtypeid
	from (
		select rt.recordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'recordType'
		inner join dbo.ams_recordTypes as rt on cast(rt.recordtypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
			union
		select rtrt.masterrecordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'role'
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
			union
		select rtrt.childrecordtypeid
		from dbo.ams_virtualGroupConditions as condition
		inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
			and k.conditionKey = 'role'
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
		where condition.orgID = @orgID
		and condition.conditionTypeID = 1
	) as tmp;

	INSERT INTO dataTransfer.dbo.sync_vgc_conditions (orgcode, orgID, conditionID, datatypeid, displaytypeid, expressionid, 
		datepart, dateexpressionid, verbose, fieldCode, uid, subProc, processArea, processValuesSection, hashvalue, 
		cvid, conditionkeyid, conditionvalue, afid, valueUID)
	select @orgcode, @orgID, condition.conditionID, condition.datatypeid, condition.displaytypeid, condition.expressionid, 
		isnull(condition.[datepart],'') as [datepart], isnull(condition.dateexpressionid,0) as dateexpressionid, 
		condition.[verbose], condition.fieldCode, condition.[uid], condition.subProc, condition.processArea,
		isnull(condition.processValuesSection,0) as processValuesSection, 
		'0x' + CAST('' AS XML).value('xs:hexBinary(sql:column("condition.hashValue"))', 'VARCHAR(MAX)') as hashvalue,
		condvalue.cvid, condvalue.conditionkeyid, condvalue.conditionvalue, isnull(condvalue.afid,0) as afid, 
		isnull(
			case	
			when k.conditionkey = 'subSubType' then (select cast([uid] as varchar(36)) from dbo.sub_Types where typeID = condvalue.conditionvalue)	
			when k.conditionkey = 'subSubscription' then (select cast([uid] as varchar(36)) from dbo.sub_subscriptions where subscriptionID = condvalue.conditionvalue)
			when k.conditionkey = 'subRate' then (select cast([uid] as varchar(36)) from dbo.sub_rates where rateID = condvalue.conditionvalue)
			when k.conditionkey = 'subFrequency' then (select cast([uid] as varchar(36)) from dbo.sub_frequencies where frequencyID = condvalue.conditionvalue)
			when k.conditionkey = 'programList' then (select cast([uid] as varchar(36)) from dbo.cp_programs where programID = condvalue.conditionvalue)
			when k.conditionkey = 'programRate' then (select cast([uid] as varchar(36)) from dbo.cp_rates where rateID = condvalue.conditionvalue)
			when k.conditionkey = 'programDistribution' then (select cast([uid] as varchar(36)) from dbo.cp_distributions where distribID = condvalue.conditionvalue)
			when k.conditionkey in ('programMonetaryCustomField','programNonMonetaryCustomField','clientReferralsCustomFieldID')
				then (select cast([uid] as varchar(36)) from dbo.cf_fields where fieldID = condvalue.conditionvalue)
			when k.conditionkey = 'programFrequency' then (select cast([uid] as varchar(36)) from dbo.cp_frequencies where frequencyID = condvalue.conditionvalue)
			end,'') as valueUID
	from dbo.ams_virtualGroupConditions as condition
	left outer join dbo.ams_virtualGroupConditionValues as condvalue 
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		on condvalue.conditionID = condition.conditionID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_rules (orgcode, orgID, rulename, rulexml, ruleSQL, isActive, uid, groupid)
	select @orgcode, @orgID, vgrule.rulename, cast(vgrv.rulexml as varchar(max)) as rulexml, vgrv.ruleSQL, vgrule.isActive, vgrule.uid, [group].groupid
	from dbo.ams_virtualGroupRules as vgrule
	inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
		and vgrv.ruleVersionID = vgrule.activeVersionID
	left outer join dbo.ams_virtualGroupRuleGroups as [group] on [group].ruleID = vgrule.ruleid
	where vgrule.orgID = @orgID
	and vgrule.ruleTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allrulegroups (orgcode, orgID, groupID, groupcode, groupname, thePathExpanded, uid)
	select distinct @orgcode, @orgID, vgroup.groupID, isnull(g.groupcode,'') as groupcode, g.groupname, vgroup.thePathExpanded, isnull(g.[uid],'') as [uid]
	from dbo.ams_virtualGroupRuleGroups as rg
	inner join dbo.ams_virtualGroupRules as r on r.ruleID = rg.ruleID
	inner join dbo.ams_groups as g on g.orgID = @orgID
		AND g.groupID = rg.groupID
	inner join dbo.fn_getRecursiveGroups(@orgID) as vgroup on vgroup.groupID = g.groupID
	where r.orgID = @orgID
	and r.ruleTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allfields (orgcode, orgID, fieldCode, fieldLabel)
	select distinct @orgcode, @orgID, field.fieldCode, isnull(vf.fieldLabel,'') as fieldLabel
	from dbo.ams_virtualGroupConditions as field
	cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(@orgID,field.fieldCode) as vf
	where field.orgID = @orgID
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_allvalueids (orgcode, orgID, conditionid, valueid, columnValue)
	select @orgcode, @orgID, condition.conditionid, 
		valueid = case 
			when left(condition.fieldCode,3) = 'md_' then mdcv.valueid
			when left(condition.fieldcode,4) = 'mad_' OR left(condition.fieldcode,5) = 'madt_' then dv.valueid
			when right(condition.fieldcode,10) = '_stateprov' then s.stateid
			when right(condition.fieldcode,8) = '_country' then c.countryid
			when left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED' then mat.addressTagTypeID
			when left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED' then met.emailTagTypeID
			else 0 end,
		columnValue = case 
			when left(condition.fieldCode,3) = 'md_' then coalesce(mdcv.columnValueString,cast(mdcv.columnValueDecimal2 as varchar(10)),cast(mdcv.columnValueInteger as varchar(10)),convert(varchar(10),mdcv.columnValueDate,101))
			when left(condition.fieldcode,4) = 'mad_' OR left(condition.fieldcode,5) = 'madt_' then dv.vendorValue
			when right(condition.fieldcode,10) = '_stateprov' then s.name
			when right(condition.fieldcode,8) = '_country' then c.country
			when left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED' then mat.addressTagType
			when left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED' then met.emailTagType
			else '' end
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupExpressions as exp on exp.expressionID = condition.expressionID
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as valuekey on valuekey.conditionKeyID = condvalue.conditionKeyID
		and valuekey.conditionKey = 'value'
	left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'md_'
	left outer join dbo.ams_states as s on s.stateid = condvalue.conditionvalue and right(condition.fieldcode,10) = '_stateprov'
	left outer join dbo.ams_countries as c on c.countryid = condvalue.conditionvalue and right(condition.fieldcode,8) = '_country'
	left outer join dbo.ams_memberDistrictValues as dv on dv.valueID = condvalue.conditionvalue and (left(condition.fieldCode,4) = 'mad_' or left(condition.fieldCode,5) = 'madt_')
	left outer join dbo.ams_memberAddressTagTypes as mat on mat.orgID = @orgID and mat.addressTagTypeID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED'
	left outer join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and met.emailTagTypeID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED'
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1
	and (
		left(condition.fieldCode,3) = 'md_' 
		or left(condition.fieldCode,4) = 'mad_' 
		or left(condition.fieldCode,5) = 'madt_' 
		or right(condition.fieldcode,10) = '_stateprov' 
		or right(condition.fieldcode,8) = '_country'
		or (left(condition.fieldCode,3) = 'ma_' and condition.subProc = 'MA_TAGGED')
		or (left(condition.fieldCode,3) = 'me_' and condition.subProc = 'ME_TAGGED')
	)
	and condition.displayTypeId in (2,3)
	and exp.expression in ('eq','neq','istaggedwith')
	and condition.dataTypeID <> 5;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'mw', mwt.websitetypeid, mwt.websitetype, mwt.[uid]
	from dbo.ams_memberWebsiteTypes as mwt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mwt.websitetypeid
	where mwt.orgID = @orgID
	and left(field.fieldcode,3) = 'mw_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'me', met.emailtypeid, met.emailtype, met.[uid]
	from dbo.ams_memberEmailTypes as met
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtypeid
	where met.orgID = @orgID
	and left(field.fieldcode,3) = 'me_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'me', met.emailtypeid, met.emailtype, met.[uid]
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'emailTypeID'
	inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID and cast(met.emailTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'met', met.emailtagtypeid, met.emailtagtype, met.[uid]
	from dbo.ams_memberEmailTagTypes as met
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtagtypeid
	where met.orgID = @orgID
	and left(field.fieldcode,4) = 'met_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'met', met.emailtagtypeid, met.emailtagtype, met.[uid]
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'emailTagTypeID'
	inner join dbo.ams_memberEmailTagTypes as met on met.orgID = @orgID and cast(met.emailTagTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'ma', mat.addresstypeid, mat.addresstype, mat.[uid]
	from dbo.ams_memberAddressTypes as mat
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstypeid
	where mat.orgID = @orgID
	and (left(field.fieldcode,3) in ('ma_','mp_') or left(field.fieldcode,4) = 'mad_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mat', mat.addresstagtypeid, mat.addresstagtype
	from dbo.ams_memberAddressTagTypes as mat
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstagtypeid
	where mat.orgID = @orgID
	and (left(field.fieldcode,4) in ('mat_','mpt_') OR left(field.fieldcode,5) = 'madt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'mp', mpt.phonetypeid, mpt.phonetype, mpt.[uid]
	from dbo.ams_memberPhoneTypes as mpt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mpt.phonetypeid
	where mpt.orgID = @orgID
	and (left(field.fieldcode,3) = 'mp_' or left(field.fieldcode,4) = 'mpt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mad', mdt.districttypeid, mdt.districttype
	from dbo.ams_memberDistrictTypes as mdt
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mdt.districttypeid
	where mdt.orgID = @orgID
	and (left(field.fieldcode,4) = 'mad_' OR left(field.fieldcode,5) = 'madt_')
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mpl', mpl.PLTypeID, mpl.PLName
	from dbo.ams_memberProfessionalLicenseTypes as mpl
	inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mpl.PLTypeID
	where mpl.orgID = @orgID
	and left(field.fieldcode,4) = 'mpl_'
	and field.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgcode, @orgID, 'mh', cat.categoryID, cat.categoryName, cat.thePathExpanded
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('historyCategory','historySubCategory')
	inner join @tblMHCats as cat on cast(cat.categoryID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'af', af.afid, af.afname, af.uid
	from dbo.af_advanceFormulas as af
	inner join dbo.sites as s on s.siteID = af.siteID
	where s.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'rt', rt.recordtypeid, rt.recordtypecode
	from @tblRT as tmp
	inner join dbo.ams_recordTypes as rt on rt.recordtypeid = tmp.recordtypeid;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'rrt', rt.relationshiptypeid, rt.relationshiptypecode
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'role'
	inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
	inner join dbo.ams_recordRelationshipTypes as rt on rt.relationshiptypeid = rtrt.relationshipTypeID
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemID2, itemID3, itemID4)
	select distinct @orgcode, @orgID, 'rtrt', rtrt.recordtyperelationshiptypeid, rtrt.relationshiptypeid, rtrt.masterrecordtypeid, rtrt.childrecordtypeid
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'role'
	inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordtyperelationshiptypeid as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgcode, @orgID, 'acctmp', mp.profileID, mp.profileCode, mp.profileName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('acctBalMP','acctInvCOFMP','acctCCProf')
	inner join dbo.mp_profiles as mp on cast(mp.profileID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'acctip', ip.profileID, ip.profileName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'acctInvProf'
	inner join dbo.tr_invoiceProfiles as ip on cast(ip.profileID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'acctcctype', mpct.cardTypeID, mpct.cardType
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'acctCCCardType'
	inner join dbo.mp_cardTypes as mpct on cast(mpct.cardTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType, itemUID)
	select distinct @orgcode, @orgID, 'acctgl', gl.GLAccountID, gl.accountName, gl.uid
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'revenueGL'
	inner join dbo.tr_GLAccounts as gl on cast(gl.glAccountID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID
	and condition.conditionTypeID = 1;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'sgsubuser', su.subuserID, su.username
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'sendGridSubUser'
	inner join platformMail.dbo.sendgrid_subusers as su on cast(su.subuserID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'mcsiteid', s.siteID, s.siteCode
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey in ('suppListSiteID','referralSiteID')
	inner join dbo.sites as s on cast(s.siteID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'clmodeid', clm.consentListModeID, clm.modeName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListModeID'
	inner join platformMail.dbo.email_consentListModes as clm on cast(clm.consentListModeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'cltypeid', clt.consentListTypeID, clt.consentListTypeName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListTypeID'
	inner join platformMail.dbo.email_consentListTypes as clt on cast(clt.consentListTypeID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	-- don't limit to active consent lists here
	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select distinct @orgcode, @orgID, 'clid', cl.consentListID, cl.consentListName
	from dbo.ams_virtualGroupConditions as condition
	inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = condvalue.conditionKeyID
		and k.conditionKey = 'consentListID'
	inner join platformMail.dbo.email_consentLists as cl on cast(cl.consentListID as varchar(10)) = condvalue.conditionValue
	where condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'refstatus', rs.clientReferralStatusID, rs.statusName
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'clientReferralStatusID'
	INNER JOIN dbo.ref_clientReferralStatus AS rs ON CAST(rs.clientReferralStatusID as varchar(10)) = condvalue.conditionValue
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'refpanel', p.panelID, CASE WHEN p2.panelID IS NOT NULL THEN p2.[name] + ' \ ' ELSE '' END + p.[name]
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'panelID'
	INNER JOIN dbo.ref_panels AS p ON CAST(p.panelID as varchar(10)) = condvalue.conditionValue
	LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = p.panelParentID
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType2)
	SELECT DISTINCT @orgcode, @orgID, 'reffdstat', st.feeDiscrepancyStatusID, st.statusName
	FROM dbo.ams_virtualGroupConditions AS condition
	INNER JOIN dbo.ams_virtualGroupConditionValues AS condvalue ON condvalue.conditionID = condition.conditionID
	INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = condvalue.conditionKeyID
		AND k.conditionKey = 'feeDiscrepancyStatusID'
	INNER JOIN dbo.ref_feeDiscrepancyStatuses AS st ON CAST(st.feeDiscrepancyStatusID as varchar(10)) = condvalue.conditionValue
	WHERE condition.orgID = @orgID;

	INSERT INTO dataTransfer.dbo.sync_vgc_supporting (orgcode, orgID, cat, itemID, itemType)
	select @orgcode, @orgID, 'ck', conditionkeyid, conditionkey
	from dbo.ams_virtualGroupConditionKeys;

	-- export to files
	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, conditionID, datatypeid, displaytypeid, expressionid, datepart, dateexpressionid, verbose, fieldCode, uid, subProc, processArea, processValuesSection, hashvalue, cvid, conditionkeyid, conditionvalue, afid, valueUID, useValue, finalAction from datatransfer.dbo.sync_vgc_conditions where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_conditions.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, rulename, rulexml, ruleSQL, isActive, uid, groupid, finalAction from datatransfer.dbo.sync_vgc_rules where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_rules.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, groupID, groupcode, groupname, thePathExpanded, uid, useGroupID from datatransfer.dbo.sync_vgc_allrulegroups where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allrulegroups.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, fieldCode, fieldLabel, useFieldCode from dataTransfer.dbo.sync_vgc_allfields where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allfields.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, conditionid, valueid, columnValue, useValue from dataTransfer.dbo.sync_vgc_allvalueids where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_allvalueids.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	set @cmd = 'bcp "select orgcode, cast(null as int) as orgID, cat, itemID, itemID2, itemID3, itemID4, itemType, itemUID, itemType2, useID from dataTransfer.dbo.sync_vgc_supporting where orgcode = ''' + @orgcode + '''" queryout "'+@exportPath+'sync_vgc_supporting.bcp" -t'+char(7)+' -w -T -S' + @svrName;
	exec master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear sync tables
	DELETE FROM datatransfer.dbo.sync_vgc_conditions WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_rules WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allrulegroups WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allfields WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_allvalueids WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_vgc_supporting WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
