<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);
		
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="qryEventRoles" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryRoles = "">

		<cfset var EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.siteID)>

		<cfquery name="qryRoles" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @eventRolesCTID int, @EventAdminSRID int;
				set @EventAdminSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#EventAdminSRID#">;
				select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@EventAdminSRID,'Event Roles');

				select categoryID, categoryName
				from dbo.cms_categories
				where categoryTreeID = @eventRolesCTID
				and isActive = 1 
				and parentCategoryID is NULL;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn qryRoles>
	</cffunction>	

	<cffunction name="getEventRoleFields" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.siteID)>

		<cfquery name="local.qryEventRoleFields" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @EventAdminSRID int, @usageID int, @eventRolesCTID int;
				set @EventAdminSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.EventAdminSRID#">;
				select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');
				select @usageID = dbo.fn_cf_getUsageID('EventAdmin','Role',null);

				select c.categoryID, f.fieldID, c.categoryname + ': ' + f.fieldReference as titleOnInvoice
				from dbo.cf_fields as f 
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					and f.usageID = @usageID
					and f.controllingSiteResourceID = @eventAdminSRID
				inner join dbo.cms_categories as c on c.categoryID = f.detailID
				where c.categoryTreeID = @eventRolesCTID
				and c.isActive = 1 
				and c.parentCategoryID is NULL
				and f.isActive = 1
				order by c.categoryID, f.fieldOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrFields = ArrayNew(1)>
		<cfloop query="local.qryEventRoleFields">
			<cfset local.tmpStr = { categoryID=local.qryEventRoleFields.categoryID, fieldID=local.qryEventRoleFields.fieldID, titleOnInvoice=local.qryEventRoleFields.titleOnInvoice }>
			<cfset arrayAppend(local.arrFields, local.tmpStr)>
		</cfloop>

		<cfreturn serializeJSON(local.arrFields)>
	</cffunction>	

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.frmReportView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmreportview/text())")>
			<cfif NOT len(local.frmReportView)>
				<cfset local.frmReportView = "summary,detail">
			</cfif>
			<cfset local.frmRegRTL = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmregrtl/text())")>
			<cfif NOT len(local.frmRegRTL)>
				<cfset local.frmRegRTL = 1>
			</cfif>
			<cfset local.frmRegistrantNotes = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmregistrantnotes/text())")>
			<cfif NOT len(local.frmRegistrantNotes)>
				<cfset local.frmRegistrantNotes = 1>
			</cfif>
			<cfset local.frmPageBreaks = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmpagebreaks/text())")>
			<cfset local.frmRoleFields = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrolefields/text())")>

			<cfset local.qryRoles = qryEventRoles(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfset local.jsonRoleFields = getEventRoleFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

			<cfset local.strEventWidgetData = { title='Define Event Filter', 
				description='Filter the events appearing on this report using the defined criteria below.',
				gridext="#this.siteResourceID#_1", gridClassList='mb-5 stepDIV', 
				initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				filterMode=1, showIcons=1 }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strEventWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strEventWidget = createObject("component","model.admin.common.modules.eventWidget.eventWidget").renderWidget(strWidgetData=local.strEventWidgetData)>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strEventWidget.js#
				<script language="javascript">
					var #toScript(local.jsonRoleFields,"jsonRoleFields")#

					function forceEvent() {
						var feResult = false;

						var arrView = $('##frmReportView').val() || '';
						if (arrView.length == 0) {
							rptShowAlert('This report requires you to select one or more sections to include.'); 
							feResult=false;
						} else {
							var forceEventResult = function(s) {
								if (s.success && s.success.toLowerCase() == 'true' && s.eventcount > 0) feResult=true; 
								else { rptShowAlert('This report requires you to select one or more events in the Event Filter.'); feResult=false; }
							};
							var objParams = { rptid:#val(arguments.event.getValue('qryReportInfo').reportID)#, csrid:#this.siteResourceID# };
							TS_AJX_SYNC('EVWIDGET','checkEventFilterLength',objParams,forceEventResult,forceEventResult,5000,forceEventResult);
						}
						return feResult;
					}

					function checkSections() {
						$('##divReportShowScreenLoading').hide();
						$('##divReportShowScreen').html('').hide();
						var arrView = $('##frmReportView').val() || '';

						<!--- if only summary, then hide customcsv buttons and fieldsets --->
						if (arrView == 'summary' || arrView == '') {
							$('button##btnReportBarcustomcsv, div##stepFieldsetsDIVfieldsets, div##div_rtl, div##div_erf').hide();
						} else {
							$('button##btnReportBarcustomcsv, div##stepFieldsetsDIVfieldsets').show();
							if (arrView.indexOf('detail') !== -1) {
								$('div##div_rtl').show();
							} else {
								$('div##div_rtl').hide();
							}

							<cfif listLen(local.frmRoleFields)>
								if ($('##frmRoleFields').find('option').length == 0) {
									var #toScript(listToArray(local.frmRoleFields),"currSelFlds")#
								} else {
									var currSelFlds = $('##frmRoleFields').val() || '';
								}
							<cfelse>
								var currSelFlds = $('##frmRoleFields').val() || '';
							</cfif>
							var newSel = $('<select>');
							var jsonFlds = $.parseJSON(jsonRoleFields);
							for (var i=0, tot=arrView.length; i < tot; i++) {
								if (arrView[i] == 'summary' || arrView[i] == 'detail') {
								} else {
									$.each(jsonFlds, function(idx, obj) {
										if (obj.CATEGORYID == arrView[i])
											newSel.append('<option value="' + obj.CATEGORYID + '_' + obj.FIELDID + '">' + obj.TITLEONINVOICE + '</option>');
									});
								}
							}
							$('##frmRoleFields').find('option').remove().end();
							newSel.clone().children().appendTo('##frmRoleFields');
							$('##frmRoleFields').find('option').removeAttr('selected');
							$('##frmRoleFields').val(currSelFlds);
							$("##frmRoleFields").trigger('change');

							if ($('##frmRoleFields').find('option').length) {
								$('div##div_erf').show();
							} else {
								$('div##div_erf').hide();
							}
						}
					}

					<!--- for IE --->
					if (!Array.prototype.indexOf) {
					   Array.prototype.indexOf = function(item) {
					      var i = this.length;
					      while (i--) {
					         if (this[i] === item) return i;
					      }
						  return -1;
					   }
					}

					$(function() {
						mca_setupSelect2();
						checkSections();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfform name="frmReport"  id="frmReport" method="post">
					<cfinput type="hidden" name="reportAction"  id="reportAction" value="">
					#local.strEventWidget.html#
					#showStepMemberCriteria(event=arguments.event, title="Optionally Define Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")#

					<div class="mb-5 stepDIV">
						<h5>Additional Report Criteria</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmReportView" class="col-md-4 col-sm-12 col-form-label">Include Sections</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmReportView" id="frmReportView" onChange="checkSections()" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Choose Sections">
											<option value="summary" <cfif listFindNoCase(local.frmReportView,"summary")>selected</cfif>>Summary Statistics</option>
											<option value="detail" <cfif listFindNoCase(local.frmReportView,"detail")>selected</cfif>>Registrant Detail</option>
											<cfloop query="local.qryRoles">
												<option value="#local.qryRoles.categoryID#" <cfif listFind(local.frmReportView,local.qryRoles.categoryID)>selected</cfif>>Event Role: #local.qryRoles.categoryName#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row" id="div_erf">
									<label for="frmRoleFields" class="col-md-4 col-sm-12 col-form-label">Include Role Fields</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmRoleFields" id="frmRoleFields" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Choose Role Custom Fields"></select>
									</div>
								</div>
								<div class="form-group row" id="div_rtl">
									<label class="col-md-4 col-sm-12 col-form-label">Registrant Detail</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input type="checkbox" class="form-check-input" name="frmRegRTL" id="frmRegRTL" value="1" <cfif local.frmRegRTL is 1>checked</cfif>>
											<label class="form-check-label" for="frmRegRTL">Include registration rate, period, and location in the registrant detail</label>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label class="col-md-4 col-sm-12 col-form-label">PDF Page Breaks</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input type="checkbox" class="form-check-input" name="frmPageBreaks" id="frmPageBreaks" value="1" <cfif local.frmPageBreaks is 1>checked</cfif>>
											<label class="form-check-label" for="frmPageBreaks">Insert page breaks into PDF after each event</label>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label class="col-md-4 col-sm-12 col-form-label">Registrant Notes</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input type="checkbox" class="form-check-input" name="frmRegistrantNotes" id="frmRegistrantNotes" value="1" <cfif local.frmRegistrantNotes is 1>checked</cfif>>
											<label class="form-check-label" for="frmRegistrantNotes">Include Internal Registrant Notes</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepFieldsets(event=arguments.event, desc="Fieldsets will be included in both Registrant Detail and Event Role sections.")#
					#showButtonBar(event=arguments.event,validateFunction='forceEvent')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="generateData" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="frmReportView" type="string" required="true">
		<cfargument name="frmRegRTL" type="numeric" required="true">
		<cfargument name="frmRegistrantNotes" type="numeric" required="true">		
		<cfargument name="frmRoleFields" type="string" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="csvFileName" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfset local.frmIncludeCatIDs = arguments.frmReportView>
		<cfif ListFind(local.frmIncludeCatIDs,"summary")>
			<cfset local.frmIncludeCatIDs = ListDeleteAt(local.frmIncludeCatIDs,ListFind(local.frmIncludeCatIDs,"summary"))>
		</cfif>
		<cfif ListFind(local.frmIncludeCatIDs,"detail")>
			<cfset local.frmIncludeCatIDs = ListDeleteAt(local.frmIncludeCatIDs,ListFind(local.frmIncludeCatIDs,"detail"))>
		</cfif>
		<cfif arguments.reportAction eq "customcsv">
			<cfset local.mode = "export">
		<cfelse>
			<cfset local.mode = "report">
		</cfif>

		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventRoles">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @eventAdminSRID int, @eventRolesCTID int;
				set @eventAdminSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.EventAdminSRID#">;
				select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');
				
				select categoryID, categoryName, quoteName(categoryName) as catNameQuoted, 'eventroles.' + quoteName(categoryName) as catNameSelect
				from dbo.cms_categories
				where categoryTreeID = @eventRolesCTID
				and isActive = 1 
				<cfif NOT listFindNoCase(arguments.frmReportView,"detail") and listlen(local.frmIncludeCatIDs)>
					and categoryID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.frmIncludeCatIDs#" list="true">)
				</cfif>
				and parentCategoryID is NULL
				order by categoryName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryData" result="local.strReturn.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @fullsql varchar(max);
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@outputFieldsXML xml;

				<cfif len(arguments.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#Final') IS NOT NULL
					DROP TABLE ###local.tempTableName#Final;
				IF OBJECT_ID('tempdb..###local.tempTableName#Roles') IS NOT NULL
					DROP TABLE ###local.tempTableName#Roles;
				IF OBJECT_ID('tempdb..##tmpSummary') IS NOT NULL
					DROP TABLE ##tmpSummary;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);	
				CREATE TABLE ###local.tempTableName#Roles (categoryID int, categoryName varchar(200));
				CREATE TABLE ##tmpSummary (eventID int, numRegistered int, numAttended int, regFee decimal(18,2), regDue decimal(18,2));
				CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
				CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

				<cfif listLen(local.frmIncludeCatIDs)>
					IF OBJECT_ID('tempdb..###local.tempTableName#RolesQA') IS NOT NULL
						DROP TABLE ###local.tempTableName#RolesQA;
					IF OBJECT_ID('tempdb..#####local.tempTableName#RolesData') IS NOT NULL
						DROP TABLE #####local.tempTableName#RolesData;
					CREATE TABLE ###local.tempTableName#RolesQA (registrantID int, titleOnInvoice varchar(max), answer varchar(max), INDEX IX_tbltmpRQA_registrantID (registrantID));
				</cfif>

				-- get event fees
				IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
					DROP TABLE ##tmpEventsForFee; 
				IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
					DROP TABLE ##tmpEventsForFeeResult; 
				IF OBJECT_ID('tempdb..###local.tempTableName#Trans') IS NOT NULL
					DROP TABLE ###local.tempTableName#Trans;
				create table ##tmpEventsForFee (eventID int PRIMARY KEY);
				create table ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);
				CREATE TABLE ###local.tempTableName#Trans (registrantID int, totalRegFee decimal(18,2), regFeePaid decimal(18,2));

				insert into ##tmpEventsForFee (eventID)
				select eventID from @tblE;

				EXEC dbo.ev_registrantTransactionsByEventBulk;

				INSERT INTO ###local.tempTableName#Trans (registrantID, totalRegFee, regFeePaid)
				select fees.registrantID, sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
				from ##tmpEventsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				group by fees.registrantID;

				SELECT e.eventID, tmp.startTime, tmp.endTime, tmp.isAllDayEvent, e.reportCode, tmp.eventTitle,
					tmp.locationTitle, r.registrantid, r.dateRegistered, cast(r.attended as smallint) as Attended, 
					regFee.totalRegFee, regFee.totalRegFee-regFee.regFeePaid as amountDue, regFee.regFeePaid,
					rate.rateID, rate.rateName, rate.reportCode as rateReportCode,  
					convert(varchar(10),dateadd(dd,1-datepart(dw,r.dateRegistered),r.dateRegistered),112) as regWeekSort,
					'Week of ' + convert(varchar(10),dateadd(dd,1-datepart(dw,r.dateRegistered),r.dateRegistered),101) as regWeek,
					isnull(nullif(maFirst.stateName,'') + ' - ' + nullif(maFirst.city,''),'Undisclosed') as regLocation,
					m.memberID, m.lastname, m.firstname, m.memberNumber, r.internalNotes, m.Company, m.hasMemberPhotoThumb 
					<cfif local.qryEventRoles.recordcount>
						, #valueList(local.qryEventRoles.catNameSelect)#
						, (select '-' + STRING_AGG(innerrc.categoryID,'|') + '-' 
							from dbo.ev_registrantCategories as innerrc
							where innerrc.categoryID in (#valuelist(local.qryEventRoles.categoryID)#)
							and innerrc.registrantID = r.registrantid) as roleIDPipeList
					</cfif>
				into ###local.tempTableName#
				FROM dbo.ev_events as e
				INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
				INNER JOIN dbo.ev_registration as rn on rn.siteID = @siteID and rn.eventID = e.eventID
					AND rn.status = 'A'
				INNER JOIN dbo.ev_registrants as r on r.registrationID = rn.registrationID
					and r.status = 'A'
				LEFT OUTER JOIN dbo.ev_rates as rate on rate.rateID = r.rateID
				INNER JOIN dbo.ams_members as mReg on mReg.memberID = r.memberID and mReg.orgID = @orgID
				INNER JOIN dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0 
					and m.orgID = @orgID
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
				LEFT OUTER JOIN dbo.ams_memberAddresses as maFirst 
					INNER JOIN dbo.ams_memberAddressTypes as matFirst on matFirst.addressTypeID = maFirst.addressTypeID and matFirst.addressTypeOrder = 1
					on maFirst.orgID = @orgID
					and maFirst.memberid = m.memberid
				LEFT OUTER JOIN ###local.tempTableName#Trans as regFee on regFee.registrantID = r.registrantID
				<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
				<cfif local.qryEventRoles.recordcount>
					OUTER APPLY (
						SELECT #valueList(local.qryEventRoles.catNameQuoted)#
						FROM (
							SELECT c.categoryName
							FROM dbo.ev_registrantCategories er
							INNER JOIN dbo.cms_categories c on c.categoryID = er.categoryID
							WHERE er.registrantID = r.registrantID
						) as tmp
						PIVOT (min(categoryName) for categoryName in (#valueList(local.qryEventRoles.catNameQuoted)#)) as pvt
					) as eventroles
				</cfif>
				WHERE e.siteID = @siteID
				AND e.status = 'A'
				<cfif NOT listFindNoCase(arguments.frmReportView,"detail") and listlen(local.frmIncludeCatIDs)>
					AND EXISTS (SELECT registrantID 
								FROM dbo.ev_registrantCategories as innerRC2 
								WHERE innerRC2.categoryID in (#valuelist(local.qryEventRoles.categoryID)#)
								and innerRC2.registrantID = r.registrantid)
				</cfif>;

				<cfif arguments.reportAction eq "customcsv">
					EXEC tempdb..sp_rename '###local.tempTableName#.eventID', 'Event ID', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.startTime', 'Event Start Date', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.endTime', 'Event End Date', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.reportCode', 'Event Report Code', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.eventTitle', 'Event', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.locationTitle', 'Event Location', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.dateRegistered', 'Date Registered', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.totalRegFee', 'Amount Billed', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.amountDue', 'Amount Due', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.regFeePaid', 'Amount Paid', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.lastname', 'Last Name', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.firstname', 'First Name', 'COLUMN';
					EXEC tempdb..sp_rename '###local.tempTableName#.memberNumber', 'MemberNumber', 'COLUMN';
					<cfif arguments.frmRegistrantNotes is 0>
						ALTER TABLE ###local.tempTableName# DROP COLUMN internalNotes;
					<cfelse>
						EXEC tempdb..sp_rename '###local.tempTableName#.internalNotes', 'Internal Registrant Notes', 'COLUMN';
					</cfif>
					ALTER TABLE ###local.tempTableName# DROP COLUMN isAllDayEvent;
					ALTER TABLE ###local.tempTableName# DROP COLUMN regWeekSort;
					ALTER TABLE ###local.tempTableName# DROP COLUMN hasMemberPhotoThumb;
					<cfif arguments.frmRegRTL is 0>
						ALTER TABLE ###local.tempTableName# DROP COLUMN rateName;
						ALTER TABLE ###local.tempTableName# DROP COLUMN rateReportCode;
						ALTER TABLE ###local.tempTableName# DROP COLUMN regWeek;
						ALTER TABLE ###local.tempTableName# DROP COLUMN regLocation;
					<cfelse>
						EXEC tempdb..sp_rename '###local.tempTableName#.rateName', 'Rate', 'COLUMN';
						EXEC tempdb..sp_rename '###local.tempTableName#.rateReportCode', 'Rate Report Code', 'COLUMN';
						EXEC tempdb..sp_rename '###local.tempTableName#.regWeek', 'Registration Period', 'COLUMN';
						EXEC tempdb..sp_rename '###local.tempTableName#.regLocation', 'Registration Location', 'COLUMN';
					</cfif>
				</cfif>

				CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName# (memberID asc);

				<cfif listLen(arguments.frmRoleFields)>
					<cfloop query="local.qryEventRoles">
						INSERT INTO ###local.tempTableName#Roles VALUES (#local.qryEventRoles.categoryID#, '#local.qryEventRoles.categoryName#');
					</cfloop>

					-- role custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT registrantID, 'EventRole'
					FROM ###local.tempTableName#;

					EXEC dbo.cf_getFieldData;
						
					INSERT INTO ###local.tempTableName#RolesQA (registrantID, titleOnInvoice, answer)
					SELECT fd.itemID AS registrantID, replace(tmpR.categoryName + ': ' + f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
					FROM ###local.tempTableName# as registrants
					INNER JOIN dbo.ev_registrantCategories as regcat on regcat.registrantID = registrants.registrantID
					INNER JOIN ###local.tempTableName#Roles as tmpR on tmpR.categoryID = regcat.categoryID
					INNER JOIN ##tmp_CF_FieldData as fd on fd.itemID = registrants.registrantID
					INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
						and f.detailID = regcat.categoryID
					WHERE cast(tmpR.categoryID as varchar(10)) + '_' + cast(f.fieldID as varchar(10)) in (#listQualify(arguments.frmRoleFields,"'")#);
					
					DECLARE @rqList varchar(max);
					SELECT @rqList = COALESCE(@rqList + ',', '') + quoteName(titleonInvoice) from ###local.tempTableName#RolesQA group by titleOnInvoice;
					IF @rqList <> '' BEGIN
						SELECT @fullsql = '
							SELECT * 
							into #####local.tempTableName#RolesData
							FROM (
								SELECT registrantid as roleDataRegistrantID, titleOnInvoice, answer
								FROM ###local.tempTableName#RolesQA
							) as reg
							PIVOT (min(answer) for titleonInvoice in (' + @rqList + ')) as p ';
						EXEC(@fullsql);
					END
					ELSE
						EXEC('SELECT registrantID as roleDataRegistrantID INTO #####local.tempTableName#RolesData FROM ###local.tempTableName# WHERE 0=1');
				</cfif>
				
				-- get members fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
					@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
					@membersTableName='###local.tempTableName#', @membersResultTableName='##tmpMembersFS', 
					@linkedMembers=0, @mode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.mode#">, 
					@outputFieldsXML=@outputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				EXEC tempdb..sp_rename '###local.tempTableName#.memberID', 'MCMemberID', 'COLUMN';

				SELECT tm.*, m.*<cfif listLen(arguments.frmRoleFields)>, rtq.*</cfif>
				INTO ###local.tempTableName#Final
				FROM ###local.tempTableName# as tm
				INNER JOIN ##tmpMembersFS as m on m.memberID = tm.MCMemberID
				<cfif listLen(arguments.frmRoleFields)>
					LEFT OUTER JOIN #####local.tempTableName#RolesData as rtq on rtq.roleDataRegistrantID = tm.registrantid 
				</cfif>;

				ALTER TABLE ###local.tempTableName#Final DROP COLUMN MCMemberID;

				<cfif arguments.reportAction eq "customcsv">
					#generateFinalBCPTable(tblName="###local.tempTableName#Final", dropFields="memberid,registrantid,rateID,roleIDPipeList,roleDataRegistrantID")#
				<cfelse>
					insert into ##tmpSummary (eventID, numRegistered, numAttended, regFee, regDue)
					select eventID, count(registrantID) as NumRegistered, sum(case when attended = 1 then 1 else 0 end) as NumAttended,
						sum(totalRegFee) as regFee, sum(amountDue) as regDue
					from ###local.tempTableName#Final
					group by eventID;

					SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM (
						SELECT f.*, s.numRegistered as NumRegisteredForEvent, s.numAttended as NumAttendedForEvent, 
							s.regFee as regFeeForEvent, s.regDue as regDueForEvent,
							ROW_NUMBER() OVER(ORDER BY f.startTime, f.eventTitle, f.eventID, f.lastname, f.firstname) as mc_row
						FROM ###local.tempTableName#Final as f
						INNER JOIN ##tmpSummary as s on s.eventID = f.eventID
					) AS finalData
					ORDER BY mc_row;
				</cfif>

				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#Final') IS NOT NULL
					DROP TABLE ###local.tempTableName#Final;
				IF OBJECT_ID('tempdb..###local.tempTableName#Trans') IS NOT NULL
					DROP TABLE ###local.tempTableName#Trans;
				IF OBJECT_ID('tempdb..###local.tempTableName#Roles') IS NOT NULL
					DROP TABLE ###local.tempTableName#Roles;
				IF OBJECT_ID('tempdb..##tmpSummary') IS NOT NULL
					DROP TABLE ##tmpSummary;

				<cfif listLen(local.frmIncludeCatIDs)>
					IF OBJECT_ID('tempdb..###local.tempTableName#RolesQA') IS NOT NULL
						DROP TABLE ###local.tempTableName#RolesQA;
					IF OBJECT_ID('tempdb..#####local.tempTableName#RolesData') IS NOT NULL
						DROP TABLE #####local.tempTableName#RolesData;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	

		<cfset local.strReturn.frmIncludeCatIDs = local.frmIncludeCatIDs>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportview/text())")>
			<cfif NOT len(local.frmReportView)>
				<cfset local.frmReportView = "summary,detail">
			</cfif>
			<cfset local.frmRegRTL = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmregrtl/text())")>
			<cfif NOT len(local.frmRegRTL)>
				<cfset local.frmRegRTL = 1>
			</cfif>
			<cfset local.frmPageBreaks = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmpagebreaks/text())")>
			<cfset local.frmRegistrantNotes = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmregistrantnotes/text())")>
			<cfif NOT len(local.frmRegistrantNotes)>
				<cfset local.frmRegistrantNotes = 1>
			</cfif>
			<cfset local.frmRoleFields = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrolefields/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>

			<cfif arguments.reportAction eq "pdf">
				<cfset local.isPDF = true>
			<cfelse>
				<cfset local.isPDF = false>
			</cfif>

			<cfset local.qryRoles = qryEventRoles(siteID=local.mc_siteInfo.siteID)>

			<cfset local.strReport = generateData(strSQLPrep=local.strSQLPrep, orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID, 
				 frmReportView=local.frmReportView, frmRegRTL=local.frmRegRTL, frmRegistrantNotes=local.frmRegistrantNotes,
				 frmRoleFields=local.frmRoleFields, reportAction=arguments.reportAction)>

			<cfif local.strReport.qryData.recordcount>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.strReport.qryData.mc_outputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.strReport.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfset local.qryData = local.strReport.qryData>
					<cfoutput query="local.qryData" group="eventid">
						<cfif listFindNoCase(local.frmReportView,"summary")>
							<!--- this silly workaround came from https://luceeserver.atlassian.net/browse/LDEV-685 since I couldnt run the aggregate in QofQ in Lucee directly because of a "bad" column name in original data --->
							<cfquery name="local.qryEventDataRaw" dbtype="query">
								select rateID, rateName, regWeek, regWeekSort, UPPER(regLocation) as regLocation, totalRegFee, registrantid, attended
								from [local].qryData
								where eventid = #local.qryData.eventid#
							</cfquery>
							<cfquery name="local.qrySummaryRates" dbtype="query">
								select rateID, rateName, sum(totalRegFee) as regFee, count(registrantid) as regCount, sum(attended) as attendCount
								from [local].qryEventDataRaw
								group by rateID, rateName
								order by regCount desc, rateName
							</cfquery>
							<cfquery name="local.qrySummaryTimes" dbtype="query">
								select regWeek, regWeekSort, sum(totalRegFee) as regFee, count(registrantid) as regCount, sum(attended) as attendCount
								from [local].qryEventDataRaw
								group by regWeek, regWeekSort
								order by regCount desc, regWeekSort
							</cfquery>
							<cfquery name="local.qrySummaryGeo" dbtype="query">
								select regLocation, sum(totalRegFee) as regFee, count(registrantid) as regCount, sum(attended) as attendCount
								from [local].qryEventDataRaw
								group by regLocation
								order by regCount desc, regLocation
							</cfquery>
						</cfif>

						<cfset local.qryEventRegCredits = CreateObject("component","model.admin.credit.credit").getCreditOfferedGrid("events",local.qryData.eventID)>
						<cfquery name="local.qryEventRegCreditsApproved" dbtype="query">
							select AuthorityName, offeredCreditTypes, statementAppProvider, creditMessage
							from [local].qryEventRegCredits	
							where status = 'Approved'
							order by AuthorityName
						</cfquery>
						<cfsavecontent variable="local.creditdetail">
							<cfloop query="local.qryEventRegCreditsApproved">
								<cfset local.arrTypes = xmlSearch(local.qryEventRegCreditsApproved.offeredCreditTypes,"/creditTypes/ect")>
								#local.qryEventRegCreditsApproved.AuthorityName#: 
								<cfloop from="1" to="#arrayLen(local.arrTypes)#" index="local.thisTypeNum">
									#local.arrTypes[local.thisTypeNum].xmlAttributes.creditValue# #local.arrTypes[local.thisTypeNum].xmlAttributes.creditType#<cfif local.thisTypeNum lt arrayLen(local.arrTypes)>, </cfif>
								</cfloop>
								<cfif local.qryEventRegCreditsApproved.currentrow lt local.qryEventRegCreditsApproved.recordcount>
									<br/>
								</cfif>
							</cfloop>		
						</cfsavecontent>

						<cfsavecontent variable="local.eventtime">
							<cfif local.qryData.isAllDayEvent>
								<cfif Month(local.qryData.endTime) is not Month(local.qryData.startTime) or Year(local.qryData.endTime) is not Year(local.qryData.startTime)>
									#DateFormat(local.qryData.startTime, "mmmm d, yyyy")# - #DateFormat(local.qryData.endTime, "mmmm d, yyyy")#
								<cfelse>
									#DateFormat(local.qryData.startTime, "mmmm d")#<cfif DateCompare(local.qryData.endTime,local.qryData.startTime,"d")>-#DateFormat(local.qryData.endTime, "d")#</cfif>#DateFormat(local.qryData.startTime, ", yyyy")#
								</cfif>
							<cfelse>
								#DateFormat(local.qryData.startTime, "mmmm d, yyyy")#
								<cfif DateCompare(local.qryData.endTime,local.qryData.startTime,"d") is 0 and DateDiff("n",local.qryData.endTime,local.qryData.startTime) is 0>
									#TimeFormat(local.qryData.startTime, "h:mm TT")#
								<cfelseif DateCompare(local.qryData.endTime,local.qryData.startTime,"d") is 0>
									#TimeFormat(local.qryData.startTime, "h:mm TT")# - #timeformat(local.qryData.endTime,"h:mm TT")#
								<cfelse>
									#TimeFormat(local.qryData.startTime, "h:mm TT")# - #DateFormat(local.qryData.endTime, "mmmm d, yyyy")# #TimeFormat(local.qryData.endTime, "h:mm TT")#
								</cfif>
							</cfif>
						</cfsavecontent>			

						<div class="mb-1 font-size-lg" style="color:##0E568D;">#local.qryData.eventTitle#</div>
						<div class="mb-3 font-size-md" style="color:##0E568D;">
							#local.eventtime#
							<cfif len(local.qryData.locationTitle)>
								<br/>#local.qryData.locationTitle#
							</cfif>
							<cfif len(local.creditdetail)>
								<br/>#trim(local.creditdetail)#
							</cfif>
						</div>

						<cfif listFindNoCase(local.frmReportView,"summary")>							
							<table class="table table-sm table-borderless">
							<thead>
								<tr>
									<th class="p-2 text-left" colspan="5">Registrant Summary</th>
								</tr>
							</thead>
							<tbody>
							<tr>
								<td width="120">## Registered:</td>
								<td width="30" class="text-right">#numberFormat(local.qryData.NumRegisteredForEvent,"9,999")#</td>
								<td width="100">&nbsp;</td>
								<td width="120">Total Fees:</td>
								<td width="50" class="text-right">#dollarFormat(local.qryData.regFeeForEvent)#</td>
							</tr>
							<tr>
								<td width="120">## Attended:</td>
								<td width="30" class="text-right">#numberFormat(local.qryData.NumAttendedForEvent,"9,999")#</td>
								<td width="100">&nbsp;</td>
								<td width="120">Amount Due:</td>
								<td width="50" class="text-right">#dollarFormat(local.qryData.regDueForEvent)#</td>
							</tr>
							</tbody>
							</table>
							<br/>
							
							<table class="table table-sm table-borderless">
								<thead>
								<tr>
									<th class="text-left">Registration by Rate</th>
									<th class="text-right" nowrap>Total Fees</th>
									<th class="text-right">Reg</th>
									<th class="text-right">Attend</th>
								</tr>
								</thead>
								<tbody>
								<cfloop query="local.qrySummaryRates">
									<tr>
										<td class="align-top">
											<cfif len(local.qrySummaryRates.rateName)>
												#local.qrySummaryRates.rateName#
											<cfelse>
												No rate
											</cfif>
										</td>				
										<td width="90" class="text-right align-top">#dollarFormat(local.qrySummaryRates.regFee)#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryRates.regCount,"9,999")#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryRates.attendCount,"9,999")#</td>
									</tr>
								</cfloop>
								</tbody>
							</table>
							<br/>
		
							<table class="table table-sm table-borderless">
								<thead>
								<tr>
									<th class="text-left">Registration by Time</th>
									<th class="text-right" nowrap>Total Fees</th>
									<th class="text-right">Reg</th>
									<th class="text-right">Attend</th>
								</tr>
								</thead>
								<tbody>
								<cfloop query="local.qrySummaryTimes">
									<tr>
										<td>#local.qrySummaryTimes.regWeek#</td>				
										<td width="90" class="text-right align-top">#dollarFormat(local.qrySummaryTimes.regFee)#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryTimes.regCount,"9,999")#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryTimes.attendCount,"9,999")#</td>
									</tr>
								</cfloop>
								</tbody>
							</table>
							<br/>
		
							<table class="table table-sm table-borderless">
								<thead>
								<tr>
									<th class="text-left">Registration by Location</th>
									<th class="text-right" nowrap>Total Fees</th>
									<th class="text-right">Reg</th>
									<th class="text-right">Attend</th>
								</tr>
								</thead>
								<tbody>
								<cfloop query="local.qrySummaryGeo">
									<tr>
										<td>#local.qrySummaryGeo.regLocation#</td>				
										<td width="90" class="text-right align-top">#dollarFormat(local.qrySummaryGeo.regFee)#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryGeo.regCount,"9,999")#</td>
										<td width="30" class="text-right align-top">#numberFormat(local.qrySummaryGeo.attendCount,"9,999")#</td>
									</tr>
								</cfloop>
								</tbody>
							</table>
							<br/>
						</cfif>

						<cfif len(local.strReport.frmIncludeCatIDs)>
							<cfset local.useEventID = local.qryData.eventid>
							<cfloop list="#local.strReport.frmIncludeCatIDs#" index="local.thisCatID">
								<cfquery name="local.qryRoleName" dbtype="query">
									select categoryName
									from [local].qryRoles
									where categoryID = #local.thisCatID#
								</cfquery>
								
								<cfquery name="local.qryRoleMembers" dbtype="query">
									select *
									from [local].qryData 
									where eventid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useEventID#">
									and roleIDPipeList like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%-#local.thisCatID#-%">
									order by mc_row
								</cfquery>
								
								<cfif local.qryRoleMembers.recordcount>
									<cfset local.roleFieldList = "">
									<!--- this gets the columns in the order and case defined in the query --->
									<cfset local.qryRoleMembersColumn = QueryColumnArray(local.qryRoleMembers)/>
									<cfloop list="#arrayToList(local.qryRoleMembersColumn)#" index="local.thisRoleField">
										<cfif getToken(local.thisRoleField,1,":") eq local.qryRoleName.categoryName and local.thisRoleField neq local.qryRoleName.categoryName>
											<cfset local.roleFieldList = listAppend(local.roleFieldList,local.thisRoleField)>
										</cfif>
									</cfloop>
									<div>
										<table class="table table-sm table-borderless">
											<thead>
												<tr>
													<th class="text-left p-2">Event Role: #local.qryRoleName.categoryName#</th>
												</tr>
											</thead>
										</table>
									</div>
									<cfloop query="local.qryRoleMembers">
										<div class="pt-1 pb-2">
											<b><cfif local.frmShowMemberNumber is 1>#local.qryRoleMembers["Extended MemberNumber"]#<cfelse>#local.qryRoleMembers["Extended Name"]#</cfif></b><br/>
											<cfif local.frmShowCompany is 1 AND len(local.qryRoleMembers.company)>#local.qryRoleMembers.company#<br/></cfif>
											<cfloop query="local.qryOutputFields">
												<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
													<cfset local.AddrToShow = local.qryRoleMembers[local.qryOutputFields.dbField][local.qryRoleMembers.currentrow]>
													<cfloop condition="Find(', , ',local.AddrToShow)">
														<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
													</cfloop>
													<cfif len(local.AddrToShow)>
														#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
													</cfif>
												</cfif>
											</cfloop>
											<cfloop query="local.qryOutputFieldsForLoop">
												<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
													#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryRoleMembers[local.qryOutputFieldsForLoop.fieldlabel][local.qryRoleMembers.currentrow])#<br/>
												<cfelseif len(local.qryRoleMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryRoleMembers.currentrow])>
													<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
														#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryRoleMembers[local.qryOutputFieldsForLoop.fieldlabel][local.qryRoleMembers.currentrow], "m/d/yyyy")#<br/>
													<cfelse>
														#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryRoleMembers[local.qryOutputFieldsForLoop.fieldlabel][local.qryRoleMembers.currentrow]#<br/>
													</cfif>
												</cfif>
											</cfloop>
											<div class="ind">
												<cfloop list="#local.roleFieldList#" index="local.thisRoleField">
													<cfif len(local.qryRoleMembers[local.thisRoleField][local.qryRoleMembers.currentRow])>
														#listRest(local.thisRoleField,':')#: #local.qryRoleMembers[local.thisRoleField][local.qryRoleMembers.currentRow]#<br/>
													</cfif>
												</cfloop>
											</div>
										</div>
									</cfloop>
									<br/>
								</cfif>
							</cfloop>
						</cfif>

						<cfif listFindNoCase(local.frmReportView,"detail")>
							<table class="table table-sm table-borderless">
							<thead>
								<tr>
									<th class="text-left p-2" colspan="<cfif local.frmShowPhotos is 1>4<cfelse>3</cfif>">Registrant Detail</th>
								</tr>
							</thead>
							<tbody>
							<cfoutput>
								<tr>
									<cfif local.frmShowPhotos is 1>
										<td class="align-top" style="width:80px;">
											<cfif local.qryData.hasMemberPhotoThumb is 1>
												<cfif local.isPDF is 0>
													<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryData.MemberNumber)#.jpg">
												<cfelse>
													<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryData.MemberNumber)#.jpg">
												</cfif>
											<cfelse>
												<cfif local.isPDF is 0>
													<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
												<cfelse>
													<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
												</cfif>
											</cfif>
										</td>
									</cfif>
									<td class="align-top pt-1 pb-2">
										<b><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></b><br/>
										<cfif local.frmShowCompany is 1 AND len(local.qryData.company)>#local.qryData.company#<br/></cfif>
										<cfloop query="local.qryOutputFields">
											<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
												<cfset local.AddrToShow = local.qryData[local.qryOutputFields.dbfield][local.qryData.currentrow]>
												<cfloop condition="Find(', , ',local.AddrToShow)">
													<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
												</cfloop>
												<cfif len(local.AddrToShow)>
													#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
												</cfif>
											</cfif>
										</cfloop>
										<cfloop query="local.qryOutputFieldsForLoop">
											<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
												#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow])#<br/>
											<cfelseif len(local.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryData.currentrow])>
												<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
													#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow], "m/d/yyyy")#<br/>
												<cfelse>
													#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow]#<br/>
												</cfif>
											</cfif>
										</cfloop>
										<div class="mt-2">
											<cfif local.frmRegRTL is 1>
												<cfif len(local.qryData.rateName)>
													Registration Rate: #local.qryData.rateName#<br/>
												</cfif>
												Registration Period: #local.qryData.regWeek#<br/>
												Registration Location: #local.qryData.regLocation#<br/>
											</cfif>
											<cfif local.frmRegistrantNotes is 1>
												Internal Registrant Notes: #local.qryData.internalNotes#<br/>
											</cfif>
										</div>
									</td>
									<td class="pt-1 pb-2 align-top" nowrap><cfif local.qryData.attended is 1>Attended<cfelse>Did Not Attend</cfif></td>
									<td class="pt-1 pb-2 text-right align-top" nowrap>#dollarformat(local.qryData.totalRegFee)# (#dollarformat(local.qryData.amountDue)# due)</td>
								</tr>
							</cfoutput>
							</tbody>
							</table>
							<br/>
						</cfif>

						<cfif local.isPDF AND (local.frmPageBreaks eq 1) AND (local.qryData.currentRow neq local.qryData.recordCount)>
							<div style="page-break-before: always;">&nbsp;</div>
						<cfelse>
							<br/><br/><br/>
						</cfif>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.strReport.qryData", strQryResult=local.strReport.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>		
	
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportview/text())")>
		<cfset local.frmRegRTL = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmregrtl/text())")>
		<cfset local.frmRegistrantNotes = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmregistrantnotes/text())")>
		<cfset local.frmRoleFields = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrolefields/text())")>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
	
			<cfscript>
			local.strReport = generateData(strSQLPrep=local.strSQLPrep, orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				frmReportView=local.frmReportView, frmRegRTL=local.frmRegRTL, frmRegistrantNotes=local.frmRegistrantNotes, 
				frmRoleFields=local.frmRoleFields, reportAction=arguments.reportAction);
			local.arrInitialReportSort = arrayNew(1);
			local.strTemp = { field='Event Start Date', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='Event', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			local.strReportQry = { qryReportFields=local.strReport.qryData, strQryResult=local.strReport.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);

			local.strFields = structNew();
			local.strFields.frmreportview = { label="Include Sections", value=arguments.event.getValue('frmreportview','summary,detail') };
			local.strFields.frmregrtl = { label="Include Rate, Period, and Location in the Registrant Detail", value=arguments.event.getValue('frmregrtl',0) };
			local.strFields.frmregistrantnotes = { label="Include Internal Registrant Notes", value=arguments.event.getValue('frmregistrantnotes',0) };
			local.strFields.frmpagebreaks = { label="Insert Page Breaks", value=arguments.event.getValue('frmpagebreaks',0) };
			local.strFields.frmrolefields = { label="Role Custom Fields", value=arguments.event.getValue('frmrolefields','') };
			local.strFields.eidList = { label="Events List", value=XMLSearch(local.otherXML,'string(/report/extra/eidlist/text())') };

			reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
			return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>