<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<!--- check for bots --->
	<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
		<cfset variables.isBot = 1>
	<cfelse>
		<cfset variables.isBot = 0>
	</cfif>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			//add condition for responsive when responsive folder is available			
			
			if ((application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)) {
				arguments.event.setValue('viewDirectory', 'responsive');
			} else {
				arguments.event.setValue('viewDirectory', 'default');
			}
		
			local.alternateUpdateMemberLink = arguments.event.getValue('mc_siteInfo.alternateUpdateMemberLink','');
			
			// build quick links
			this.link.edit = buildLink(arguments.event,"edit");
			this.link.save = buildLink(arguments.event,"save");
			this.link.message = buildLink(arguments.event,"message");
			this.link.updatePhoto = buildLink(arguments.event,"updatePhoto");
			this.link.uploadAndCropPhoto = buildLink(arguments.event,"uploadAndCropPhoto");
			this.link.savePhoto = buildLink(arguments.event,"savePhoto");
			this.link.deleteDocument 	= buildLink(arguments.event,"deleteDocument");	
			
			// bots
			if (variables.isBot && NOT arguments.event.valueExists('message'))
				application.objCommon.redirect('#this.link.message#&message=1');

			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			
			if (len(local.alternateUpdateMemberLink))
				application.objCommon.redirect('#local.alternateUpdateMemberLink#');
				
			if (arguments.event.valueExists('id') and application.objUser.isSuperUser(cfcuser=session.cfcuser))
				application.objCommon.redirect('#this.link.message#&message=3');
	
			if (arguments.event.valueExists('id') && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)){
				try {
					local.decryptString = decrypt(arguments.event.getValue('id'),"TRiaL_SMiTH", "CFMX_COMPAT", "Hex");
					local.requestInfo = deserializeJSON(local.decryptString);
				} catch (any e) {
					local.requestInfo = structNew();
					application.objCommon.redirect('#this.link.message#&message=2');
				}

				local.mUpdateLinkStr = structNew();
				local.mUpdateLinkStr.memberID = 0;
				local.mUpdateLinkStr.siteID = 0;
				local.mUpdateLinkStr.date = "01/01/2000";
				local.mUpdateLinkStr.memberUpdateLink = 0;		
				local.mUpdateLinkStr.firstname = "";
				local.mUpdateLinkStr.lastname = "";
				local.mUpdateLinkStr.membernumber = "";						
				
				if(structKeyExists(local.requestInfo,"memberUpdateLink")){
					local.memberUpdateProcess = true;
					local.mUpdateLinkStr.memberUpdateLink  = 1;	
				}	
				if(structKeyExists(local.requestInfo,"m"))
					local.mUpdateLinkStr.memberID  = local.requestInfo.m;	
				if(structKeyExists(local.requestInfo,"s"))
					local.mUpdateLinkStr.siteID  = local.requestInfo.s;	
				if(structKeyExists(local.requestInfo,"d"))
					local.mUpdateLinkStr.date  = local.requestInfo.d;
					
				local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=local.mUpdateLinkStr.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
				arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
				
				if (not val(local.tmpRights.editOwn))
					application.objCommon.redirect('#this.link.message#&message=1&serialized=1&m=#local.mUpdateLinkStr.memberID#');				
									
				if (dateDiff("d", dateFormat(local.mUpdateLinkStr.date,"mm/dd/yyyy"), now()) gt 30)
					application.objCommon.redirect('#this.link.message#&message=2');
					
				arguments.event.setValue('memberid',local.mUpdateLinkStr.memberID);
				application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=arguments.event.getValue('memberid'));
				
				if (not structKeyExists(session.cfcuser,"mUpdateLinkStr")){
					session.cfcuser.mUpdateLinkStr = structNew();
					local.strMember = application.objMember.getMember(local.mUpdateLinkStr.memberID, arguments.event.getValue('mc_siteInfo.siteid'), arguments.event.getValue('mc_siteInfo.orgid'));
					local.mUpdateLinkStr.firstname = local.strMember.qryMember.firstname;
					local.mUpdateLinkStr.lastname = local.strMember.qryMember.lastname;	
					local.mUpdateLinkStr.membernumber = local.strMember.qryMember.membernumber;						
					session.cfcuser.mUpdateLinkStr = local.mUpdateLinkStr;	
				}											
			}	
			else {
				if (structKeyExists(session.cfcuser,"mUpdateLinkStr") && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)){
					local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.mUpdateLinkStr.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
					arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
					
					if (not val(local.tmpRights.editOwn))
						application.objCommon.redirect('#this.link.message#&message=1');
						
					arguments.event.setValue('memberid',session.cfcuser.mUpdateLinkStr.memberID);					
				}
				else 
					arguments.event.setValue('memberid',session.cfcuser.memberdata.memberid);
			}

			// if no memberid, get out
			if (int(val(arguments.event.getValue('memberid'))) is 0 and not arguments.event.valueExists('message'))		
				application.objCommon.redirect('/?pg=login&returnurl=#URLEncodedFormat("/?pg=updatemember")#');

			variables.instanceSettings = getInstanceSettings(this.appInstanceID);
			variables.instanceSettings.fieldSetArray = ArrayNew(1);
			local.fieldSet = getLocatorFieldsetID(this.siteResourceID,"update");
			if (local.fieldSet.recordCount) {
				local.memberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
				
				local.count = 0;				
				while (local.count lt local.fieldSet.RecordCount) {
					local.count = local.count + 1;
					
					local.tmpRights = buildRightAssignments(siteResourceID=local.fieldSet.useSiteResourceID[local.count], memberID=arguments.event.getValue("memberID"), siteID=arguments.event.getValue("mc_siteInfo.siteid"));
					if (structKeyExists(local.tmpRights,'fsQualify') AND local.tmpRights['fsQualify'] is 1)
					{
						local.tmpStruct = structNew();
						local.tmpStruct.fieldSetID = local.fieldSet.fieldSetID[local.count];
						local.tmpStruct.xmlFields = local.memberFieldsets.getMemberFieldsXML(fieldsetid=val(local.tmpStruct.fieldSetID), usage="updateMember");
						arrayAppend(variables.instanceSettings.fieldSetArray,local.tmpStruct);
					}
				}
			}	

			arguments.event.paramValue('memaction','edit');
			arguments.event.paramValue("returl", this.link.edit);		
			if(arguments.event.getValue('returl') EQ '/')
				arguments.event.setValue("returl", this.link.edit);
			
			// method to run
			local.methodToRun = this[arguments.event.getValue('memaction')];
		</cfscript>	

		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>	

	<cffunction name="getMemberPhotoSource" access="private" returntype="string" output="false"> 
		<cfargument name="PhotoExists" type="boolean" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		
		<cfscript> 
			if (arguments.PhotoExists) {
				return "/memberphotos/#LCASE(arguments.memberNumber)#.jpg?cb=#getTickCount()#";	
			} else { 
				return "/assets/common/images/directory/default.jpg";
			}
		</cfscript>	
	</cffunction>
	
	<cffunction name="getMemberPhotoImageHTML" access="private" returntype="string" output="false"> 
		<cfargument name="photoSrc" type="string" required="true">
		<cfargument name="width" type="string" required="true">
		<cfargument name="height" type="string" required="true">
		
		<cfreturn '<img src="#arguments.photoSrc#" id="memberPhoto" width="#arguments.Width#" height="#arguments.Height#">'>
	</cffunction>
	
	<cffunction name="edit" access="public" output="false" returntype="struct" hint="edit member data">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
			local.rc = arguments.event.getCollection();
			local.loginProcess = FALSE;
			local.memberUpdateProcess = FALSE;
			local.submitForm = this.link.save;
			local.viewDirectory = arguments.event.getValue('viewDirectory', 'default');
			
			if (arguments.event.getValue('mc_admintoolInfo.myRights.editOwn') && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)){
				local.memberUpdateProcess = true;									
			}
			
			if (arguments.event.valueExists('loginProcessLink') or local.memberUpdateProcess)
				local.loginProcess = TRUE;
			
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(local.rc.mc_siteinfo.siteCode);
			local.xmlAdditionalData = application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(orgID=local.rc.mc_siteinfo.orgID);
			
			// get member
			local.strMember = CreateObject("component","model.admin.members.members").getMember(local.rc.memberID, local.rc.mc_siteinfo.orgID);
			local.xmlAdditionalData_Member = local.strMember.xmlAdditionalData;

			local.objDocument = CreateObject("component","model.system.platform.document");

			local.memberPhotoExists = val(local.strMember.qryMember.hasMemberPhoto);
			local.memberPhotoSrc = getMemberPhotoSource( local.memberPhotoExists, local.strMember.qryMember.membernumber );
			local.memberPhotoImgHTML = getMemberPhotoImageHTML( local.memberPhotoSrc, local.qrySiteSettings.memberPhotoWidth, local.qrySiteSettings.memberPhotoHeight );
			local.hasUploadPhotoRights = ( arguments.event.getValue('mc_adminToolInfo.myRights.uploadPhoto',false) );

			local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(local.rc.mc_siteinfo.orgID);
			local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.rc.mc_siteinfo.orgID);
			local.showFSDescription = val(local.qrySiteSettings.showFieldSetDescription);
			local.qryStates = application.objCommon.getStates();

			local.defaultLabel = 'Name and Company';
			local.updMemOVNameLabel = len(local.qrySiteSettings.updMemOVNameLabel) ? local.qrySiteSettings.updMemOVNameLabel : local.defaultLabel;
			
			local.defaultLabel = local.qryOrgAddresses.recordcount gt 1 ? 'Addresses and Phone Information' : 'Address and Phone Information';
			local.updMemOVAddrLabel = len(local.qrySiteSettings.updMemOVAddrLabel) ? local.qrySiteSettings.updMemOVAddrLabel : local.defaultLabel;
			
			local.defaultLabel = local.qryOrgEmails.recordcount gt 1 ? 'Email Addresses' : 'Email Address';
			local.updMemOVEmailLabel = len(local.qrySiteSettings.updMemOVEmailLabel) ? local.qrySiteSettings.updMemOVEmailLabel : local.defaultLabel;

			local.defaultLabel = local.qryOrgPlTypes.recordcount gt 1 ? 'Professional Licenses' : 'Professional License';
			local.updMemOVProfLicLabel = len(local.qrySiteSettings.updMemOVProfLicLabel) ? local.qrySiteSettings.updMemOVProfLicLabel : local.defaultLabel;
			
			
			if (local.qryOrgWebsites.recordCount) {
				local.defaultLabel = local.qryOrgWebsites.recordcount gt 1 ? 'Websites' : 'Website';
				local.updMemOVWebLabel = len(local.qrySiteSettings.updMemOVWebLabel) ? local.qrySiteSettings.updMemOVWebLabel : local.defaultLabel;
			}
			
			local.returl = local.rc.returl;
		</cfscript>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			select o.hasPrefix, o.usePrefixList, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryOrgMemberFields.usePrefixList is 1>
			<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.rc.mc_siteinfo.orgID)>
		</cfif>
		
		<cfset local.objMemberUpdate = CreateObject("component","model.admin.memberUpdate.memberUpdate")/>
		<cfset local.updateMemberSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='UpdateMember',siteID=arguments.event.getValue('mc_siteinfo.siteid'))/>
		<cfset local.qryCurrentProfessionalLicenses = local.objMemberUpdate.getCurrentProfessionalLicenses(siteResourceID=local.updateMemberSiteResourceID)>
		
		<cfif NOT val(local.strMember.qryMember.memberID)>
			<cfset local.memberID = session.cfcuser.memberData.memberID>
		<cfelse>
			<cfset local.memberID = local.strMember.qryMember.memberID>
		</cfif>
		
		<cfset local.qryProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=local.memberID, orgID=local.rc.mc_siteinfo.orgID)/>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.updateMemberSiteResourceID, memberID=local.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=local.rc.mc_siteinfo.orgID)>
		
		<cfset local.qryOrgProfLicenseLabels = application.objOrgInfo.getOrgProfLicenseLabels(orgID=local.rc.mc_siteinfo.orgID)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="/views/updateMember/#local.viewDirectory#/frm_member.cfm">
			</cfoutput>
		</cfsavecontent>

		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="save" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		arguments.event.setValue('data',form);
		local.rc = arguments.event.getCollection();
		local.memberID = int(val(arguments.event.getValue('data.memberID')));
		</cfscript>		
		
		<cfset local.updateStatus = 1>
		<cfif StructKeyExists(local.rc,'newUserName')>
			<cfif arguments.event.getValue('mc_siteinfo.sf_sitePasswords') IS 1>
				<cfquery name="local.userData" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT np.profileID
					FROM dbo.ams_networkProfiles AS np
					INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON np.profileID = mnp.profileID
					INNER JOIN dbo.ams_members AS m ON m.memberID = mnp.memberID
					WHERE m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
					and mnp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
					AND mnp.UserName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.rc.newUserName#">
					AND m.memberID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
					AND np.status = 'A'
					AND mnp.status <> 'D';
					
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			<cfelse>
				<cfquery name="local.userData" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT profileID
					FROM dbo.ams_networkProfiles 
					WHERE username = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.rc.newUserName#"> 
					AND networkId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.loginNetworkID')#">
					AND depomemberdataid <> <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberData.depomemberdataid#">
					AND status = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>
			<cfif local.userData.recordcount gt 0>
				<cfset local.updateStatus = 2>
			<cfelseif StructKeyExists(local.rc,'oldPassword') and len(local.rc.oldPassword)>
				<cfset local.correctCurrentPassword = application.objUser.login_checkPassword(memberid=local.memberID, siteid=arguments.event.getValue('mc_siteinfo.siteID'), password=local.rc.oldPassword)>
				<cfif NOT local.correctCurrentPassword>
					<cfset local.updateStatus = 3>
				</cfif>
			</cfif>
			
			<cfif local.updateStatus eq 1>
				<cfset saveProcess(arguments.event)>
			</cfif>
		<cfelse>			
			<cfset saveProcess(arguments.event)>
		</cfif>	
		
		<cfset local.returl = arguments.event.getValue("returl", this.link.edit)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<form name="frmUMSave" method="post" action="#local.returl#">
					<input type="hidden" name="frmSave" id="frmSave" value="#local.updateStatus#">
				</form>
				<script>document.frmUMSave.submit();</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
			
	<cffunction name="saveProcess" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>			
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.arrFields = arrayNew(1);
		</cfscript>
		
		<!--- get old memberdata (before save) for comparison in email --->
		<cfset local.rc.memberID = int(val(arguments.event.getValue('data.memberID')))>
		<cfset local.qryMemberOrig = application.objMember.getMemberInfo(memberid=local.rc.memberID, orgid=local.rc.mc_siteinfo.orgid)>
		<!--- determine what data has changed and save data if ImmediateMemberUpdates set to yes --->
		<cfset saveLoginData(event=arguments.event)>

		<cfset local.strArgs = { event=arguments.event, doSave=local.rc.mc_siteinfo.immediateMemberUpdates }>

		<cfif local.rc.mc_siteinfo.immediateMemberUpdates>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.rc.memberID, sitecode=local.rc.mc_siteinfo.sitecode)>
			<cfset local.strArgs.objSaveMember = local.objSaveMember>
		</cfif>

		<cfset local.arrFields.addAll(saveMemberData(argumentCollection=local.strArgs))>

		<cfset local.saveCustomFieldResultData = saveCustomFieldData(argumentCollection=local.strArgs)>
		<cfset local.arrFields.addAll(local.saveCustomFieldResultData.arrFields)>

		<cfset local.arrFields.addAll(saveAddressData(argumentCollection=local.strArgs))>
		<cfset local.arrFields.addAll(saveEmailData(argumentCollection=local.strArgs))>
		<cfset local.arrFields.addAll(saveWebsiteData(argumentCollection=local.strArgs))>
		<cfset local.arrFields.addAll(saveProfessionalLicensesData(argumentCollection=local.strArgs))>

		<cfif local.rc.mc_siteinfo.immediateMemberUpdates>
			<cfset local.strResult = local.objSaveMember.saveData()>
			<cfif NOT local.strResult.success>
				<cfthrow message="Member could not be saved." detail="#local.strResult.errMsg#">
			</cfif>
		</cfif>

		<!--- Were there any changes? --->
		<cfset local.isChanged = false>
		<cfloop array="#local.arrFields#" index="local.thisField">
			<cfif local.thisField.isChanged is 1>
				<cfset local.isChanged = true>
				<cfbreak>
			</cfif>
		</cfloop>	

		<!--- email any changes if necessary --->
		<cfif local.isChanged and len(local.rc.mc_siteinfo.emailMemberUpdates)>
			<cfset local.objMemberUpdate = CreateObject("component","model.admin.memberUpdate.memberUpdate")>
			<cfset local.updateMemberSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='UpdateMember',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.qryCurrentProfessionalLicenses = local.objMemberUpdate.getCurrentProfessionalLicenses(siteResourceID=local.updateMemberSiteResourceID)>
			
			<cftry>
				<cfsavecontent variable="local.emailContent">
					<cfinclude template="/views/updateMember/dsp_updateEmail.cfm">
				</cfsavecontent>

				<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Membership Information Updated">

				<cfset local.arrEmailTo = []>
				<cfset local.toEmailArr = listToArray(local.rc.mc_siteinfo.emailMemberUpdates,';')>
				<cfloop array="#local.toEmailArr#" item="local.thisEmail">
					<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
				</cfloop>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=local.rc.mc_siteinfo.orgname, email=local.rc.mc_siteinfo.networkEmailFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.rc.mc_siteinfo.supportProviderEmail,
					emailsubject="Membership Information Updated: #local.qryMemberOrig.firstname# #local.qryMemberOrig.lastname#",
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.emailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					memberID=local.rc.mc_siteinfo.sysmemberid,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="UPDATEMEMBER"),
					sendingSiteResourceID=this.siteResourceID)/>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>
	</cffunction>
	
	<cffunction name="savePhoto" access="public" output="false" returntype="struct"> 
		<cfargument name="Event" type="any">
		
		<cfscript>
		local.loginProcess = FALSE;
		local.submitForm = this.link.save;
			
		if (arguments.event.valueExists('loginProcessLink'))
			local.loginProcess = TRUE;
			
		local.memberID = session.cfcuser.memberdata.memberID;
		local.firstname = session.cfcuser.memberdata.firstname;
		local.lastname = session.cfcuser.memberdata.lastname;
		local.membernumber = session.cfcuser.memberdata.membernumber;
		if (structKeyExists(session.cfcuser,"mUpdateLinkStr") && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)){
			local.memberID = session.cfcuser.mUpdateLinkStr.memberID;	
			local.firstname = session.cfcuser.mUpdateLinkStr.firstname;
			local.lastname = session.cfcuser.mUpdateLinkStr.lastname;
			local.membernumber = session.cfcuser.mUpdateLinkStr.membernumber;		
		}		
			
		local.objWebsite = CreateObject('component','model.admin.website.website');
		local.qrySiteSettings = local.objWebsite.getSettings(arguments.event.getValue('mc_siteinfo.siteCode'));
		</cfscript>
			
		<!--- decrypt photodir --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.decryptDownloadURL(d=arguments.event.getValue('photodir',''))>
		<cfcatch type="Any">
			<cflocation url="#this.link.edit#" addtoken="false">
		</cfcatch>
		</cftry>

		<cfset local.memberFullImagePath_LG = "#local.strFolder.sourceFilePath#/LG_#local.memberID#.jpg">
		<cfset local.memberFullImagePath_final_LG = "#local.strFolder.sourceFilePath#/final_LG_#local.memberID#.jpg">

		<cfset local.photoFolder = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.event.getValue('mc_siteinfo.orgCode'))#/memberphotos">
		<cfset local.photoDestination = local.photoFolder & "/#LCASE(local.memberNumber)#.jpg">

		<!--- crop photo --->
		<cfif fileExists(local.memberFullImagePath_LG) and arguments.event.getValue('w',0) gt 0 and arguments.event.getValue('h',0) gt 0>
			<cfif arguments.event.getValue('x',0) LT 0>
				<cfset local.x = 0>
			<cfelse>
				<cfset local.x = Int(arguments.event.getValue('x',0))>
			</cfif>
			<cfif arguments.event.getValue('y',0) LT 0>
				<cfset local.y = 0>
			<cfelse>
				<cfset local.y = Int(arguments.event.getValue('y',0))>
			</cfif>
						
			<cfset local.cropPoints = application.objCommon.getManualCropPointsForThumbor(x=local.x,y=local.y,width=Int(arguments.event.getValue('w',0)),height=Int(arguments.event.getValue('h',0)))>
			<cfset local.command = "#local.cropPoints#/#local.qrySiteSettings.memberPhotoWidth#x#local.qrySiteSettings.memberPhotoHeight#">
			<cfset local.cropRequest = application.objCommon.thumborImageTranform(command=local.command,filePath=local.memberFullImagePath_LG, outputfilePath=local.photoDestination)>
			
			<cfif local.cropRequest.success >
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_saveMemberPhoto">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
				</cfstoredproc>

				<cfset createObject('component','model.system.platform.history').addMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'),
					actorMemberID=local.memberID, receiverMemberID=local.memberID, mainMessage="Member Photo Added")>

				<cfif len(local.qrySiteSettings.emailMemberUpdates)>
					<cfsavecontent variable="local.emailContent">
						<cfoutput>
						<p><a href="#arguments.event.getValue('mc_siteinfo.scheme')#://#application.objPlatform.getCurrentHostname()#/?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=#local.memberID#">#local.firstname# #local.lastname#</a> has updated their member photo.</p>
						</cfoutput>
					</cfsavecontent>

					<cfset local.arrEmailTo = []>
					<cfset local.toEmailArr = listToArray(local.qrySiteSettings.emailMemberUpdates,';')>
					<cfloop array="#local.toEmailArr#" item="local.thisEmail">
						<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
					</cfloop>
					
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email="<EMAIL>" },
						emailto=local.arrEmailTo,
						emailreplyto="<EMAIL>",
						emailsubject="Member Updated Photo - #local.firstname# #local.lastname#",
						emailtitle="Member Updated Photo",
						emailhtmlcontent=local.emailContent,
						emailAttachments=[{ file="#local.memberNumber#.jpg", folderpath=local.photoFolder }],
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=arguments.event.getValue('mc_siteinfo.sysmemberid'),
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="UPDATEMEMBER"),
						sendingSiteResourceID=this.siteResourceID)/>
				</cfif>
 			</cfif>
		</cfif>
		
		<cflocation url="#this.link.edit#" addtoken="false">
	</cffunction>
	
	<cffunction name="uploadAndCropPhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfscript> 
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(arguments.event.getValue('mc_siteinfo.siteCode'));
			local.viewDirectory = arguments.event.getValue('viewDirectory', 'default');
		</cfscript>
		
		<!--- upload the photo --->
		<cfif len(arguments.event.getTrimValue('new_sn_prof_pic'))>
			
			<!--- create temp folder --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.imgDirEnc = local.strFolder.folderPathEnc>

			<!--- upload photo --->
			<cffile action="UPLOAD" filefield="new_sn_prof_pic" destination="#local.strFolder.folderPath#" nameconflict="MAKEUNIQUE" result="local.uploadedPhoto">

			<!--- if uploaded --->
			<cfif local.uploadedPhoto.fileWasSaved>

				<cftry>
					<cfset local.serverDirectoryAndFileName = "#local.uploadedPhoto.ServerDirectory#/#local.uploadedPhoto.serverFile#">
					<cfset local.serverDirectoryAndFileName_LG = "#local.uploadedPhoto.ServerDirectory#/LG_#val(arguments.event.getValue('memberid'))#.jpg">
					<cfset local.processPhoto = application.objCommon.thumborImageTranform(command="fit-in/500x500",filePath=local.serverDirectoryAndFileName, outputfilePath=local.serverDirectoryAndFileName_LG)>
					<cfset local.resultPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.serverDirectoryAndFileName_LG)>

					<cfset local.success = local.processPhoto.success>
					<cfif local.success EQ 'No'>
						<cfthrow message="Thumbor Image Tranform Error" 
							type="thumborImageTranformError" 
							detail="#local.resultPhotoInfo#">						
					<cfelse>
						<cfset local.imageHeight = local.resultPhotoInfo.imageInfo.source.height>
						<cfset local.imageWidth = local.resultPhotoInfo.imageInfo.source.width>
						<!--- copy to /temp for preview purposes --->
						<cfset local.imgInTemp = "#createUUID()##createUUID()#.jpg">
						<cffile action="copy" destination="#application.paths.RAIDTemp.path##local.imgInTemp#" source="#local.serverDirectoryAndFileName_LG#">
					</cfif>

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.resultPhotoInfo)>
					<cflocation url="#this.link.updatePhoto#&msg=1" addtoken="false">
				</cfcatch>
				</cftry>

			</cfif>	
		<cfelse>
			<cflocation url="#this.link.updatePhoto#" addtoken="false">
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="/views/updateMember/#local.viewDirectory#/frm_profilePicture.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="updatePhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfscript> 
			local.rc = arguments.event.getCollection();
			
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(local.rc.mc_siteinfo.siteCode);
			local.viewDirectory = arguments.event.getValue('viewDirectory', 'default');
			
			// get member
			local.strMember = application.objMember.getMember(local.rc.memberID, local.rc.mc_siteinfo.siteID, local.rc.mc_siteinfo.orgID);
			
			local.memberPhotoExists = val(local.strMember.qryMember.hasMemberPhoto);
			local.memberPhotoSrc = getMemberPhotoSource( local.memberPhotoExists, local.strMember.qryMember.membernumber );
			local.memberPhotoImgHTML = getMemberPhotoImageHTML( local.memberPhotoSrc, local.qrySiteSettings.memberPhotoWidth, local.qrySiteSettings.memberPhotoHeight );
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="/views/updateMember/#local.viewDirectory#/frm_uploadProfilePicture.cfm">
		</cfsavecontent>

		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>
	
	
	<cffunction name="buildLink" access="private" returntype="string" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="action" type="string" required="TRUE">
		<cfscript>
			var local = structNew();
			local.newURL = '/?pg=updatemember';
			local.newURL = local.newURL & '&memaction=' & arguments.action;
		</cfscript>
		<cfreturn local.newURL>
	</cffunction>
	
	<cffunction name="saveLoginData" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrChanges = arrayNew(1)>
		<cfset local.arrHistoryChanges = ArrayNew(1)>
		<cfset local.arrHistoryMessages = ArrayNew(1)>
		<cfset local.objAdminMember = CreateObject('component','model.admin.members.members')>

		<cfset local.actorMemberID = session.cfcuser.memberdata.memberID>
		<cfif structKeyExists(session.cfcuser,"mUpdateLinkStr") && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.actorMemberID = session.cfcuser.mUpdateLinkStr.memberID>		
		</cfif>		

		<cfif arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1>
			<cfif len(arguments.event.getTrimValue('data.newUserName',''))>
				<cfset application.objUser.login_setUsername(memberID=arguments.event.getValue('data.memberID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), username=arguments.event.getTrimValue('data.newUserName',''), recordedByMemberID=local.actorMemberID)>
			</cfif>
			
			<!--- are we changing password? If so, check if existing is correct --->
			<cfset local.oldVal = arguments.event.getTrimValue('data.oldPassword','')>
			<cfset local.newVal = arguments.event.getTrimValue('data.newPassword','')>
			<cfset local.newValConf = arguments.event.getTrimValue('data.newPasswordConfirm','')>
			<cfif len(local.oldVal) and len(local.newVal) and compare(local.newVal,local.newValConf) is 0 and compare(local.oldVal,local.newVal)>
				<cfset local.correctCurrentPassword = application.objUser.login_checkPassword(memberid=arguments.event.getValue('data.memberID'), siteid=arguments.event.getValue('mc_siteInfo.siteID'), password=local.oldVal)>
				<cfif local.correctCurrentPassword>
					<cfset application.objUser.login_setPassword(memberID=arguments.event.getValue('data.memberID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), password=local.newVal, recordedByMemberID=local.actorMemberID)>
				</cfif>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="saveMemberData" access="private" output="false" returntype="array">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		<cfargument name="objSaveMember" type="any" required="no">
		
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>
		<cfset local.strDemoFieldData = structNew()>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
		</cfquery>

		<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
		<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
			<cfquery name="local.qryCurrValues" datasource="#application.dsn.membercentral.dsn#">
				select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalsuffix, m.company
				from dbo.ams_members as m
				WHERE m.memberid = <cfqueryparam value="#arguments.event.getValue('data.memberid')#" cfsqltype="CF_SQL_INTEGER">
				and m.memberid = m.activememberid
				AND m.orgid = #arguments.event.getValue('mc_siteInfo.orgid')#
			</cfquery>

			<cfif local.qryOrgMemberFields.hasPrefix is 1>
				<cfset local.tmpStr = { s='demo', fc='m_prefix', c='prefix', label='Prefix', o=local.qryCurrValues.prefix, n=arguments.event.getTrimValue('data.m_prefix',''), isChanged=0, t='CF_SQL_VARCHAR' }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
					<cfset local.strDemoFieldData['prefix'] = arguments.event.getTrimValue('data.m_prefix','')>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfset local.tmpStr = { s='demo', fc='m_firstname', c='firstname', label='First Name', o=local.qryCurrValues.firstname, n=arguments.event.getTrimValue('data.m_firstname',''), isChanged=0, t='CF_SQL_VARCHAR' }>
			<cfset local.tmpStr.od = local.tmpStr.o>
			<cfset local.tmpStr.nd = local.tmpStr.n>
			<cfif compare(local.tmpStr.o,local.tmpStr.n)>
				<cfset local.tmpStr.isChanged = 1>
				<cfset local.strDemoFieldData['firstname'] = arguments.event.getTrimValue('data.m_firstname','')>
			</cfif>
			<cfset arrayAppend(local.arrFields,local.tmpStr)>

			<cfif local.qryOrgMemberFields.hasMiddleName is 1>
				<cfset local.tmpStr = { s='demo', fc='m_middlename', c='middlename', label='Middle Name', o=local.qryCurrValues.middlename, n=arguments.event.getTrimValue('data.m_middlename',''), isChanged=0, t='CF_SQL_VARCHAR' }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
					<cfset local.strDemoFieldData['middlename'] = arguments.event.getTrimValue('data.m_middlename','')>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfset local.tmpStr = { s='demo', fc='m_lastname', c='lastname', label='Last Name', o=local.qryCurrValues.lastname, n=arguments.event.getTrimValue('data.m_lastname',''), isChanged=0, t='CF_SQL_VARCHAR' }>
			<cfset local.tmpStr.od = local.tmpStr.o>
			<cfset local.tmpStr.nd = local.tmpStr.n>
			<cfif compare(local.tmpStr.o,local.tmpStr.n)>
				<cfset local.tmpStr.isChanged = 1>
				<cfset local.strDemoFieldData['lastname'] = arguments.event.getTrimValue('data.m_lastname','')>
			</cfif>
			<cfset arrayAppend(local.arrFields,local.tmpStr)>

			<cfif local.qryOrgMemberFields.hasSuffix is 1>
				<cfset local.tmpStr = { s='demo', fc='m_suffix', c='suffix', label='Suffix', o=local.qryCurrValues.suffix, n=arguments.event.getTrimValue('data.m_suffix',''), isChanged=0, t='CF_SQL_VARCHAR' }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
					<cfset local.strDemoFieldData['suffix'] = arguments.event.getTrimValue('data.m_suffix','')>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfif local.qryOrgMemberFields.hasProfessionalSuffix is 1>
				<cfset local.tmpStr = { s='demo', fc='m_professionalsuffix', c='professionalsuffix', label='Professional Suffix', o=local.qryCurrValues.professionalsuffix, n=arguments.event.getTrimValue('data.m_professionalsuffix',''), isChanged=0, t='CF_SQL_VARCHAR' }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
					<cfset local.strDemoFieldData['professionalsuffix'] = arguments.event.getTrimValue('data.m_professionalsuffix','')>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfset local.tmpStr = { s='demo', fc='m_company', c='company', label='Company', o=local.qryCurrValues.company, n=arguments.event.getTrimValue('data.m_company',''), isChanged=0, t='CF_SQL_VARCHAR' }>
			<cfset local.tmpStr.od = local.tmpStr.o>
			<cfset local.tmpStr.nd = local.tmpStr.n>
			<cfif compare(local.tmpStr.o,local.tmpStr.n)>
				<cfset local.tmpStr.isChanged = 1>
				<cfset local.strDemoFieldData['company'] = arguments.event.getTrimValue('data.m_company','')>
			</cfif>
			<cfset arrayAppend(local.arrFields,local.tmpStr)>
		<cfelse>
			<cfif local.qryOrgMemberFields.hasPrefix is 1>
				<cfset local.strDemoFieldData['prefix'] = arguments.event.getTrimValue('data.m_prefix','')>
			</cfif>
			<cfset local.strDemoFieldData['firstname'] = arguments.event.getTrimValue('data.m_firstname','')>
			<cfif local.qryOrgMemberFields.hasMiddleName is 1>
				<cfset local.strDemoFieldData['middlename'] = arguments.event.getTrimValue('data.m_middlename','')>
			</cfif>
			<cfset local.strDemoFieldData['lastname'] = arguments.event.getTrimValue('data.m_lastname','')>
			<cfif local.qryOrgMemberFields.hasSuffix is 1>
				<cfset local.strDemoFieldData['suffix'] = arguments.event.getTrimValue('data.m_suffix','')>
			</cfif>
			<cfif local.qryOrgMemberFields.hasProfessionalSuffix is 1>
				<cfset local.strDemoFieldData['professionalsuffix'] = arguments.event.getTrimValue('data.m_professionalsuffix','')>
			</cfif>
			<cfset local.strDemoFieldData['company'] = arguments.event.getTrimValue('data.m_company','')>
		</cfif>

		<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
			<cfif structIsEmpty(local.strDemoFieldData)>
				<cfset local.strDemoFieldData['firstname'] = arguments.event.getTrimValue('data.m_firstname','')>
				<cfset local.strDemoFieldData['lastname'] = arguments.event.getTrimValue('data.m_lastname','')>
			</cfif>
			<cfset arguments.objSaveMember.setDemo(argumentCollection=local.strDemoFieldData)>
		</cfif>

		<cfreturn local.arrFields>
	</cffunction>

	<cffunction name="saveCustomFieldData" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		<cfargument name="objSaveMember" type="any" required="no">

		<cfset var local = structNew()>
		<cfset local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfset local.colsProcessed = "">
		<cfset local.arrFields = arrayNew(1)>
		<cfset local.arrDocumentFields = arrayNew(1)>
		<cfset local.arrHistoryMessages = ArrayNew(1)>
		<cfset local.strNewDocData = {}>

		<!--- loop over fieldsets. currColumnID ensures these are md_ fields only. --->
		<cfloop array="#variables.instanceSettings.fieldSetArray#" index="local.thisfieldset">
			<cfloop array="#local.thisfieldset.xmlFields.fields.xmlChildren#" index="local.mf">
				<cfset local.currColumnID = local.mf.xmlAttributes.mdColumnID>
				<cfset local.currColumnName = local.mf.xmlAttributes.dbField>
				<cfset local.currColumnFieldCode = local.mf.xmlAttributes.fieldCode>
				<cfset local.currColumnDisplayCode = local.mf.xmlAttributes.displayTypeCode>

				<cfif local.currColumnID gt 0 and not local.mf.xmlattributes.isReadOnly and not listFind(local.colsProcessed,local.currColumnID)>
					<cfset local.oldVal = arguments.event.getTrimValue('data.md_#local.currColumnID#_old','')>
					<cfset local.newVal = arguments.event.getTrimValue('data.md_#local.currColumnID#','')>

					<cfif local.mf.xmlAttributes.displayTypeCode eq 'DOCUMENT'>
						<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0, isDoc=1 }>
						<cfset local.tmpStr.od = "">
						<cfset local.tmpStr.nd = "">
						<cfif len(local.oldVal)>
							<cfset local.tmpStr.n = local.tmpStr.o>
						</cfif>
						<cfif len(local.newVal)>
							<cfset local.tmpStr.isChanged = 1>
							<cfset arrayAppend(local.arrFields,local.tmpStr)>
							<cfset arrayAppend(local.arrDocumentFields,local.tmpStr)>
						</cfif>
					<cfelse>
						<cfif listFindNoCase("RADIO,SELECT,CHECKBOX",local.mf.xmlAttributes.displayTypeCode) and local.newVal eq "__NOVALUE__">
							<cfset local.newVal = "">
						</cfif>

						<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
							<cfif listFindNoCase("RADIO,SELECT,CHECKBOX",local.mf.xmlAttributes.displayTypeCode)>
								<cfset arguments.objSaveMember.setCustomField(field=local.currColumnName, valueID=local.newVal)>
							<cfelse>
								<cfset arguments.objSaveMember.setCustomField(field=local.currColumnName, value=local.newVal)>
							</cfif>
						</cfif>

						<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
						<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
							<cfswitch expression="#local.mf.xmlAttributes.displayTypeCode#">
								<cfcase value="DATE">
									<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0 }>
										
									<!--- check range requirements --->
									<cfset local.failedValidation = false>
									<cfif len(local.newVal)>
										<cfif isDefined("local.mf.xmlAttributes.minValueDate") and isDefined("local.mf.xmlAttributes.maxValueDate") 
											and len(local.mf.xmlAttributes.minValueDate) and len(local.mf.xmlAttributes.maxValueDate)>
											<cfset local.mf.xmlattributes.minValueDate = dateformat(replaceNoCase(local.mf.xmlattributes.minValueDate,'T',' '),"m/d/yyyy")>
											<cfset local.mf.xmlattributes.maxValueDate = dateformat(replaceNoCase(local.mf.xmlattributes.maxValueDate,'T',' '),"m/d/yyyy")>
										<cfelse>
											<cfset local.mf.xmlattributes.minValueDate = "1/1/1753">
											<cfset local.mf.xmlattributes.maxValueDate = "12/31/9999">
										</cfif>
										<cfif dateCompare(local.newVal,local.mf.xmlattributes.minValueDate,"d") lt 0 or dateCompare(local.newVal,local.mf.xmlattributes.maxValueDate,"d") gt 0>
											<cfset local.failedValidation = true>	
										</cfif>
									</cfif>

									<cfset local.tmpStr.od = local.tmpStr.o>
									<cfif local.failedValidation>
										<cfset local.tmpStr.n = local.tmpStr.o>
										<cfset local.tmpStr.nd = local.tmpStr.n>
									<cfelse>
										<cfset local.tmpStr.nd = local.tmpStr.n>
									</cfif>
									<cfif compare(local.tmpStr.o,local.tmpStr.n)>
										<cfset local.tmpStr.isChanged = 1>
									</cfif>
									<cfset arrayAppend(local.arrFields,local.tmpStr)>
								</cfcase>
								<cfcase value="TEXTBOX">
									<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0 }>

									<!--- check range requirements --->
									<cfset local.failedValidation = false>
									<cfif local.mf.xmlAttributes.dataTypeCode eq "DECIMAL2">
										<cfif isDefined("local.mf.xmlAttributes.minValueDecimal2") and isDefined("local.mf.xmlAttributes.maxValueDecimal2") 
											and len(local.mf.xmlAttributes.minValueDecimal2) and len(local.mf.xmlAttributes.maxValueDecimal2)
											and len(local.newVal)
											and (local.newVal lt local.mf.xmlAttributes.minValueDecimal2 OR local.newVal gt local.mf.xmlAttributes.maxValueDecimal2)>
											<cfset local.failedValidation = true>	
										</cfif>
									<cfelseif local.mf.xmlAttributes.dataTypeCode eq "INTEGER">
										<cfif isDefined("local.mf.xmlAttributes.minValueInt") and isDefined("local.mf.xmlAttributes.maxValueInt") 
											and len(local.mf.xmlAttributes.minValueInt) and len(local.mf.xmlAttributes.maxValueInt)
											and len(local.newVal)
											and (local.newVal lt local.mf.xmlAttributes.minValueInt OR local.newVal gt local.mf.xmlAttributes.maxValueInt)>
											<cfset local.failedValidation = true>	
										</cfif>
									<cfelseif local.mf.xmlAttributes.dataTypeCode eq "STRING">
										<cfif isDefined("local.mf.xmlAttributes.minChars") and isDefined("local.mf.xmlAttributes.maxChars") 
											and local.mf.xmlAttributes.minChars gt 0 and local.mf.xmlAttributes.maxChars gt 0
											and len(local.newVal)
											and (len(local.newVal) lt local.mf.xmlAttributes.minChars OR len(local.newVal) gt local.mf.xmlAttributes.maxChars)>
											<cfset local.failedValidation = true>	
										</cfif>
									</cfif>
									<cfif local.failedValidation>
										<cfset local.tmpStr.n = local.tmpStr.o>
									</cfif>

									<cfset local.tmpStr.od = local.tmpStr.o>
									<cfset local.tmpStr.nd = local.tmpStr.n>
									<cfif compare(local.tmpStr.o,local.tmpStr.n)>
										<cfset local.tmpStr.isChanged = 1>
									</cfif>
									<cfset arrayAppend(local.arrFields,local.tmpStr)>
								</cfcase>
								<cfcase value="RADIO,SELECT,CHECKBOX">
									<!--- sort so can compare lists in order --->
									<cfif ListFindNoCase("SELECT,CHECKBOX",local.mf.xmlAttributes.displayTypeCode) and local.mf.xmlattributes.allowMultiple is 1>
										<cfset local.oldVal = ListSort(local.oldVal,"numeric")>
										<cfset local.newVal = ListSort(local.newVal,"numeric")>
									</cfif>
									
									<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0 }>

									<!--- check range requirements --->
									<cfset local.failedValidation = false>
									<cfif local.mf.xmlattributes.allowMultiple is 1
										and isDefined("local.mf.xmlAttributes.minSelected") and isDefined("local.mf.xmlAttributes.maxSelected") 
										and local.mf.xmlAttributes.minSelected gt 0 and local.mf.xmlAttributes.maxSelected gt 0
										and len(local.newVal) 
										and (listlen(local.newVal) lt local.mf.xmlAttributes.minSelected or listlen(local.newVal) gt local.mf.xmlAttributes.maxSelected)>
										<cfset local.failedValidation = true>	
									</cfif>
									<cfif local.failedValidation>
										<cfset local.tmpStr.n = local.tmpStr.o>
									</cfif>

									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryColumnValues">
										
										<cfif local.mf.xmlAttributes.dataTypeCode eq "Integer">
											select
											(select 										
												STRING_AGG(oldVal,'|') as oldVal
													from 	
														(select 
															
																distinct mdcv1.columnValueInteger as oldVal
														from dbo.ams_memberDataColumns mdc
														left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
														left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
														where mdc.columnID = #local.currColumnID#
														and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as oldVal,
											(select 
												STRING_AGG(newVal,'|') as newVal
													from 	
														(select 
																distinct  mdcv2.columnValueInteger as newVal
							
														from dbo.ams_memberDataColumns mdc
														left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
														left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
														where mdc.columnID = #local.currColumnID#
														and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as newVal
										<cfelseif local.mf.xmlAttributes.dataTypeCode eq "Decimal2">
											select
											(select           
												STRING_AGG(oldVal,'|') as oldVal
												from 	
													(select 
														
															distinct mdcv1.columnValueDecimal2 as oldVal
													from dbo.ams_memberDataColumns mdc
													left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
													left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
													where mdc.columnID = #local.currColumnID#
													and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as oldVal,
											(select 
                								STRING_AGG(newVal,'|') as newVal
												from 	
													(select 
														distinct mdcv2.columnValueDecimal2 as newVal
													
													from dbo.ams_memberDataColumns mdc
													left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
													left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
													where mdc.columnID = #local.currColumnID#
													and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as newVal	
										<cfelseif local.mf.xmlAttributes.dataTypeCode eq "Date"><!--- no multi support --->
											select 
												convert(varchar(10),mdcv1.columnValueDate,101) as oldVal, 
                       							convert(varchar(10),mdcv2.columnValueDate,101) as newVal		
											from dbo.ams_memberDataColumns mdc
											left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
											left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
											where mdc.columnID = #local.currColumnID#
											and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#	
										<cfelseif local.mf.xmlAttributes.dataTypeCode eq "Bit"> <!--- no multi support --->
											select 
												case when mdcv1.columnValueBit = 1 then 'Yes' when mdcv1.columnValueBit = 0 then 'No' else '' end as oldVal, 
												case when mdcv2.columnValueBit = 1 then 'Yes' when mdcv2.columnValueBit = 0 then 'No' else '' end as newVal	
											from dbo.ams_memberDataColumns mdc
											left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
											left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
											where mdc.columnID = #local.currColumnID#
											and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#		
										<cfelse>
											select 
											(select 
                   								STRING_AGG(oldVal,'|') as oldVal
												from 	
													(select 
														distinct mdcv1.columnValueString as oldVal	
													from dbo.ams_memberDataColumns mdc
													left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
													left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
													where mdc.columnID = #local.currColumnID#
													and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as oldVal,
											(select 
												STRING_AGG(newVal,'|') as newVal 
												from 	
													(select 
														distinct mdcv2.columnValueString as newVal	
													from dbo.ams_memberDataColumns mdc
													left outer join dbo.ams_memberDataColumnValues mdcv1 on mdcv1.columnID = mdc.columnID and mdcv1.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.oldVal#" list="true">)
													left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.newVal#" list="true">)
													where mdc.columnID = #local.currColumnID#
													and mdc.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#) as tmp) as newVal
										</cfif>	
									</cfquery>
									
									<!--- if multiple, n needs to be a list of valueIDs (which it is already). otherwise, it needs to be the option value itself. --->
									<cfif local.mf.xmlattributes.allowMultiple is not 1>
										<cfset local.tmpStr.o = local.qryColumnValues.oldVal>
										<cfset local.tmpStr.n = local.qryColumnValues.newVal>
									</cfif>

									<cfset local.tmpStr.od = local.qryColumnValues.oldVal>
									<cfset local.tmpStr.nd = local.qryColumnValues.newVal>
									<cfif compare(local.tmpStr.o,local.tmpStr.n)>
										<cfset local.tmpStr.isChanged = 1>
									</cfif>
									<cfset arrayAppend(local.arrFields,local.tmpStr)>
								</cfcase>
								<cfcase value="TEXTAREA">					
									<cfswitch expression="#local.mf.xmlAttributes.dataTypeCode#">
									<cfcase value="XML">
										<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0 }>
										<cfset local.tmpStr.od = local.tmpStr.o>
										<cfset local.tmpStr.nd = local.tmpStr.n>
										<cfif compare(local.tmpStr.o,local.tmpStr.n)>
											<cfset local.tmpStr.isChanged = 1>
										</cfif>
										<cfset arrayAppend(local.arrFields,local.tmpStr)>
									</cfcase>
									<cfcase value="CONTENTOBJ">
										<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0, isCon=1, isHTML=0 }>

										<!--- check min/max requirements --->
										<cfset local.failedValidation = false>
										<cfif isDefined("local.mf.xmlAttributes.minChars") and isDefined("local.mf.xmlAttributes.maxChars") 
											and local.mf.xmlAttributes.minChars gt 0 and local.mf.xmlAttributes.maxChars gt 0
											and len(local.newVal) gt 0 
											and (len(local.newVal) lt local.mf.xmlAttributes.minChars OR len(local.newVal) gt local.mf.xmlAttributes.maxChars)>
											<cfset local.failedValidation = true>
										</cfif>

										<cfset local.tmpStr.od = "">
										<cfset local.tmpStr.nd = "">
										<cfif len(local.oldVal)>
											<cfset local.thisFieldRawContent = getRawContentFromSiteResourceID(siteID=local.orgDefaultSiteID, siteResourceID=val(local.oldVal))>
											<cfset local.tmpStr.n = local.tmpStr.o>
											<cfset local.tmpStr.od = local.thisFieldRawContent>
											<cfif local.failedValidation>
												<cfset local.tmpStr.nd = local.tmpStr.od>
											<cfelse>
												<cfset local.tmpStr.nd = local.newVal>
											</cfif>
											<cfif compare(local.tmpStr.od,local.tmpStr.nd)>
												<cfset local.tmpStr.isChanged = 1>
											</cfif>
										<cfelseif NOT local.failedValidation and len(local.newVal)>
											<cfset local.tmpStr.n = 0>
											<cfset local.tmpStr.nd = local.newVal>
											<cfset local.tmpStr.isChanged = 1>
										</cfif>
										<cfset arrayAppend(local.arrFields,local.tmpStr)>
									</cfcase>
									</cfswitch>
								</cfcase>
								<cfcase value="HTMLCONTENT">
									<cfset local.tmpStr = { s='cf', dc=local.currColumnDisplayCode, fc=local.currColumnFieldCode, c=local.currColumnName, label=local.currColumnName, o=local.oldVal, n=local.newVal, isChanged=0, isCon=1, isHTML=1 }>

									<!--- check min/max requirements --->
									<cfset local.failedValidation = false>
									<cfif isDefined("local.mf.xmlAttributes.minChars") and isDefined("local.mf.xmlAttributes.maxChars") 
										and local.mf.xmlAttributes.minChars gt 0 and local.mf.xmlAttributes.maxChars gt 0
										and len(local.newVal) gt 0 
										and (len(local.newVal) lt local.mf.xmlAttributes.minChars OR len(local.newVal) gt local.mf.xmlAttributes.maxChars)>
										<cfset local.failedValidation = true>
									</cfif>

									<cfset local.tmpStr.od = "">
									<cfset local.tmpStr.nd = "">

									<cfif len(local.oldVal)>
										<cfset local.thisFieldRawContent = getRawContentFromSiteResourceID(siteID=local.orgDefaultSiteID, siteResourceID=val(local.oldVal))>
										<cfset local.tmpStr.n = local.tmpStr.o>
										<cfset local.tmpStr.od = local.thisFieldRawContent>
										<cfif local.failedValidation>
											<cfset local.tmpStr.nd = local.tmpStr.od>
										<cfelse>
											<cfset local.tmpStr.nd = local.newVal>
										</cfif>
										<cfif compare(local.tmpStr.od,local.tmpStr.nd)>
											<cfset local.tmpStr.isChanged = 1>
										</cfif>
									<cfelseif NOT local.failedValidation and len(local.newVal)>
										<cfset local.tmpStr.n = 0>
										<cfset local.tmpStr.nd = local.newVal>
										<cfset local.tmpStr.isChanged = 1>
									</cfif>
									<cfset arrayAppend(local.arrFields,local.tmpStr)>
								</cfcase>
							</cfswitch>
						</cfif>

						<cfset local.colsProcessed = listAppend(local.colsProcessed,local.currColumnID)>
					</cfif>
				</cfif>
			</cfloop>
		</cfloop>

		<!--- save documents and record history --->
		<cfif arrayLen(local.arrDocumentFields)>
			
			<cfloop array="#local.arrDocumentFields#" index="local.thisField">
				<cfif not structKeyExists(local,"objDocument")>
					<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
					<cfset local.rootSectionID = CreateObject("component","model.system.platform.section").getSectionFromSectionCode(siteID=local.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
					<cfset local.orgDefaultSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.orgDefaultSiteID)>
					<cfset local.memberAdminSiteResourceID = application.objSiteInfo.mc_siteInfo[local.orgDefaultSiteCode].memberAdminSiteResourceID>

					<cfset local.docmemberID = session.cfcuser.memberdata.memberID>
					<cfif structKeyExists(session.cfcuser,"mUpdateLinkStr") && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<cfset local.docmemberID = session.cfcuser.mUpdateLinkStr.memberID>
					</cfif>
				</cfif>

				<!--- if there is an old, the new upload will become a version of the old and nothing needs to be done in memberdata --->
				<!--- deleting docs is handled via a delete document link and not here --->
				<!--- only need to add memberdata if there wasnt an old value --->
				<cfset local.fileUploaded = TRUE>
				<cftry>
					<cfset local.newFile = local.objDocument.uploadFile(local.thisField.fc)>
					<cfcatch type="any">
						<cfset local.fileUploaded = FALSE>
					</cfcatch>
				</cftry>
				<cfif local.fileUploaded>
					<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
					<cfset local.uploadedFileName = local.newFile.clientFile>
					<cfset local.uploadedFileExt = local.newFile.clientFileExt>

					<!--- if there's an existing document, insert new version --->
					<cfif val(local.thisField.o)>
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDocumentDetails">
							SELECT d.documentID, dl.documentLanguageID
							FROM dbo.cms_documents d
							INNER JOIN dbo.cms_documentLanguages dl on d.documentID = dl.documentID 
								AND dl.languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.mcstruct.languageID#">
							WHERE d.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisField.o)#">
						</cfquery>
					
						<cfset local.documentID = local.qryDocumentDetails.documentID>
						<cfset local.documentVersionID = local.objDocument.insertVersion(orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
							sitecode=local.orgDefaultSiteCode, fileData=local.newFile, documentLanguageID=local.qryDocumentDetails.documentLanguageID,
							author='', contributorMemberID=local.docmemberID, isActive=arguments.doSave, recordedByMemberID=local.docmemberID, delayInMinutes=5, 
							oldFileExt=local.uploadedFileExt)>
						
						<cfset local.thisField.od = "Document Updated">
						<cfset local.thisField.nd = local.uploadedFileName>
						<cfset ArrayAppend(local.arrHistoryMessages, "#local.thisField.label# was updated.")>

						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateMemberDate">
							UPDATE dbo.ams_members
							SET dateLastUpdated = getdate()
							WHERE memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('data.memberid')#">
						</cfquery>

					<!--- otherwise add new document --->
					<cfelse>
						<cfset local.insertResults = local.objDocument.insertDocument(siteID=local.orgDefaultSiteID, 
							resourceType='ApplicationCreatedDocument', parentSiteResourceID=local.memberAdminSiteResourceID,
							sectionID=local.rootSectionID, docTitle='', docDesc='', author='', fileData=local.newFile, isVisible=false, 
							contributorMemberID=local.docmemberID, isActive=arguments.doSave, recordedByMemberID=local.docmemberID, fromCustomFields=true, 
							delayInMinutes=5, oldFileExt=local.uploadedFileExt)>
						<cfset local.documentID = local.insertResults.documentID>
						<cfset local.documentVersionID = local.insertResults.documentVersionID>

						<cfset local.thisField.od = "">
						<cfset local.thisField.nd = local.uploadedFileName>
						<cfset local.thisField.n = local.insertResults.documentSiteResourceID>
						<cfset ArrayAppend(local.arrHistoryMessages, "#local.thisField.label# was added.")>

						<!--- using setMemberData here since PMI does not support DOCUMENTOBJ --->
						<cfstoredproc procedure="ams_setMemberData" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('data.memberid')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.thisField.c#">
							<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.thisField.n#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.docmemberID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						</cfstoredproc>
					</cfif>
					
					<!--- copy file to temp location so the ESQ can attach it. The s3upload queue will upload and delete it from orig location in delayInMinutes --->
					<cfset local.uploadedFileFolderPath = application.paths.RAIDSiteDocuments.path & arguments.event.getValue('mc_siteInfo.orgcode') & "/" & application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.orgDefaultSiteID)>
					<cfset local.uploadedFileNameWithExt = local.documentVersionID & "." & local.newFile.serverFileExt>
					<cfset local.origFileNameWithExt = local.newFile.ServerFileName & "." & local.newFile.serverFileExt>
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.sitecode'))>
					<cffile action="copy" source="#local.uploadedFileFolderPath#/#local.uploadedFileNameWithExt#" destination="#local.strFolder.folderPath#/#local.origFileNameWithExt#">

					<cfset local.strNewDocData[local.thisfield.fc] = { documentID:local.documentID, documentVersionID:local.documentVersionID }>
				</cfif>
			</cfloop>

			<!--- record history --->
			<cfif arrayLen(local.arrHistoryMessages)>
				<cfset local.actorMemberID = session.cfcuser.memberdata.memberID>
				<cfif structKeyExists(session.cfcuser,"mUpdateLinkStr") && not application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.actorMemberID = session.cfcuser.mUpdateLinkStr.memberID>		
				</cfif>
				<cfset createObject('component','model.system.platform.history').addMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteInfo.orgid'),
					actorMemberID=local.actorMemberID, receiverMemberID=val(arguments.event.getValue('data.memberid')),
					mainMessage="Member Info Updated", messages=local.arrHistoryMessages)>
			</cfif>
		</cfif>

		<cfreturn { arrFields:local.arrFields, strNewDocData:local.strNewDocData }>
	</cffunction>

	<cffunction name="saveAddressData" access="private" output="false" returntype="array">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		<cfargument name="objSaveMember" type="any" required="no">
		
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>

		<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		
		<cfloop query="local.qryOrgAddresses">
			<cfset local.thisAddressTypeID = local.qryOrgAddresses.addressTypeID>
			
			<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
			<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrValues">
					select ma.addressID, mat.addressType, ma.attn, ma.address1, ma.address2, ma.address3, ma.city, 
						ma.stateID, ma.stateName, ma.postalCode, ma.county, ma.countryID, 
						(select countryCode from ams_countries where countryID = ma.countryID) as countryCode
					from dbo.ams_memberAddresses ma
					inner join dbo.ams_memberAddressTypes mat on mat.addressTypeID = ma.addressTypeID
					where ma.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					and ma.memberID = <cfqueryparam value="#arguments.event.getValue('data.memberid')#" cfsqltype="CF_SQL_INTEGER">
					and ma.addressTypeID = #local.thisAddressTypeID#
				</cfquery>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrPhone">
					select mp.phoneTypeID, mp.addressID, mp.phone, mpt.phoneType
					from dbo.ams_memberPhones mp
					inner join dbo.ams_memberPhoneTypes mpt on mpt.phoneTypeID = mp.phoneTypeID
					where mp.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					and mp.memberID = <cfqueryparam value="#arguments.event.getValue('data.memberid')#" cfsqltype="CF_SQL_INTEGER">
					and mp.addressID = #val(local.qryCurrValues.addressID)#
				</cfquery>

				<cfif local.qryOrgAddresses.hasAttn eq 1>
					<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_attn', label='#local.qryOrgAddresses.addresstype# ATTN', o=local.qryCurrValues.attn, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_attn',''), isChanged=0 }>
					<cfset local.tmpStr.od = local.tmpStr.o>
					<cfset local.tmpStr.nd = local.tmpStr.n>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_address1', label='#local.qryOrgAddresses.addresstype# Address Line 1', o=local.qryCurrValues.address1, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address1',''), isChanged=0 }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>

				<cfif local.qryOrgAddresses.hasAddress2 eq 1>
					<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_address2', label='#local.qryOrgAddresses.addresstype# Address Line 2', o=local.qryCurrValues.address2, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address2',''), isChanged=0 }>
					<cfset local.tmpStr.od = local.tmpStr.o>
					<cfset local.tmpStr.nd = local.tmpStr.n>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfif local.qryOrgAddresses.hasAddress3 eq 1>
					<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_address3', label='#local.qryOrgAddresses.addresstype# Address Line 3', o=local.qryCurrValues.address3, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address3',''), isChanged=0 }>
					<cfset local.tmpStr.od = local.tmpStr.o>
					<cfset local.tmpStr.nd = local.tmpStr.n>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_city', label='#local.qryOrgAddresses.addresstype# City', o=local.qryCurrValues.city, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_city',''), isChanged=0 }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>

				<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_stateID', label='#local.qryOrgAddresses.addresstype# State', o=local.qryCurrValues.stateID, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_stateID',''), isChanged=0 }>
				<cfset local.tmpStr.od = local.qryCurrValues.stateName>
				<cfquery name="local.qryNewStateName" datasource="#application.dsn.membercentral.dsn#">
					select name
					from dbo.ams_states
					where stateID = <cfqueryparam value="#val(local.tmpStr.n)#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
				<cfset local.tmpStr.nd = local.qryNewStateName.name>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>

				<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_postalCode', label='#local.qryOrgAddresses.addresstype# Postal Code', o=local.qryCurrValues.postalCode, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_postalCode',''), isChanged=0 }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>

				<cfif local.qryOrgAddresses.hasCounty eq 1>
					<cfset local.tmpStr = { s='addr', fc='ma_#local.thisAddressTypeID#_County', label='#local.qryOrgAddresses.addresstype# County', o=local.qryCurrValues.County, n=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_County',''), isChanged=0 }>
					<cfset local.tmpStr.od = local.tmpStr.o>
					<cfset local.tmpStr.nd = local.tmpStr.n>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfloop query="local.qryOrgPhones">
					<cfquery dbtype="query" name="local.qryOldPhoneCheck">
						select phone 
						from [local].qryCurrPhone 
						where phoneTypeID = #local.qryOrgPhones.phoneTypeID#
					</cfquery>

					<cfset local.tmpStr = { s='addr', fc='mp_#local.thisAddressTypeID#_#local.qryOrgPhones.phoneTypeID#_phone', label='#local.qryOrgAddresses.addresstype# #local.qryOrgPhones.phoneType#', o=local.qryOldPhoneCheck.phone, n=arguments.event.getTrimValue('data.mp_#local.thisAddressTypeID#_#local.qryOrgPhones.phoneTypeID#_phone',''), isChanged=0 }>
					<cfset local.tmpStr.od = local.tmpStr.o>
					<cfset local.tmpStr.nd = local.tmpStr.n>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfloop>
			</cfif>

			<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
				<cfset arguments.objSaveMember.setAddress(type=local.qryOrgAddresses.addressType, 
													attn=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_attn',''), 
													address1=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address1',''),
													address2=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address2',''), 
													address3=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_address3',''), 
													city=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_city',''), 
													stateID=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_stateid',0), 
													postalCode=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_postalCode',''),
													county=arguments.event.getTrimValue('data.ma_#local.thisAddressTypeID#_county',''))>

				<cfloop query="local.qryOrgPhones">
					<cfset arguments.objSaveMember.setPhone(addressType=local.qryOrgAddresses.addressType, type=local.qryOrgPhones.phoneType, 
									value=arguments.event.getTrimValue('data.mp_#local.thisAddressTypeID#_#local.qryOrgPhones.phoneTypeID#_phone',''))>
				</cfloop>
			</cfif>
		</cfloop>

		<cfloop query="local.qryOrgAddressTags">
			<cfset local.selectedAddressType = arguments.event.getValue('data.mat_#local.qryOrgAddressTags.addressTagTypeID#_','')>
			<cfif local.qryOrgAddressTags.allowMembersToUpdate is 1 and len(local.selectedAddressType)>
				<cfquery name="local.qryAddressTypeName" dbtype="query">
					select addressType
					from [local].qryOrgAddresses
					where addressTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.selectedAddressType#">
				</cfquery>

				<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
				<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
					<cfquery name="local.qryCurrAddressTag" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

						select mat.addressTypeID, mat.addressType
						from dbo.ams_memberAddressTags as matag
						inner join dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID AND mat.addressTypeID = matag.addressTypeID
						where matag.orgID = @orgID
						and matag.memberID = <cfqueryparam value="#arguments.event.getValue('data.memberid')#" cfsqltype="CF_SQL_INTEGER">
						and matag.addressTagTypeID = #local.qryOrgAddressTags.addressTagTypeID#;
					</cfquery>

					<cfset local.tmpStr = { s='addrtag', fc='mat_#local.qryOrgAddressTags.addressTagTypeID#_', label='Designated #local.qryOrgAddressTags.addresstagtype# Address', o=local.qryCurrAddressTag.addressTypeID, n=local.selectedAddressType, isChanged=0 }>
					<cfset local.tmpStr.od = local.qryCurrAddressTag.addressType>
					<cfset local.tmpStr.nd = local.qryAddressTypeName.addressType>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
					<cfset arguments.objSaveMember.setAddressTag(tag=local.qryOrgAddressTags.addressTagType, type=local.qryAddressTypeName.addressType)>
				</cfif>
			</cfif>
		</cfloop>

		<cfreturn local.arrFields>
	</cffunction>

	<cffunction name="saveEmailData" access="private" output="true" returntype="array">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		<cfargument name="objSaveMember" type="any" required="no">
		
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>

		<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfloop query="local.qryOrgEmails">
			<cfset local.thisEmail = left(arguments.event.getTrimValue('data.me_#local.qryOrgEmails.emailTypeID#_email',''),255)>
			<cfif len(local.thisEmail) and NOT isValid("regex",local.thisEmail,application.regEx.email)>
				<cfset local.thisEmail = ''>
			</cfif>

			<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
			<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrEmail">
					select me.email
					from dbo.ams_memberEmails me
					inner join dbo.ams_memberEmailTypes met on met.emailTypeID = me.emailTypeID
					where me.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					and me.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('data.memberid')#">
					and me.emailTypeID = #local.qryOrgEmails.emailTypeID#
				</cfquery>

				<cfset local.tmpStr = { s='eml', fc='me_#local.qryOrgEmails.emailTypeID#_email', label=local.qryOrgEmails.emailType, o=local.qryCurrEmail.email, n=local.thisEmail, isChanged=0, eid=local.qryOrgEmails.emailTypeID }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
				<cfset arguments.objSaveMember.setEmail(type=local.qryOrgEmails.emailType, value=local.thisEmail)>
			</cfif>
		</cfloop>

		<cfloop query="local.qryOrgEmailTags">
			<cfset local.selectedEmailType = arguments.event.getValue('data.met_#local.qryOrgEmailTags.emailTagTypeID#_','')>
			<cfif local.qryOrgEmailTags.allowMembersToUpdate is 1 and len(local.selectedEmailType)>
				<cfquery name="local.qryEmailTypeName" dbtype="query">
					select emailType
					from [local].qryOrgEmails
					where emailTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.selectedEmailType#">
				</cfquery>

				<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
				<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
					<cfquery name="local.qryCurEmailTag" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

						select met.emailTypeID, met.emailType
						from dbo.ams_memberEmailTags as metag
						inner join dbo.ams_memberEmailTypes as met on met.orgID = @orgID and met.emailTypeID = metag.emailTypeID
						where metag.orgID = @orgID
						and metag.memberID = <cfqueryparam value="#arguments.event.getValue('data.memberid')#" cfsqltype="CF_SQL_INTEGER">
						and metag.emailTagTypeID = #local.qryOrgEmailTags.emailTagTypeID#;
					</cfquery>

					<cfset local.tmpStr = { s='emailtag', fc='met_#local.qryOrgEmailTags.emailTagTypeID#_', label='Designated #local.qryOrgEmailTags.emailTagType# Email', o=local.qryCurEmailTag.emailTypeID, n=local.selectedEmailType, isChanged=0 }>
					<cfset local.tmpStr.od = local.qryCurEmailTag.emailType>
					<cfset local.tmpStr.nd = local.qryEmailTypeName.emailType>
					<cfif compare(local.tmpStr.o,local.tmpStr.n)>
						<cfset local.tmpStr.isChanged = 1>
					</cfif>
					<cfset arrayAppend(local.arrFields,local.tmpStr)>
				</cfif>

				<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
					<cfset arguments.objSaveMember.setEmailTag(tag=local.qryOrgEmailTags.emailTagType, type=local.qryEmailTypeName.emailType)>
				</cfif>
			</cfif>
		</cfloop>

		<cfreturn local.arrFields>
	</cffunction>

	<cffunction name="saveWebsiteData" access="public" output="true" returntype="array">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>

		<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfloop query="local.qryOrgWebsites">
			<cfset local.thisWebsite = left(arguments.event.getTrimValue('data.mw_#local.qryOrgWebsites.websiteTypeID#_website',''),400)>
			<cfif len(local.thisWebsite) and NOT isValid("regex",local.thisWebsite,application.regEx.url)>
				<cfset local.thisWebsite = "">
			</cfif>

			<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
			<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates'))>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrWebsite">
					select mw.website
					from dbo.ams_memberWebsites mw
					inner join dbo.ams_memberWebsiteTypes mwt on mwt.websiteTypeID = mw.websiteTypeID
					where mw.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
					and mw.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('data.memberid')#">
					and mw.websiteTypeID = #local.qryOrgWebsites.websiteTypeID#
				</cfquery>

				<cfset local.tmpStr = { s='web', fc='mw_#local.qryOrgWebsites.websiteTypeID#_website', label=local.qryOrgWebsites.websiteType, o=local.qryCurrWebsite.website, n=local.thisWebsite, isChanged=0, wid=local.qryOrgWebsites.websiteTypeID }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember')>
				<cfset arguments.objSaveMember.setWebsite(type=local.qryOrgWebsites.websiteType, value=local.thisWebsite)>
			</cfif>
		</cfloop>

		<cfreturn local.arrFields>
	</cffunction>

	<cffunction name="saveProfessionalLicensesData" access="public" output="true" returntype="array">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="doSave" type="boolean" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrFields = arrayNew(1)>
		
		<!--- prof licenses --->		
		<cfset local.objMemberUpdate = CreateObject("component","model.admin.memberUpdate.memberUpdate")/>
		<cfset local.updateMemberSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='UpdateMember',siteID=arguments.event.getValue('mc_siteinfo.siteid'))/>
		<cfset local.qryCurrentProfessionalLicenses = local.objMemberUpdate.getCurrentProfessionalLicenses(siteResourceID=local.updateMemberSiteResourceID)>
		<cfset local.qryMemberProLicenses = CreateObject("component","model.admin.members.members").getMember_prolicenses(memberID=arguments.event.getValue('data.memberid'), orgID=arguments.event.getValue('mc_siteInfo.orgid'))/>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.updateMemberSiteResourceID, memberID=arguments.event.getValue('data.memberid'), siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.formData = arguments.event.valueExists('data') and isStruct(arguments.event.getValue('data')) ? arguments.event.getValue('data') : structNew()>

		<cfloop query="local.qryCurrentProfessionalLicenses">
			<cfquery dbtype="query" name="local.qrySelectedProLicenses">
				SELECT *
				FROM local.qryMemberProLicenses
				WHERE PLTypeID=#local.qryCurrentProfessionalLicenses.PLTypeID#;
			</cfquery>

			<cfif (local.tmpRights.addLicense or local.tmpRights.editLicenseNumber) and structKeyExists(local.formData,'mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_licenseNumber')>
				<cfset local.tmpLicenseNumber = left(trim(local.formData["mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_licenseNumber"]),200)>
			<cfelse>
				<cfset local.tmpLicenseNumber = local.qrySelectedProLicenses.LicenseNumber/>
			</cfif>
			<cfif (local.tmpRights.addLicense or local.tmpRights.editLicenseActiveDate) and structKeyExists(local.formData,'mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_activeDate')>
				<cfset local.tmpActiveDate = trim(local.formData["mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_activeDate"])>
			<cfelse>
				<cfset local.tmpActiveDate = local.qrySelectedProLicenses.ActiveDate/>
			</cfif>
			<cfif (local.tmpRights.addLicense or local.tmpRights.editLicenseStatus) and structKeyExists(local.formData,'mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_status')>
				<cfset local.tmpStatus = trim(local.formData["mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_status"])>
			<cfelse>
				<cfset local.tmpStatus = local.qrySelectedProLicenses.statusName/>
			</cfif>		

			<!--- if emailMemberUpdates is defined, we need to compare fields for changes  --->
			<cfif len(arguments.event.getValue('mc_siteinfo.emailMemberUpdates')) and listFindNoCase(arguments.event.getTrimValue('mpl_pltypeid',''),local.qryCurrentProfessionalLicenses.PLTypeID)>
				<cfset local.tmpStr = { s='profLn', fc='mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_licenseNumber', label=local.qryCurrentProfessionalLicenses.PLName, o=local.qrySelectedProLicenses.LicenseNumber, n=local.tmpLicenseNumber, isChanged=0, pid=local.qryCurrentProfessionalLicenses.PLTypeID }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
				<cfset local.tmpStr = { s='profAd', fc='mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_activeDate', label=local.qryCurrentProfessionalLicenses.PLName, o=local.qrySelectedProLicenses.ActiveDate, n=local.tmpActiveDate, isChanged=0, pid=local.qryCurrentProfessionalLicenses.PLTypeID }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
				<cfset local.tmpStr = { s='profS', fc='mpl_#local.qryCurrentProfessionalLicenses.PLTypeID#_licenseNumber', label=local.qryCurrentProfessionalLicenses.PLName, o=local.qrySelectedProLicenses.statusName, n=local.tmpStatus, isChanged=0, pid=local.qryCurrentProfessionalLicenses.PLTypeID }>
				<cfset local.tmpStr.od = local.tmpStr.o>
				<cfset local.tmpStr.nd = local.tmpStr.n>
				<cfif compare(local.tmpStr.o,local.tmpStr.n)>
					<cfset local.tmpStr.isChanged = 1>
				</cfif>
				<cfset arrayAppend(local.arrFields,local.tmpStr)>
			</cfif>

			<cfif arguments.doSave is 1 and structKeyExists(arguments,'objSaveMember') and len(local.tmpStatus)>
				<cfset arguments.objSaveMember.setProLicense(name=local.qryCurrentProfessionalLicenses.PLName, status=local.tmpStatus, license=local.tmpLicenseNumber, date=local.tmpActiveDate)>
			</cfif>
		</cfloop>

		<cfreturn local.arrFields>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Member Update</h4>
				<cfif arguments.event.valueExists('message')>
					<p class="tsAppBodyText">
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>The link you accessed is not valid. Contact your association for assistance.</b></cfcase>
							<cfcase value="2"><b>Your link has expired. <a href="#this.link.edit#">Click here</a> to login and/or request a password reset.</b></cfcase>
							<cfcase value="3"><b>The Member profile cannot be viewed since you are logged in as an Administrator. Please log out.</cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="deleteDocument" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgid')>
		<cfset local.memberID = arguments.event.getValue('memberID',0)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetColumnName">
			select TOP 1 columnName 
			from dbo.ams_memberDataColumns 
			where columnID = <cfqueryparam value="#arguments.event.getValue('deleteDocCol',0)#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfstoredproc procedure="ams_deleteMemberCustomDocument" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('deleteDocCol',0)#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('deleteDoc',0)#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>

		<cfset local.arrHistoryMessages = ["#local.qryGetColumnName.columnName# was removed."]>
		<cfset createObject('component','model.system.platform.history').addMemberUpdateHistory(orgID=local.orgID,
			actorMemberID=session.cfcuser.memberdata.memberID, receiverMemberID=local.memberID, 
			mainMessage="Member Info Updated", messages=local.arrHistoryMessages)>
		
		<cflocation url="#this.link.edit#&memberID=#arguments.event.getValue('memberID',0)#&tab=Basics&msg=2" addtoken="false">
	</cffunction>	
	
	<cffunction name="updateMemberDocDownload" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.lstDoc = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('doc',''),"xPcmKx","%","ALL")))),"M3mbeR_CenTR@l")>
			<cfif variables.isBot OR listLen(local.lstDoc,'|') is not 3>
				<cfabort>
			</cfif>
			<cfset local.strDoc = { columnID=getToken(local.lstDoc,1,'|'), memberID=getToken(local.lstDoc,3,'|') }>

			<cfset local.activeMemberID = application.objMember.getActiveMemberID(local.strDoc.memberID)>
			<cfif local.activeMemberID is 0 OR arguments.event.getValue('memberid',0) neq local.activeMemberID>
				<cfabort>
			<cfelse>
				<cfset local.xmlAdditionalData_Member = CreateObject("component","model.admin.members.members").getMemberAdditionalData(memberid=local.activeMemberID)>
				<cfset local.actualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.strDoc.columnID#]/@actualColumnValue)")>
				<cfset local.getDocument =	createObject("model.system.platform.document").getDocumentDataBySiteResourceID(local.actualValue)>
		
				<cfif len(local.getDocument.DOCUMENTID) is 0>
					<cfabort>
				<cfelse>
					<cfset local.theFile = "#application.paths.RAIDSiteDocuments.path##local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#">
					<cfset local.s3keyMod = numberFormat(local.getdocument.documentVersionID mod 1000,"0000")>
					<cfset local.s3objectKey = lcase("sitedocuments/#local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.s3keyMod#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#")>
					<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.theFile, 
						displayName=local.getDocument.fileName, forceDownload=0, s3bucket="membercentralcdn", s3objectKey=local.s3objectKey, 
						s3expire=10, s3requesttype="ssl")>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfabort>
		</cfcatch>
		</cftry>
	</cffunction>	

	<cffunction name="showContentEditor" access="public" output="false" returntype="struct">
		<cfargument name="frmField" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="tools" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryContent" datasource="#application.dsn.membercentral.dsn#">
			DECLARE @contentID int;

			SELECT TOP 1 @contentID = contentID
			FROM dbo.cms_content
			WHERE siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ContentID DESC;

			SET @contentID = ISNULL(@contentID,0);

			SELECT c.rawContent
			FROM dbo.fn_getContent(@contentID,1) as c
		</cfquery>

		<cfset local.data.html = application.objWebEditor.embed(objname=arguments.frmField, objValue=local.qryContent.rawContent, tools=arguments.tools)>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getMemberGroupInfo" access="private" returntype="query" output="false">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">		
		<cfargument name="orgID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGroupName" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

			select distinct(g.groupName)
			from dbo.cache_members_groups as mg
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberid = mg.memberid 
				and m.memberid = m.activememberID
				and m.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
				and m.status <> 'D'
			inner join dbo.ams_groups as g on g.orgID = @orgID
				and g.groupID = mg.groupID
			where mg.orgID = @orgID
			and mg.groupid = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfreturn local.qryGroupName>
	</cffunction>
	
	<cffunction name="getFSContentDetails" access="private" returntype="query" output="false">
		<cfargument name="contentID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGetFSContent" datasource="#application.dsn.membercentral.dsn#">
			select
				contentID,
				isHTML,
				contentLanguageID,
				languageID,
				contentVersionID,
				contentTitle,
				contentDesc,
				rawContent
			from
				dbo.fn_getContent(<cfqueryparam value="#arguments.contentID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultLanguageID#" cfsqltype="CF_SQL_INTEGER">)			
		</cfquery>
		<cfreturn local.qryGetFSContent />
	</cffunction>

	<cffunction name="getRawContentFromSiteResourceID" access="private" returntype="string" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var qryRawContent = "">

		<cfquery name="qryRawContent" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @contentID int, @languageID int;
			SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.mcstruct.languageID#">;

			SELECT @contentID = contentID 
			FROM dbo.cms_content 
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;

			SELECT rawContent FROM dbo.fn_getContent(@contentID,@languageID);
		</cfquery>

		<cfreturn qryRawContent.rawContent>
	</cffunction>
	
</cfcomponent>
