ALTER PROC dbo.ams_setMemberData
@memberID int,
@orgID int,
@columnName varchar(128),
@columnValue varchar(max),
@recordedByMemberID int,
@byPassQueue bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY	

	DECLARE @columnID int, @allowMultiple bit, @minSelected int, @maxSelected int, @thisValueID int, @dataTypeCode varchar(20), @updateColumnID int;

	SELECT @columnID = mdc.columnID, @allowMultiple = mdc.allowMultiple, @minSelected = mdc.minSelected, @maxSelected = mdc.maxSelected, @dataTypeCode = dt.dataTypeCode
	FROM dbo.ams_memberDataColumns AS mdc
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = mdc.dataTypeID
	WHERE orgID = @orgID
	AND columnName = @columnName;

	IF @memberID is null or @columnID is null
		RAISERROR('Unable to save data. Information missing.',16,1);

	IF @dataTypeCode = 'DATE'
		SELECT @updateColumnID = columnID
		FROM dbo.ams_memberDataColumns 
		WHERE orgID = @orgID
		AND linkedDateColumnID = @columnID;

	BEGIN TRAN;
		-- delete records from memberdata for this column and user
		EXEC dbo.ams_deleteMemberData @memberID=@memberID, @columnID=@columnID, @byPassQueue=@byPassQueue;

		-- if allowMultiple = 1, then @columnValue will always be a list of valueIDs.
		IF @allowMultiple = 1 BEGIN
			select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',');
			while @thisValueID is not null BEGIN
				EXEC dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=@thisValueID, @columnValue=null, @byPassQueue=@byPassQueue;
				select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',') where listitem > @thisValueID;
			END

			-- validation for min/max selected
			IF @minSelected is not null and @maxSelected is not null BEGIN
				IF EXISTS(		
					select top 1 md.memberid
					from dbo.ams_memberData as md
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
					where mdcv.columnID = @columnID
					and md.memberid = @memberID
					group by md.memberid
					having count(*) not between @minSelected and @maxSelected
				) RAISERROR('Unable to save data. Failed validation for min/max selected.',16,1);
			END
		END ELSE BEGIN
			EXEC dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=NULL, @columnValue=@columnValue, @byPassQueue=@byPassQueue;
		END
	COMMIT TRAN;

	IF @updateColumnID IS NOT NULL
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=@memberID, @columnID=@updateColumnID, 
			@recordedByMemberID=@recordedByMemberID, @byPassQueue=@byPassQueue;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
