<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		local.methodToRun = this[arguments.event.getValue('mca_ta')];

		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="listParticipants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.listParticipantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getParticipants&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_participants.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="swlSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qrySWLAssociations = CreateObject("component","seminarWebParticipants").getParticipantsByFormat(format='SWL')>
		<cfset local.arrZoomWebinarLicenses = CreateObject("component","model.seminarWeb.SWZoomWebinar").getWebinarLicenses()>

		<cfset local.seminarsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLSchedulePrograms&mode=stream'>
		<cfset local.seminarsExportLink = buildCurrentLink(arguments.event,"exportSWLSchedule") & "&mode=stream">

		<cfset local.pDateFrom = dateformat(Now(),"m/d/yyyy")>
		<cfset local.pDateTo = dateformat(dateAdd("yyyy",1,Now()),"m/d/yyyy")>

		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
		<cfset local.defaultTimeZoneID = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).defaultTimeZoneID>
		<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.timeZoneAbbr = local.objTZ.getTZAllFromTZID(timeZoneID=local.defaultTimeZoneID).timeZoneAbbr>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_swlSchedule.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWLSchedule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";

			var strFilters = {};
			strFilters.keyword = arguments.event.getValue('fKeyword','');
			strFilters.publisher = arguments.event.getValue('fPublisher','');
			strFilters.seminarID = arguments.event.getValue('fSeminarID','');
			strFilters.isNATLE = arguments.event.getValue('fIsNATLE','');
			strFilters.ZoomWebinarHostID = arguments.event.getValue('fZoomHostID','');
			strFilters.dateFrom = dateformat(arguments.event.getTrimValue('fDateFrom',Now()),"m/d/yyyy");
			strFilters.dateTo = dateformat(arguments.event.getTrimValue('fDateTo',dateAdd("yyyy",1,Now())),"m/d/yyyy");
			
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "Programs#DateFormat(strFilters.dateFrom,'yyyymmdd')#-#DateFormat(strFilters.dateTo,'yyyymmdd')#.csv";

			local.licenseStatement = "case when swl.providerID = 3 then ";
			local.arrZoomWebinarLicenses = createObject("component","model.seminarWeb.SWZoomWebinar").getWebinarLicenses();
			if (arrayLen(local.arrZoomWebinarLicenses)) {
				local.licenseStatement = local.licenseStatement & "case swl.ZoomWebinarHostID ";
				for (local.row in local.arrZoomWebinarLicenses) {
					local.licenseStatement = local.licenseStatement & "when '#local.row.id#' then '#local.row.firstname#' ";
				}
				local.licenseStatement = local.licenseStatement & "else '' end ";
			} else {
				local.licenseStatement = local.licenseStatement & "'' ";
			}
			
			local.licenseStatement = local.licenseStatement & "else '' end ";

			local.sqlParams = {};
			structInsert(local.sqlParams,'datefrom',{ value=strFilters.dateFrom, cfsqltype="CF_SQL_DATE" });
			structInsert(local.sqlParams,'dateto',{ value="#strFilters.dateTo# 23:59:59", cfsqltype="CF_SQL_TIMESTAMP" });
			if (strFilters.publisher neq '')
				structInsert(local.sqlParams,'publisherid',{ value=strFilters.publisher, cfsqltype="CF_SQL_INTEGER" });
			else
				structInsert(local.sqlParams,'publisherid',{ null=true, cfsqltype="CF_SQL_INTEGER" });
			if (strFilters.seminarID neq '')
				structInsert(local.sqlParams,'seminarid',{ value=strFilters.seminarID, cfsqltype="CF_SQL_INTEGER" });
			else
				structInsert(local.sqlParams,'seminarid',{ null=true, cfsqltype="CF_SQL_INTEGER" });
			if (strFilters.keyword neq '')
				structInsert(local.sqlParams,'keyword',{ value=strFilters.keyword, cfsqltype="CF_SQL_VARCHAR" });
			else
				structInsert(local.sqlParams,'keyword',{ null=true, cfsqltype="CF_SQL_VARCHAR" });
			if (strFilters.isNATLE neq '')
				structInsert(local.sqlParams,'isNATLE',{ value=strFilters.isNATLE, cfsqltype="CF_SQL_BIT" });
			else
				structInsert(local.sqlParams,'isNATLE',{ null=true, cfsqltype="CF_SQL_BIT" });
			if (strFilters.ZoomWebinarHostID neq '')
				structInsert(local.sqlParams,'ZoomWebinarHostID',{ value=strFilters.ZoomWebinarHostID, cfsqltype="CF_SQL_VARCHAR" });
			else
				structInsert(local.sqlParams,'ZoomWebinarHostID',{ null=true, cfsqltype="CF_SQL_VARCHAR" });
			structInsert(local.sqlParams,'filename',{ value="#local.strFolder.folderPathUNC#\#local.reportFileName#", cfsqltype="CF_SQL_VARCHAR" });

			var qryExport = queryExecute("
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @sd date = :datefrom, @ed datetime = :dateto, @pub int = :publisherid, @sem int = :seminarid, 
						@kw varchar(100) = :keyword, @fn varchar(600) = :filename, @isNATLE bit = :isNATLE,
						@ZoomWebinarHostID varchar(30) = :ZoomWebinarHostID;

					IF OBJECT_ID('tempdb..##tmpSWL') IS NOT NULL
						DROP TABLE ##tmpSWL;

					SELECT 'SWL-' + cast(s.seminarId as varchar(10)) as [ProgramID], p.orgcode as [Publisher], 
						s.seminarName as [Program], s.seminarSubTitle as [ProgramSubTitle], swl.dateStart as [ProgramStart], 
						swl.dateEnd as [ProgramEnd], case when s.isPublished = 1 then 'Active' else 'Inactive' end as [Status], 
						swlp.provider as [Provider],
						#local.licenseStatement# as [License], 
						case when swl.isNATLE = 1 then 'NATLE' else '' end as [IsNATLE], COUNT(e.enrollmentID) AS [NumberofEnrollments], 
						min(e.dateEnrolled) as [FirstEnrollmentDate]
					INTO ##tmpSWL
					FROM dbo.tblSeminars AS s 
					INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID 
					LEFT OUTER JOIN dbo.tblEnrollments AS e ON s.seminarID = e.seminarID and e.isActive = 1
					LEFT OUTER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
					LEFT OUTER JOIN dbo.tblSeminarsSWLiveProviders AS swlp ON swlp.providerID = swl.providerID
					WHERE swl.dateStart between @sd and @ed
					AND s.isDeleted = 0
					AND 1 = case when @pub is not null and p.participantID <> @pub then 0 else 1 end
					AND 1 = case when @kw is not null and s.seminarName not like '%'+@kw+'%' then 0 else 1 end
					AND 1 = case when @sem is not null and s.seminarID <> @sem then 0 else 1 end
					AND 1 = case when @isNATLE is not null and swl.isNATLE <> @isNATLE then 0 else 1 end
					AND 1 = case when @ZoomWebinarHostID is not null and isnull(swl.ZoomWebinarHostID,'') <> @ZoomWebinarHostID then 0 else 1 end
					GROUP BY s.seminarID, p.orgcode, s.seminarName, s.seminarSubTitle, swl.dateStart, swl.dateEnd, s.isPublished, 
						swl.providerID, swlp.provider, swl.isNATLE, swl.ACLicenseID, swl.ZoomWebinarHostID;

					DECLARE @selectsql varchar(max) = 'SELECT [ProgramID], [Publisher], [Program], [ProgramSubTitle], [ProgramStart], 
						[ProgramEnd], [Status], [Provider], [License], [IsNATLE], [NumberofEnrollments], [FirstEnrollmentDate],
						ROW_NUMBER() OVER(order by [ProgramStart], [Program]) as mcCSVorder 
						*FROM* ##tmpSWL';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@fn, @returnColumns=0;
						
					IF OBJECT_ID('tempdb..##tmpSWL') IS NOT NULL
						DROP TABLE ##tmpSWL;
				
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
				", 
				local.sqlParams, { datasource=application.dsn.tlasites_seminarweb.dsn }
			);

			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWLPrograms('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="swlReminders" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.swlRemindersLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLReminders&mode=stream">

		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_swlReminders.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="createProgramInvitations" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.loadInvitationTypeFormLink = buildCurrentLink(arguments.event,"loadInvitationTypeForm") & '&mode=stream'>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_createProgramInvitations.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadInvitationTypeForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.ft = arguments.event.getValue('ft','')>
		<cfset local.objSWPAdmin = CreateObject("component","seminarWebParticipants")>

		<cfset local.showInvitationPreviewLink = buildCurrentLink(arguments.event,"showInvitationPreview") & '&ft=#local.ft#&mode=direct'>

		<cfif local.ft eq "SWL">
			<cfset local.subject = "New SeminarWeb Live! Seminar Announced">
			<cfset local.qrySeminars = CreateObject("component","seminarWebSWL").getAllSeminarsForInvitation()>
		<cfelseif local.ft eq "SWOD">
			<cfset local.subject = "New SeminarWeb On Demand Seminar Announced">
		<cfelseif local.ft eq "SWB">
			<cfset local.subject = "New Bundle Announced">
		</cfif>

		<cfset local.qryAssociations = local.objSWPAdmin.getParticipantsByFormat(format=local.ft)>
		<cfset local.qryMarketingLists = local.objSWPAdmin.getMarketingLists()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_invitations.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadProgramsForInvitation" access="public" output="false" returntype="struct">
		<cfargument name="ft" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct["success"] = true>

		<cftry>
			<cfif arguments.ft eq "SWOD">
				<cfset local.qrySeminars = CreateObject("component","model.seminarweb.SWODSeminars").getSeminarsForCatalogByName(catalogorgCode=arguments.orgcode, letter="", 
					startRow="1", maxRows="1000", depoMemberDataID=0, memberID=0)>
				<cfset local.arrSeminars = []>
				<cfloop query="local.qrySeminars">
					<cfif local.qrySeminars.format eq "SWOD">
						<cfset arrayAppend(local.arrSeminars, { "s"=local.qrySeminars.contentName, "i"=local.qrySeminars.contentID })>
					</cfif>
				</cfloop>
				<cfset local.returnStruct["programs"] = local.arrSeminars>
			<cfelseif arguments.ft eq "SWB">
				<cfif len(trim(arguments.orgcode))>
					<cfset local.qryBundles = CreateObject("component","model.seminarweb.SWBundles").getBundlesByOrgCodeWithOptins(orgcode=arguments.orgcode)>
				<cfelse>
					<cfset local.qryBundles = CreateObject("component","SeminarWebSWB").getAllBundlesForInvitation()>
				</cfif>
				<cfset local.arrBundles = []>
				<cfloop query="local.qryBundles">
					<cfset arrayAppend(local.arrBundles, { "s"=local.qryBundles.bundleName, "i"=local.qryBundles.bundleID })>
				</cfloop>
				<cfset local.returnStruct["programs"] = local.arrBundles>
			<cfelse>
				<cfset local.returnStruct["success"] = false>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendTestInvitation" access="public" output="false" returntype="struct">
		<cfargument name="ft" type="string" required="true">
		<cfargument name="subject" type="string" required="true">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="programIDlist" type="string" required="true">
		<cfargument name="template" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.msg = "">

		<cfif arguments.ft eq "SWL">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWL")>
		<cfelseif arguments.ft eq "SWOD">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWOD")>
		<cfelseif arguments.ft eq "SWB">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWB")>
		</cfif>

		<cftry>
			<cfif listFindNoCase("SWL,SWOD", arguments.ft)>
				<cfset local.objProgram.sendTestInvitationEmail(seminarIDlist=arguments.programIDlist, testEmail=arguments.email, 
					performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="testInvitation", orgcode=arguments.orgcode, 
					subject=arguments.subject, preheaderText=arguments.preheaderText, template=arguments.template)>
			<cfelseif arguments.ft eq "SWB">
				<cfset local.objProgram.sendTestInvitationEmail(bundleIDList=arguments.programIDlist, testEmail=arguments.email, 
					performedBy=session.cfcuser.memberdata.depomemberdataid, outgoingType="testInvitation", orgcode=arguments.orgcode, 
					subject=arguments.subject, preheaderText=arguments.preheaderText, template=arguments.template)>
			</cfif>
			<cfset local.returnStruct.msg = "Test Invitation has been sent to #arguments.email#."> 			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="showInvitationPreview" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.ft = arguments.event.getValue('ft')>
		<cfset local.programID = arguments.event.getValue('programID')>
		<cfset local.orgCode = arguments.event.getValue('orgcode')>
		<cfset local.subject = arguments.event.getValue('subject')>
		<cfset local.preheaderText = arguments.event.getValue('preheaderText')>
		<cfset local.template = arguments.event.getValue('SWTemplate')>
		
		<cfif local.ft eq "SWL">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWL")>
			<cfset local.objProgramModel = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.utm_source = "SWL Marketing">
		<cfelseif local.ft eq "SWOD">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWOD")>
			<cfset local.objProgramModel = CreateObject("component","model.seminarweb.SWODSeminars")>
			<cfset local.utm_source = "SWOD Marketing">
		<cfelseif local.ft eq "SWB">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWB")>
			<cfset local.objProgramModel = CreateObject("component","model.seminarweb.SWBundles")>
			<cfset local.utm_source = "SWB Marketing">
		</cfif>

		<cfif listFindNoCase("SWL,SWOD", local.ft)>
			<cfswitch expression="#local.template#">
				<cfcase value="2021">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2021(seminarIDlist=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject), preheaderText=urlDecode(local.preheaderText))>
				</cfcase>
				<cfcase value="2020">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2020(seminarIDlist=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject))>
				</cfcase>
				<cfcase value="2017NONSAE">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2017NONSAE(seminarIDlist=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject))>
				</cfcase>
			</cfswitch>

			<cfif ListLen(local.programID) EQ 1>
				<cfset local.utm_campaign = local.objProgramModel.getSeminarBySeminarID(seminarID=val(local.programID)).seminarName>
			<cfelse>
				<cfset local.utm_campaign = 'Multiple Programs'>
			</cfif>

		<cfelseif local.ft eq "SWB">
			<cfswitch expression="#local.template#">
				<cfcase value="2021">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2021(bundleIDList=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject), preheaderText=urlDecode(local.preheaderText))>
				</cfcase>
				<cfcase value="2020">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2020(bundleID=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject))>
				</cfcase>
				<cfcase value="2017">
					<cfset local.strEmailInvitation = local.objProgram.generateInvitationEmailFor2017(bundleID=local.programID, orgcode=local.orgCode, subject=urlDecode(local.subject))>
				</cfcase>
			</cfswitch>

			<cfif ListLen(local.programID) EQ 1>
				<cfset local.utm_campaign = local.objProgramModel.getBundleByBundleID(bundleID=val(local.programID), orgcode=local.orgcode).bundleName>
			<cfelse>
				<cfset local.utm_campaign = 'Multiple Programs'>
			</cfif>
		</cfif>
	
		<cfset local.strEmailInvitation.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailInvitation.html, utm_campaign=local.utm_campaign,
			utm_source=local.utm_source, utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailInvitation.subject#")>

		<cfsavecontent variable="local.data">
			<cfoutput>#local.strEmailInvitation.html#</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateMailingName" access="public" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.msg = "">

		<!--- this action uses the same code even though the button appears in both SWL and SWOD. --->
		<cftry>
			<cfset CreateObject("component","model.admin.seminarweb.seminarWebSWL").updateMailingName()>
			<cfset local.returnStruct.msg = "Mailing Names have been updated."> 			
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendLyrisInvite" access="public" output="false" returntype="struct">
		<cfargument name="ft" type="string" required="true">
		<cfargument name="programIDlist" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="subject" type="string" required="true">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="ListChoice" type="string" required="true">
		<cfargument name="template" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.msg = "">

		<cfif arguments.ft eq "SWL">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWL")>
		<cfelseif arguments.ft eq "SWOD">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWOD")>
		<cfelseif arguments.ft eq "SWB">
			<cfset local.objProgram = CreateObject("component","model.admin.seminarweb.seminarWebSWB")>
		</cfif>

		<cftry>
			<cfif arguments.template eq "">
				<cfthrow message="Missing marketing template.">
			</cfif>

			<cfif listFindNoCase("SWL,SWOD", arguments.ft)>
				<cfset local.objProgram.sendInvitationEmail(seminarIDlist=arguments.programIDlist, performedBy=session.cfcuser.memberdata.depomemberdataid,
					outgoingType="manualLyrisInvitation", orgcode=arguments.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText,
					listchoice=arguments.ListChoice, template=arguments.template)>
			<cfelseif arguments.ft eq "SWB">
				<cfset local.objProgram.sendInvitationEmail(bundleIDList=arguments.programIDlist, performedBy=session.cfcuser.memberdata.depomemberdataid,
					outgoingType="manualLyrisInvitation", orgcode=arguments.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText,
					listchoice=arguments.ListChoice, template=arguments.template)>
			</cfif>
			<cfset local.returnStruct.msg = "Invitation has been sent to Lyris.">
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="listSWODSubmissions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.qryAssociations = CreateObject("component","seminarWebSWOD").getSWODSubmittedAssociations()>
		<cfset local.submissionsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODSubmissionsList&mode=stream'>
		<cfset local.viewSubmisssionLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='submitSWODProgram') & "&mode=stream">
		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWOD_submissions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="swodProgramSearch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.qrySWODAssociations = CreateObject("component","seminarWebParticipants").getParticipantsByFormat(format='SWOD')>
		<cfset local.seminarsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODList&gridmode=swSearchGrid&mode=stream'>
		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
		<cfset local.createdDateFrom = ''>
		<cfset local.createdDateTo = ''>
		<cfset local.activatedDateFrom = ''>
		<cfset local.activatedDateTo = ''>
		<cfset local.origPublishDateFrom = ''>
		<cfset local.origPublishDateTo = ''>
		<cfset local.seminarsExportLink = buildCurrentLink(arguments.event,"exportSWODSeminars") & "&mode=stream">
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_swodProgramSearch.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSWODSeminars" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = StructNew();
			local.data = "The export file could not be generated. Contact SeminarWeb for assistance.";
			local.keyWord = arguments.event.getTrimValue('fKeyword','');
			local.programCode = arguments.event.getTrimValue('fProgramCode','');
			local.publisherType = arguments.event.getValue('fPubType','PO');
			local.hideInactive = arguments.event.getValue('fStatus',1);
			local.featuredOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.activatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.activatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.origPublishDateFrom = arguments.event.getValue('fOrigPublishDateFrom','');
			local.origPublishDateTo = arguments.event.getValue('fOrigPublishDateTo','');
			local.createdDateFrom = arguments.event.getTrimValue('fCreatedDateFrom','');
			local.createdDateTo = arguments.event.getTrimValue('fCreatedDateTo','');
			
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'));
			local.reportFileName = "OnDemandPrograms.csv";
			
			CreateObject("component","model.admin.seminarWeb.seminarwebSWOD").getPrograms(sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), 
				mode='exportswSearchGrid', keyword=local.keyWord, programCode=local.programCode, publisherType=local.publisherType, hideInactive=local.hideInactive,
				featuredOnly=local.featuredOnly, createdDateFrom=local.createdDateFrom, createdDateTo=local.createdDateTo, activatedDateFrom=local.activatedDateFrom, activatedDateTo=local.activatedDateTo, 
				origPublishDateFrom=local.origPublishDateFrom, origPublishDateTo=local.origPublishDateTo, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
			
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportSWODPrograms('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="bundlesSearch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.qrySWBAssociations = CreateObject("component","seminarWebParticipants").getParticipantsByFormat(format='SWB')>
		<cfset local.bundlesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBList&gridmode=swSearchGrid&mode=stream'>
		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
		<cfset local.copySWProgramPrompt = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='copySWProgramPrompt') & "&mode=direct">
		<cfset local.activatedDateFrom = ''>
		<cfset local.activatedDateTo = ''>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_bundlesSearch.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="certificateProgramsSearch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.qrySWCPAssociations = CreateObject("component","seminarWebParticipants").getParticipantsByFormat(format='SWCP')>
		<cfset local.listSWCPProgramsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWCPList&gridmode=swSearchGrid&mode=stream'>
		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_certificateProgramsSearch.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showNATLEOptInReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.seminarOptIns_List = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getlistNATLEOptIns&mode=stream">
		<cfset local.rptStart = DateFormat( dateadd("m",-3, createDate(year(Now()), month(Now()), 1)) ,"m/d/yyyy")>
		<cfset local.rptEnd = DateFormat(Now(),"m/d/yyyy")>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_NATLEOptInReport.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showProfitMarginReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.generateDownloadLink = buildCurrentLink(arguments.event,"generateProfitMarginReport")>

		<cfquery name="local.qryReportMonths" datasource="#application.dsn.platformStatsMC.dsn#">
			SELECT runID, activityReportMonth 
			FROM dbo.sw_MonthlyBillingRun
			ORDER BY activityReportMonth DESC
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_ProfitMarginsReport.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateProfitMarginReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strReturn.strFolder = application.objDocDownload.createHoldingFolder(prefix="SW")>

		<cfstoredproc procedure="sw_getMonthlyBillingDate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('runID',0)#">
			<cfprocparam type="out" cfsqltype="CF_SQL_DATE" variable="local.startMonthActivity">
		</cfstoredproc>

		<cfset local.strReturn.fileName = "SeminarWebProfitMargin#DateFormat(local.startMonthActivity,"YYYYMM")#.csv">

		<cfstoredproc procedure="up_generateProfitMarginReport" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('runID',0)#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('rptType','program')#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.strReturn.strFolder.folderPathUNC#\#local.strReturn.fileName#">
		</cfstoredproc>		

		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strReturn.strFolder.folderPath#/#local.strReturn.fileName#", displayName=local.strReturn.fileName, forceDownload=1, deleteSourceFile=1)>

		<cfreturn returnAppStruct("No data found.","echo")>
	</cffunction>

	<cffunction name="listSWCreditAuthorities" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.listCreditAuthoritiesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getCreditAuthorities&mode=stream">
		<cfset local.editCreditAuthorityLink = "#buildCurrentLink(arguments.event,"editCreditAuthority")#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWSetup_creditAuthorities.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCreditAuthority" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.creditAuthorityID = arguments.event.getValue('caid',0)>

		<cfset local.objSWCredit = CreateObject("model.seminarWeb.SWCredits")>

		<cfset local.qryAuthority = local.objSWCredit.getAuthority(authorityID=local.creditAuthorityID)>
		<cfset local.qryStates = CreateObject("component","seminarWebSWCredits").getStates()>
		<cfif isWddx(local.qryAuthority.wddxcreditTypes)>
			<cfwddx action="WDDX2CFML" input="#local.qryAuthority.wddxcreditTypes#" output="local.arrCreditTypes">
		<cfelse>
			<cfset local.arrCreditTypes = ArrayNew(1)>
		</cfif>

		<cfset local.qryPromptTypes = local.objSWCredit.getCreditPrompts()>

		<cfset local.qryLinkedSponsors = local.objSWCredit.getSponsorsLinkedToAuthority(authorityID=local.creditAuthorityID)>
		<cfset local.qrySponsors = local.objSWCredit.getSponsors()>

		<cfset local.saveCreditAuthorityDetailsLink = buildCurrentLink(arguments.event,"saveCreditAuthorityDetails") & "&creditAuthorityID=#local.creditAuthorityID#&mode=stream">
		<cfset local.saveCreditAuthoritySWLLink = buildCurrentLink(arguments.event,"saveCreditAuthoritySWL") & "&creditAuthorityID=#local.creditAuthorityID#&mode=stream">
		<cfset local.saveCreditAuthoritySWODLink = buildCurrentLink(arguments.event,"saveCreditAuthoritySWOD") & "&creditAuthorityID=#local.creditAuthorityID#&mode=stream">
		<cfset local.saveCreditAuthoritySponsorsLink = buildCurrentLink(arguments.event,"saveCreditAuthoritySponsors") & "&creditAuthorityID=#local.creditAuthorityID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_SWSetup_creditAuthorities.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCreditAuthorityDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cfset local.errMsg = "">
		<cfset local.objAdminSWCredit = CreateObject("component","seminarWebSWCredits")>
		<cfset local.creditAuthorityID = arguments.event.getValue('creditAuthorityID',0)>

		<cfset local.arrCredits = ArrayNew(1)>
		<cfset local.arrCreditsForWDDX = ArrayNew(1)>

		<cfloop from="1" to="#arguments.event.getValue('creditTypeCols',0)#" index="local.thisEl">
			<cfif len(arguments.event.getValue('credit_#local.thisEl#',''))>
				<cfset local.newEl = Arraylen(local.arrCreditsForWDDX)+1>
				<cfset local.arrCreditsForWDDX[local.newEl] = StructNew()>
				<cfset local.arrCreditsForWDDX[local.newEl]["displayname"] = arguments.event.getValue('credit_#local.thisEl#')>
				<cfset local.arrCreditsForWDDX[local.newEl]["fieldname"] = rereplaceNoCase(arguments.event.getValue('credit_#local.thisEl#'),"[^A-Z0-9]","","ALL")>
			</cfif>

			<cfset local.newEl = Arraylen(local.arrCredits)+1>
			<cfset local.arrCredits[local.newEl] = StructNew()>
			<cfset local.arrCredits[local.newEl]["oldfieldname"] = arguments.event.getValue('credit_#local.thisEl#_old')>
			<cfset local.arrCredits[local.newEl]["displayname"] = arguments.event.getValue('credit_#local.thisEl#')>
			<cfset local.arrCredits[local.newEl]["fieldname"] = rereplaceNoCase(arguments.event.getValue('credit_#local.thisEl#'),"[^A-Z0-9]","","ALL")>
		</cfloop>

		<cfwddx action="CFML2WDDX" input="#local.arrCreditsForWDDX#" output="local.wddxCreditTypes">

		<cfif local.creditAuthorityID eq 0 OR NOT local.objAdminSWCredit.isLinkedCreditRemoved(creditAuthorityID=local.creditAuthorityID, arrCredits=local.arrCredits)>
			<cfset local.strArgs = {
				creditAuthorityID = local.creditAuthorityID,
				authorityName = arguments.event.getTrimValue('authorityName',''),
				code = arguments.event.getTrimValue('code',''),
				jurisdiction=arguments.event.getTrimValue('jurisdiction',''),
				contact=arguments.event.getTrimValue('contact',''),
				address=arguments.event.getTrimValue('address',''),
				city=arguments.event.getTrimValue('city',''),
				state=arguments.event.getTrimValue('state',''),
				zip=arguments.event.getTrimValue('zip',''),
				phone=arguments.event.getTrimValue('phone',''),
				email=arguments.event.getTrimValue('email',''),
				website=arguments.event.getTrimValue('website',''),
				creditIDText=arguments.event.getTrimValue('creditIDText',''),
				wddxCreditTypes=local.wddxCreditTypes, 
				arrCredits=local.arrCredits
			}>
			<cfif local.creditAuthorityID gt 0>
				<cfset local.objAdminSWCredit.updateCreditAuthorityDetails(argumentCollection=local.strArgs)>
				<cfset local.process = 'update'>
			<cfelse>
				<cfset local.creditAuthorityID = local.objAdminSWCredit.insertCreditAuthority(argumentCollection=local.strArgs)>
				<cfset local.process = 'insert'>
			</cfif>
		<cfelse>
			<cfset local.success = false>
			<cfset local.errMsg = "Sorry, you cannot remove a credit type when it is assigned to a seminar.">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.success>
					<cfif local.process is 'insert'>
						<script type="text/javascript">
							sw_creditauthorityid = #local.creditAuthorityID#;
						</script>
					<cfelse>
						Updated Successfully.
					</cfif>
				<cfelse>
					<script type="text/javascript">
						saveErrMsg = '#local.errMsg#';
					</script>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCreditAuthoritySWL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strArgs = { 
			creditAuthorityID = arguments.event.getValue('creditAuthorityID',0),
			swlPromptTypeID = arguments.event.getTrimValue('swlPromptTypeID',0),
			swlPromptInterval = arguments.event.getValue('swlPromptInterval',0),			 
			swlMustAttend = arguments.event.getValue('swlMustAttend',0), 
			swlMustAttendMinutes = arguments.event.getValue('swlMustAttendMinutes',0), 
			preExamRequired = arguments.event.getValue('preExamRequired',0), 
			examRequired = arguments.event.getValue('examRequired',0), 
			evaluationRequired = arguments.event.getValue('evaluationRequired',0), 
			daysToCompleteExam = arguments.event.getValue('daysToCompleteExam',0), 
			daysToCompleteEvaluation = arguments.event.getValue('daysToCompleteEvaluation',0)
		}>

		<cfset CreateObject("component","seminarWebSWCredits").saveCreditAuthoritySWL(argumentCollection=local.strArgs)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				Updated Successfully.
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCreditAuthoritySWOD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strArgs = {
			creditAuthorityID = arguments.event.getValue('creditAuthorityID',0),
			swodPromptTypeID = arguments.event.getTrimValue('swodPromptTypeID',0),
			swodPromptInterval = arguments.event.getValue('swodPromptInterval',0),
			preExamRequired = arguments.event.getValue('preExamRequired',0),
			examRequired = arguments.event.getValue('examRequired',0),
			evaluationRequired = arguments.event.getValue('evaluationRequired',0),
			mediaRequiredPct = arguments.event.getValue('mediaRequiredPct',0),
			daysToComplete = arguments.event.getValue('daysToComplete',0),
			mustAttendMinutes = arguments.event.getValue('mustAttendMinutes',0)
		}>

		<cfset CreateObject("component","seminarWebSWCredits").saveCreditAuthoritySWOD(argumentCollection=local.strArgs)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				Updated Successfully.
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCreditAuthoritySponsors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.arrSponsorInfo = ArrayNew(1)>

		<cfloop list="#arguments.event.getValue('CSALinkID','')#" index="local.linkID">
			<cfset local.newEl = Arraylen(local.arrSponsorInfo)+1>
			<cfset local.arrSponsorInfo[local.newEl] = StructNew()>
			<cfset local.arrSponsorInfo[local.newEl].linkID = local.linkID>
			<cfset local.arrSponsorInfo[local.newEl].certificateMessage = arguments.event.getValue('certificateMessage#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].creditMessage = arguments.event.getValue('creditMessage#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].swlGrantedCreditCert = arguments.event.getValue('swlGrantedCreditCert#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].swlDeniedCreditCert = arguments.event.getValue('swlDeniedCreditCert#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].swodGrantedCreditCert = arguments.event.getValue('swodGrantedCreditCert#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].swodDeniedCreditCert = arguments.event.getValue('swodDeniedCreditCert#local.linkID#','')>
			<cfset local.arrSponsorInfo[local.newEl].programIDList = arguments.event.getValue('programID#local.linkID#','')>
		</cfloop>

		<cfset CreateObject("component","seminarWebSWCredits").saveCreditAuthorityLinkedSponsors(arrSponsorInfo=local.arrSponsorInfo)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				Updated Successfully.
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listSWCreditSponsors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.creditSponsorsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getCreditSponsors&mode=stream'>

		<cfset local.editCreditSponsorLink = "#buildCurrentLink(arguments.event,"editCreditSponsor")#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_creditSponsors.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCreditSponsor" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSWCredit = createObject("model.seminarWeb.SWCredits")>
		<cfset local.objAdminSWCredit = createObject("component","seminarWebSWCredits")>

		<cfset local.qryParticipants = createObject("component","model.seminarweb.SWParticipants").getAssociations()>
		<cfset local.qryStates = local.objAdminSWCredit.getStates()>
		<cfset local.qryAuthorities = local.objSWCredit.getAuthorities()>
		<cfset local.linkedParticipantsList = local.objAdminSWCredit.getLinkedSponsorParticipants()>
		<cfset local.qrySponsor = local.objSWCredit.getSponsor(sponsorID=arguments.event.getValue('csid',0))>
		<cfset local.sponsorID = val(local.qrySponsor.sponsorID)>
		<cfset local.qryLinkedAuthorities = local.objSWCredit.getAuthoritiesLinkedToSponsor(sponsorID=local.sponsorID)>
		
		<cfset local.saveCreditSponsorDetailsLink = buildCurrentLink(arguments.event,"saveCreditSponsorDetails") & "&sponsorID=#local.sponsorID#&mode=stream">
		<cfif local.sponsorID gt 0>
			<cfset local.saveCreditAuthoritySponsorsLink = buildCurrentLink(arguments.event,"saveCreditAuthoritySponsors") & "&sponsorID=#local.sponsorID#&mode=stream">
			<cfset local.sponsorsCreditDetailsLink = buildCurrentLink(arguments.event,"sponsorsCreditDetails") & "&sponsorID=#local.sponsorID#&mode=stream">
			<cfset local.getSWSeminarCreditFormUploadLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='getSWSeminarCreditFormUpload') & "&mode=direct">
			<cfset local.viewSWSeminarCreditFormLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewSWSeminarCreditForm') & "&mode=direct">
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_creditSponsor.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sponsorsCreditDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSWCredit = createObject("model.seminarWeb.SWCredits")>

		<cfset local.maxRows = 10>
		<cfset local.programType = arguments.event.getValue('ft','')>
		<cfset local.qryStatuses = local.objSWCredit.getCreditStatuses()>

		<cfswitch expression="#local.programType#">
			<cfcase value="SWL">
				<cfset local.title = "SeminarWeb Live Seminars">
				<cfset local.qryCreditsGrid = local.objSWCredit.getCreditsGridBySponsorSWL(sponsorID=arguments.event.getValue('sponsorID',0), start=arguments.event.getValue('start',0), maxrows=(local.maxRows+1))>
			</cfcase>
			<cfcase value="SWOD">
				<cfset local.title = "SeminarWeb OnDemand Seminars">
				<cfset local.qryCreditsGrid = local.objSWCredit.getCreditsGridBySponsorSWOD(sponsorID=arguments.event.getValue('sponsorID',0), start=arguments.event.getValue('start',0), maxrows=(local.maxRows+1))>
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfif local.qryCreditsGrid.recordCount EQ 0>
				<cfoutput>
					<div class="alert alert-info my-2">There are no #local.title# to which this sponsor has been associated.</div>
				</cfoutput>
			<cfelse>
				<cfinclude template="frm_creditSponsor_credit.cfm">
			</cfif>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCreditSponsorDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objAdminSWCredit = CreateObject("component","seminarWebSWCredits")>
		<cfset local.sponsorID = arguments.event.getValue('sponsorID',0)>

		<cfset local.strArgs = {
				sponsorID = local.sponsorID,
				sponsorName = arguments.event.getTrimValue('sponsorName',''),
				linkedOrgCode = arguments.event.getTrimValue('linkedOrgCode',''),
				contact=arguments.event.getTrimValue('contact',''),
				contactTitle=arguments.event.getTrimValue('contactTitle',''),
				address=arguments.event.getTrimValue('address',''),
				sponsorCity=arguments.event.getTrimValue('sponsorCity',''),
				sponsorState=arguments.event.getTrimValue('sponsorState',''),
				sponsorZIP=arguments.event.getTrimValue('sponsorZIP',''),
				sponsorPhone=arguments.event.getTrimValue('sponsorPhone',''),
				sponsorContactEmail=arguments.event.getTrimValue('sponsorContactEmail',''),
				sponsorWebsite=arguments.event.getTrimValue('sponsorWebsite',''),
				statementAppProvider=arguments.event.getTrimValue('statementAppProvider',''),
				statementAppProgram=arguments.event.getTrimValue('statementAppProgram',''),
				statementPendProgram=arguments.event.getTrimValue('statementPendProgram','')
			}>

		<cfif local.sponsorID gt 0>
			<cfset local.objAdminSWCredit.updateSponsorDetails(argumentCollection=local.strArgs)>
			<cfset local.process = 'update'>
		<cfelse>
			<cfset local.sponsorID = local.objAdminSWCredit.insertSponsorDetails(argumentCollection=local.strArgs)>
			<cfset local.process = 'insert'>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					reloadCreditSponsorsTable();
					<cfif local.process is 'insert'>
						editCreditSponsor(#local.sponsorID#);
					<cfelse>
						$("button##btnSaveSponsor").prop('disabled',false);
						$('##saveDetailsInfo').html('Saved Successfully').show().fadeOut(10000);
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listSWAuthors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.authorsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWAuthorsList&showAllAssociations=1&mode=stream";
			local.editAuthorLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editAuthor') & "&swtype=Authors&mode=direct";
			local.authorsExportLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='exportSWAuthors') & "&showAllAssociations=1&mode=stream";
			local.qryAuthorTypes = CreateObject("component","seminarWebAuthors").getAuthorTypes();
			local.qryAuthorAssociations = CreateObject("component","seminarWebAuthors").getAuthorAssociations();
			local.showAllAssociations = 1;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SWAuthors.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageProgramImages" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.manageProgramImagesLink = buildCurrentLink(arguments.event,"manageProgramImages")>
		<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.featureImageConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.event.getValue('mc_siteinfo.siteid'), referenceType="platformSWProgram")>

		<cfset local.arrConfigs = [ { "ftdExt":"#this.siteResourceID#_swl", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"controllingReferenceType":"platformSWProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"referenceType":"platformSWLProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb Live", 
										"onDeleteImageHandler":"", "onSaveImageHandler":"reloadProgramImages", "header":'<h6>Default SeminarWeb Live Image</h6>', 
										"ftdImgClassList":"pl-3 mb-5" 
									},
									{ "ftdExt":"#this.siteResourceID#_swod", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"controllingReferenceType":"platformSWProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"referenceType":"platformSWODProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb OnDemand", 
										"onDeleteImageHandler":"", "onSaveImageHandler":"reloadProgramImages", "header":'<h6>Default SeminarWeb OnDemand Image</h6>', 
										"ftdImgClassList":"pl-3 mb-5" 
									},
									{ "ftdExt":"#this.siteResourceID#_swtl", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"controllingReferenceType":"platformSWProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"referenceType":"platformSWTLProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb Titles", 
										"onDeleteImageHandler":"", "onSaveImageHandler":"reloadProgramImages", "header":'<h6>Default SeminarWeb Titles Image</h6>', 
										"ftdImgClassList":"pl-3 mb-5" 
									},
									{ "ftdExt":"#this.siteResourceID#_swb", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"controllingReferenceType":"platformSWProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"referenceType":"platformSWBProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"SeminarWeb Bundles", 
										"onDeleteImageHandler":"", "onSaveImageHandler":"reloadProgramImages", "header":'<h6>Default SeminarWeb Bundles Image</h6>', 
										"ftdImgClassList":"pl-3 mb-5" 
									},
									{ "ftdExt":"#this.siteResourceID#_mcev", "controllingReferenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"controllingReferenceType":"platformSWProgram", "referenceID":arguments.event.getValue('mc_siteinfo.siteid'), 
										"referenceType":"platformMCEVProgram", "resourceType":"SeminarWebAdmin", "resourceTypeTitle":"MemberCentral Events", 
										"onDeleteImageHandler":"", "onSaveImageHandler":"reloadProgramImages", "header":'<h6>Default MemberCentral Events Image</h6>', 
										"ftdImgClassList":"pl-3 mb-5" 
									} ]>
		<cfset local.strFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteinfo.orgCode'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), arrConfigs=local.arrConfigs)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_manageProgramImages.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listSWDiscretionaryFees" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.qryAssociations = CreateObject("component","seminarWebParticipants").getAllParticipantsForSWBilling();
			local.swBillingLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWBillingLogs&logType=adhoc&showAssoc=1&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_discretionaryFees.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listRegistrants" access="public" output="false" returntype="struct">
 		<cfargument name="Event" type="any">

 		<cfset var local = structNew()>
		<cfscript>
 			local.rDateFrom = "#month(now())#/1/#year(now())#";
			local.rDateTo = "#dateformat(now(),'m/d/yyyy')#";
			local.qryAssociations = CreateObject("component","seminarWebParticipants").getAllParticipantsForSWBilling();
		</cfscript>

 		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
		<cfset local.registrantsListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getRegistrantsList&gridmode=regsearchgrid&mode=stream'>
 		<cfset local.registrantsListExportLink = buildCurrentLink(arguments.event,"exportRegistrantsList") & "&gridmode=exportregsearch&mode=stream">

 		<cfsavecontent variable="local.data">
 			<cfinclude template="dsp_listRegistrants.cfm">
 		</cfsavecontent>

 		<cfreturn returnAppStruct(local.data,"echo")>
 	</cffunction>

	<cffunction name="exportRegistrantsList" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			
			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getTrimValue('mc_siteinfo.sitecode',''));
			local.pKeyword = arguments.event.getTrimValue('frpKeyword','');
			local.pProgramCode = arguments.event.getTrimValue('frpProgramCode','');				
			local.pformat = arguments.event.getValue('frformat',0);
			local.pHideInactive = arguments.event.getTrimValue('frpStatus',1);
			local.rdateFrom = arguments.event.getValue('frrDateFrom','');
			local.rdateTo = arguments.event.getValue('frrDateTo','');
			local.pPublisher = arguments.event.getValue('frpPublisher',0);	
			local.rHideDeleted = arguments.event.getValue('frrHideDeleted',1);
			local.reportFileName = "RegistrantsList.csv";

			CreateObject("component","seminarwebSWCommon").getRegistrants(mode="exportregsearch", 
				rdateFrom=local.rdateFrom, rdateTo=local.rdateTo, rHideDeleted=local.rHideDeleted, 
				pKeyword=local.pkeyword, pProgramCode=local.pProgramCode, pformat=local.pformat, pHideInactive=local.pHideInactive,
				pPublisher=local.pPublisher, folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName);
		
			application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#");
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					doExportRegistrantsList('#local.stDownloadURL#');
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateMonthlyBilling" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFile = runMonthlyBilling(runID=arguments.event.getValue('runID',0), participantID=arguments.event.getValue('participantID',0), format=arguments.event.getValue('format','pdf'))>

		<cfif len(local.strFile.sourceFilePath)>
			<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.strFile.sourceFilePath, displayName=local.strFile.fileName, forceDownload=1, deleteSourceFile=1)>
		</cfif>

		<cfreturn returnAppStruct("No data found.","echo")>
	</cffunction>

	<cffunction name="runMonthlyBilling" access="public" output="false" returntype="struct">
		<cfargument name="runID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="format" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { strFolder={}, fileName="", sourceFilePath="", isPayable=0, payableAmount=0 }>

		<cfsetting requesttimeout="900">

		<cfstoredproc procedure="sw_getMonthlyBillingDate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.runID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_DATE" variable="local.startMonthActivity">
		</cfstoredproc>

		<cfif not len(local.startMonthActivity)>
			<cfreturn local.strReturn>
		</cfif>

		<cfset local.fileFormat = arguments.format>
		<cfif local.fileFormat eq "csvreg">
			<cfset local.fileFormat = "csv">
		</cfif>

		<cfset local.strReturn.strFolder = application.objDocDownload.createHoldingFolder(prefix="SW")>
		<cfset local.strReturn.fileName = "SeminarWebStatement#DateFormat(local.startMonthActivity,"YYYYMM")#.#local.fileFormat#">
		<cfset local.strReturn.sourceFilePath = "#local.strReturn.strFolder.folderPath#/#local.strReturn.fileName#">

		<cfif arguments.format eq "pdf">
			<cfsavecontent variable="local.pdfHeader">
				<cfoutput>
				<table align="center" style="width:201px;">
				<tr>
					<td align="center" height="32"><img src="file:///#application.paths.RAIDAssetRoot.path#common/images/seminarwebInvoice.png" width="201" height="32"></td>
				</tr>
				</table>
				</cfoutput>
			</cfsavecontent>

			<cfquery name="local.qryParticipants" datasource="#application.dsn.platformStatsMC.dsn#">
				SELECT distinct rd.participantID, rd.siteCode, oi.organizationName as orgName
				FROM dbo.sw_MonthlyBillingRunDetail rd
				INNER JOIN membercentral.dbo.sites as s on s.siteCode = rd.siteCode
				INNER JOIN membercentral.dbo.organizations as o on o.orgID = s.orgID
				INNER JOIN membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
				WHERE rd.runID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.runID#">
				AND rd.participantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.participantID#">
			</cfquery>

			<cfset local.strBilling = runMonthlyBillingByParticipant(runID=arguments.runID, startMonthActivity=local.startMonthActivity, 
				participantID=local.qryParticipants.participantID, orgName=local.qryParticipants.orgName, pdfheader=local.pdfheader, 
				filename=local.strReturn.sourceFilePath)>

			<cfset local.strReturn.isPayable = local.strBilling.isPayable>
			<cfset local.strReturn.payableAmount = local.strBilling.payableAmount>
		
		<cfelseif arguments.format eq "csv">
			<cfquery name="local.qryAuditFile" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @selectsql varchar(max);
				DECLARE @auditCSVFileName varchar(400) = '#local.strReturn.strFolder.folderPathUNC#\#local.strReturn.fileName#';

				SET @selectsql = 'SELECT program, programCode, ProgramName, amount, detail, syndicateSales, ROW_NUMBER() OVER(order by program, detailSection) as mcCSVorder 
					*FROM* platformStatsMC.dbo.sw_MonthlyBillingRunDetail
					WHERE runID = #arguments.runID#
					AND participantID = #arguments.participantID#';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@auditCSVFileName, @returnColumns=0;
			</cfquery>

		<cfelseif arguments.format eq "csvreg">
			<cfquery name="local.qryParticipant" datasource="#application.dsn.tlasites_seminarWeb.dsn#">
				select s.orgID
				from dbo.tblParticipants as p
				inner join membercentral.dbo.sites as s on s.siteCode = p.orgCode
				where p.participantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.participantID#">
			</cfquery>

			<cfquery name="local.qryAuditFile" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @selectsql varchar(max);
				DECLARE @auditCSVFileName varchar(400) = '#local.strReturn.strFolder.folderPathUNC#\#local.strReturn.fileName#';

				SET @selectsql = 'SELECT rd.program, rd.programCode, rd.ProgramName, 
						m.LastName + '', '' + m.FirstName + '' ('' + case when m.orgID = #local.qryParticipant.orgID# then m.memberNumber else ''Sold at Other Site'' end + '')'' as Registrant, 
						coalesce(e.dateEnrolled,bo.dateOfOrder) as PurchaseDate, rd.amount, rd.detail, rd.syndicateSales, 
						ROW_NUMBER() OVER(order by rd.program, m.LastName, m.FirstName, rd.detailSection) as mcCSVorder 
					*FROM* platformStatsMC.dbo.sw_MonthlyBillingRunDetailByReg as rd
					INNER JOIN dbo.ams_members as m2 on m2.memberID = rd.memberID
					INNER JOIN dbo.ams_members as m on m.memberID = m2.activeMemberID
					LEFT OUTER JOIN seminarweb.dbo.tblEnrollments as e on e.enrollmentID = rd.enrollmentID
					LEFT OUTER JOIN seminarWeb.dbo.tblBundleOrders as bo on bo.orderID = rd.orderID
					WHERE rd.runID = #arguments.runID#
					AND rd.participantID = #arguments.participantID#';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@auditCSVFileName, @returnColumns=0;
			</cfquery>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="runMonthlyBillingByParticipant" access="private" output="false" returntype="struct">
		<cfargument name="runID" type="numeric" required="true">
		<cfargument name="startMonthActivity" type="date" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="orgName" type="string" required="true">
		<cfargument name="pdfheader" type="string" required="true">
		<cfargument name="filename" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.df = createObject("java", "java.text.DecimalFormat")>
		<cfset local.strReturn = { isPayable=0, payableAmount=0 }>

		<cfstoredproc procedure="sw_getMonthlyBillingDataByParticipant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.runID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocresult name="local.qryOrgSummary" resultset="1">
			<cfprocresult name="local.qryOrgProgramSummary" resultset="2">
			<cfprocresult name="local.qryOrgProgramDetails" resultset="3">
			<cfprocresult name="local.qryOrgRegistrantSummary" resultset="4">
		</cfstoredproc>

		<cfset local.qryOrgProgramSummary_SWL = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 1; })>
		<cfset local.qryOrgProgramSummary_SWOD = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 2; })>
		<cfset local.qryOrgProgramSummary_SWTL = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 3; })>
		<cfset local.qryOrgProgramSummary_SWM = local.qryOrgProgramSummary.filter(function(row) { return arguments.row.programSummarySection is 4; })>
		<cfset local.qryOrgProgramDetails_M = local.qryOrgProgramDetails.filter(function(row) { return arguments.row.sectionCode eq 'MONSUP'; })>
		<cfset local.qryOrgProgramDetails_P = local.qryOrgProgramDetails.filter(function(row) { return arguments.row.sectionCode neq 'MONSUP'; })>
		
		<cfsavecontent variable="local.pdfSummary">
			<cfoutput>
			<div style="margin-top:10px;text-align:center;font-size:1.1em;font-weight:bold;font-family:verdana;">#arguments.orgName#</div>
			<div style="margin-top:4px;text-align:center;font-size:1.1em;font-weight:bold;font-family:verdana;">Program Activity Statement</div>
			<div style="margin-top:4px;text-align:center;font-size:1em;font-weight:bold;font-family:verdana;">#DateFormat(arguments.startMonthActivity,"MMMM YYYY")#</div>
			<div style="margin-top:40px;">
				<div style="width:100%;font-size:1em;font-weight:bold;font-family:verdana;margin-bottom:10px;border:1px solid ##000;letter-spacing:2pt;padding:2px;">Overall Summary</div>
				<table width="100%" cellspacing="0" cellpadding="4">
				<cfloop query="local.qryOrgSummary">
					<tr valign="top">
						<cfif NOT local.qryOrgSummary.isTotal>
							<td style="font-size:.8em;font-family:verdana;">
								#local.qryOrgSummary.summaryDescription#
							</td>
							<td align="right" width="140" style="font-size:.8em;font-family:verdana;">
								#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgSummary.totalamount))#
							</td>
						<cfelse>
							<td align="left" style="border-top:1px solid ##ccc;font-size:.8em;font-family:verdana;font-weight:bold;">
								Net <cfif local.qryOrgSummary.totalamount lt 0>Cost (Payable to SeminarWeb)<cfelse>Revenue (Payable to You)</cfif>
							</td>
							<td align="right" style="border-top:1px solid ##ccc;font-size:.8em;font-family:verdana;font-weight:bold;">
								#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgSummary.totalamount))#
							</td>
							<cfif local.qryOrgSummary.totalamount gt 0>
								<cfset local.strReturn.isPayable = 1>
								<cfset local.strReturn.payableAmount = local.qryOrgSummary.totalamount>
							</cfif>
						</cfif>
					</tr>
				</cfloop>
				</table>
			</div>
			<div style="margin-top:100px;margin-bottom:40px;font-size:.9em;font-family:verdana;page-break-after:always;">
				Questions about your statement or billing should be <NAME_EMAIL>.<br/>
				For a detailed summary of all transactions and specific fees, you may access an online version of this report and detail in your Control Panel under SeminarWeb Activity Statements.
			</div>

			<cfif local.qryOrgProgramSummary_SWL.recordcount OR local.qryOrgProgramSummary_SWOD.recordcount OR local.qryOrgProgramSummary_SWTL.recordcount OR local.qryOrgProgramSummary_SWM.recordcount>
				<div style="width:100%;font-size:1em;font-weight:bold;font-family:verdana;border:1px solid ##000;letter-spacing:2pt;padding:2px;">Summary by Program</div>

				<cfif local.qryOrgProgramSummary_SWL.recordcount>
					<div style="margin-top:10px;">
						<table width="100%" cellspacing="0" cellpadding="3">
							<tr valign="top">
								<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">Webinars (including Syndicated)</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Sales</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Adj</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Fees</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Net</th>
							</tr>
							<cfloop query="local.qryOrgProgramSummary_SWL">
								<tr valign="top">
									<td style="font-size:8pt;font-family:verdana;">
										#local.qryOrgProgramSummary_SWL.program#: #local.qryOrgProgramSummary_SWL.programName#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWL.sales))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWL.adjustments))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWL.fees))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWL.Net))#
									</td>
								</tr>
							</cfloop>
						</table>
					</div>
				</cfif>

				<cfif local.qryOrgProgramSummary_SWOD.recordcount>
					<div style="margin-top:10px;">
						<table width="100%" cellspacing="0" cellpadding="3">
							<tr valign="top">
								<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">On-Demand (including Syndicated)</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Sales</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Adj</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Fees</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Net</th>
							</tr>
							<cfloop query="local.qryOrgProgramSummary_SWOD">
								<tr valign="top">
									<td style="font-size:8pt;font-family:verdana;">
										#local.qryOrgProgramSummary_SWOD.program#: #local.qryOrgProgramSummary_SWOD.programName#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWOD.sales))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWOD.adjustments))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWOD.fees))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWOD.Net))#
									</td>
								</tr>
							</cfloop>
						</table>
					</div>
				</cfif>

				<cfif local.qryOrgProgramSummary_SWTL.recordcount>
					<div style="margin-top:10px;">
						<table width="100%" cellspacing="0" cellpadding="3">
							<tr valign="top">
								<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">Title Library (including Syndicated)</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Sales</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Adj</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Fees</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Net</th>
							</tr>
							<cfloop query="local.qryOrgProgramSummary_SWTL">
								<tr valign="top">
									<td style="font-size:8pt;font-family:verdana;">
										#local.qryOrgProgramSummary_SWTL.program#: #local.qryOrgProgramSummary_SWTL.programName#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWTL.sales))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWTL.adjustments))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWTL.fees))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWTL.Net))#
									</td>
								</tr>
							</cfloop>
						</table>
					</div>
				</cfif>

				<cfif local.qryOrgProgramSummary_SWM.recordcount>
					<div style="margin-top:10px;">
						<table width="100%" cellspacing="0" cellpadding="3">
							<tr valign="top">
								<th align="left" colspan="2" style="font-size:8pt;font-family:verdana;font-weight:bold;">Other SeminarWeb Fees and Adjustments</th>
							</tr>
							<cfloop query="local.qryOrgProgramSummary_SWM">
								<tr valign="top">
									<td style="font-size:8pt;font-family:verdana;">
										#local.qryOrgProgramSummary_SWM.programName#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramSummary_SWM.Net))#
									</td>
								</tr>
							</cfloop>
						</table>
					</div>
				</cfif>
			</cfif>

			<div style="margin-top:50px;width:100%;font-size:1em;font-weight:bold;font-family:verdana;border:1px solid ##000;letter-spacing:2pt;padding:2px;">Detail by Program</div>
			
			<cfoutput query="local.qryOrgProgramDetails_P" group="program">
				<div style="margin-top:10px;">
					<table width="100%" cellspacing="0" cellpadding="3">
						<tr valign="top">
							<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">#local.qryOrgProgramDetails_P.program#: #local.qryOrgProgramDetails_P.programName#</th>
							<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Amount</th>
						</tr>
						<cfoutput>
							<tr valign="top">
								<td style="font-size:8pt;font-family:verdana;">#local.qryOrgProgramDetails_P.detail#</td>
								<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
									#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramDetails_P.amount))#
								</td>
							</tr>
						</cfoutput>
					</table>
				</div>
			</cfoutput>
			<cfif local.qryOrgProgramDetails_M.recordCount>
				<div style="margin-top:10px;">
					<table width="100%" cellspacing="0" cellpadding="3">
						<tr valign="top">
							<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">#local.qryOrgProgramDetails_M.programName#</th>
							<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Amount</th>
						</tr>
						<tr valign="top">
							<td style="font-size:8pt;font-family:verdana;">#local.qryOrgProgramDetails_M.detail#</td>
							<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
								#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgProgramDetails_M.amount))#
							</td>
						</tr>
					</table>
				</div>
			</cfif>

			<cfif local.qryOrgRegistrantSummary.recordcount>
				<div style="page-break-after:always;"><br/></div>
				<div style="margin-top:50px;width:100%;font-size:1em;font-weight:bold;font-family:verdana;border:1px solid ##000;letter-spacing:2pt;padding:2px;">Registrant Summary by Program</div>

				<cfoutput query="local.qryOrgRegistrantSummary" group="program">
					<div style="margin-top:10px;">
						<table width="100%" cellspacing="0" cellpadding="3">
							<tr valign="top">
								<th align="left" style="font-size:8pt;font-family:verdana;font-weight:bold;">#local.qryOrgRegistrantSummary.program#: #local.qryOrgRegistrantSummary.programName#</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Sales</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Adj</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Fees</th>
								<th align="right" style="font-size:8pt;font-family:verdana;font-weight:bold;">Net</th>
							</tr>
							<cfoutput>
								<tr valign="top">
									<td style="font-size:8pt;font-family:verdana;">
										#local.qryOrgRegistrantSummary.registrantName#
										&nbsp; <i>#dateformat(local.qryOrgRegistrantSummary.enrollDate,"m/d/yy")#</i>
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgRegistrantSummary.sales))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgRegistrantSummary.adjustments))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgRegistrantSummary.fees))#
									</td>
									<td align="right" width="75" style="font-size:8pt;font-family:verdana;">
										#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",local.qryOrgRegistrantSummary.Net))#
									</td>
								</tr>
							</cfoutput>
						</table>
					</div>
				</cfoutput>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfdocument filename="#arguments.filename#" pagetype="letter" margintop="0.1" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfdocumentsection margintop=".1" marginbottom=".5" marginright=".5" marginleft=".5">
				<cfdocumentitem type="header" evalatprint="true"><cfoutput>#arguments.pdfHeader#</cfoutput></cfdocumentitem>
				<cfoutput>
					#local.pdfSummary#
				</cfoutput>
			</cfdocumentsection>
		</cfdocument>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>