<cfcomponent output="false">

	<cffunction name="getOrgIDFromOrgCode" access="public" output="false" returntype="numeric">
		<cfargument name="orgCode" type="string" required="yes">
	
		<cfset var qryOrg = "">

		<cfquery name="qryOrg" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getOrgIDFromOrgCode(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orgCode#">) as orgID
		</cfquery>

		<cfreturn qryOrg.orgID>
	</cffunction>

	<cffunction name="getOrgDefaultSiteID" access="public" output="false" returntype="numeric">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qrySite = "">

		<cfquery name="qrySite" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getDefaultSiteIDFromOrgID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">) as siteID
		</cfquery>

		<cfreturn qrySite.siteID>
	</cffunction>

	<cffunction name="getOrgAddressTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="includeTags" type="boolean" required="no" default="0">
	
		<cfset var qryOrgAddressTypes = "">

		<cfstoredproc procedure="ams_getOrgAddressTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeTags#">
			<cfprocresult name="qryOrgAddressTypes">
		</cfstoredproc>
	
		<cfreturn qryOrgAddressTypes>
	</cffunction>

	<cffunction name="getOrgAddressTagTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgAddressTagTypes = "">

		<cfstoredproc procedure="ams_getOrgAddressTagTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgAddressTagTypes">
		</cfstoredproc>
	
		<cfreturn qryOrgAddressTagTypes>
	</cffunction>

	<cffunction name="getOrgEmailTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="includeTags" type="boolean" required="no" default="0">
		
		<cfset var qryOrgEmailTypes = "">
		
		<cfstoredproc procedure="ams_getOrgEmailTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeTags#">
			<cfprocresult name="qryOrgEmailTypes">
		</cfstoredproc>

		<cfreturn qryOrgEmailTypes>
	</cffunction>

	<cffunction name="getOrgEmailTagTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgEmailTagTypes = "">

		<cfstoredproc procedure="ams_getOrgEmailTagTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgEmailTagTypes">
		</cfstoredproc>
	
		<cfreturn qryOrgEmailTagTypes>
	</cffunction>

	<cffunction name="getOrgMemberDataColumns" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgMemberDataColumns = "">

		<cfstoredproc procedure="ams_getOrgMemberDataColumns" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgMemberDataColumns">
		</cfstoredproc>
	
		<cfreturn qryOrgMemberDataColumns>
	</cffunction>

	<cffunction name="getOrgPhoneTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgPhoneTypes = "">

		<cfstoredproc procedure="ams_getOrgPhoneTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgPhoneTypes">
		</cfstoredproc>
	
		<cfreturn qryOrgPhoneTypes>
	</cffunction>

	<cffunction name="getOrgPrefixTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgPrefixTypes = "">

		<cfstoredproc procedure="ams_getOrgPrefixTypes" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,2,0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgPrefixTypes">
		</cfstoredproc>

		<cfreturn qryOrgPrefixTypes>
	</cffunction>

	<cffunction name="getOrgProfessionalLicenseTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgProfessionalLicenseTypes = "">

		<cfstoredproc procedure="ams_getOrgProfessionalLicenseTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgProfessionalLicenseTypes">
		</cfstoredproc>

		<cfreturn qryOrgProfessionalLicenseTypes>
	</cffunction>

	<cffunction name="getOrgProfessionalLicenseStatuses" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgProfessionalStatuses = "">

		<cfstoredproc procedure="ams_getOrgProfessionalLicenseStatuses" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgProfessionalStatuses">
		</cfstoredproc>

		<cfreturn qryOrgProfessionalStatuses>
	</cffunction>

	<cffunction name="getOrgWebsiteTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgWebsiteTypes = "">

		<cfstoredproc procedure="ams_getOrgWebsiteTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgWebsiteTypes">
		</cfstoredproc>

		<cfreturn qryOrgWebsiteTypes>
	</cffunction>

	<cffunction name="getOrgRecordTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgRecordTypes = "">

		<cfstoredproc procedure="ams_getOrgRecordTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgRecordTypes">
		</cfstoredproc>

		<cfreturn qryOrgRecordTypes>
	</cffunction>

	<cffunction name="getOrgRecordRelationshipTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgRecordRelationshipTypes = "">

		<cfstoredproc procedure="ams_getOrgRecordRelationshipTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgRecordRelationshipTypes">
		</cfstoredproc>

		<cfreturn qryOrgRecordRelationshipTypes>
	</cffunction>

	<cffunction name="getOrgRecordTypeRelationshipTypes" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgRecordTypeRelationshipTypes = "">

		<cfstoredproc procedure="ams_getOrgRecordTypeRelationshipTypes" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgRecordTypeRelationshipTypes">
		</cfstoredproc>

		<cfreturn qryOrgRecordTypeRelationshipTypes>
	</cffunction>

	<cffunction name="getOrgMemberViewFields" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryMemberFields = "">

		<cfstoredproc procedure="ams_getOrgMemberViewFields" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryMemberFields">
		</cfstoredproc>		

		<cfreturn qryMemberFields>
	</cffunction>

	<cffunction name="getOrgIdentities" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgIdentities = "">

		<cfstoredproc procedure="ams_getOrgIdentities" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocresult name="qryOrgIdentities">
		</cfstoredproc>

		<cfreturn qryOrgIdentities>
	</cffunction>

	<cffunction name="getOrgIdentity" access="public" output="false" returntype="query">
		<cfargument name="orgIdentityID" type="numeric" required="yes">
	
		<cfset var qryOrgIdentity = "">

		<cfstoredproc procedure="ams_getOrgIdentity" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgIdentityID#">
			<cfprocresult name="qryOrgIdentity">
		</cfstoredproc>

		<cfreturn qryOrgIdentity>
	</cffunction>

	<cffunction name="getOrgProfLicenseLabels" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var qryOrgProfLicenseLabels = "">

		<cfquery name="qryOrgProfLicenseLabels" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT profLicenseStatusLabel, profLicenseNumberLabel, profLicenseDateLabel
			FROM dbo.organizations
			WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryOrgProfLicenseLabels>
	</cffunction>

	<cffunction name="getOrgConsentLists" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var qryConsentLists = "">

		<cfquery name="qryConsentLists" datasource="#application.dsn.platformMail.dsn#">
			set nocount on;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT l.consentListID, l.consentListName, m.modeName
			FROM dbo.email_consentLists as l
			INNER JOIN dbo.email_consentListModes as m 
				on m.consentListModeID = l.consentListModeID
				and m.modeName <> 'GlobalOptOut'
			INNER JOIN dbo.email_consentListTypes as clt on clt.consentListTypeID = l.consentListTypeID
			WHERE clt.orgID = @orgID
			AND l.[status] = 'A'
			ORDER BY l.consentListName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryConsentLists>
	</cffunction>

</cfcomponent>