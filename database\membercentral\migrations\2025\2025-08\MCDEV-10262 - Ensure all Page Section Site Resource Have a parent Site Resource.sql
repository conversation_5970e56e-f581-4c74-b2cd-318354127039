USE membercentral;
GO

-- correct resourceTypeID for OLD MCAMSMemberDocuments cms_siteResources entries
DECLARE @sectionResourceTypeID int;

SELECT @sectionResourceTypeID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = 'SystemCreatedSection';

;WITH tmpSREntries AS (
	select sr.siteID, sr.resourceTypeID, srt.resourceType, sr.siteResourceID, sr.parentSiteResourceID, ps.sectionName
	from dbo.cms_siteResources sr
	inner join dbo.cms_pageSections as ps on ps.siteResourceID = sr.siteResourceID
	inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID
		and srt.resourceType IN ('ApplicationCreatedSection')
	where sr.siteResourceStatusID in (1,2)
	and ps.sectionName IN ('MCAMSMemberDocuments')
)
UPDATE sr
SET sr.resourceTypeID = @sectionResourceTypeID
FROM dbo.cms_siteResources AS sr
INNER JOIN tmpSREntries AS tmp ON tmp.siteResourceID = sr.siteResourceID
	AND tmp.siteID = sr.siteID;
GO

-- update parentSiteResourceID for SystemCreatedSection, UserCreatedSection & ApplicationCreatedSection
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSectionSiteResources') IS NOT NULL 
		DROP TABLE #tmpSectionSiteResources;

	CREATE TABLE #tmpSectionSiteResources (rowID INT IDENTITY(1,1), resourceTypeID int, siteID int, siteResourceID int, newParentSiteResourceID int, sectionName varchar(50), thePathExpanded varchar(max));

	-- SystemCreatedSection & UserCreatedSection
	INSERT INTO #tmpSectionSiteResources (resourceTypeID, siteID, siteResourceID, sectionName, thePathExpanded)
	select sr.resourceTypeID, sr.siteID, sr.siteResourceID, ps.sectionName, ps.thePathExpanded
	from dbo.cms_siteResources sr
	inner join dbo.cms_pageSections as ps on ps.siteResourceID = sr.siteResourceID
	where sr.siteResourceStatusID in (1,2)
	and sr.parentSiteResourceID is null
	and sr.resourceTypeID in (2,3);

	UPDATE tmp
	SET tmp.newParentSiteResourceID = s.siteResourceID
	FROM #tmpSectionSiteResources AS tmp
	INNER JOIN dbo.sites AS s on s.siteID = tmp.siteID
	WHERE tmp.resourceTypeID in (2,3);

	-- ApplicationCreatedSection (MemberDocument) : parentSiteResourceID was null for entries until siteID 41
	INSERT INTO #tmpSectionSiteResources (resourceTypeID, siteID, siteResourceID, sectionName, thePathExpanded, newParentSiteResourceID)
	select sr.resourceTypeID, sr.siteID, sr.siteResourceID, ps.sectionName, ps.thePathExpanded, ai.siteResourceID
	from dbo.cms_siteResources sr
	inner join dbo.cms_pageSections as ps on ps.siteResourceID = sr.siteResourceID
	inner join dbo.cms_applicationInstances as ai on ai.siteID = sr.siteID
	inner join dbo.cms_siteResources as sr_ai on sr_ai.siteResourceID = ai.siteResourceID
		and sr_ai.siteResourceStatusID = 1
	inner join dbo.cms_applicationTypes at on at.applicationTypeID = ai.applicationTypeID
		and at.applicationTypeName = 'Admin'
	where sr.siteResourceStatusID in (1,2)
	and sr.parentSiteResourceID is null
	and sr.resourceTypeID = 4
	and ps.sectionName IN ('MemberDocument');

	-- ApplicationCreatedSection (StoreDocs appInstanceID) : parentSiteResourceID was null for entries until siteID 41
	INSERT INTO #tmpSectionSiteResources (resourceTypeID, siteID, siteResourceID, sectionName, thePathExpanded, newParentSiteResourceID)
	select sr.resourceTypeID, sr.siteID, sr.siteResourceID, ps.sectionName, ps.thePathExpanded, ai.siteResourceID
	from dbo.cms_siteResources sr
	inner join dbo.cms_pageSections as ps on ps.siteResourceID = sr.siteResourceID
	inner join dbo.cms_applicationInstances as ai on ai.siteID = sr.siteID
		and ai.applicationInstanceID = CAST(REPLACE(ps.sectionName, 'StoreDocs ', '') AS INT)
	inner join dbo.cms_applicationTypes at on at.applicationTypeID = ai.applicationTypeID
		and at.applicationTypeName = 'Store'
	where sr.siteResourceStatusID in (1,2)
	and sr.parentSiteResourceID is null
	and sr.resourceTypeID = 4
	and left(ps.sectionName,10) = 'StoreDocs ';

	--SELECT * FROM #tmpSectionSiteResources ORDER BY resourceTypeID, siteID;

	UPDATE sr
	SET sr.parentSiteResourceID = tmp.newParentSiteResourceID
	FROM dbo.cms_siteResources AS sr
	INNER JOIN #tmpSectionSiteResources AS tmp ON tmp.siteResourceID = sr.siteResourceID
		AND tmp.siteID = sr.siteID
	WHERE sr.parentSiteResourceID IS NULL;

	IF OBJECT_ID('tempdb..#tmpSectionSiteResources') IS NOT NULL 
		DROP TABLE #tmpSectionSiteResources;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

ALTER PROC dbo.cms_createPageSection
@siteID int,
@sectionResourceTypeID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(50),
@sectionBreadcrumb varchar(200),
@inheritPlacements bit,
@sectionID int OUTPUT

AS


SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @sectionResourceID int, @rc int, @parentSiteResourceID int = null;

	SET @sectionID = null;

	-- get parentSiteResourceID of parentSection
	IF @parentSectionID IS NOT NULL
		select @parentSiteResourceID=sr.parentSiteResourceID 
		from dbo.cms_pageSections ps 
		inner join cms_siteResources sr 
			on ps.siteResourceID = sr.siteResourceID 
			and ps.siteID=@siteID
			and sr.siteID=@siteID
			and ps.sectionID=@parentSectionID;
	
	IF @parentSectionID IS NULL AND @sectionCode = 'Root'
		SELECT @parentSiteResourceID = siteResourceID
		FROM dbo.sites
		WHERE siteID = @siteID;

	-- sectionCode must be A-Z 0-9 only and be unique in site
	select @sectionCode = dbo.fn_regExReplace(isnull(@sectionCode,''),'[^A-Z0-9\-_]+','');
	IF EXISTS (select sectionID from dbo.cms_pageSections ps inner join cms_siteResources sr on ps.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ps.siteID = @siteID and ps.sectionCode = @sectionCode and ps.sectioncode <> '')
		RAISERROR('SectionCode must be unique',16,1);

	BEGIN TRAN;
		exec dbo.cms_createSiteResource @resourceTypeID = @sectionResourceTypeID, @siteResourceStatusID = 1,
			@siteID = @siteid, @isVisible = 1, @parentSiteResourceID = @parentSiteResourceID, @siteResourceID   = @sectionResourceID OUTPUT;

		INSERT INTO dbo.cms_pageSections (siteID, ovTemplateID, ovTemplateIDMobile, ovModeID, parentSectionID, sectionName, sectionCode,sectionBreadcrumb, inheritPlacements, siteResourceID)
		VALUES (@siteID, @ovTemplateID, @ovTemplateIDMobile, @ovModeID, @parentSectionID, @sectionName, nullif(@sectionCode,''),nullif(@sectionBreadcrumb,''), @inheritPlacements, @sectionResourceID);
	
		select @sectionID = SCOPE_IDENTITY();

		exec dbo.cache_cms_updateRecursivePageSections @siteID = @siteID, @restrictToSectionID = @sectionID;

		exec dbo.cache_cms_updateDerivedPageSectionSettings @restrictToSiteID = @siteID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO