ALTER PROC dbo.ams_createMemberDataColumn
@siteID int,
@columnName varchar(128),
@columnDesc varchar(255),
@allowMultiple bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(14,2),
@maxValueDecimal2 decimal(14,2),
@minValueDate date,
@maxValueDate date,
@linkedDateColumnID int, 
@linkedDateCompareDate date, 
@linkedDateCompareDateAFID int,
@linkedDateAdvanceDate date, 
@linkedDateAdvanceAFID int,
@recordedByMemberID int,
@columnID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @valueID int, @dataTypeID int, @displayTypeID int, @crlf varchar(10), @msgjson varchar(max);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	-- cannot be a reserved name
	IF (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1
		RAISERROR('That column name is invalid or already in use.', 16, 1);

	-- if not there, add it
	SET @columnID = null;
	SET @crlf = char(13) + char(10);
	SELECT @dataTypeID = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = @dataTypeCode;
		IF @dataTypeID is null RAISERROR('invalid datatypecode', 16, 1);
	SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode;
		IF @displayTypeID is null RAISERROR('invalid displaytypecode', 16, 1);

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO' BEGIN
		SET @displayTypeCode = 'CHECKBOX';
		SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode;
	END

	-- validations
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('CONTENTOBJ','DOCUMENTOBJ')
		SET @allowNull = 1;
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SET @allowNull = 1;
	IF @allowNull = 1
		SET @defaultValue = '';
	
	BEGIN TRAN;
		-- add column
		INSERT INTO dbo.ams_memberDataColumns (orgID, columnName, columnDesc, dataTypeID, displayTypeID, allowNull, 
			allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, 
			minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate, linkedDateColumnID, 
			linkedDateCompareDate, linkedDateCompareDateAFID, linkedDateAdvanceDate, linkedDateAdvanceAFID)
		VALUES (@orgID, @columnName, @columnDesc, @dataTypeID, @displayTypeID, @allowNull, @allowNewValuesOnImport, 
			null, @isReadOnly, @allowMultiple, @minChars, @maxChars, @minSelected, @maxSelected, @minValueInt, @maxValueInt, 
			@minValueDecimal2, @maxValueDecimal2, @minValueDate, @maxValueDate, @linkedDateColumnID, @linkedDateCompareDate, 
			@linkedDateCompareDateAFID, @linkedDateAdvanceDate, @linkedDateAdvanceAFID);
		SELECT @columnID = SCOPE_IDENTITY();
		
		-- if adding a bit column, add the two values automatically
		IF @dataTypeCode = 'BIT' BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=0, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create bit column value 0', 16, 1);
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=1, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create bit column value 1', 16, 1);
		END

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT;
				IF @valueID = 0 RAISERROR('unable to create default column value', 16, 1);
			UPDATE dbo.ams_memberDataColumns 
			SET defaultValueID = @valueID
			WHERE columnID = @columnID;
		
			-- Anyone who doesnt have a value for this column needs this value.
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
			CREATE TABLE #tblMDDEF (memberid int PRIMARY KEY);

			insert into #tblMDDEF (memberid)
			select distinct m.memberid
			from dbo.ams_members as m
			left outer join dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
				on md.memberid = m.memberID
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
			and md.dataid is null;

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF;

			UPDATE dbo.ams_members
			SET dateLastUpdated = getdate()
			WHERE memberID in (select memberID from #tblMDDEF);

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
		END
	COMMIT TRAN;

	-- audit log
	SET @msgjson = 'New Custom Field [' + @columnName + '] has been created.' + @crlf
		+ 'Data stored as: ' + @dataTypeCode + @crlf
		+ 'Display field as: ' + @displayTypeCode;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES('{ "c":"auditLog", "d": {
		"AUDITCODE":"MEMCF",
		"ORGID":' + CAST(@orgID AS varchar(10)) + ',
		"SITEID":' + CAST(@siteID AS varchar(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
		"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

	-- recreate view for org
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- execute linked date custom field rules after creating org views
	IF @linkedDateColumnID IS NOT NULL
		EXEC dbo.ams_runLinkedDateCustomFieldRule @orgID=@orgID, @memberID=NULL, @columnID=@columnID, @recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
