<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);
		
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>	

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.strEventWidgetData = { title='Define Event Filter', 
				description='Filter the events appearing on this report using the defined criteria below.',
				gridext="#this.siteResourceID#_1", gridClassList='mb-5 stepDIV', 
				initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				filterMode=1, showIcons=1 }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strEventWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strEventWidget = createObject("component","model.admin.common.modules.eventWidget.eventWidget").renderWidget(strWidgetData=local.strEventWidgetData)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreditAuthorities">
				select DISTINCT ca.authorityID, ca.authorityName
				FROM dbo.crd_authorities as ca
				INNER JOIN dbo.crd_authoritySponsors as cas on cas.authorityID = ca.authorityID
				INNER JOIN dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID
					and cs.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
				order by ca.authorityName
			</cfquery>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strEventWidget.js#
				<script language="javascript">
					function forceEvent() {
						var feResult = false;
						var forceEventResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.eventcount > 0) feResult=true; 
							else { rptShowAlert('This report requires you to select one or more events in the Event Filter.'); feResult=false; }
						};
						var objParams = { rptid:#arguments.event.getValue('qryReportInfo').reportID#, csrid:#this.siteResourceID# };
						TS_AJX_SYNC('EVWIDGET','checkEventFilterLength',objParams,forceEventResult,forceEventResult,5000,forceEventResult);
						return feResult;
					}
					function changeView() {
						if ($('##frmView').val() == 'pivot') { $('button##btnReportBarscreen, button##btnReportBarpdf').hide(); }
						else { $('button##btnReportBarscreen, button##btnReportBarpdf').show(); }
					}

					$(function() { 
						mca_setupSelect2(); 
						changeView(); 
					});
				</script>
				</cfoutput>
			</cfsavecontent>		
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmview/text())")>
					<cfset local.caIDList = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/caidlist/text())")>

					<cfform name="frmReport" id="frmReport" method="post">
					<cfinput type="hidden" name="reportAction" id="reportAction" value="">
					#local.strEventWidget.html#
					#showStepMemberCriteria(event=arguments.event, title="Optionally Define Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")#

					<div class="mb-5 stepDIV">
						<h5>Define Extra Criteria</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="creditAuth" class="col-md-4 col-sm-12 col-form-label">Limit to Credit Authority</label>
									<div class="col-md-8 col-sm-12">
										<select name="creditAuth" id="creditAuth" class="form-control form-control-sm" multiple="true" <cfif local.qryCreditAuthorities.recordcount EQ 1>disabled</cfif> data-toggle="custom-select2">
											<cfloop query="local.qryCreditAuthorities">
												<option value="#local.qryCreditAuthorities.authorityID#" <cfif local.qryCreditAuthorities.recordcount EQ 1>selected<cfelseif ListContains(local.caIDList, local.qryCreditAuthorities.authorityID)>selected</cfif>>#local.qryCreditAuthorities.authorityName#</option>
											</cfloop>
										</select>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmView" id="frmView" onchange="changeView();" class="form-control form-control-sm">
											<option value="normal" <cfif local.frmView eq "normal">selected</cfif>>Standard view</option>
											<option value="pivot" <cfif local.frmView eq "pivot">selected</cfif>>Pivoted - one row per registrant</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepFieldsets(event=arguments.event)#
					#showButtonBar(event=arguments.event,validateFunction='forceEvent')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<cfset local.caidList = XMLSearch(arguments.qryReportInfo.otherXML,'string(/report/extra/caidlist/text())')>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>

			<cfset local.tempTableName = "rpt#getTickCount()#">

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">,
						@outputFieldsXML xml;

					<cfif len(local.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
					
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					SELECT m.memberID, m.firstname, m.lastname, m.memberNumber, m.company, m.hasMemberPhotoThumb, tmp.startTime, 
						e.eventID, tmp.eventTitle, rc.creditValueAwarded, isnull(ecast.ovTypeName,cat.typeName) as creditType
					INTO ###local.tempTableName#
					FROM dbo.ev_registrants as r
					INNER JOIN dbo.ev_registration as rn on rn.registrationID = r.registrationID 
						and r.recordedOnSiteID = rn.siteID
						AND rn.status = 'A'
					INNER JOIN dbo.ev_events as e on e.eventID = rn.eventID 
						and e.siteID = rn.siteID
						and e.status = 'A'
					INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID
						and rc.creditAwarded = 1
					INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					INNER JOIN dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID
					<cfif LEN(local.caidList)>
						and cat.authorityID IN (#local.caidList#)
					</cfif>
					INNER JOIN dbo.ams_members as mReg on mReg.memberID = r.memberID
					INNER JOIN dbo.ams_members as m on m.memberID = mReg.activeMemberID 
						and m.isProtected = 0
						and m.orgID = @orgID
						and m.memberID = m.activeMemberID
						and m.status <> 'D'
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					WHERE r.status = 'A'
					and r.attended = 1;
					
					CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName# (memberID);

					-- get fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
						@membersTableName='###local.tempTableName#', @membersResultTableName='##tmpMembersFS', @linkedMembers=0,
						@mode='report',	@outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM (
						SELECT tmp.lastname, tmp.firstname, tmp.membernumber, tmp.company, tmp.hasMemberPhotoThumb, tmp.eventID, 
							tmp.startTime, tmp.eventTitle, tmp.creditValueAwarded, tmp.creditType, m.*,
							ROW_NUMBER() OVER (ORDER BY tmp.lastname, tmp.firstname, tmp.membernumber) as mc_row
						FROM ###local.tempTableName# AS tmp
						INNER JOIN ##tmpMembersFS AS m ON m.memberID = tmp.memberID
					) as finalData
					ORDER BY mc_row;

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
						DROP TABLE ##tmpEventsOnSite;
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	

			<cfif local.qryData.recordcount>
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.qryData.mc_outputFieldsXML)>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where fieldcodeSect NOT IN ('mc','m','ma','mat')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated')
				</cfquery>
			</cfif>
	
			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
					<tr>
						<th class="text-left" <cfif local.frmShowPhotos is 1>colspan="2"</cfif>>Member</th>
						<th>&nbsp;</th>
						<th class="text-left">Credits Awarded</th>
					</tr>
					</thead>
					<tbody>
					</cfoutput>

					<cfoutput query="local.qryData" group="memberid">
						<tr>
							<cfif local.frmShowPhotos is 1>
								<td class="align-top" style="width:80px;">
									<cfif local.qryData.hasMemberPhotoThumb is 1>
										<cfif arguments.reportAction eq "screen">
											<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryData.MemberNumber)#.jpg">
										<cfelse>
											<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryData.MemberNumber)#.jpg">
										</cfif>
									<cfelse>
										<cfif arguments.reportAction eq "screen">
											<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
										<cfelse>
											<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
										</cfif>
									</cfif>
								</td>
							</cfif>
							<td colspan="3" class="align-top pt-2 <cfif local.frmShowPhotos is 1>pl-2</cfif>">
								<cfif arguments.reportAction eq "screen">
									<a href="#local.memberLink#&memberid=#local.qryData.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></a><br/>
								<cfelse>
									<b><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></b><br/>
								</cfif>
								<cfif local.frmShowCompany is 1 AND len(local.qryData.company)>#local.qryData.company#<br/></cfif>							
								<cfloop query="local.qryOutputFields">
									<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
										<cfset local.AddrToShow = local.qryData[local.qryOutputFields.dbfield][local.qryData.currentrow]>
										<cfloop condition="Find(', , ',local.AddrToShow)">
											<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
										</cfloop>
										<cfif len(local.AddrToShow)>
											#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
										</cfif>
									</cfif>
								</cfloop>
								<cfloop query="local.qryOutputFieldsForLoop">
									<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
										#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow])#<br/>
									<cfelseif len(local.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryData.currentrow])>
										<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
											#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow], "m/d/yyyy")#<br/>
										<cfelse>
											#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow]#<br/>
										</cfif>
									</cfif>
								</cfloop>
							</td>
						</tr>
						<cfoutput group="eventid">
							<tr>
								<td <cfif local.frmShowPhotos is 1>colspan="2"</cfif></td>
								<td class="align-top">#dateformat(local.qryData.startTime,'m/d/yyyy')# - #local.qryData.eventTitle#</td>
								<td class="align-top pb-1" nowrap>
									<cfoutput>#local.qryData.creditValueAwarded# &nbsp; #local.qryData.creditType#<br/></cfoutput>
								</td>
							</tr>
						</cfoutput>
					</cfoutput>
					<cfoutput>
					</tbody>
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryData", strQryResult=local.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvReport" access="package" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.tempTableName = "rpt#getTickCount()#">
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfif arguments.reportAction EQ 'csv'>
				<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
					reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
					existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m",
					dropTblName="###local.tempTableName#", dropColList="memberid,pvtMemberID,pvtEventID")>

				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.sitecode)>
				<cfset local.frmView ='pivot'>
			<cfelse>
				<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
					reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
					existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>

				<cfset local.frmView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>
			</cfif>

			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<cfset local.caidList = XMLSearch(arguments.qryReportInfo.otherXML,'string(/report/extra/caidlist/text())')>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">,
						@outputFieldsXML xml;

					<cfif len(local.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
					
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..#####local.tempTableName#_3') IS NOT NULL
						DROP TABLE #####local.tempTableName#_3;
					IF OBJECT_ID('tempdb..#####local.tempTableName#_4') IS NOT NULL
						DROP TABLE #####local.tempTableName#_4;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
					
					SELECT m.memberID, m.lastname, m.firstname, m.MemberNumber, m.company, e.eventID, tmp.startTime, tmp.eventTitle, 
						e.reportCode, rc.creditValueAwarded, ca.authorityCode + '_' + cat.typeCode as pivotTypeCode,
						ca.authorityName, isnull(ecast.ovTypeName,cat.typeName) as typeAwarded, oc.ApprovalNum
					INTO ###local.tempTableName#_2
					FROM dbo.ev_registrants as r
					INNER JOIN dbo.ev_registration as rn on rn.registrationID = r.registrationID
						AND rn.status = 'A' and r.recordedOnSiteID = rn.siteID
					INNER JOIN dbo.ev_events as e on e.eventID = rn.eventID
						and e.status = 'A' and e.siteID = rn.siteID
					INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
					INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID
						and rc.creditAwarded = 1
					INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
					INNER JOIN dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
					INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID
					INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
					<cfif LEN(local.caidList)>
						AND ca.authorityID IN (#local.caidList#)
					</cfif>
					INNER JOIN dbo.crd_offerings as oc on ect.offeringID = oc.offeringID
					INNER JOIN dbo.ams_members as mReg on mReg.memberID = r.memberID
					INNER JOIN dbo.ams_members as m on m.memberID = mReg.activeMemberID and m.isProtected = 0
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					WHERE m.orgID = @orgID
					and m.memberID = m.activeMemberID
					and m.status <> 'D'
					and r.status = 'A'
					and r.attended = 1;
					
					CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);

					<cfif local.frmView eq "Normal">
						-- get fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
							@mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select tm.lastname AS [Last Name], tm.firstname AS [First Name], tm.MemberNumber, tm.Company, tm.eventID AS [Event ID], 
							tm.startTime AS [Event Date], tm.eventTitle AS [Event], tm.reportCode AS [Event Report Code], tm.creditValueAwarded AS [Num Awarded], 
							tm.authorityName AS [Credit Authority], tm.typeAwarded AS [Type Awarded], tm.ApprovalNum AS [Approval Number], m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_2 as tm
						inner join ##tmpMembersFS as m on m.memberID = tm.memberID;

						#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="memberid")#
					<cfelse>
						DECLARE @creditColumnsPVT VARCHAR(8000), @approvalColumnsPVT VARCHAR(8000), @fullsql varchar(8000);

						SELECT @creditColumnsPVT = COALESCE(@creditColumnsPVT + ',', '') + quoteName(ca.authorityCode + '_' + cat.typeCode),
							   @approvalColumnsPVT = COALESCE(@approvalColumnsPVT + ',', '') + quoteName(ca.authorityCode + '_' + cat.typeCode + '_ApprovalNum')
						FROM dbo.crd_sponsors as cs
						INNER JOIN dbo.crd_authoritySponsors as cas on cas.sponsorID = cs.sponsorID
						INNER JOIN dbo.crd_authoritySponsorTypes as cst on cst.ASID = cas.ASID
						INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
						<cfif LEN(local.caidList)>
							AND ca.authorityID IN (#local.caidList#)
						</cfif>
						INNER JOIN dbo.crd_authorityTypes as cat on cat.authorityID = ca.authorityID and cat.typeID = cst.typeID
						WHERE cs.orgID = @orgID;

						-- Create pivot table for credit values
						set @fullsql = 'select memberID as pvtMemberID, eventID as pvtEventID, ' + @creditColumnsPVT + '
							into #####local.tempTableName#_3
							from (
								select memberID, eventID, pivotTypeCode, creditValueAwarded
								from ###local.tempTableName#_2
							) as tmp
							PIVOT (min(creditValueAwarded) for pivotTypeCode in (' + @creditColumnsPVT + ')) as pvt';
						EXEC(@fullsql);

						-- Create pivot table for approval numbers
						set @fullsql = 'select memberID as pvtMemberID, eventID as pvtEventID, ' + @approvalColumnsPVT + '
							into #####local.tempTableName#_4
							from (
								select memberID, eventID, pivotTypeCode + ''_ApprovalNum'' as pivotTypeCode, ApprovalNum
								from ###local.tempTableName#_2
							) as tmp
							PIVOT (min(ApprovalNum) for pivotTypeCode in (' + @approvalColumnsPVT + ')) as pvt';
						EXEC(@fullsql);

						-- get fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
							@mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select distinct tmp2.lastname AS [Last Name], tmp2.firstname AS [First Name], tmp2.MemberNumber, tmp2.Company,
							tmp2.eventID AS [Event ID], tmp2.startTime AS [Event Date], tmp2.eventTitle AS [Event],
							tmp2.reportCode AS [Event Report Code], tmp3.*, m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_2 as tmp2
						inner join #####local.tempTableName#_3 as tmp3 on tmp3.pvtMemberID = tmp2.memberID and tmp3.pvtEventID = tmp2.eventID
						inner join ##tmpMembersFS as m on m.memberID = tmp2.memberID;

						-- Add approval number columns via ALTER TABLE and UPDATE
						DECLARE @alterSql varchar(max), @updateSql varchar(max);

						SELECT @alterSql = COALESCE(@alterSql + ' ', '') + 'ALTER TABLE ###local.tempTableName# ADD ' + quoteName(ca.authorityCode + '_' + cat.typeCode + '_ApprovalNum') + ' varchar(50) NULL;'
						FROM dbo.crd_sponsors as cs
						INNER JOIN dbo.crd_authoritySponsors as cas on cas.sponsorID = cs.sponsorID
						INNER JOIN dbo.crd_authoritySponsorTypes as cst on cst.ASID = cas.ASID
						INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
						<cfif LEN(local.caidList)>
							AND ca.authorityID IN (#local.caidList#)
						</cfif>
						INNER JOIN dbo.crd_authorityTypes as cat on cat.authorityID = ca.authorityID and cat.typeID = cst.typeID
						WHERE cs.orgID = @orgID;

						IF @alterSql IS NOT NULL EXEC(@alterSql);

						-- Update approval number columns from the approval pivot table
						set @updateSql = 'UPDATE t SET ' + STUFF((
							SELECT ', ' + quoteName(ca.authorityCode + '_' + cat.typeCode + '_ApprovalNum') + ' = tmp4.' + quoteName(ca.authorityCode + '_' + cat.typeCode + '_ApprovalNum')
							FROM dbo.crd_sponsors as cs
							INNER JOIN dbo.crd_authoritySponsors as cas on cas.sponsorID = cs.sponsorID
							INNER JOIN dbo.crd_authoritySponsorTypes as cst on cst.ASID = cas.ASID
							INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
							<cfif LEN(local.caidList)>
								AND ca.authorityID IN (#local.caidList#)
							</cfif>
							INNER JOIN dbo.crd_authorityTypes as cat on cat.authorityID = ca.authorityID and cat.typeID = cst.typeID
							WHERE cs.orgID = @orgID
							FOR XML PATH('')
						), 1, 2, '') + '
						FROM ###local.tempTableName# t
						INNER JOIN #####local.tempTableName#_4 tmp4 ON t.[Event ID] = tmp4.pvtEventID';

						IF @updateSql IS NOT NULL AND @updateSql <> 'UPDATE t SET ' EXEC(@updateSql);

						<cfif arguments.reportAction EQ 'csv'>
							<cfif len(local.strSQLPrep.CSVDropSQL)>#PreserveSingleQuotes(local.strSQLPrep.CSVDropSQL)#</cfif>

							DECLARE @selectsql varchar(max) = '
								SELECT *, ROW_NUMBER() OVER (ORDER BY [Last Name], [First Name], MemberNumber) as mcCSVorder 
								*FROM* ###local.tempTableName#';
							EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\#arguments.qryReportInfo.reportName#.csv', @returnColumns=0;
						<cfelse>
							#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="memberid,pvtMemberID,pvtEventID")#
						</cfif>
					</cfif>

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
						DROP TABLE ##tmpEventsOnSite;
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..#####local.tempTableName#_3') IS NOT NULL
						DROP TABLE #####local.tempTableName#_3;
					IF OBJECT_ID('tempdb..#####local.tempTableName#_4') IS NOT NULL
						DROP TABLE #####local.tempTableName#_4;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	

			<cfscript>
				if (arguments.reportAction EQ 'csv') {
					local.strReturn.csvDownloadPath = '#local.strFolder.folderPath#/#arguments.qryReportInfo.reportName#.csv';
				} else {
					local.arrInitialReportSort = arrayNew(1);
					local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strReportQry = { qryReportFields=local.qryData, strQryResult=local.qryDataResult };
					local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
				}
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);
		
		local.strFields = structNew();
		local.strFields.caidlist = { label="Credit List", value=arguments.event.getValue('creditAuth','') };
		local.strFields.frmview = { label="Report View", value=arguments.event.getValue('frmView','') };
		local.strFields.eidList = { label="Event List", value=XMLSearch(local.otherXML,'string(/report/extra/eidlist/text())') };
		
		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>
